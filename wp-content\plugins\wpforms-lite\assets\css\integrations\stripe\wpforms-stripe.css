.wpforms-form .wpforms-field-stripe-credit-card .StripeElement {
  margin-bottom: 5px;
}

.wpforms-form .wpforms-field-stripe-credit-card .StripeElement .__PrivateStripeElement iframe {
  margin: 0 0 0 -4px !important;
}

.wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview {
  display: flex;
  position: absolute;
  width: fit-content;
  height: 36px;
  line-height: 36px;
  padding: 0 0 0 10px;
  top: 0;
  left: 0;
  bottom: 0;
}

.wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview svg {
  width: 24px;
  fill: #000000;
  margin-right: 10px;
}

.wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview span {
  opacity: 0.5;
  font-size: 16px;
  color: #333333;
}

.wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-expcvc-preview {
  display: block;
  position: absolute;
  width: fit-content;
  height: 36px;
  line-height: 36px;
  padding: 0 10px 0 0;
  font-size: 16px;
  top: 0;
  right: 0;
  bottom: 0;
  color: #333333;
  opacity: 0.5;
}

.wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-row {
  container-type: inline-size;
  container-name: wpforms-field-row-small  wpforms-field-row-responsive;
}

@container wpforms-field-row-small (max-width: 200px) {
  .wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-row .wpforms-stripe-payment-element-cvc-preview svg,
  .wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-row .wpforms-stripe-payment-element-cardnumber-preview,
  .wpforms-form .wpforms-field-stripe-credit-card .wpforms-field-row .wpforms-field-stripe-credit-card-number-expcvc-preview {
    display: none;
  }
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field-stripe-credit-card .wpforms-field-row {
  margin-left: 0;
  margin-right: 0;
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field-stripe-credit-card .wpforms-field-row .StripeElement {
  width: calc( 100% + 8px);
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field-stripe-credit-card .wpforms-field-row select {
  max-width: 100%;
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field-stripe-credit-card-number-placeholder-preview {
  height: 46px;
  line-height: 46px;
  color: rgba(var(--wpforms-lead-forms-secondary-text-color), 1);
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field-stripe-credit-card-number-placeholder-preview span {
  color: rgba(var(--wpforms-lead-forms-secondary-text-color), 1);
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field-stripe-credit-card-number-expcvc-preview {
  height: 46px;
  line-height: 46px;
  color: rgba(var(--wpforms-lead-forms-secondary-text-color), 0.5);
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=date],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=datetime],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=datetime-local],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=email],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=month],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=number],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=password],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=range],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=search],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=tel],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=text],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=time],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=url],
.wpforms-lead-forms-container.wpforms-container .wpforms-field input[type=week],
.wpforms-lead-forms-container.wpforms-container .wpforms-field select,
.wpforms-lead-forms-container.wpforms-container .wpforms-field textarea {
  opacity: 1;
  background-color: transparent;
}

.wpforms-lead-forms-container.wpforms-container .wpforms-field .wpforms-field-row.wpforms-no-columns {
  display: block;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-cardnumber {
  background-color: var(--wpforms-field-background-color);
  box-sizing: border-box;
  border-radius: var(--wpforms-field-border-radius);
  color: var(--wpforms-field-text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--wpforms-field-size-font-size);
  border-width: var(--wpforms-field-border-size);
  border-style: var(--wpforms-field-border-style);
  border-color: var(--wpforms-field-border-color);
  padding: 0 var(--wpforms-field-size-padding-h);
  height: var(--wpforms-field-size-input-height);
  width: 100%;
  line-height: 1;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-cardnumber.wpforms-stripe-element-focus {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-cardnumber.wpforms-stripe-element-invalid {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-cardnumber.wpforms-stripe-element-invalid:hover {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  box-shadow: 0 0 2px 0 var(--wpforms-label-error-color);
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-cardnumber.wpforms-stripe-element-invalid.wpforms-stripe-element-focus {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  box-shadow: 0 0 0 1px var(--wpforms-label-error-color);
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-cardnumber > .__PrivateStripeElement {
  width: 100%;
  height: calc( var( --wpforms-field-size-font-size ) + 4px);
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-sublabel + .wpforms-error {
  margin-top: calc( 1.5 * var( --wpforms-field-size-sublabel-spacing ));
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview {
  display: flex;
  position: absolute;
  width: 100%;
  height: var(--wpforms-field-size-input-height);
  line-height: var(--wpforms-field-size-input-height);
  padding: 0 0 0 var(--wpforms-field-size-padding-h);
  top: 0;
  left: 0;
  bottom: 0;
  overflow-x: hidden;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview svg {
  width: 24px;
  fill: #000000;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview span {
  opacity: 0.5;
  font-size: var(--wpforms-field-size-font-size);
  color: var(--wpforms-field-text-color);
  white-space: nowrap;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-expcvc-preview {
  display: block;
  position: absolute;
  width: fit-content;
  height: var(--wpforms-field-size-input-height);
  line-height: var(--wpforms-field-size-input-height);
  padding: 0 calc( var( --wpforms-field-size-padding-h ) * 2) 0 0;
  font-size: var(--wpforms-field-size-font-size);
  top: 0;
  right: 0;
  bottom: 0;
  color: var(--wpforms-field-text-color);
  opacity: 0.5;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-row {
  container-type: inline-size;
  container-name: wpforms-field-row-small;
}

@container wpforms-field-row-small (max-width: 300px) {
  div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-field-row .wpforms-field-stripe-credit-card-number-expcvc-preview {
    display: none;
  }
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-stripe-payment-element-cvc-preview svg {
  position: absolute;
  bottom: calc( ( var( --wpforms-field-size-input-height ) - 24px ) / 2);
  right: var(--wpforms-field-size-padding-h);
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-stripe-payment-element .wpforms-field-row {
  container-type: inline-size;
  container-name: wpforms-field-row-xs wpforms-field-row-s wpforms-field-row-m wpforms-field-row-responsive;
}

@container wpforms-field-row-m (max-width: 320px) {
  div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-stripe-payment-element .wpforms-field-row .wpforms-stripe-payment-element-cardnumber-preview {
    width: 64px;
  }
}

@container wpforms-field-row-s (max-width: 240px) {
  div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-stripe-payment-element .wpforms-field-row .wpforms-stripe-payment-element-cardnumber-preview {
    width: 32px;
  }
}

@container wpforms-field-row-xs (max-width: 210px) {
  div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-stripe-payment-element .wpforms-field-row .wpforms-stripe-payment-element-cardnumber-preview {
    display: none;
  }
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .wpforms-stripe-payment-element-cardnumber-preview {
  position: absolute;
  bottom: calc( ( var( --wpforms-field-size-input-height ) - 20px ) / 2);
  right: var(--wpforms-field-size-padding-h);
  width: 136px;
  height: 20px;
  background-image: url("../../../images/integrations/stripe/cc-preview.png");
  background-repeat: no-repeat;
  background-size: 136px 20px;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .StripeElement {
  margin: 0 4px var(--wpforms-field-size-input-spacing) -4px;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .StripeElement:last-of-type {
  margin-bottom: 0;
}

div.wpforms-container.wpforms-render-modern .wpforms-field-stripe-credit-card .StripeElement .__PrivateStripeElement iframe {
  margin: 0 !important;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
