!function(){var e={905:function(){jQuery,acf.jsxNameReplacements={"accent-height":"accentHeight",accentheight:"accentHeight","accept-charset":"acceptCharset",acceptcharset:"acceptCharset",accesskey:"accessKey","alignment-baseline":"alignmentBaseline",alignmentbaseline:"alignmentBaseline",allowedblocks:"allowedBlocks",allowfullscreen:"allowFullScreen",allowreorder:"allowReorder","arabic-form":"arabicForm",arabicform:"arabicForm",attributename:"attributeName",attributetype:"attributeType",autocapitalize:"autoCapitalize",autocomplete:"autoComplete",autocorrect:"autoCorrect",autofocus:"autoFocus",autoplay:"autoPlay",autoreverse:"autoReverse",autosave:"autoSave",basefrequency:"baseFrequency","baseline-shift":"baselineShift",baselineshift:"baselineShift",baseprofile:"baseProfile",calcmode:"calcMode","cap-height":"capHeight",capheight:"capHeight",cellpadding:"cellPadding",cellspacing:"cellSpacing",charset:"charSet",class:"className",classid:"classID",classname:"className","clip-path":"clipPath","clip-rule":"clipRule",clippath:"clipPath",clippathunits:"clipPathUnits",cliprule:"clipRule","color-interpolation":"colorInterpolation","color-interpolation-filters":"colorInterpolationFilters","color-profile":"colorProfile","color-rendering":"colorRendering",colorinterpolation:"colorInterpolation",colorinterpolationfilters:"colorInterpolationFilters",colorprofile:"colorProfile",colorrendering:"colorRendering",colspan:"colSpan",contenteditable:"contentEditable",contentscripttype:"contentScriptType",contentstyletype:"contentStyleType",contextmenu:"contextMenu",controlslist:"controlsList",crossorigin:"crossOrigin",dangerouslysetinnerhtml:"dangerouslySetInnerHTML",datetime:"dateTime",defaultchecked:"defaultChecked",defaultvalue:"defaultValue",diffuseconstant:"diffuseConstant",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback","dominant-baseline":"dominantBaseline",dominantbaseline:"dominantBaseline",edgemode:"edgeMode","enable-background":"enableBackground",enablebackground:"enableBackground",enctype:"encType",enterkeyhint:"enterKeyHint",externalresourcesrequired:"externalResourcesRequired","fill-opacity":"fillOpacity","fill-rule":"fillRule",fillopacity:"fillOpacity",fillrule:"fillRule",filterres:"filterRes",filterunits:"filterUnits","flood-color":"floodColor","flood-opacity":"floodOpacity",floodcolor:"floodColor",floodopacity:"floodOpacity","font-family":"fontFamily","font-size":"fontSize","font-size-adjust":"fontSizeAdjust","font-stretch":"fontStretch","font-style":"fontStyle","font-variant":"fontVariant","font-weight":"fontWeight",fontfamily:"fontFamily",fontsize:"fontSize",fontsizeadjust:"fontSizeAdjust",fontstretch:"fontStretch",fontstyle:"fontStyle",fontvariant:"fontVariant",fontweight:"fontWeight",for:"htmlFor",foreignobject:"foreignObject",formaction:"formAction",formenctype:"formEncType",formmethod:"formMethod",formnovalidate:"formNoValidate",formtarget:"formTarget",frameborder:"frameBorder","glyph-name":"glyphName","glyph-orientation-horizontal":"glyphOrientationHorizontal","glyph-orientation-vertical":"glyphOrientationVertical",glyphname:"glyphName",glyphorientationhorizontal:"glyphOrientationHorizontal",glyphorientationvertical:"glyphOrientationVertical",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits","horiz-adv-x":"horizAdvX","horiz-origin-x":"horizOriginX",horizadvx:"horizAdvX",horizoriginx:"horizOriginX",hreflang:"hrefLang",htmlfor:"htmlFor","http-equiv":"httpEquiv",httpequiv:"httpEquiv","image-rendering":"imageRendering",imagerendering:"imageRendering",innerhtml:"innerHTML",inputmode:"inputMode",itemid:"itemID",itemprop:"itemProp",itemref:"itemRef",itemscope:"itemScope",itemtype:"itemType",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keyparams:"keyParams",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",keytype:"keyType",lengthadjust:"lengthAdjust","letter-spacing":"letterSpacing",letterspacing:"letterSpacing","lighting-color":"lightingColor",lightingcolor:"lightingColor",limitingconeangle:"limitingConeAngle",marginheight:"marginHeight",marginwidth:"marginWidth","marker-end":"markerEnd","marker-mid":"markerMid","marker-start":"markerStart",markerend:"markerEnd",markerheight:"markerHeight",markermid:"markerMid",markerstart:"markerStart",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",maxlength:"maxLength",mediagroup:"mediaGroup",minlength:"minLength",nomodule:"noModule",novalidate:"noValidate",numoctaves:"numOctaves","overline-position":"overlinePosition","overline-thickness":"overlineThickness",overlineposition:"overlinePosition",overlinethickness:"overlineThickness","paint-order":"paintOrder",paintorder:"paintOrder","panose-1":"panose1",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",playsinline:"playsInline","pointer-events":"pointerEvents",pointerevents:"pointerEvents",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",radiogroup:"radioGroup",readonly:"readOnly",referrerpolicy:"referrerPolicy",refx:"refX",refy:"refY","rendering-intent":"renderingIntent",renderingintent:"renderingIntent",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",rowspan:"rowSpan","shape-rendering":"shapeRendering",shaperendering:"shapeRendering",specularconstant:"specularConstant",specularexponent:"specularExponent",spellcheck:"spellCheck",spreadmethod:"spreadMethod",srcdoc:"srcDoc",srclang:"srcLang",srcset:"srcSet",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles","stop-color":"stopColor","stop-opacity":"stopOpacity",stopcolor:"stopColor",stopopacity:"stopOpacity","strikethrough-position":"strikethroughPosition","strikethrough-thickness":"strikethroughThickness",strikethroughposition:"strikethroughPosition",strikethroughthickness:"strikethroughThickness","stroke-dasharray":"strokeDasharray","stroke-dashoffset":"strokeDashoffset","stroke-linecap":"strokeLinecap","stroke-linejoin":"strokeLinejoin","stroke-miterlimit":"strokeMiterlimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth",strokedasharray:"strokeDasharray",strokedashoffset:"strokeDashoffset",strokelinecap:"strokeLinecap",strokelinejoin:"strokeLinejoin",strokemiterlimit:"strokeMiterlimit",strokeopacity:"strokeOpacity",strokewidth:"strokeWidth",suppresscontenteditablewarning:"suppressContentEditableWarning",suppresshydrationwarning:"suppressHydrationWarning",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tabindex:"tabIndex",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",templatelock:"templateLock","text-anchor":"textAnchor","text-decoration":"textDecoration","text-rendering":"textRendering",textanchor:"textAnchor",textdecoration:"textDecoration",textlength:"textLength",textrendering:"textRendering","underline-position":"underlinePosition","underline-thickness":"underlineThickness",underlineposition:"underlinePosition",underlinethickness:"underlineThickness","unicode-bidi":"unicodeBidi","unicode-range":"unicodeRange",unicodebidi:"unicodeBidi",unicoderange:"unicodeRange","units-per-em":"unitsPerEm",unitsperem:"unitsPerEm",usemap:"useMap","v-alphabetic":"vAlphabetic","v-hanging":"vHanging","v-ideographic":"vIdeographic","v-mathematical":"vMathematical",valphabetic:"vAlphabetic","vector-effect":"vectorEffect",vectoreffect:"vectorEffect","vert-adv-y":"vertAdvY","vert-origin-x":"vertOriginX","vert-origin-y":"vertOriginY",vertadvy:"vertAdvY",vertoriginx:"vertOriginX",vertoriginy:"vertOriginY",vhanging:"vHanging",videographic:"vIdeographic",viewbox:"viewBox",viewtarget:"viewTarget",vmathematical:"vMathematical","word-spacing":"wordSpacing",wordspacing:"wordSpacing","writing-mode":"writingMode",writingmode:"writingMode","x-height":"xHeight",xchannelselector:"xChannelSelector",xheight:"xHeight","xlink:actuate":"xlinkActuate","xlink:arcrole":"xlinkArcrole","xlink:href":"xlinkHref","xlink:role":"xlinkRole","xlink:show":"xlinkShow","xlink:title":"xlinkTitle","xlink:type":"xlinkType",xlinkactuate:"xlinkActuate",xlinkarcrole:"xlinkArcrole",xlinkhref:"xlinkHref",xlinkrole:"xlinkRole",xlinkshow:"xlinkShow",xlinktitle:"xlinkTitle",xlinktype:"xlinkType","xml:base":"xmlBase","xml:lang":"xmlLang","xml:space":"xmlSpace",xmlbase:"xmlBase",xmllang:"xmlLang","xmlns:xlink":"xmlnsXlink",xmlnsxlink:"xmlnsXlink",xmlspace:"xmlSpace",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"}},487:function(e){var t={utf8:{stringToBytes:function(e){return t.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(t.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],n=0;n<e.length;n++)t.push(255&e.charCodeAt(n));return t},bytesToString:function(e){for(var t=[],n=0;n<e.length;n++)t.push(String.fromCharCode(e[n]));return t.join("")}}};e.exports=t},12:function(e){var t,n;t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&n.rotl(e,8)|4278255360&n.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=n.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],n=0,r=0;n<e.length;n++,r+=8)t[r>>>5]|=e[n]<<24-r%32;return t},wordsToBytes:function(e){for(var t=[],n=0;n<32*e.length;n+=8)t.push(e[n>>>5]>>>24-n%32&255);return t},bytesToHex:function(e){for(var t=[],n=0;n<e.length;n++)t.push((e[n]>>>4).toString(16)),t.push((15&e[n]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],n=0;n<e.length;n+=2)t.push(parseInt(e.substr(n,2),16));return t},bytesToBase64:function(e){for(var n=[],r=0;r<e.length;r+=3)for(var o=e[r]<<16|e[r+1]<<8|e[r+2],i=0;i<4;i++)8*r+6*i<=8*e.length?n.push(t.charAt(o>>>6*(3-i)&63)):n.push("=");return n.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var n=[],r=0,o=0;r<e.length;o=++r%4)0!=o&&n.push((t.indexOf(e.charAt(r-1))&Math.pow(2,-2*o+8)-1)<<2*o|t.indexOf(e.charAt(r))>>>6-2*o);return n}},e.exports=n},738:function(e){function t(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e){return null!=e&&(t(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&t(e.slice(0,0))}(e)||!!e._isBuffer)}},568:function(e,t,n){var r,o,i,a,s;r=n(12),o=n(487).utf8,i=n(738),a=n(487).bin,(s=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?a.stringToBytes(e):o.stringToBytes(e):i(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var n=r.bytesToWords(e),l=8*e.length,c=1732584193,p=-271733879,u=-1732584194,d=271733878,f=0;f<n.length;f++)n[f]=16711935&(n[f]<<8|n[f]>>>24)|4278255360&(n[f]<<24|n[f]>>>8);n[l>>>5]|=128<<l%32,n[14+(l+64>>>9<<4)]=l;var h=s._ff,m=s._gg,g=s._hh,y=s._ii;for(f=0;f<n.length;f+=16){var b=c,k=p,v=u,x=d;c=h(c,p,u,d,n[f+0],7,-680876936),d=h(d,c,p,u,n[f+1],12,-389564586),u=h(u,d,c,p,n[f+2],17,606105819),p=h(p,u,d,c,n[f+3],22,-1044525330),c=h(c,p,u,d,n[f+4],7,-176418897),d=h(d,c,p,u,n[f+5],12,1200080426),u=h(u,d,c,p,n[f+6],17,-1473231341),p=h(p,u,d,c,n[f+7],22,-45705983),c=h(c,p,u,d,n[f+8],7,1770035416),d=h(d,c,p,u,n[f+9],12,-1958414417),u=h(u,d,c,p,n[f+10],17,-42063),p=h(p,u,d,c,n[f+11],22,-1990404162),c=h(c,p,u,d,n[f+12],7,1804603682),d=h(d,c,p,u,n[f+13],12,-40341101),u=h(u,d,c,p,n[f+14],17,-1502002290),c=m(c,p=h(p,u,d,c,n[f+15],22,1236535329),u,d,n[f+1],5,-165796510),d=m(d,c,p,u,n[f+6],9,-1069501632),u=m(u,d,c,p,n[f+11],14,643717713),p=m(p,u,d,c,n[f+0],20,-373897302),c=m(c,p,u,d,n[f+5],5,-701558691),d=m(d,c,p,u,n[f+10],9,38016083),u=m(u,d,c,p,n[f+15],14,-660478335),p=m(p,u,d,c,n[f+4],20,-405537848),c=m(c,p,u,d,n[f+9],5,568446438),d=m(d,c,p,u,n[f+14],9,-1019803690),u=m(u,d,c,p,n[f+3],14,-187363961),p=m(p,u,d,c,n[f+8],20,1163531501),c=m(c,p,u,d,n[f+13],5,-1444681467),d=m(d,c,p,u,n[f+2],9,-51403784),u=m(u,d,c,p,n[f+7],14,1735328473),c=g(c,p=m(p,u,d,c,n[f+12],20,-1926607734),u,d,n[f+5],4,-378558),d=g(d,c,p,u,n[f+8],11,-2022574463),u=g(u,d,c,p,n[f+11],16,1839030562),p=g(p,u,d,c,n[f+14],23,-35309556),c=g(c,p,u,d,n[f+1],4,-1530992060),d=g(d,c,p,u,n[f+4],11,1272893353),u=g(u,d,c,p,n[f+7],16,-155497632),p=g(p,u,d,c,n[f+10],23,-1094730640),c=g(c,p,u,d,n[f+13],4,681279174),d=g(d,c,p,u,n[f+0],11,-358537222),u=g(u,d,c,p,n[f+3],16,-722521979),p=g(p,u,d,c,n[f+6],23,76029189),c=g(c,p,u,d,n[f+9],4,-640364487),d=g(d,c,p,u,n[f+12],11,-421815835),u=g(u,d,c,p,n[f+15],16,530742520),c=y(c,p=g(p,u,d,c,n[f+2],23,-995338651),u,d,n[f+0],6,-198630844),d=y(d,c,p,u,n[f+7],10,1126891415),u=y(u,d,c,p,n[f+14],15,-1416354905),p=y(p,u,d,c,n[f+5],21,-57434055),c=y(c,p,u,d,n[f+12],6,1700485571),d=y(d,c,p,u,n[f+3],10,-1894986606),u=y(u,d,c,p,n[f+10],15,-1051523),p=y(p,u,d,c,n[f+1],21,-2054922799),c=y(c,p,u,d,n[f+8],6,1873313359),d=y(d,c,p,u,n[f+15],10,-30611744),u=y(u,d,c,p,n[f+6],15,-1560198380),p=y(p,u,d,c,n[f+13],21,1309151649),c=y(c,p,u,d,n[f+4],6,-145523070),d=y(d,c,p,u,n[f+11],10,-1120210379),u=y(u,d,c,p,n[f+2],15,718787259),p=y(p,u,d,c,n[f+9],21,-343485551),c=c+b>>>0,p=p+k>>>0,u=u+v>>>0,d=d+x>>>0}return r.endian([c,p,u,d])})._ff=function(e,t,n,r,o,i,a){var s=e+(t&n|~t&r)+(o>>>0)+a;return(s<<i|s>>>32-i)+t},s._gg=function(e,t,n,r,o,i,a){var s=e+(t&r|n&~r)+(o>>>0)+a;return(s<<i|s>>>32-i)+t},s._hh=function(e,t,n,r,o,i,a){var s=e+(t^n^r)+(o>>>0)+a;return(s<<i|s>>>32-i)+t},s._ii=function(e,t,n,r,o,i,a){var s=e+(n^(t|~r))+(o>>>0)+a;return(s<<i|s>>>32-i)+t},s._blocksize=16,s._digestsize=16,e.exports=function(e,t){if(null==e)throw new Error("Illegal argument "+e);var n=r.wordsToBytes(s(e,t));return t&&t.asBytes?n:t&&t.asString?a.bytesToString(n):r.bytesToHex(n)}},408:function(e,t){"use strict";var n=Symbol.for("react.element"),r=(Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.provider"),Symbol.for("react.context"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.iterator,{isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}}),o=Object.assign,i={};function a(e,t,n){this.props=e,this.context=t,this.refs=i,this.updater=n||r}function s(){}function l(e,t,n){this.props=e,this.context=t,this.refs=i,this.updater=n||r}a.prototype.isReactComponent={},a.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},a.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},s.prototype=a.prototype;var c=l.prototype=new s;c.constructor=l,o(c,a.prototype),c.isPureReactComponent=!0;Array.isArray;var p=Object.prototype.hasOwnProperty,u=null,d={key:!0,ref:!0,__self:!0,__source:!0};t.createElement=function(e,t,r){var o,i={},a=null,s=null;if(null!=t)for(o in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)p.call(t,o)&&!d.hasOwnProperty(o)&&(i[o]=t[o]);var l=arguments.length-2;if(1===l)i.children=r;else if(1<l){for(var c=Array(l),f=0;f<l;f++)c[f]=arguments[f+2];i.children=c}if(e&&e.defaultProps)for(o in l=e.defaultProps)void 0===i[o]&&(i[o]=l[o]);return{$$typeof:n,type:e,key:a,ref:s,props:i,_owner:u}}},294:function(e,t,n){"use strict";e.exports=n(408)}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}!function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t,n,r){return(n=function(t){var n=function(t,n){if("object"!==e(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,"string");if("object"!==e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"===e(n)?n:String(n)}(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t}n(905);var r=n(294);function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?o(Object(r),!0).forEach((function(n){t(e,n,r[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}const a=n(568);((e,t)=>{const{BlockControls:n,InspectorControls:o,InnerBlocks:s,useBlockProps:l,AlignmentToolbar:c,BlockVerticalAlignmentToolbar:p}=wp.blockEditor,{ToolbarGroup:u,ToolbarButton:d,Placeholder:f,Spinner:h}=wp.components,{Fragment:m}=wp.element,{Component:g}=React,{withSelect:y}=wp.data,{createHigherOrderComponent:b}=wp.compose,k=wp.blockEditor.__experimentalBlockAlignmentMatrixToolbar||wp.blockEditor.BlockAlignmentMatrixToolbar,v=wp.blockEditor.__experimentalBlockAlignmentMatrixControl||wp.blockEditor.BlockAlignmentMatrixControl,x=wp.blockEditor.__experimentalBlockFullHeightAligmentControl||wp.blockEditor.__experimentalBlockFullHeightAlignmentControl||wp.blockEditor.BlockFullHeightAlignmentControl,w=wp.blockEditor.__experimentalUseInnerBlocksProps||wp.blockEditor.useInnerBlocksProps,S={};function E(e){return S[e]||!1}function A(e){return E(e).acf_block_version||1}function T(e){const t=wp.data.select("core/block-editor").getBlockParents(e);return wp.data.select("core/block-editor").getBlocksByClientId(t).filter((e=>"core/query"===e.name)).length}function _(){return"string"==typeof pagenow&&"site-editor"===pagenow}function C(){const e=O("core/edit-post");return!!e&&!!e.isEditingTemplate&&e.isEditingTemplate()}function B(){return e("iframe[name=editor-canvas]").length&&!function(){const e=O("core/edit-post");return!e||(e.__experimentalGetPreviewDeviceType?"Desktop"===e.__experimentalGetPreviewDeviceType():!e.getPreviewDeviceType||"Desktop"===e.getPreviewDeviceType())}()}function j(e){const o=e.post_types||[];if(o.length){o.push("wp_block");const e=acf.get("postType");if(!o.includes(e))return!1}if("string"==typeof e.icon&&"<svg"===e.icon.substr(0,4)){const t=e.icon;e.icon=(0,r.createElement)($,null,t)}e.icon||delete e.icon,wp.blocks.getCategories().filter((({slug:t})=>t===e.category)).pop()||(e.category="common"),e=acf.parseArgs(e,{title:"",name:"",category:"",api_version:2,acf_block_version:1});for(const t in e.attributes)0===e.attributes[t].default.length&&delete e.attributes[t].default;e.supports.anchor&&(e.attributes.anchor={type:"string"});let a=L,s=q;var l;(e.supports.alignText||e.supports.align_text)&&(e.attributes=Z(e.attributes,"align_text","string"),a=function(e,t){const o=Q;return t.alignText=o(t.alignText),class extends g{render(){const{attributes:t,setAttributes:a}=this.props,{alignText:s}=t;return(0,r.createElement)(m,null,(0,r.createElement)(n,{group:"block"},(0,r.createElement)(c,{value:o(s),onChange:function(e){a({alignText:o(e)})}})),(0,r.createElement)(e,i({},this.props)))}}}(a,e)),(e.supports.alignContent||e.supports.align_content)&&(e.attributes=Z(e.attributes,"align_content","string"),a=function(e,o){let a,s,l=o.supports.align_content||o.supports.alignContent;return"matrix"===l?(a=v||k,s=K):(a=p,s=G),a===t?(console.warn(`The "${l}" alignment component was not found.`),e):(o.alignContent=s(o.alignContent),class extends g{render(){const{attributes:t,setAttributes:o}=this.props,{alignContent:l}=t;return(0,r.createElement)(m,null,(0,r.createElement)(n,{group:"block"},(0,r.createElement)(a,{label:acf.__("Change content alignment"),value:s(l),onChange:function(e){o({alignContent:s(e)})}})),(0,r.createElement)(e,i({},this.props)))}})}(a,e)),(e.supports.fullHeight||e.supports.full_height)&&(e.attributes=Z(e.attributes,"full_height","boolean"),l=a,e.blockType,a=x?class extends g{render(){const{attributes:e,setAttributes:t}=this.props,{fullHeight:o}=e;return(0,r.createElement)(m,null,(0,r.createElement)(n,{group:"block"},(0,r.createElement)(x,{isActive:o,onToggle:function(e){t({fullHeight:e})}})),(0,r.createElement)(l,i({},this.props)))}}:l),e.edit=e=>(0,r.createElement)(a,i({},e)),e.save=()=>(0,r.createElement)(s,null),S[e.name]=e;const u=wp.blocks.registerBlockType(e.name,e);return u.attributes.anchor&&(u.attributes.anchor={type:"string"}),u}function O(e){return"core/block-editor"===e?wp.data.select("core/block-editor")||wp.data.select("core/editor"):wp.data.select(e)}const P={},R={};function I(t){const{attributes:n={},context:r={},query:o={},clientId:s=null,delay:l=0}=t,c=a(JSON.stringify(i(i(i({},n),r),o))),p=P[c]||{query:{},timeout:!1,promise:e.Deferred(),started:!1};return p.query=i(i({},p.query),o),p.started||(clearTimeout(p.timeout),p.timeout=setTimeout((()=>{p.started=!0,R[c]?(P[c]=null,p.promise.resolve.apply(R[c][0],R[c][1])):e.ajax({url:acf.get("ajaxurl"),dataType:"json",type:"post",cache:!1,data:acf.prepareForAjax({action:"acf/ajax/fetch-block",block:JSON.stringify(n),clientId:s,context:JSON.stringify(r),query:p.query})}).always((()=>{P[c]=null})).done((function(){R[c]=[this,arguments],p.promise.resolve.apply(this,arguments)})).fail((function(){p.promise.reject.apply(this,arguments)}))}),l),P[c]=p),p.promise}function D(e,t){return JSON.stringify(e)===JSON.stringify(t)}function M(e,t,n=0){const o=function(e,t){switch(e){case"innerblocks":return t<2?s:"ACFInnerBlocks";case"script":return X;case"#comment":return null;default:e=H(e)}return e}(e.nodeName.toLowerCase(),t);if(!o)return null;const a={};if(1===n&&"ACFInnerBlocks"!==o&&(a.ref=React.createRef()),acf.arrayArgs(e.attributes).map(F).forEach((({name:e,value:t})=>{a[e]=t})),"ACFInnerBlocks"===o)return(0,r.createElement)(z,i({},a));const l=[o,a];return acf.arrayArgs(e.childNodes).forEach((e=>{if(e instanceof Text){const t=e.textContent;t&&l.push(t)}else l.push(M(e,t,n+1))})),React.createElement.apply(this,l)}function H(e){return acf.isget(acf,"jsxNameReplacements",e)||e}function z(e){const{className:t="acf-innerblocks-container"}=e,n=w({className:t},e);return(0,r.createElement)("div",i({},n),n.children)}function F(e){let t=e.name,n=e.value,r=acf.applyFilters("acf_blocks_parse_node_attr",!1,e);if(r)return r;switch(t){case"class":t="className";break;case"style":const e={};n.split(";").forEach((t=>{const n=t.indexOf(":");if(n>0){let r=t.substr(0,n).trim();const o=t.substr(n+1).trim();"-"!==r.charAt(0)&&(r=acf.strCamelCase(r)),e[r]=o}})),n=e;break;default:if(0===t.indexOf("data-"))break;t=H(t);const r=n.charAt(0);"["!==r&&"{"!==r||(n=JSON.parse(n)),"true"!==n&&"false"!==n||(n="true"===n)}return{name:t,value:n}}acf.parseJSX=(t,n)=>(t=(t="<div>"+t+"</div>").replace(/<InnerBlocks([^>]+)?\/>/,"<InnerBlocks$1></InnerBlocks>"),M(e(t)[0],n,0).props.children);const N=b((e=>class extends g{constructor(e){super(e);const{name:n,attributes:r}=this.props,o=E(n);if(!o)return;Object.keys(r).forEach((e=>{""===r[e]&&delete r[e]}));const i={full_height:"fullHeight",align_content:"alignContent",align_text:"alignText"};Object.keys(i).forEach((e=>{r[e]!==t?r[i[e]]=r[e]:r[i[e]]===t&&o[e]!==t&&(r[i[e]]=o[e]),delete o[e],delete r[e]}));for(let e in o.attributes)r[e]===t&&o[e]!==t&&(r[e]=o[e])}render(){return(0,r.createElement)(e,i({},this.props))}}),"withDefaultAttributes");function q(){return(0,r.createElement)(s.Content,null)}wp.hooks.addFilter("editor.BlockListBlock","acf/with-default-attributes",N);class L extends g{constructor(e){super(e),this.setup()}setup(){const{name:e,attributes:t,clientId:n}=this.props,r=E(e);function o(e){e.includes(t.mode)||(t.mode=e[0])}if(T(n)||_()||B()||C())o(["preview"]);else switch(r.mode){case"edit":o(["edit","preview"]);break;case"preview":o(["preview","edit"]);break;default:o(["auto"])}}render(){const{name:e,attributes:t,setAttributes:a,clientId:s}=this.props,l=E(e),c=T(s)||_()||B()||C();let{mode:p}=t;c&&(p="preview");let f=l.supports.mode;("auto"===p||c)&&(f=!1);const h="preview"===p?acf.__("Switch to Edit"):acf.__("Switch to Preview"),g="preview"===p?"edit":"welcome-view-site";return(0,r.createElement)(m,null,(0,r.createElement)(n,null,f&&(0,r.createElement)(u,null,(0,r.createElement)(d,{className:"components-icon-button components-toolbar__control",label:h,icon:g,onClick:function(){a({mode:"preview"===p?"edit":"preview"})}}))),(0,r.createElement)(o,null,"preview"===p&&(0,r.createElement)("div",{className:"acf-block-component acf-block-panel"},(0,r.createElement)(V,i({},this.props)))),(0,r.createElement)(U,i({},this.props)))}}const U=y(((e,t)=>{const{clientId:n}=t,r=e("core/block-editor").getBlockRootClientId(n);return{index:e("core/block-editor").getBlockIndex(n,r)}}))((function(e){const{attributes:t,isSelected:n,name:o}=e,{mode:a}=t;let s=!0,c="acf-block-component acf-block-body";return("auto"===a&&!n||"preview"===a)&&(c+=" acf-block-preview",s=!1),A(o)>1?(0,r.createElement)("div",i({},l({className:c})),s?(0,r.createElement)(V,i({},e)):(0,r.createElement)(Y,i({},e))):(0,r.createElement)("div",i({},l()),(0,r.createElement)("div",{className:"acf-block-component acf-block-body"},s?(0,r.createElement)(V,i({},e)):(0,r.createElement)(Y,i({},e))))}));class $ extends g{render(){return(0,r.createElement)("div",{dangerouslySetInnerHTML:{__html:this.props.children}})}}class X extends g{render(){return(0,r.createElement)("div",{ref:e=>this.el=e})}setHTML(t){e(this.el).html(`<script>${t}<\/script>`)}componentDidUpdate(){this.setHTML(this.props.children)}componentDidMount(){this.setHTML(this.props.children)}}const W={};class J extends g{constructor(e){super(e),this.setRef=this.setRef.bind(this),this.id="",this.el=!1,this.subscribed=!0,this.renderMethod="jQuery",this.setup(e),this.loadState()}setup(e){}fetch(){}maybePreload(e,n,r){if(this.state.html===t&&!T(this.props.clientId)){const t=acf.get("preloadedBlocks");if(t&&t[e])return!(r&&!t[e].form||!r&&t[e].form)&&t[e].html.replaceAll(e,n)}return!1}loadState(){this.state=W[this.id]||{}}setState(e){W[this.id]=i(i({},this.state),e),this.subscribed&&super.setState(e)}setHtml(t){if((t=t?t.trim():"")===this.state.html)return;const n={html:t};if("jsx"===this.renderMethod){if(n.jsx=acf.parseJSX(t,A(this.props.name)),n.jsx||(console.warn("Your ACF block template contains no valid HTML elements. Appending a empty div to prevent React JS errors."),n.html+="<div></div>",n.jsx=acf.parseJSX(n.html,A(this.props.name))),Array.isArray(n.jsx)){let e=n.jsx.find((e=>React.isValidElement(e)));n.ref=e.ref}else n.ref=n.jsx.ref;n.$el=e(this.el)}else n.$el=e(t);this.setState(n)}setRef(e){this.el=e}render(){return this.state.jsx?A(this.props.name)>1?(this.setRef(this.state.jsx),this.state.jsx):(0,r.createElement)("div",{ref:this.setRef},this.state.jsx):(0,r.createElement)("div",{ref:this.setRef},(0,r.createElement)(f,null,(0,r.createElement)(h,null)))}shouldComponentUpdate({index:e},{html:t}){return e!==this.props.index&&this.componentWillMove(),t!==this.state.html}display(t){if("jQuery"===this.renderMethod){const t=this.state.$el,n=t.parent(),r=e(this.el);r.html(t),n.length&&n[0]!==r[0]&&n.html(t.clone())}switch(t){case"append":this.componentDidAppend();break;case"remount":this.componentDidRemount()}}componentDidMount(){this.state.html===t?this.fetch():this.display("remount")}componentDidUpdate(e,t){this.display("append")}componentDidAppend(){acf.doAction("append",this.state.$el)}componentWillUnmount(){acf.doAction("unmount",this.state.$el),this.subscribed=!1}componentDidRemount(){this.subscribed=!0,setTimeout((()=>{acf.doAction("remount",this.state.$el)}))}componentWillMove(){acf.doAction("unmount",this.state.$el),setTimeout((()=>{acf.doAction("remount",this.state.$el)}))}}class V extends J{setup({clientId:e}){this.id=`BlockForm-${e}`}fetch(){const{attributes:e,context:t,clientId:n}=this.props,r=ee(e,t),o=this.maybePreload(r,n,!0);o?this.setHtml(o):I({attributes:e,context:t,clientId:n,query:{form:!0}}).done((({data:e})=>{this.setHtml(e.form.replaceAll(e.clientId,n))}))}componentDidRemount(){super.componentDidRemount();const{$el:e}=this.state;!0!==e.data("acf-events-added")&&this.componentDidAppend()}componentDidAppend(){super.componentDidAppend();const{attributes:e,setAttributes:t,clientId:n}=this.props,{$el:r}=(this.props,this.state);function o(o=!1){const i=acf.serialize(r,`acf-${n}`);o?e.data=i:t({data:i})}let i=!1;r.on("change keyup",(()=>{clearTimeout(i),i=setTimeout(o,300)})),r.data("acf-events-added",!0),e.data||o(!0)}}class Y extends J{setup({clientId:e,name:t}){const n=E(t),r=acf.isget(this.props,"context","postId");this.id=`BlockPreview-${e}`,r&&(this.id=`BlockPreview-${e}-${r}`),n.supports.jsx&&(this.renderMethod="jsx")}fetch(e={}){const{attributes:t=this.props.attributes,clientId:n=this.props.clientId,context:r=this.props.context,delay:o=0}=e,{name:i}=this.props;this.setState({prevAttributes:t,prevContext:r});const a=ee(t,r);let s=this.maybePreload(a,n,!1);if(s)return 1==A(i)&&(s='<div class="acf-block-preview">'+s+"</div>"),void this.setHtml(s);I({attributes:t,context:r,clientId:n,query:{preview:!0},delay:o}).done((({data:e})=>{let t=e.preview.replaceAll(e.clientId,n);1==A(i)&&(t='<div class="acf-block-preview">'+t+"</div>"),this.setHtml(t)}))}componentDidAppend(){super.componentDidAppend(),this.renderBlockPreviewEvent()}shouldComponentUpdate(e,t){const n=e.attributes,r=this.props.attributes;if(!D(n,r)||!D(e.context,this.props.context)){let t=0;n.className!==r.className&&(t=300),n.anchor!==r.anchor&&(t=300),this.fetch({attributes:n,context:e.context,delay:t})}return super.shouldComponentUpdate(e,t)}renderBlockPreviewEvent(){const{attributes:t,name:n}=this.props,{$el:r,ref:o}=this.state;var i;const a=t.name.replace("acf/","");i=o&&o.current?e(o.current).parent():1==A(n)?r:r.parents(".acf-block-preview"),acf.doAction("render_block_preview",i,t),acf.doAction(`render_block_preview/type=${a}`,i,t)}componentDidRemount(){super.componentDidRemount(),D(this.state.prevAttributes,this.props.attributes)&&D(this.state.prevContext,this.props.context)||this.fetch(),this.renderBlockPreviewEvent()}}function G(e){return["top","center","bottom"].includes(e)?e:"top"}function Q(e){const t=acf.get("rtl")?"right":"left";return["left","center","right"].includes(e)?e:t}function K(e){if(e){const[t,n]=e.split(" ");return`${G(t)} ${Q(n)}`}return"center center"}function Z(e,t,n){return e[t]={type:n},e}function ee(e,t){return e._acf_context=t,a(JSON.stringify(Object.keys(e).sort().reduce(((t,n)=>(t[n]=e[n],t)),{})))}acf.addAction("prepare",(function(){wp.blockEditor||(wp.blockEditor=wp.editor);const e=acf.get("blockTypes");e&&e.map(j)}))})(jQuery)}()}();