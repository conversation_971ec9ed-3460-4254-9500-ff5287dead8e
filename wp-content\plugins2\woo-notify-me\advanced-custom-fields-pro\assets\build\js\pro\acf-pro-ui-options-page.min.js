!function(){var e={412:function(){!function(e,t){const a=function(t){return void 0===t.element?t:t.children&&"None"===t.text?void 0:("acfOptionsPages"===t.text&&(t.text=acf.__("Options Pages")),e('<span class="acf-selection"></span>').data("element",t.element).html(acf.escHtml(t.text)))},n=function(t){if(void 0===t.element)return t;const a=e('<span class="acf-selection"></span>');return a.html(acf.escHtml(t.element.innerHTML)),"options"!==t.id&&"edit_posts"!==t.id||a.append('<span class="acf-select2-default-pill">'+acf.__("Default")+"</span>"),a.data("element",t.element),a};new acf.Model({id:"UIOptionsPageManager",wait:"ready",events:{"change .acf-options-page-parent_slug":"toggleMenuPositionDesc"},initialize:function(){"ui_options_page"===acf.get("screen")&&(acf.newSelect2(e("select.acf-options-page-parent_slug"),{field:!1,templateSelection:a,templateResult:a,dropdownCssClass:"field-type-select-results"}),acf.newSelect2(e("select.acf-options-page-capability"),{field:!1,templateSelection:n,templateResult:n}),acf.newSelect2(e("select.acf-options-page-data_storage"),{field:!1,templateSelection:n,templateResult:n}),this.toggleMenuPositionDesc())},toggleMenuPositionDesc:function(t,a){"none"===e("select.acf-options-page-parent_slug").val()?(e(".acf-menu-position-desc-child").hide(),e(".acf-menu-position-desc-parent").show()):(e(".acf-menu-position-desc-parent").hide(),e(".acf-menu-position-desc-child").show())}}),new acf.Model({id:"optionsPageModalManager",events:{"change .location-rule-value":"createOptionsPage"},createOptionsPage:function(t){const n=e(t.target);if("add_new_options_page"!==n.val())return;let o=!1;const c=function(t){o=acf.newPopup({title:t.data.title,content:t.data.content,width:"600px"}),o.$el.addClass("acf-create-options-page-popup");const n=o.$el.find("#acf_ui_options_page-page_title"),c=n.val();n.focus().val("").val(c),acf.newSelect2(e("#acf_ui_options_page-parent_slug"),{field:!1,templateSelection:a,templateResult:a,dropdownCssClass:"field-type-select-results"}),o.on("submit","form",s)},s=function(t){t.preventDefault(),acf.validateForm({form:e("#acf-create-options-page-form"),success:i,failure:l})},i=function(){const t=e("#acf-create-options-page-form").serializeArray(),a={action:"acf/create_options_page"};t.forEach((e=>{a[e.name]=e.value})),e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(a),type:"post",dataType:"json",success:r})},l=function(t){const a=e("#acf-create-options-page-form"),n=a.find(".acf-field .acf-error-message");a.find(".acf-notice").first().remove(),n.each((function(){const t=e(this).closest(".acf-field").find(".acf-label:first");e(this).attr("class","acf-options-page-modal-error").appendTo(t)}))},r=function(e){e.success&&e.data.menu_slug?(n.prepend('<option value="'+e.data.menu_slug+'">'+e.data.page_title+"</option>"),n.val(e.data.menu_slug),o.close()):!e.success&&e.data.error&&alert(e.data.error)};!function(){const t=e(".acf-headerbar-title-field").val(),a={action:"acf/create_options_page",acf_parent_page_choices:this.acf.data.acfParentPageChoices?this.acf.data.acfParentPageChoices:[]};t.length&&(a.field_group_title=t),e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(a),type:"post",dataType:"json",success:c})}()}})}(jQuery)}},t={};function a(n){var o=t[n];if(void 0!==o)return o.exports;var c=t[n]={exports:{}};return e[n](c,c.exports,a),c.exports}a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,{a:t}),t},a.d=function(e,t){for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";a(412)}()}();