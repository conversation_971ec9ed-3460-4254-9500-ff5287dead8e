<?php

// ApiInterface.php
#################################################
##
## PHPLicengine
##
#################################################
## Copyright 2009-{current_year} PHPLicengine
## 
## Licensed under the Apache License, Version 2.0 (the "License");
## you may not use this file except in compliance with the License.
## You may obtain a copy of the License at
##
##    http://www.apache.org/licenses/LICENSE-2.0
##
## Unless required by applicable law or agreed to in writing, software
## distributed under the License is distributed on an "AS IS" BASIS,
## WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
## See the License for the specific language governing permissions and
## limitations under the License.
#################################################

namespace WPO\WC\Ultimate_Barcodes\Vendor\PHPLicengine\Api;

interface ApiInterface
{
       public function get($url, $params = null, $headers = null);

       public function post($url, $params = null, $headers = null);

       public function delete($url, $params = null, $headers = null); 

       public function put($url, $params = null, $headers = null);
    
       public function patch($url, $params = null, $headers = null); 
    
       public function setApiKey($api_key);
       
       public function setOAuth();
       
}
