/* main.css */
/* Container div for elFinder */
.elfinder,
.elfinder .elfinder-dialog,
.elfinder .elfinder-toolbar .elfinder-buttonset .elfinder-button-menu {
  background: #fff;
  border: 1px solid #69bcee;
  box-shadow: 0 0 5px #cdcdcd;
  border-radius: 0;
}

/* Override styles in child elements of elFinder div */
/* Use for consistently setting text sizes and overriding general jQuery UI styles */
.elfinder * {
  /*color: #000;*/
  font-family: 'Open Sans', sans-serif;
}

/* Resizer */
/* Used if elFinder is resizable and on dialogs */
.elfinder .ui-icon-gripsmall-diagonal-se,
.elfinder-dialog .ui-icon-gripsmall-diagonal-se {
  /* */
}
