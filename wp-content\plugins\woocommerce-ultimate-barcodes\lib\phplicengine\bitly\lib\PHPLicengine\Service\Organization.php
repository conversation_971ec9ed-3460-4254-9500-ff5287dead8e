<?php

// Organization.php
#################################################
##
## PHPLicengine
##
#################################################
## Copyright 2009-{current_year} PHPLicengine
## 
## Licensed under the Apache License, Version 2.0 (the "License");
## you may not use this file except in compliance with the License.
## You may obtain a copy of the License at
##
##    http://www.apache.org/licenses/LICENSE-2.0
##
## Unless required by applicable law or agreed to in writing, software
## distributed under the License is distributed on an "AS IS" BASIS,
## WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
## See the License for the specific language governing permissions and
## limitations under the License.
#################################################

namespace WPO\WC\Ultimate_Barcodes\Vendor\PHPLicengine\Service;
use WPO\WC\Ultimate_Barcodes\Vendor\PHPLicengine\Exception\ResponseException;
use WPO\WC\Ultimate_Barcodes\Vendor\PHPLicengine\Exception\CurlException;
use WPO\WC\Ultimate_Barcodes\Vendor\PHPLicengine\Api\ApiInterface;

class Organization {
 
       private $url;
       private $api;      
      
       public function __construct(ApiInterface $api)
       {
              $this->api = $api;
              $this->url = 'https://api-ssl.bitly.com/v4/organizations';       
       }
 
       /*
      Retrieve Organizations
      https://dev.bitly.com/api-reference#getOrganizations
      *
      @license Apache-2.0
      Modified using {@see https://github.com/BrianHenryIE/strauss}.
*/
       public function getOrganizations() 
       {
              return $this->api->get($this->url);
       }
      
       /*
      Retrieve Organization Shorten Counts
      https://dev.bitly.com/api-reference#getOrganizationShortenCounts
      */
       public function getOrganizationShortenCounts(string $organization_guid, array $params = array()) 
       {
              return $this->api->get($this->url.'/'.$organization_guid.'/shorten_counts', $params);
       }
      
       /*
      Retrieve an Organization
      https://dev.bitly.com/api-reference#getOrganization
      */
       public function getOrganization(string $organization_guid) 
       {
              return $this->api->get($this->url.'/'.$organization_guid);
       }      
 
       /*
      Get Plan Limits
      https://dev.bitly.com/api-reference#getPlanLimits
      */
       public function getPlanLimits(string $organization_guid) 
       {
              return $this->api->get($this->url.'/'.$organization_guid."/plan_limits");
       }
 
}
