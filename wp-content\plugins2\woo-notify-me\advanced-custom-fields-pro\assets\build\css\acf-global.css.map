{"version": 3, "file": "acf-global.css", "mappings": ";;;AAAA,gBAAgB;ACAhB;;;;8FAAA;AAMA;AAOA;AAQA;AAgBA;;;;8FAAA;ACrCA;;;;8FAAA;ACCA;;;;8FAAA;AAMA;AACA;EACC;EACA;EACA;EACA;EACA;AHkBD;;AGhBA;EACC;EACA;EACA;EACA;AHmBD;;AGjBA;EACC;AHoBD;;AGjBA;AACA;;;;;;EAMC;EACA;EACA;AHoBD;;AGlBA;;;EAGC;AHqBD;;AGlBA;AACA;EACC;EACA;EACA;EACA;EACA;AHqBD;;AGnBA;EACC;EACA;EACA;EACA;AHsBD;;AGnBA;AACA;EACC;AHsBD;;AGpBA;EACC;AHuBD;AGtBC;EACC;AHwBF;;AGpBA;AACA;EACC;AHuBD;;AGrBA;EACC;AHwBD;;AGtBA;EACC;AHyBD;;AGtBA;AACA;EACC;AHyBD;;AGvBA;EACC;AH0BD;;AGxBA;EACC;AH2BD;;AGxBA;AACA;;EAEC;EACA;EACA;EACA;EACA;AH2BD;;AGxBA;AACA;EACC;AH2BD;;AGxBA;EACC;AH2BD;;AGxBA;AACA;EACC;AH2BD;;AGxBA;AACA;EACC;AH2BD;;AGxBA;AACA;;EAEC;AH2BD;;AGxBA;AACA;EACC;EACA;EACA;EACA;EAEA;EACA;AH0BD;;AGvBA;EACC;EACA;EACA;EACA;EAEA;EACA;AHyBD;;AGtBA;AACA;EACC;AHyBD;;AGvBA;EACC;AH0BD;;AGvBA;EACC;AH0BD;;AGxBA;EACC;AH2BD;;AGxBA;AACA;EACC;EACA;EACA;EACA;AH2BD;;AGxBA;;;;+FAAA;AAMA;AACA;EACC,mBF7HU;EE8HV,kBF/FW;EEgGX,cFpIU;EEsIT;EACA;EACA;EACA;EAED;EAEA;EACA;EACA;EAGA;EASA;AHaD;AGrBC;EACC;EACA;EACA;EACA;EACA;AHuBF;AGnBC;EACC;AHqBF;AGnBE;EACC;EACA;EACA;EACA;EACA;AHqBH;AGjBC;EACC;AHmBF;AGjBE;EACC;EACA;EACA;EACA;EACA;AHmBH;AGfC;EACC;AHiBF;AGfE;EACC;EACA;EACA;EACA;EACA;AHiBH;AGbC;EACC;AHeF;AGbE;EACC;EACA;EACA;EACA;EACA;AHeH;AGXC;EACC;AHaF;;AGTA;AACA;EACC;AHYD;AGVC;EACC;EACA;AHYF;AGVE;EACC;AHYH;AGTE;EACC;AHWH;;AGNA;EACC;EACA;EACA;EACA;EACA;EACA;AHSD;;AGNA;EACC;EACA;AHSD;;AGNA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHSD;AGPC;ED3RA;EACA;EACA;EACA;AFqSD;;AGRA;;;;8FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHWD;AGTC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHWF;;AGNA;EACC;AHSD;;AGPA;EACC;AHUD;;AGRA;EACC;EACA;AHWD;;AGTA;EACC;AHYD;;AGVA;EACC;AHaD;;AGXA;EACC;EAGA;AHYD;;AGVA;EACC;EAGA;AHWD;;AGTA;EACC;EAGA;AHUD;;AGRA;EACC;EAGA;AHSD;;AGPA;EACC;AHUD;;AGRA;EACC;EAGA;EACA;AHSD;;AGPA;EACC;AHUD;;AGRA;EACC;EAGA;AHSD;;AGPA;EACC;EAGA;AHQD;;AGNA;EACC;AHSD;;AGPA;EACC;EAGA;AHQD;;AGNA;EACC;EAGA;AHOD;;AGLA;EACC;AHQD;;AGNA;EACC;AHSD;;AGLA;EACC;AHQD;AGPC;EAEC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHQF;AGNC;EACC;EACA;AHQF;AGNC;EACC;AHQF;;AGJA;EACC;AHOD;AGNC;EAEC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHOF;AGLC;EACC;EACA;AHOF;AGLC;EACC;AHOF;;AGFA;EACC;EAGA;AHGD;;AGDA;EACC;EAGA;AHED;;AGEA;EACC;EACA;EACA;AHCD;;AGGA;EACC;EACA;EACA;EACA;EACA;EACA;AHAD;AGGC;EACC;EACA;EACA;AHDF;AGGC;EAEC;EACA;EACA;AHFF;AGMC;EAEC;EACA;AHLF;;AGUA;EACC;EACA;EACA;AHPD;;AGWA;EACC;EACA;EACA;AHRD;;AGYA;EACC;EACA;EACA;AHTD;;AGYC;EACC;EACA;AHTF;AGWC;EAEC;AHVF;;AGeA;EACC;EACA;EACA;AHZD;AGcC;EACC;EACA;AHZF;AGcC;EAEC;AHbF;;AGkBA;;EAEC;EACA;EACA;EACA;AHfD;AGoBE;;;EAGC;AHlBH;;AGuBA;;;;8FAAA;AAKA;EACC;EACA;EACA;EACA;EAEA;EA8CA;AHlED;AGqBC;EACC;EACA;EACA;AHnBF;AGqBE;EACC;EACA;EACA;EACA;EACA;EACA;AHnBH;AGuBC;EACC;AHrBF;AGwBC;EACC;EACA;EACA;EACA;EACA;AHtBF;AGyBC;EACC;AHvBF;AG0BC;EACC;AHxBF;AG2BC;EACC;AHzBF;AG6BE;EACC;AH3BH;AGgCC;EACC;EACA;EACA;EACA;AH9BF;AGgCE;EACC;AH9BH;AE7kBC;ECinBC,qBF1nBiB;ADylBnB;AGkCE;;EAEC,qBF7nBgB;AD6lBnB;;AGqCA;;;;8FAAA;AAMA;EACC;EACA;EACA;EACA;EACA;EACA,mBFtqBY;EEuqBZ;AHnCD;AGqCC;EACC;EACA;EACA;EACA;EACA;AHnCF;AGsCC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AHpCF;AGqCE;EACC;AHnCH;AGwCC;EACC;AHtCF;AG0CC;EACC,mBFpsBU;EEqsBV;AHxCF;AG4CC;EACC,mBFzsBY;EE0sBZ;AH1CF;AG8CC;EACC,mBF9sBY;EE+sBZ;AH5CF;;AGgDA;;;;8FAAA;AAMA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EAmBA;EAcA;EAoBA;AHjGD;AG+CE;;;;EAEC;EACA;EACA;EACA;EACA;EACA;AH3CH;AG8CE;;EACC;EACA;AH3CH;AGkDG;EACC,qBF5uBe;EE6uBf;AHhDJ;AGkDI;EACC;AHhDL;AGwDE;EACC;AHtDH;AGwDG;EACC,qBF3vBe;EE4vBf;AHtDJ;AGwDI;EACC;AHtDL;AG0DG;EACC;AHxDJ;AG8DC;EACC;AH5DF;AGgEG;;;;EAEC;EACA;AH5DJ;;AGkEA;AACA;EACC;EACA;EACA;EACA;EAEA;EACA;AHhED;;AGmEA;AACA;EACC;EACA;EACA;EACA;EAEA;EACA;AHjED;;AGoEA;;;;+FAAA;AAMA;;;EAGC;EACA;EACA;AHlED;AGoEC;;;EACC;EAEC;EAED;EACA;AHlEF;;AGsEA;EACC;EACA;AHnED;AGqEC;EACC;EACA;EACA;AHnEF;AE5vBC;ECo0BC,qBF50BmB;ADuwBrB;;AGyEA;EACC;EACA;AHtED;;AGyEA;;;;8FAAA;AAOC;EACC;AHxEF;AG2EC;EACC;AHzEF;AG4EC;EACC;AH1EF;AG4EE;EACC;AH1EH;;AG+EA;;;;8FAAA;AAMA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AH7ED;AGgFC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AH9EF;AGiFC;EACC;EACA;EACA;EACA;AH/EF;AGmFC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHjFF;AEr0BC;EACC;AFu0BF;AGkFE;EACC;EACA;AHhFH;AGmFG;EACC;EACA;EACA;AHjFJ;AGoFI;EACC;EACA;AHlFL;AGuFE;EACC;EAGA;EACA;AHvFH;AG2FE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHzFH;AG2FG;ED78BF;EACA;EACA;EACA;AFq3BD;;AG6FA;EACC;EACA;AH1FD;AG6FC;EACC;EACA;AH3FF;AG6FE;EACC;AH3FH;AGgGC;EACC;AH9FF;;AGkGA;;;;8FAAA;AAMA;EACC;EACA;EACA;AHhGD;AEh6BC;EACC;EACA;EACA;AFk6BF;AG8FC;EACC;EACA;EACA;AH5FF;AG+FC;EACC;EACA;EACA;EACA;AH7FF;AGgGC;EACC;EACA;AH9FF;AGiGC;EACC;EACA;EACA;EACA;AH/FF;AGkGC;EACC;EACA;EACA;AHhGF;AGmGC;EACC;EACA;AHjGF;AGoGC;EACC;AHlGF;AGsGC;EACC;;IAEC;IACA;IACA;IACA;EHpGD;AACF;;AGyGA;;EAEC;AHtGD;;AG0GA;EACC;AHvGD;;AG0GA;;;;8FAAA;AAOC;EACC;EACA;AHzGF;AG4GC;EACC;EACA;AH1GF;AG6GC;EACC;EACA;EACA;EACA;EACA;AH3GF;AG8GC;EACC;AH5GF;AG8GE;EACC;AH5GH;AGgHC;EACC;EACA;AH9GF;AGgHE;EACC;AH9GH;AGkHC;EACC;EACA;EACA;AHhHF;AGkHE;EACC;EACA;EACA;EACA;AHhHH;AGkHG;EAND;IAOE;EH/GF;AACF;AGiHG;EAVD;IAWE;EH9GF;AACF;AGiHE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH/GH;AGkHE;EACC;AHhHH;;AGqHA;;;;8FAAA;AAMA;EACC;EACA;AHnHD;AGqHC;EACC;EAEA;EACA;EACA;AHpHF;;AGwHA;AACA;EACC;AHrHD;;AGuHA;EACC;AHpHD;;AGsHA;EACC;AHnHD;;AGsHA;AACA;EACC;IACC;IACA;IACA;IACA;IACA;IACA;IACA;EHnHA;EGqHA;IACC;IACA;IACA;EHnHD;AACF;AGuHA;;;;8FAAA;AAMA;EACC;EACA;EAEA;EAUA;AHhID;AGuHC;EACC;EACA;EACA;EACA;EACA;EACA;AHrHF;AG0HE;EACC;EACA;AHxHH;;AG6HA;AAEC;EACC;EACA;AH3HF;;AG+HA;;;;8FAAA;AAMA;EACC;AH7HD;;AG+HA;EACC;AH5HD;;AG+HA;EACC;AH5HD;;AG+HA;EACC;AH5HD;;AG+HA;EACC;EACA;AH5HD;;AG+HA;EACC;EACA;EACA;AH5HD;;AG+HA;EACC;EACA;EACA;AH5HD;;AG+HA;;EAEC;AH5HD;;AG+HA;EACC;AH5HD;;AG+HA;;;;+FAAA;AAMA;EAEC;EACA;EACA;EACA;EACA;AH9HD;AEpqCC;EACC;EACA;EACA;AFsqCF;AG2HC;;ED5xCA;EACA;EACA;EC6xCC;AHvHF;AG0HC;EACC;EACA;AHxHF;AG2HC;EACC;EACA;EACA;AHzHF;AG2HE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,mBFvyCgB;AD8qCnB;AG+HE;EACC,mBFxyCkB;AD2qCrB;;AGkIA;AACA;EACC;IACC;EH/HA;EGiIA;;IAEC;IACA;IACA;IACA;EH/HD;EGkIA;IACC;EHhID;EGkIC;IACC;EHhIF;AACF;AGqIA;;;;+FAAA;AAMA;EACC;EACA;EACA;EAoBA;EAOA;EAMA;AHlKD;AGmIC;EACC;EACA;EACA;EACA;EACA;AHjIF;AGmIE;EACC;AHjIH;AGqIC;EACC;EACA;EACA;AHnIF;AGwIE;EACC;AHtIH;AG2IC;EACC;EACA;AHzIF;AG6IC;EACC;AH3IF;AG6IE;EACC;EACA;AH3IH;AG8IE;EACC;AH5IH;AEpuCC;ECs3CC,qBF93CmB;AD+uCrB;;AGmJA;;;;+FAAA;AAOC;EACC;AHlJF;AGqJC;EAKC;AHvJF;AGmJE;EACC;AHjJH;AGqJE;EAEE;EAED;EACA;EACA;AHrJH;AGuJG;EACC;EACA;EACA;AHrJJ;AGwJG;EAGE;AHxJL;AG4JG;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA,qBFp6CM;EEs6CP,kBFl4CQ;ADouCZ;AGiKG;EACC;AH/JJ;;AGsKC;EDl9CA;EACA;EACA;AFgzCD;AGmKE;EACC;AHjKH;AGoKE;EACC;EACA;EACA;EACA;EAGA;EACA;EACA;AHpKH;AGuKE;;;EAGC;AHrKH;;AG0KA;AACA;EACC;EACA;AHvKD;AGyKC;EACC;EACA;EACA;EACA;AHvKF;AGyKE;EACC;AHvKH;AG0KE;EACC;EACA;EACA;AHxKH;;AG6KA;AACA;EACC;IACC;IACA;EH1KA;EG4KA;IACC;IACA;IACA;EH1KD;AACF;AG8KA;AACA;EA0CC;AHrND;AG4KC;EACC;AH1KF;AG6KC;EACC;EACA;EACA;AH3KF;AG4KE;EACC;AH1KH;AG2KG;EAFD;IAGE;EHxKF;AACF;AGyKG;EALD;IAME;EHtKF;AACF;AG2KE;EACC;AHzKH;AG4KE;EACC;EACA;AH1KH;AG8KC;EACC;EACA;EACA;EACA,mBFxhDS;EEyhDT,qBFthDS;EEuhDT;EACA;EACA,kBFr/CU;ADy0CZ;AGiLE;EACC;EACA,cF5hDQ;AD62CX;;AGqLC;EACC;AHlLF;;AGuLA;EACC;AHpLD;AGqLC;EACC;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;EACA;EACA;AHtLF;AGwLC;EACC;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;AHxLF;AG6LE;EAEC;AH5LH;;AGmMC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHhMF;AGkME;EACC;EACA;AHhMH;AGmME;;EAEC;EACA;AHjMH;AGqMC;EACC;EACA;EACA;EACA;EACA;EACA;AHnMF;AGsMC;EACC;AHpMF;AGsME;EACC;AHpMH;AGuME;;EAEC;EACA;AHrMH;AGyME;EACC;AHvMH;AG0ME;EACC;AHxMH;AG6MC;EACC;IACC;EH3MD;EG6MA;IACC;EH3MD;AACF;;AG+MA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH5MD;AG8MC;;;EAGC;EACA;EACA;EACA;AH5MF;AG+MC;EACC;EACA;EACA;AH7MF;AG+ME;EACC;EACA;EACA;AH7MH;AG+ME;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH7MH;AG8MG;EACC;AH5MJ;AGiNC;EACC;EACA;EACA;EACA;EACA;AH/MF;AGkNC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AHhNF;AGkNE;EACC;EACA;AHhNH;AGoNC;EACC;EACA;EACA;EACA;AHlNF;AGoNE;EACC;AHlNH;AGuNC;EAjFD;IAkFE;IACA;IACA;IACA;EHpNA;AACF;;AGsNA;EACC;EACA;EACA;EACA;EACA;EACA,mBFxvDU;EEyvDV;EACA;AHnND;;AGsNA;;;;+FAAA;AAMA;EAMC;;IAEC;IACA;EHzNA;AACF;AG4NA;;;;8FAAA;AAOC;EAEE;EACA;EACA;EACA;AH7NH;AGgOE;EARD;IAUG;IACA;EH9NF;AACF;AGkOC;EAEE;EACA;AHjOH;AGoOE;EAND;IAQG;IACA;EHlOF;AACF;AGuOE;EADD;IAGG;EHrOF;AACF;;AG0OA;;;;oEAAA;AAMC;EACC;AHxOF;;AG4OA;;;;+FAAA;AAMC;;EAEC;EACA,kBFnzDU;EEozDV,6CFhzDa;ADskDf;AG4OE;;EAEE;EACA;EACA;EACA;AH1OJ;AG8OE;;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;AH9OJ;AGkPE;;;;EAGE;EACA;EACA;EACA;EAGA;EACA;EACA,yBF/3DO;AD8oDX;AGqPE;;;;EAEC;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAGA;EACA;AHtPJ;AGyPG;;;;;;;;EAGE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAGD,cFp6DO;AD8qDX;AG0PE;;EAEE;EACA;EACA;EACA;AHxPJ;;AG8PA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAGA;EACA;EACA,4BFl9DS;ADktDX;AGmQC;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAGD,cF99DS;ADwtDX;AGyQC;EAEE;EACA;AHxQH;AG4QC;EACC,yBF5+DS;ADkuDX;;AG8QA;;;;+FAAA;AAMC;EAEE;AH7QH;AGgRE;EACC,qBF7/DQ;AD+uDX;AGiRE;EATD;IAWG;IACA;EH/QF;AACF;AGmRC;EAEE;EACA;AHlRH;AGqRE;EAND;IAQG;IACA;EHnRF;AACF;AGuRC;EACC,qBFvhES;ADkwDX;;AGyRA;;;;+FAAA;AAQG;;EAEC;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAGD;AH9RJ;;AGoSA;;;;+FAAA;AAMC;EAIC;EACA;EACA;EACA;EAgCA;EACA;EACA;EACA,kBFpkEU;ADgwDZ;AGuUC;EACC;AHrUF;;AGyUA;;;;8FAAA;AAMC;EACC;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAED,yBFznES;EE2nER;EACA;EACA,qBF3nEQ;EE6nET,kBFpmEU;ADyxDZ;AG8UE;EAEE;AH7UJ;;AGmVA;;;;8FAAA;AAKA;EACC;AHhVD;AGkVC;EAEE;AHjVH;;AGsVA;;;;8FAAA;AAMC;;;EAGC;EACA;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAED;EAEC;EACA;EACA;EAED,kBF3pEU;EE4pEV,6CFxpEa;EEypEb,cF9rES;ADo2DX;AG4VE;;;EACC;EACA;EACA;EAEC;EACA;EACA;EACA;AHzVJ;AG6VE;;;EACC;EAEC;EAED;EACA;AH3VH;AG+VE;;;EAEE;EACA;AH5VJ;AGgWE;;;EACC;EACA;EACA;EACA;EACA;AH5VH;AG8VG;;;EAEE;EAGA;EAGD;AH/VJ;AGoWE;;;;;;EAEC;EACA;EACA;EACA;EACA;AH9VH;AGgWG;;;;;;EACC;EAEA;EACA;EACA;EACA,WAJY;EAKZ,YALY;EAMZ,yBFnwEO;EEowEP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH1VJ;AG6VG;;;;;;EACC,yBF/wEO;ADy7DX;AG0VE;;;EACC;EACA;EACA;AHtVH;AGwVG;;;EACC,yBF1xEO;ADs8DX;AGyVE;;;EACC;EAEA;EACA;EACA;EACA;EACA;EACA,WANY;EAOZ,YAPY;EASX;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHxVH;AG2VE;;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,yBFhzEU;EEizEV,kBFlyES;EEmyET,6CF9xEY;ADu8Df;AG0VE;;;EACC;EAEC;EACA;AHvVJ;AG6VC;EACC;AH3VF;AG8VC;EAEE;AH7VH;AGkWC;EACC;EACA;AHhWF;AGkWE;EACC;EACA;AHhWH;AGmWE;EACC,yBFn1Ea;ADk/DhB;AGsWC;;;EAGC;EACA;AHpWF;AGsWE;;;EACC;EACA;AHlWH;AGqWE;;;EACC,yBFl2EY;ADigEf;;AG0WE;;;EACC;AHrWH;AGwWE;;;EACC;EACA;AHpWH;AGsWG;;;EACC;EACA;AHlWJ;AGoWI;;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,yBFp5EM;EEq5EN;AHhWL;AGoWK;;;EACC;AHhWN;;AG0WC;;EACC;AHtWF;AGwWE;;EACC;EACA;AHrWH;AGwWE;;EACC;EACA;AHrWH;AGwWE;;EACC;EACA;AHrWH;AG6WG;;;;EACC;EACA;AHxWJ;;AG8WA;;;;8FAAA;AAKA;EACC;EACA;EACA;EAEC;EACA;EAED,oEFn7Ec;EEo7Ed;EACA;EACA;EACA;EACA;EACA;EACA;AH7WD;;AGgXA;;;;8FAAA;AAQE;EACC;EACA;EACA;EAEC;EAGA;EACA;EACA;EAED;EACA;EACA;EACA;EACA,kBFj9ES;AD6lEZ;AGsXG;EACC;EACA;EACA;EACA;AHpXJ;AGuXG;EACC;EACA;EACA;EACA;AHrXJ;AGyXG;EACC;EACA;AHvXJ;AG2XG;EACC;EACA;AHzXJ;AG6XG;EACC;EACA;AH3XJ;;AIxsEA;;;;+FAAA;AAMC;EACC;AJ0sEF;;AItsEA;;;;+FAAA;AAOC;EACC,cH0CS;AD6pEX;;AIlsEA;;;;+FAAA;AAMA;;;EACC;EACA;AJssED;;AInsEA;;;;;;;;;;;;;;;;;EACC;EACA;AJstED;;AIntEA;;;;;;;;;;EACC;EACA;AJ+tED;;AI3sEA;;;;+FAAA;AAQC;EACC;AJ2sEF;AIxsEC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EACC;AJwuEF;AIruEC;EACC;AJuuEF;AIpuEC;;;;;;;;;;;EACC;AJgvEF;AI7uEC;;;;;EACC;AJmvEF;AIhvEC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EACC;AJgxEF;AI7wEC;;;EACC;AJixEF;AI9wEC;EACC;AJgxEF;;AI3wEA;;;;+FAAA;AAKA;EAEC,cH5DU;ADy0EX;;AI1wEA;;;;+FAAA;AAOC;EACC;AJ2wEF;AIxwEC;EACC;AJ0wEF;;AIrwEA;;;;+FAAA;AASA;;;;+FAAA;AAMC;EACC;EACA;AJmwEF;AIhwEC;EACC;EACA;AJkwEF;;AK35EA;EAEC;;;;iGAAA;EAuCA;;;;iGAAA;EAcA;;;;iGAAA;EAcA;;;;iGAAA;EAeA;;;;iGAAA;EA4CA;;;;iGAAA;EAsEA;;;;iGAAA;EAkBA;;;;iGAAA;EAkBA;;;;iGAAA;EAqCA;;;;iGAAA;EAwGA;;;;iGAAA;EAqCA;;;;iGAAA;EAkCA;;;;iGAAA;EASA;;;;iGAAA;EA0HA;;;;iGAAA;EA+BA;;;;iGAAA;EAsBA;EAgUA;;;;iGAAA;AL8jDD;AK7+EC;;;;;EAKC;EACA;EAEC;EACA;EAED;EACA,qBJ4BS;EI3BT,6CJoEa;EInEb,kBJ8DU;EI5DV,cJ4BS;ADg9EX;AK1+EE;;;;;EACC,0BJiEO;EIhEP,qBJiCQ;AD+8EX;AK7+EE;;;;;EACC,yBJaQ;EIZR;ALm/EH;AKh/EE;;;;;EACC,cJYQ;AD0+EX;AK1+EE;EACC,yBJLQ;EIMR,cJFQ;AD8+EX;AKh+EE;;EAEC;ALk+EH;AKx9EC;EACC;EAEC;EACA;EAED;EACA;ALw9EF;AKh9EC;EACC;EACA;EAEC;EACA;EAED;EACA;EACA;ALg9EF;AK78EE;EAEC,cJ1CQ;ADw/EX;AK38EE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AL68EH;AKv8EE;EAEE;EACA;EAED;ALu8EH;AK97EC;;EAEC;EACA;EACA;EACA;EAEC;EACA;EACA,qBJ9FQ;EIgGT;EACA;AL87EF;AK57EE;;EACC,yBJ5FQ;EI6FR,qBJxFQ;ADuhFX;AK57EE;;;EAEC,yBJlGQ;EImGR,qBJ9FQ;AD6hFX;AK77EG;;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ALi8EJ;AK57EE;;EACC;AL+7EH;AK57EE;;EACC,yBJvIQ;EIwIR,qBJrIQ;ADokFX;AKr7EI;;;EACC;ALy7EL;AKx6EG;EACC;AL06EJ;AKz5EG;EACC;AL25EJ;AK54EE;;;;EAGE;AL+4EJ;AK34EE;;EAEE;AL64EJ;AK14EG;;EAEE;AL44EL;AKr4EE;;EACC;EACA;EACA;ALw4EH;AK93EC;EACC;EACA;EACA;EACA,yBJzOS;EI0OT;ALg4EF;AK93EE;EACC,yBJ5OQ;AD4mFX;AK73EE;EACC;AL+3EH;AK53EE;EACC,yBJvOQ;ADqmFX;AK53EG;EACC,yBJzOO;ADumFX;AK33EG;EACC;AL63EJ;AKx3EE;;EAEC;AL03EH;AKv3EE;EACC;EACA;EACA;EACA;EACA;ALy3EH;AKp3EC;EACC;EACA;ALs3EF;AKp3EE;EACC;EACA;EACA;EAEC;EACA;EACA;ALq3EJ;AKl3EG;EAEE;ALm3EL;AK/2EG;EAEE;ALg3EL;AK52EG;EACC;EAEC;EACA;AL62EL;AKn2EG;EAEE;EACA;ALo2EL;AKh2EG;EAEE;EACA;ALi2EL;AKr1EC;EACC;EACA;EAEC;EAGA;EACA;EACA;EACA;EAED;EACA;EACA,kBJxTU;EI0TT;EACA;EACA,qBJlVQ;EIoVT;ALi1EF;AK/0EE;EACC,qBJtVQ;EIuVR;EACA;ALi1EH;AKt0EC;EACC;EACA;EACA;EAEC;EACA;EAED;EACA;EACA;EACA,qBJ/WS;EIgXT,kBJ1VU;EI4VV,cJlXS;ADurFX;AKn0EE;EACC;EACA,qBJtXQ;EIuXR,cJvXQ;AD4rFX;AKn0EE;EACC;EACA,0BJ7VO;EI8VP,cJ5XQ;ADisFX;AK3zEC;EACC;AL6zEF;AKnzEE;EACC;EACA;ALqzEH;AKlzEE;EACC;EAEC;EACA;EAED;EAEC;EACA;EACA,qBJ9aO;EIgbR,6CJvYY;EIwYZ,kBJ7YS;EI+YT,cJ/aQ;AD8tFX;AK5yEE;EACC,0BJ3YO;EI4YP,qBJ3aQ;EI4aR,kBJrZS;ADmsFZ;AK5yEG;EACC;AL8yEJ;AKzyEI;EACC;EACA;AL2yEL;AKpyEI;EACC;EACA;ALsyEL;AK/xEE;EACC;EAEC;ALgyEJ;AK7xEG;EACC;EACA;AL+xEJ;AK1xEE;EAEE;EACA;EACA;EACA;AL2xEJ;AKvxEE;EACC;EACA;EAEC;EACA;EAED;EACA;EACA;EACA;ALuxEH;AKrxEG;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAIZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,yBJtgBO;AD4xFX;AKnxEG;EACC,yBJ7fO;ADkxFX;AKzwEC;EACC;EACA;EACA;AL2wEF;AKzwEE;EAEC,WADY;EAEZ,YAFY;EAGZ,yBJ/hBQ;ADyyFX;AKvwEE;EAEE;ALwwEJ;AKpwEE;EAEE;ALqwEJ;AK1vEC;EACC;EACA;EACA;EACA;AL4vEF;AK1vEW;EACR;EACA;AL4vEH;;AKzvEE;EACC;EACA;AL4vEH;AK3uEE;;;;;;;;;;;EACC;ALuvEH;AKnvEG;;;;;;;;;;;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;AL8vEL;AK3vEG;;;;;;;;;;;EACC;EACA;EACA;EAEC;ALswEL;AKnwEI;;;;;;;;;;;EACC;EACA;AL+wEL;AKzwEE;;;;;;;;;;;EACC;EACA;ALqxEH;AKlxEE;;;;;;;;;;;EACC;EACA;AL8xEH;AK3xEE;;;;;;;;;;;EACC;EACA;EACA;EACA;ALuyEH;AKnyEE;;;;;;;;;;;EACC;AL+yEH;AK7yEY;EACR;AL+yEJ;;AK1yEE;;;;;;;;;;;EACC;EACA;EACA;EACA;EACA;ALuzEH;AKrzEG;;;;;;;;;;;EACC;EAEA;EACA;EACA;EACA;EACA;EACA,WANY;EAOZ,YAPY;EAQZ;EACA;EACA,yBJlqBO;EImqBP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ALg0EJ;AK7zEG;;;;;;;;;;;EACC;ALy0EJ;AKh0EG;;;;;;;;;;;EACC;EACA;AL40EJ;AKr0EC;EACC;EACA;EACA;EACA;EACA;ALu0EF;AKt0EE;EACC;EACA;EACA;EACA;EACA;ALw0EH;AKr0EW;EAER;ALs0EH;;AKl0EE;EACC;ALq0EH;AKn0EY;EACR;ALq0EJ;;AKh0EE;EACC;EACA;EACA;ALm0EH;AKh0EI;EACC;EAEA;EACA;EACA;EACA;EACA,WALY;EAMZ,YANY;EAOZ;EACA;EACA,yBJlvBM;EImvBN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ALi0EL;AK/zEc;EACR;EACA;ALi0EN;;AK5zEG;EACC;EAEA;EACA;EACA;EACA;AL8zEJ;AK5zEa;EACR;EACA;AL8zEL;;AK3zEI;EACC,yBJrxBM;EIsxBN;AL8zEL;AKxzEE;EACC;AL0zEH;AKtzEG;EACC;EACA;ALwzEJ;AKnzEE;EACC;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAED;ALmzEH;AKjzEG;EACC;EACA;EACA;EAEC;EAED;ALizEJ;AK/yEI;EACC;EACA;ALizEL;AK1yEE;EACC;EACA;AL4yEH;AK1yEG;EACC;EAEA;EACA;EACA,WAHY;EAIZ,YAJY;EAKZ;EACA;EACA,yBJt0BO;EIu0BP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AL2yEJ;AKzyEa;EACR;EACA;AL2yEL;;AKtyEE;EACC;EACA;EACA;EACA;EACA,yBJh3BQ;EIk3BP;EACA;EACA,yBJl3BO;EIq3BP;EACA;EACA,4BJv3BO;EIy3BR,cJv3BQ;EIw3BR;EAEC;EAGA;EACA;EACA;EACA;EAED;ALiyEH;AKlxEG;;;EACA;EACA;ALsxEH;;AK5wEC;;EACC;EACA;ALgxEF;;AM/tGA;;;;+FAAA;AAQC;EACC;AN+tGF;AM3tGC;EACC;AN6tGF;AMztGC;EAEE;EACA;EACA;EACA;EAED,kBL2DU;EK1DV;EACA;EACA,6CL4Da;AD6pGf;AMvtGE;EACC,cLiBQ;EKhBR;ANytGH;AMttGE;EACC;EACA;ANwtGH;AMrtGE;;EAEC,cLSQ;AD8sGX;AMrtGG;;EACC;ANwtGJ;AMrtGG;;EAEE;EACA;EACA;ANutGL;AMptGI;EAPD;;IAQE;IAEC;IACA;ENutGJ;AACF;AMltGG;;EACC;EACA;ANqtGJ;AMltGG;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,mBLjCO;EKkCP;EACA;EACA;EACA,cLjCO;ADsvGX;AMltGG;;EACC,cLxCO;AD6vGX;AMhtGE;;EAEC;EAEC;EACA;EAED;EACA,yBLxDQ;EKyDR,qBLvDQ;EKyDR;AN+sGH;AM7sGG;EAbD;;IAeG;IACA;ENgtGH;AACF;AM5sGI;EADD;;IAEE;ENgtGH;AACF;AM1sGE;;EAEC;EACA;EAEC;EACA;EACA;EACA;EAED;EACA;EAEC;EACA,4BLzFO;EK0FP;ANysGJ;AMrsGG;EAnBD;;IAqBG;IACA;ENwsGH;AACF;AMnsGE;EACC;ANqsGH;AMjsGE;EACC;EACA;EACA;EACA;EACA;EAEC;EAED,cLnHQ;ADozGX;AM7rGE;EACC;EACA;EACA;EACA;EAEC;EAED;EACA,cLhIQ;AD6zGX;AM1rGE;EAEC,cLpIQ;AD+zGX;AMvrGE;;EAEC;ANyrGH;AMvrGG;;EAEE;ANyrGL;AMlrGE;EACC;IAAoB;ENqrGrB;AACF;AMlrGG;EACC;EACA;EACA;EACA;ANorGJ;AM7qGG;EAEE;EACA;AN8qGL;AM1qGG;EAEE;EACA;AN2qGL;AMpqGC;EAEE;EAGA;EACA;EACA;EACA;EAGD;EACA,cLpMS;ADq2GX;AM/pGE;EACC,cL7OS;AD84GZ;AM1pGC;;EAGE;AN2pGH;;AMrpGA;;;;8FAAA;AAUE;EACC;ANmpGH;AMhpGE;EACC;ANkpGH;AMjpGG;EAAU;ANopGb;AMjpGE;EAEE;EAED;ANipGH;;AMzoGA;;;;8FAAA;AAOC;;EAEC;AN0oGF;;AMroGA;;;;+FAAA;AAOC;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAED,cLtRS;ADw5GX;;AM7nGA;;;;8FAAA;AAKA;EAEE;EACA;EACA;EACA;AN+nGF;AM5nGC;EACC;EAEC;EACA;EACA;EACA;AN6nGH;AMznGC;EAlBD;IAmBE;IACA;IACA;IACA;IACA;EN4nGA;EM1nGA;IACC;EN4nGD;AACF;;AMrnGC;EAEE;EACA;ANunGH;AMnnGC;EARD;IASE;IACA;IACA;IACA;ENsnGA;AACF;;AMnnGA;;;;8FAAA;AAKA;EACC;EACA;EACA;EAEC;ANqnGF;AMlnGC;EAEE;EACA;EAED,cLpWS;ADs9GX;AM/mGE;EACC,cLvWQ;ADw9GX;;AM1mGA;;;;8FAAA;AAOC;EACC;EACA;AN2mGF;AMzmGE;EACC;AN2mGH;AMxmGE;EAEE;EACA;EACA;EACA;ANymGJ;AMrmGE;EACC;EACA;ANumGH;AMrmGG;EAEE;EACA;EACA;EACA;ANsmGL;AMnmGI;EAEE;ANomGN;AM3lGE;EACC;AN6lGH;;AMtlGA;;;;8FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAED;ANqlGD;AMllGC;EAIC;EACA;EACA;EACA;EACA;EAEC;ANglGH;AM5kGE;EAEC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,yBL3cQ;EK4cR;EACA,uBAXY;EAYZ,eAZY;EAaZ;EACA;EACA;EACA;AN4kGH;AMtkGC;EACC;EACA;ANwkGF;AMpkGC;EACC;EACA;ANskGF;AMlkGC;EACC;EACA;ANokGF;AMhkGC;EACC;EACA;ANkkGF;AM9jGC;EACC,qBLhfS;EKifT;ANgkGF;AM9jGE;EACC,yBLpfQ;ADojHX;AM1jGC;EACC;AN4jGF;AM1jGE;EACC,yBL7gBQ;ADykHX;;AMrjGA;;;;+FAAA;AAKA;;;;EAIC;EACA;EAEC;EACA;ANujGF;AMpjGC;;;;;;;;;;;;;;;;EAIC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ANkkGF;AMhkGE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAGC;AN+mGH;AM5mGE;;;;;;;;;;;;;;;;EAGE;EACA;EAED;EACA,cL1jBQ;ADorHX;AMvnGE;;;;;;;;;;;;;;;;EAGE;EACA;EAED;EACA,cLtkBQ;AD2sHX;AMnoGG;;;;;;;;;;;;;;;;EACC;EACA;EAEC;ANmpGL;AM3oGE;;;;;;;;;;;;;;;;EACC;EAEC;AN2pGJ;AMvpGE;;;;;;;;;;;;;;;;EAEE;ANuqGJ;AM/pGE;;;;;;;;EACC;EACA;ANwqGH;AMnqGE;;;;EACC;EACA;ANwqGH;;AM7pGC;;;;;;;;;;;;;;;;;;;;;;EAIC;ANkrGF;AM7qGE;;;;;;;;EAEC;ANqrGH;;AM9qGA;EACC;ANirGD;;AM9qGA;;;;+FAAA;AAOC;EACC;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA,yBLrpBS;EKspBT;EACA,uBATY;EAUZ,eAVY;EAWZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AN6qGF;AM1qGC;EACC;EACA;AN4qGF;;AMvqGA;;;;+FAAA;AAOC;EAEC;;;IAGC;ENuqGD;AACF;;AO55HA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA,mBNyCU;EMxCV,cNqCU;AD03HX;AO75HC;EACC;EACA;EACA;EACA;EACA;AP+5HF;AO75HE;EACC;EACA;AP+5HH;AO75HG;EACC;;IAEC;EP+5HH;EO35HG;;IAEC;EP65HJ;AACF;AOv5HE;EACC;EACA;APy5HH;AOt5HE;EACC;EACA;APw5HH;AOt5HG;EAJD;IAKE;EPy5HF;AACF;AOr5HC;EAlDD;IAmDE;EPw5HA;AACF;AOt5HC;EACC;EAEC;EAED;APs5HF;AOp5HE;EAEE;APq5HJ;AOj5HE;EACC;EACA;EACA;APm5HH;AO/4HC;EACC;EACA,cNrCS;ADs7HX;AO94HC;EACC;EACA;EACA;EACA;EAEC;EAGA;EACA;EACA;EACA;EAGA;EACA;EACA;EAED,kBNpBU;EMsBV,cNzDS;EM0DT;APy4HF;AOv4HE;EACC,yBN3DQ;EM4DR;APy4HH;AOv4HE;EACC,yBN/DQ;EMgER,cNtEQ;AD+8HX;AOv4HE;EAEE;EACA;EACA,qBNvEO;AD+8HX;AOr4HE;EACC;APu4HH;AOj4HG;EACC,yBNjFO;EMkFP,cNxFO;AD29HX;AO/3HE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAED;EACA,kBNnES;EMoET;AP+3HH;AO73HG;EACC;EACA;EACA;EACA;EACA;EACA;AP+3HJ;AO73HI;EACC;EACA;EACA;EACA;AP+3HL;AO13HG;EACC;EACA;AP43HJ;AO13HI;;EAEC;AP43HL;AOz3HI;EACC,mBNzIM;EM0IN;EACA;EACA;EACA;AP23HL;AOz3HK;EACC,cN1IK;EM2IL;EACA;AP23HN;AOz3HM;EACC,mBNrJI;ADghIX;AOr3HI;EACC;EACA;EACA,cNtJM;EMuJN;EAEC;EACA;EACA,yBNlKK;ADwhIX;AOn3HK;EAEC;EACA,cNxJK;AD4gIX;AOj3HK;EACC;EAEA,WADY;EAEZ,YAFY;EAGZ,uBAHY;EAIZ,eAJY;EAKZ;APk3HN;AO/2HK;EACC;EACA;EACA;EACA,oENvJS;EMwJT;EACA;EACA;EACA;EACA;EACA;EAEC;EACA;APg3HP;AO52HK;EACC;EACA;EACA;AP82HN;AO32HK;EACC;EACA;EACA;EACA;EACA;EACA;EAEC;EACA;EAED;EACA;EACA;EACA;AP22HN;AOr2HK;EACC;APu2HN;AOj2HG;EAEC;APk2HJ;AO71HG;EACC;AP+1HJ;AOz1HC;EACC;EACA;EAEC;EACA;EACA;EACA;AP01HH;AOr1HC;EACC;IACC;EPu1HD;AACF;;AOl1HC;EACC;EACA;APq1HF;AOn1HE;EAEE;EACA;APo1HJ;AO/0HC;EAEE;EACA;APg1HH;;AO30HA;;;;+FAAA;AAQE;;EACC;EAEC;EACA;AP20HJ;AOx0HG;;EACC;EACA;EAEA,WADY;EAEZ,YAFY;EAGZ,uBAHY;EAIZ,eAJY;EAMX;EACA;APy0HL;AO5zHG;;;;;;;EACC;APo0HJ;AO9zHG;;;EACC,yBN1UO;AD4oIX;AO5zHE;EAEE;EACA;AP6zHJ;AOtzHE;EAEC,mEADW;EAEX,2DAFW;APyzHd;AOjzHE;EAEC,gEADW;EAEX,wDAFW;APozHd;AO5yHE;EAEC,iEADW;EAEX,yDAFW;AP+yHd;AOvyHE;EAEC,4DADW;EAEX,oDAFW;AP0yHd;AOlyHE;EAEC,8DADW;EAEX,sDAFW;APqyHd;AO7xHE;EAEC,oEADW;EAEX,4DAFW;APgyHd;;AQttIA;;;;+FAAA;AAOC;EACC;ARutIF;AQptIC;EACC;ARstIF;;AQjtIA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EAEC;EAGA;EACA;EACA;EACA;EAED;EACA,6CPgDc;ADgqIf;AQ9sIC;EACC;EACA;EACA;EACA;EACA,iBPkDU;AD8pIZ;AQ7sIC;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;AR4sIH;AQzsIE;EACC,cPjBQ;AD4tIX;AQvsIC;EA/CD;IAgDE;ER0sIA;AACF;AQxsIC;EAnDD;IAoDE;IACA;IACA;IACA;IAEC;ER0sID;AACF;AQvsIC;EACC;EACA;EACA;ARysIF;AQvsIE;EALD;IAME;ER0sID;EQxsIC;;IAEC;ER0sIF;EQvsIC;IAEE;ERwsIH;AACF;AQjsIC;EACC;EACA;EACA;EACA;EACA;EACA;ARmsIF;AQjsIE;EACC;EACA;EACA;ARmsIH;AQ/rIC;EACC;ARisIF;AQ/rIE;EAHD;IAIE;ERksID;AACF;AQ/rIC;EACC;ARisIF;AQ/rIE;EAEE;ARgsIJ;AQ5rIE;EACC,yBP9FQ;EO+FR;EACA;EACA;AR8rIH;;AQvrIA;;;;+FAAA;AAKA;EACC;EACA;EACA;EAEC;EAED;ARwrID;AQtrIC;EATD;IAUE;IACA;IACA;IAEC;IAGA;IACA;ERsrID;AACF;AQnrIC;EAtBD;IAuBE;IACA;ERsrIA;AACF;AQjrIE;EAFD;IAGE;IACA;IACA;IACA;IACA;ERorID;EQlrIC;IACC;ERorIF;EQjrIC;IACC;IACA;IACA;ERmrIF;EQjrIE;IACC;IACA;IACA;IACA;ERmrIH;AACF;AQ3qIC;EAEE;AR4qIH;;AQtqIA;;EAEC;EACA;ARyqID;AQvqIC;;EAEE;EACA;ARyqIH;AQpqIE;;EAEE;EACA;ARsqIJ;;ASn5IA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAED,yBR6CU;EQ5CV,kBRkEW;EQhEV;EACA;EACA;EAED;EACA;EACA;EACA;ATk5ID;ASh5IC;EACC;ATk5IF;AS/4IC;EACC,yBR6BS;EQ5BT;EACA;ATi5IF;AS94IC;EACC;EAEC;EACA;EACA;EACA;AT+4IH;AS14IC;EACC;EACA;EACA,qBRSS;ADm4IX;AS14IE;EACC;AT44IH;ASx4IC;EACC;EACA;EACA,qBRfS;ADy5IX;ASx4IE;EACC;EACA,qBRlBQ;AD45IX;ASt4IC;EACC;EACA;EACA;ATw4IF;ASt4IE;EACC;ATw4IH;ASp4IC;EACC,oERFa;EQGb;EACA;EACA;ATs4IF;ASp4IE;EACC;ATs4IH;;ASj4IA;;;;+FAAA;AAMC;EAEC,WADY;EAEZ,YAFY;EAGZ,uBAHY;EAIZ,eAJY;EAMX;EACA;ATi4IH;AS53IE;EAEC,WADY;EAEZ,YAFY;EAGZ,uBAHY;EAIZ,eAJY;EAMX;EACA;AT43IJ;;ASr3IC;EAEE;EACA;ATu3IH;ASl3IE;EAEE;EACA;ATm3IJ;;AS72IA;;;;+FAAA;AAMC;EACC;EACA;EACA;AT+2IF;;AUvgJA;;;;8FAAA;AAOC;;EAEC;EACA,WAFY;EAGZ,YAHY;EAIZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AVwgJF;;AUpgJA;;;;8FAAA;AAKA;EA0JC;;;;gGAAA;AVk3ID;AUzgJC;EACC;EAEA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA;EAEA;EACA;EAEA;EACA;EAEA,6CT8Ba;ES7Bb;EAEA;EAEA;EACA;EACA;AVkgJF;AU//IC;EACC;EACA;AVigJF;AU9/IC;EACC;EACA;AVggJF;AU7/IC;EACC;EACA;AV+/IF;AU5/IC;EACC;EACA;AV8/IF;AU3/IC;EACC;EACA;AV6/IF;AU1/IC;EACC;EACA;AV4/IF;AUz/IC;EACC;EACA;AV2/IF;AUx/IC;EACC;EACA;AV0/IF;AUx/IE;EAEC;AVy/IH;AUr/IC;EACC;EACA;AVu/IF;AUp/IC;EACC;EACA;AVs/IF;AUn/IC;EACC;EACA;AVq/IF;AUl/IC;;EAEC;EACA;AVo/IF;AUj/IC;;EAEC;EACA;AVm/IF;AUh/IC;EACC;EACA;AVk/IF;AU/+IC;;EAEC;EACA;AVi/IF;AU9+IC;;EAEC;EACA;AVg/IF;AU7+IC;EACC;EACA;AV++IF;AU5+IC;EACC;EACA;AV8+IF;AU3+IC;EACC;EACA;AV6+IF;AU1+IC;EACC;EACA;AV4+IF;AUz+IC;EACC;EACA;AV2+IF;AUx+IC;EACC;EACA;AV0+IF;AUj+IE;;EACC;AVo+IH;AUl+IG;;EAEC;EACA,WAFY;EAGZ,YAHY;EAIZ,yBTzJO;ES0JP;EACA;EACA,uBAPY;EAQZ,eARY;EASZ;EACA;EACA;EACA;EACA;EACA;AVo+IJ;AUl+II;;EACC;AVq+IL;;AU99IA;;;;8FAAA;AAUE;;;;;;;;;;;;EAEC;EACA;EACA;EACA;AVs+IH;AUp+IG;;;;;;;;;;;;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAKX;EAED,yBTvMO;ESwMP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AV8+IJ;;AUl+IG;;;;;;;;EAEE;EACA;AV2+IL;;AUn+IA;;;EAGC;EACA;AVs+ID;;AUl+IA;EACC;EACA;AVq+ID;;AUj+IA;EACC;EACA;AVo+ID;;AUh+IA;EACC;EACA;AVm+ID;;AU39IC;;;EACC;EACA;AVg+IF;;AU19IA;EACC;EACA;EACA;EACA;EACA;AV69ID;;AU19IA;;;;8FAAA;AAaC;;;;;;;EACC;AV29IF;AUz9IE;;;;;;;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAIZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AVg+IH;AUz9IG;;;;;;;EACC;EACA;AVi+IJ;;AU39IA;;;;+FAAA;AAUE;;;;;;;;EAEC;EACA;EACA;EACA;AV+9IH;AU79IG;;;;;;;;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAKX;EAED,yBT7VO;ES8VP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AVm+IJ;AUv9II;;;;;;;;EAEE;EACA;AV+9IN;;AUt9IA;EACC;EACA;AVy9ID;;AUr9IA;EACC;EACA;AVw9ID;;AUp9IA;EACC;EACA;AVu9ID;;AUn9IA;EACC;EACA;AVs9ID;;AUn9IA;;;;8FAAA;AAMC;EAEC,WADY;EAEZ,YAFY;AVs9Id;;AWj6JA;;;;8FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,yBVyCU;EUvCT;EACA;EACA,qBVuCS;EUrCV;AXk6JD;AWh6JC;EAEC;EACA,WAFY;EAGZ,YAHY;EAIZ;EACA,yBVgCS;EU/BT;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AXi6JF;;AW55JA;;;;8FAAA;AAOA;EACC;EACA;AX65JD;;AWz5JA;EACC;EACA;AX45JD;;AWx5JA;EACC;EACA;AX25JD;;AWv5JA;EACC;EACA;AX05JD;;AWt5JA;EACC;EACA;AXy5JD;;AWr5JA;EACC;EACA;AXw5JD;;AWp5JA;EACC;EACA;AXu5JD;;AWn5JA;EACC;EACA;AXs5JD;;AWl5JA;EACC;EACA;AXq5JD;;AWj5JA;EACC;EACA;AXo5JD;;AWh5JA;EACC;EACA;AXm5JD;;AW/4JA;EACC;EACA;AXk5JD;;AW94JA;EACC;EACA;AXi5JD;;AW74JA;EACC;EACA;AXg5JD;;AW54JA;EACC;EACA;AX+4JD;;AW34JA;EACC;EACA;AX84JD;;AW14JA;EACC;EACA;AX64JD;;AWz4JA;EACC;EACA;AX44JD;;AWx4JA;EACC;EACA;AX24JD;;AWv4JA;EACC;EACA;AX04JD;;AWt4JA;EACC;EACA;AXy4JD;;AWr4JA;EACC;EACA;AXw4JD;;AWp4JA;EACC;EACA;AXu4JD;;AWn4JA;EACC;EACA;AXs4JD;;AWl4JA;EACC;EACA;AXq4JD;;AWj4JA;EACC;EACA;AXo4JD;;AWh4JA;EACC;EACA;AXm4JD;;AW/3JA;EACC;EACA;AXk4JD;;AW93JA;EACC;EACA;AXi4JD;;AW73JA;EACC;EACA;AXg4JD;;AW53JA;EACC;EACA;AX+3JD;;AW33JA;EACC;EACA;AX83JD;;AW13JA;EACC;EACA;AX63JD;;AWz3JA;EACC;EACA;AX43JD;;AWv3JA;EACC;EACA;AX03JD;;AWt3JA;EACC;EACA;AXy3JD;;AY/nKA;;;;+FAAA;AAOC;EACC;AZgoKF;AY7nKC;EAEE;EACA;EACA;EACA;AZ8nKH;AY3nKE;EACC;EACA;EACA;EAEC;AZ4nKJ;AYznKG;EARD;IASE;EZ4nKF;AACF;AYtnKC;EAEE;AZunKH;AYnnKC;EACC;EACA;EACA;EACA;EACA;AZqnKF;AYnnKE;EAPD;IAQE;IACA;IACA;IACA;IACA;IACA;IACA;EZsnKD;AACF;;AYhnKA;;;;+FAAA;AASE;EACC;AZ+mKH;AY3mKE;EAEE;AZ4mKJ;AYvmKE;EACC;EACA;EAEC;EACA;EACA;EACA;AZwmKJ;AYpmKE;EAEE;EACA;EACA;EACA;EAED;AZomKH;AYlmKG;EACC;AZomKJ;AYlmKI;EACC;EACA;AZomKL;AYjmKI;EACC;AZmmKL;AYhmKI;EACC;EACA;EACA;AZkmKL;AY3lKE;EACC;AZ6lKH;AY1lKE;EACC;AZ4lKH;AY1lKG;EACC;EACA;EACA,cXpFO;ADgrKX;AYzlKI;EACC;AZ2lKL;AYplKE;EAEE;EAGA;EACA;EACA,qBX1GO;EW4GR,kBXxES;AD0pKZ;AYhlKG;EACC;EACA;EACA;EACA;EACA;EACA;EAEC;EACA;EAGA;EACA;EACA,4BX7HM;AD4sKX;AY5kKI;EACC;AZ8kKL;;Aa7vKA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;AbgwKD;;Aa7vKA;;;EAGC;AbgwKD;;Aa7vKA;;;;+FAAA;AAOC;EAEE;EACA;EACA;EACA;Ab6vKH;Aa1vKE;EAEE;EACA;EACA;EACA;Ab2vKJ;AavvKE;EAjBD;IAkBE;Eb0vKD;AACF;;AapvKA;;;;+FAAA;AAOC;EACC;EAEC;EACA;EACA;AbovKH;;Aa9uKA;;;;+FAAA;AAKA;EACC;EAEC;AbgvKF;Aa7uKC;EAND;IAQG;IACA;Eb+uKD;AACF;Aa5uKC;EAEE;Ab6uKH;AazuKC;EACC;Ab2uKF;AaxuKC;EAEE;EACA;AbyuKH;AaruKC;EACC;AbuuKF;;AaluKA;;;;+FAAA;AAKA;EACC;EACA;AbquKD;AajuKE;;EAGE;EACA;EACA;EACA;EAGD,cZhFQ;ADgzKX;Aa3tKC;EAEE;EACA;EAGA;EAGA;EACA;EACA,yBZrGQ;EYuGT,cZlGS;ADyzKX;AartKE;EAEE;AbstKJ;AaltKE;EAEE;EACA;AbmtKJ;AahtKG;EAEE;AbitKL;Aa7sKG;EAEC,cZ1HO;ADw0KX;AavsKC;EACC;AbysKF;;Ac53KA;;;;8FAAA;AAOC;EACC;EACA;EACA;EAEC;EACA;EACA;EACA;EAED,oEb8Da;Ea7Db;EACA;EACA;EACA;EACA,kBb6DU;Ea5DV;Ad23KF;Acz3KE;EAlBD;IAmBE;Ed43KD;AACF;Ac13KE;EACC;Ad43KH;Acz3KE;EACC;EACA;EACA;Ad23KH;Acx3KE;EACC;EAEC;EACA;EAGD;EACA;EACA;Adu3KH;Acp3KE;EAEC,WADY;EAEZ,YAFY;EAIX;EACA;EAED,yBbfQ;ADk4KX;;Ac52KA;;;;8FAAA;AAKA;;EAEC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,cbnCU;EaoCV;EACA;EACA;Ad+2KD;Ac72KC;EAfD;;IAgBE;IACA;Edi3KA;AACF;Ac/2KC;EApBD;;IAqBE;IACA;Edm3KA;AACF;Acj3KC;;EACC;Ado3KF;Acj3KC;;EACC;EACA;EACA;Ado3KF;Acj3KC;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;Ado3KF;Acl3KE;EAXD;;IAYE;Eds3KD;AACF;Acp3KE;EAfD;;IAgBE;Edw3KD;AACF;Act3KE;;;;EAEC;EACA;Ad03KH;Acx3KG;;;;EACC;EACA;EACA;EACA;EACA;Ad63KJ;Acx3KE;;EACC;EACA;Ad23KH;Acz3KG;;EACC;EACA;Ad43KJ;Acz3KG;EATD;;IAUE;IACA;Ed63KF;AACF;Acz3KE;EAlDD;;IAmDE;IACA;IACA;IACA;IACA;IACA;IACA;Ed63KD;Ec33KC;;IACC;IACA;Ed83KF;AACF;Ac33KE;;EACC;EACA;EACA;Ad83KH;Ac53KG;;EACC;EACA;EACA;EACA;EACA,cbtIO;ADqgLX;Ac53KG;EAbD;;IAcE;IACA;IAEC;IACA;Ed+3KH;Ec53KE;;;;IAEC;Edg4KH;AACF;Ac33KE;;EACC;EACA;EACA;EACA;EACA;Ad83KH;Ac53KG;EAPD;;IAQE;IACA;IACA;Edg4KF;Ec93KE;;IACC;IACA;IACA;Edi4KH;AACF;Ac53KE;;EACC;EACA;EACA;EACA;EACA;Ad+3KH;Ac73KG;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;Adg4KJ;Ac93KI;;EACC;EACA;EACA;EACA;Adi4KL;Ac/3KK;;EACC;EACA;EACA;Adk4KN;Ac/3KK;EACC;;IAAY;IAAa;Edo4K7B;AACF;Ach4KI;;EACC;EACA;Adm4KL;Ach4KI;;EACC;EACA;Adm4KL;Ach4KI;;EACC;EACA;EACA;EACA;EACA;Adm4KL;Ac/3KG;EAxDD;;IAyDE;IACA;IACA;Edm4KF;Ecj4KE;;IACC;IACA;IACA;IACA;IACA;Edo4KH;Ecj4KG;;IACC;IACA;Edo4KJ;Ecj4KG;;IACC;Edo4KJ;AACF;Ach4KG;EAhFD;;IAiFE;IACA;IACA;IACA;IACA;IACA;Edo4KF;Ecl4KE;;IACC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;Edq4KH;Ecn4KG;;IACC;Eds4KJ;Ecn4KG;;IACC;IACA;Eds4KJ;Ecp4KI;;IACC;IACA;Edu4KL;Ecn4KG;;IACC;IACA;Eds4KJ;AACF;Ac/3KC;;EAEE;EACA;EAGA;EACA;EAGD;EACA,cbtTS;ADmrLX;Ac33KE;;EAEE;Ad63KJ;Act3KC;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;Ady3KF;Acv3KE;;EACC;Ad03KH;Acv3KE;;EAEE;EACA;Ady3KJ;Acr3KE;;EACC,oEbnTY;EaoTZ;EACA;EACA;EACA;EACA;Adw3KH;Act3KG;;EACC;Ady3KJ;Acp3KC;;EACC;EACA;EACA;EACA;EACA;EACA;EACA,cbtWS;EauWT;EACA;EACA;Adu3KF;Acr3KE;;EACC;EACA;EACA;EACA;EACA;EACA;Adw3KH;Acr3KE;;EACC;EACA;EACA;EACA;Adw3KH;Acr3KE;;EACC,cb5XQ;Ea6XR;Adw3KH;Act3KG;;EACC,cbjYO;AD0vLX;Act3KG;;EACC;EACA;EACA;Ady3KJ;Acl3KG;;EACC;EACA;Adq3KJ;Ach3KE;EArDD;;IAsDE;IACA;Edo3KD;Ecl3KC;;IACC;Edq3KF;Ecl3KC;;IACC;IACA;Edq3KF;Ecn3KE;;IACC;IACA;IACA;Eds3KH;AACF;;Ac52KC;;;EACC;Adi3KF;;Aeh1LA;;;;8FAAA;AASC;;;EACC;Afi1LF;Ae/0LC;;;EACC;Afm1LF;Ae/0LE;;;EACC;Afm1LH;Ae/0LC;;;;;;EAEC,iBdyEU;EcxEV;Afq1LF;Ael1LC;;;EACC;Afs1LF;Aeh1LE;;;;;;EAEE;EACA;EACA;EACA;Afs1LJ;Aeh1LE;;;EAEE;Afm1LJ;Ae90LC;;;EACC;Afk1LF;Ae90LC;;;EACC;Afk1LF;Ae90LC;;;EAEE;EACA;EACA;EACA;Afi1LH;Ae90LE;;;EAEE;Afi1LJ;Ae30LC;;;;;;;;;EAGC;Afm1LF;Aeh1LC;;;EACC;Afo1LF;Aej1LC;;;EACC;Afq1LF;Aen1LE;;;EACC;Afu1LH;Aer1LG;;;EAEE;EACA;Afw1LL;Aen1LE;;;EACC;EACA;EACA;EACA;EACA;Afu1LH;Aer1LG;;;EACC;Afy1LJ;Aet1LG;;;EACC;Af01LJ;Aex1LI;;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;Af41LL;Ae31LK;;;EACC;EAEA;EACA;EACA,WAHY;EAIZ,YAJY;EAKZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;Af81LN;Ae51LK;;;EACC,cdtFK;ADs7LX;Ae51LI;;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;Afg2LL;Ae/1LK;;;EACC;EACA,4BdzGK;AD48LX;Ae31LC;;;EAEE;EACA;EAGA;EACA;EACA;EACA;Af41LH;Aev1LC;;;EAEE;Af01LH;Aer1LC;;;;;;EAEC;EACA;EACA;EACA;Af21LF;Aev1LC;;;EACC;EACA;EACA;EACA;EAEC;EAGA;EACA;EAED;EAEC;EACA;EACA,4Bd9KQ;ADogMX;Aej1LC;;;EACC;EACA;EACA;EAEC;EAGA;EAGA;EACA;EACA,0Bd/LQ;AD+gMX;Ae70LE;;;EACC;EACA;EAEA,WADY;EAEZ,YAFY;EAGZ,uBAHY;EAIZ,eAJY;EAKZ,yBdxMQ;ADwhMX;;Ae10LA;EACC;EACA;EAEC;EACA;EACA;EACA;EAED;EACA;EACA,yBd3NU;Ec4NV,cdzNU;ADoiMX;;Aex0LA;EACC;Af20LD;;AgBzlMA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,mBfsEW;EerEX;EAEA;AhB2lMD;AgBzlMC;EACC;EACA;EACA;EACA;EACA;AhB2lMF;AgBzlME;;;EAGC;AhB2lMH;AgBxlME;EACC;EACA;EACA;EACA;EACA,mBfMQ;EeLR;EACA;AhB0lMH;AgBxlMG;EACC;AhB0lMJ;AgBxlMI;EACC;EACA;EACA;EACA;EACA;EAEA,WADY;EAEZ,YAFY;EAGZ;EACA;EACA,yBfRM;EeSN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AhBylML;AgBtlMI;EACC;EACA;EACA;EACA;EACA;AhBwlML;AgBnlME;EACC;EACA;EACA;EACA;AhBqlMH;AgBnlMG;EACC;AhBqlMJ;AgBllMG;EACC;AhBolMJ;AgBjlMG;;EAEC;EACA;EACA;EACA;AhBmlMJ;AgBjlMI;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,mBflEM;EemEN;EACA;EACA;EACA,cf9DM;Ee+DN;AhBolML;AgBllMK;;;;EAGC,mBfhEK;EeiEL;EACA;AhBqlMN;AgBllMK;;EACC;EACA;EACA;AhBqlMN;AgBnlMM;;EACC;EACA;AhBslMP;AgBllMK;;EACC;AhBqlMN;AgBhlMI;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,oEfxEU;EeyEV;EACA;EACA;EACA;EAEC;EACA;AhBklMN;AgBhlMK;;EACC;EACA;EACA;AhBmlMN;AgB7kME;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAED;EACA;AhB6kMH;AgB3kMG;;EAEC;EACA;AhB6kMJ;AgB1kMG;EACC;EACA;AhB4kMJ;AgBzkMG;EACC;EACA;AhB2kMJ;AgBtkMC;EACC;EACA;EACA;EACA,yBflKS;EemKT;EACA;EACA;EACA;EACA;EACA;EACA;AhBwkMF;AgBtkME;EACC;EACA;EACA,cfzKQ;ADivMX;AgBrkME;EACC;EACA;EACA;EAEC;EAGA;EACA;EAED;EACA,kBfrJS;EesJT;AhBmkMH;AgB/jME;EACC;AhBikMH;AgB9jME;EACC;AhBgkMH;AgB9jMG;EACC;EAEC;EACA;EACA;EACA;AhB+jML;AgB3jMG;EACC;EACA;EACA;EACA;EAEC;EAGA;EACA;EAED,oEf5LW;Ee6LX;EACA;EACA;EACA;EACA;EACA;AhByjMJ;AgBvjMI;EACC;EACA;EAEC;AhBwjMN;AgBljME;EACC;EACA;EACA;EACA;AhBojMH;AgBljMG;EAEC,WADY;EAEZ,YAFY;AhBqjMhB;AgBhjMG;EACC;AhBkjMJ;AgB/iMG;EACC;EACA;EACA;AhBijMJ;AgB/iMI;EACC;AhBijML;AgB3iMC;;EAEC;AhB6iMF;AgBziME;;;EAGC;AhB2iMH;AgBxiME;EACC;AhB0iMH;AgBriME;;;;;;EAMC;AhBuiMH;AgBpiME;EAEE;EACA;EACA,4Bf1SO;AD+0MX;AgBjiME;EACC;EACA;EACA;EACA;EACA;EACA;AhBmiMH;AgBjiMG;EACC;AhBmiMJ;AgBhiMG;EACC;AhBkiMJ;AgBhiMI;EACC;AhBkiML;AgB9hMG;EACC;EACA;EACA;EACA;AhBgiMJ;;AgB1hMA;;;;+FAAA;AAKA;EACC;IACC;EhB6hMA;AACF,C", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/acf-global.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_variables.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_mixins.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_global.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_typography.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_admin-inputs.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_list-table.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_admin-toolbar.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_acf-headerbar.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_btn.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_icons.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_field-type-icons.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_tools.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_updates.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_pro-upgrade.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_post-types-taxonomies.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_field-picker.scss"], "sourcesContent": ["@charset \"UTF-8\";\n/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n/* colors */\n/* acf-field */\n/* responsive */\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n/*--------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*--------------------------------------------------------------------------------------------*/\n/* Horizontal List */\n.acf-hl {\n  padding: 0;\n  margin: 0;\n  list-style: none;\n  display: block;\n  position: relative;\n}\n\n.acf-hl > li {\n  float: left;\n  display: block;\n  margin: 0;\n  padding: 0;\n}\n\n.acf-hl > li.acf-fr {\n  float: right;\n}\n\n/* Horizontal List: Clearfix */\n.acf-hl:before,\n.acf-hl:after,\n.acf-bl:before,\n.acf-bl:after,\n.acf-cf:before,\n.acf-cf:after {\n  content: \"\";\n  display: block;\n  line-height: 0;\n}\n\n.acf-hl:after,\n.acf-bl:after,\n.acf-cf:after {\n  clear: both;\n}\n\n/* Block List */\n.acf-bl {\n  padding: 0;\n  margin: 0;\n  list-style: none;\n  display: block;\n  position: relative;\n}\n\n.acf-bl > li {\n  display: block;\n  margin: 0;\n  padding: 0;\n  float: none;\n}\n\n/* Visibility */\n.acf-hidden {\n  display: none !important;\n}\n\n.acf-empty {\n  display: table-cell !important;\n}\n.acf-empty * {\n  display: none !important;\n}\n\n/* Float */\n.acf-fl {\n  float: left;\n}\n\n.acf-fr {\n  float: right;\n}\n\n.acf-fn {\n  float: none;\n}\n\n/* Align */\n.acf-al {\n  text-align: left;\n}\n\n.acf-ar {\n  text-align: right;\n}\n\n.acf-ac {\n  text-align: center;\n}\n\n/* loading */\n.acf-loading,\n.acf-spinner {\n  display: inline-block;\n  height: 20px;\n  width: 20px;\n  vertical-align: text-top;\n  background: transparent url(../../images/spinner.gif) no-repeat 50% 50%;\n}\n\n/* spinner */\n.acf-spinner {\n  display: none;\n}\n\n.acf-spinner.is-active {\n  display: inline-block;\n}\n\n/* WP < 4.2 */\n.spinner.is-active {\n  display: inline-block;\n}\n\n/* required */\n.acf-required {\n  color: #f00;\n}\n\n/* Allow pointer events in reusable blocks */\n.acf-button,\n.acf-tab-button {\n  pointer-events: auto !important;\n}\n\n/* show on hover */\n.acf-soh .acf-soh-target {\n  -webkit-transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n  -moz-transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n  -o-transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n  transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n  visibility: hidden;\n  opacity: 0;\n}\n\n.acf-soh:hover .acf-soh-target {\n  -webkit-transition-delay: 0s;\n  -moz-transition-delay: 0s;\n  -o-transition-delay: 0s;\n  transition-delay: 0s;\n  visibility: visible;\n  opacity: 1;\n}\n\n/* show if value */\n.show-if-value {\n  display: none;\n}\n\n.hide-if-value {\n  display: block;\n}\n\n.has-value .show-if-value {\n  display: block;\n}\n\n.has-value .hide-if-value {\n  display: none;\n}\n\n/* select2 WP animation fix */\n.select2-search-choice-close {\n  -webkit-transition: none;\n  -moz-transition: none;\n  -o-transition: none;\n  transition: none;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  tooltip\n*\n*---------------------------------------------------------------------------------------------*/\n/* tooltip */\n.acf-tooltip {\n  background: #1D2939;\n  border-radius: 6px;\n  color: #D0D5DD;\n  padding-top: 8px;\n  padding-right: 12px;\n  padding-bottom: 10px;\n  padding-left: 12px;\n  position: absolute;\n  z-index: 900000;\n  max-width: 280px;\n  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);\n  /* tip */\n  /* positions */\n}\n.acf-tooltip:before {\n  border: solid;\n  border-color: transparent;\n  border-width: 6px;\n  content: \"\";\n  position: absolute;\n}\n.acf-tooltip.top {\n  margin-top: -8px;\n}\n.acf-tooltip.top:before {\n  top: 100%;\n  left: 50%;\n  margin-left: -6px;\n  border-top-color: #2f353e;\n  border-bottom-width: 0;\n}\n.acf-tooltip.right {\n  margin-left: 8px;\n}\n.acf-tooltip.right:before {\n  top: 50%;\n  margin-top: -6px;\n  right: 100%;\n  border-right-color: #2f353e;\n  border-left-width: 0;\n}\n.acf-tooltip.bottom {\n  margin-top: 8px;\n}\n.acf-tooltip.bottom:before {\n  bottom: 100%;\n  left: 50%;\n  margin-left: -6px;\n  border-bottom-color: #2f353e;\n  border-top-width: 0;\n}\n.acf-tooltip.left {\n  margin-left: -8px;\n}\n.acf-tooltip.left:before {\n  top: 50%;\n  margin-top: -6px;\n  left: 100%;\n  border-left-color: #2f353e;\n  border-right-width: 0;\n}\n.acf-tooltip .acf-overlay {\n  z-index: -1;\n}\n\n/* confirm */\n.acf-tooltip.-confirm {\n  z-index: 900001;\n}\n.acf-tooltip.-confirm a {\n  text-decoration: none;\n  color: #9ea3a8;\n}\n.acf-tooltip.-confirm a:hover {\n  text-decoration: underline;\n}\n.acf-tooltip.-confirm a[data-event=confirm] {\n  color: #f55e4f;\n}\n\n.acf-overlay {\n  position: fixed;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  cursor: default;\n}\n\n.acf-tooltip-target {\n  position: relative;\n  z-index: 900002;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  loading\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-loading-overlay {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  cursor: default;\n  z-index: 99;\n  background: rgba(249, 249, 249, 0.5);\n}\n.acf-loading-overlay i {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-icon\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-icon {\n  display: inline-block;\n  height: 28px;\n  width: 28px;\n  border: transparent solid 1px;\n  border-radius: 100%;\n  font-size: 20px;\n  line-height: 21px;\n  text-align: center;\n  text-decoration: none;\n  vertical-align: top;\n  box-sizing: border-box;\n}\n.acf-icon:before {\n  font-family: dashicons;\n  display: inline-block;\n  line-height: 1;\n  font-weight: 400;\n  font-style: normal;\n  speak: none;\n  text-decoration: inherit;\n  text-transform: none;\n  text-rendering: auto;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  width: 1em;\n  height: 1em;\n  vertical-align: middle;\n  text-align: center;\n}\n\n.acf-icon.-plus:before {\n  content: \"\\f543\";\n}\n\n.acf-icon.-minus:before {\n  content: \"\\f460\";\n}\n\n.acf-icon.-cancel:before {\n  content: \"\\f335\";\n  margin: -1px 0 0 -1px;\n}\n\n.acf-icon.-pencil:before {\n  content: \"\\f464\";\n}\n\n.acf-icon.-location:before {\n  content: \"\\f230\";\n}\n\n.acf-icon.-up:before {\n  content: \"\\f343\";\n  margin-top: -0.1em;\n}\n\n.acf-icon.-down:before {\n  content: \"\\f347\";\n  margin-top: 0.1em;\n}\n\n.acf-icon.-left:before {\n  content: \"\\f341\";\n  margin-left: -0.1em;\n}\n\n.acf-icon.-right:before {\n  content: \"\\f345\";\n  margin-left: 0.1em;\n}\n\n.acf-icon.-sync:before {\n  content: \"\\f463\";\n}\n\n.acf-icon.-globe:before {\n  content: \"\\f319\";\n  margin-top: 0.1em;\n  margin-left: 0.1em;\n}\n\n.acf-icon.-picture:before {\n  content: \"\\f128\";\n}\n\n.acf-icon.-check:before {\n  content: \"\\f147\";\n  margin-left: -0.1em;\n}\n\n.acf-icon.-dot-3:before {\n  content: \"\\f533\";\n  margin-top: -0.1em;\n}\n\n.acf-icon.-arrow-combo:before {\n  content: \"\\f156\";\n}\n\n.acf-icon.-arrow-up:before {\n  content: \"\\f142\";\n  margin-left: -0.1em;\n}\n\n.acf-icon.-arrow-down:before {\n  content: \"\\f140\";\n  margin-left: -0.1em;\n}\n\n.acf-icon.-search:before {\n  content: \"\\f179\";\n}\n\n.acf-icon.-link-ext:before {\n  content: \"\\f504\";\n}\n\n.acf-icon.-duplicate {\n  position: relative;\n}\n.acf-icon.-duplicate:before, .acf-icon.-duplicate:after {\n  content: \"\";\n  display: block;\n  box-sizing: border-box;\n  width: 46%;\n  height: 46%;\n  position: absolute;\n  top: 33%;\n  left: 23%;\n}\n.acf-icon.-duplicate:before {\n  margin: -1px 0 0 1px;\n  box-shadow: 2px -2px 0px 0px currentColor;\n}\n.acf-icon.-duplicate:after {\n  border: solid 2px currentColor;\n}\n\n.acf-icon.-trash {\n  position: relative;\n}\n.acf-icon.-trash:before, .acf-icon.-trash:after {\n  content: \"\";\n  display: block;\n  box-sizing: border-box;\n  width: 46%;\n  height: 46%;\n  position: absolute;\n  top: 33%;\n  left: 23%;\n}\n.acf-icon.-trash:before {\n  margin: -1px 0 0 1px;\n  box-shadow: 2px -2px 0px 0px currentColor;\n}\n.acf-icon.-trash:after {\n  border: solid 2px currentColor;\n}\n\n.acf-icon.-collapse:before {\n  content: \"\\f142\";\n  margin-left: -0.1em;\n}\n\n.-collapsed .acf-icon.-collapse:before {\n  content: \"\\f140\";\n  margin-left: -0.1em;\n}\n\nspan.acf-icon {\n  color: #555d66;\n  border-color: #b5bcc2;\n  background-color: #fff;\n}\n\na.acf-icon {\n  color: #555d66;\n  border-color: #b5bcc2;\n  background-color: #fff;\n  position: relative;\n  transition: none;\n  cursor: pointer;\n}\na.acf-icon:hover {\n  background: #f3f5f6;\n  border-color: #0071a1;\n  color: #0071a1;\n}\na.acf-icon.-minus:hover, a.acf-icon.-cancel:hover {\n  background: #f7efef;\n  border-color: #a10000;\n  color: #dc3232;\n}\na.acf-icon:active, a.acf-icon:focus {\n  outline: none;\n  box-shadow: none;\n}\n\n.acf-icon.-clear {\n  border-color: transparent;\n  background: transparent;\n  color: #444;\n}\n\n.acf-icon.light {\n  border-color: transparent;\n  background: #f5f5f5;\n  color: #23282d;\n}\n\n.acf-icon.dark {\n  border-color: transparent !important;\n  background: #23282d;\n  color: #eee;\n}\n\na.acf-icon.dark:hover {\n  background: #191e23;\n  color: #00b9eb;\n}\na.acf-icon.dark.-minus:hover, a.acf-icon.dark.-cancel:hover {\n  color: #d54e21;\n}\n\n.acf-icon.grey {\n  border-color: transparent !important;\n  background: #b4b9be;\n  color: #fff !important;\n}\n.acf-icon.grey:hover {\n  background: #00a0d2;\n  color: #fff;\n}\n.acf-icon.grey.-minus:hover, .acf-icon.grey.-cancel:hover {\n  background: #32373c;\n}\n\n.acf-icon.small,\n.acf-icon.-small {\n  width: 20px;\n  height: 20px;\n  line-height: 14px;\n  font-size: 14px;\n}\n.acf-icon.small.-duplicate:before, .acf-icon.small.-duplicate:after,\n.acf-icon.-small.-duplicate:before,\n.acf-icon.-small.-duplicate:after {\n  opacity: 0.8;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-box\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-box {\n  background: #ffffff;\n  border: 1px solid #ccd0d4;\n  position: relative;\n  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);\n  /* title */\n  /* footer */\n}\n.acf-box .title {\n  border-bottom: 1px solid #ccd0d4;\n  margin: 0;\n  padding: 15px;\n}\n.acf-box .title h3 {\n  display: flex;\n  align-items: center;\n  font-size: 14px;\n  line-height: 1em;\n  margin: 0;\n  padding: 0;\n}\n.acf-box .inner {\n  padding: 15px;\n}\n.acf-box h2 {\n  color: #333333;\n  font-size: 26px;\n  line-height: 1.25em;\n  margin: 0.25em 0 0.75em;\n  padding: 0;\n}\n.acf-box h3 {\n  margin: 1.5em 0 0;\n}\n.acf-box p {\n  margin-top: 0.5em;\n}\n.acf-box a {\n  text-decoration: none;\n}\n.acf-box i.dashicons-external {\n  margin-top: -1px;\n}\n.acf-box .footer {\n  border-top: 1px solid #ccd0d4;\n  padding: 12px;\n  font-size: 13px;\n  line-height: 1.5;\n}\n.acf-box .footer p {\n  margin: 0;\n}\n.acf-admin-3-8 .acf-box {\n  border-color: #E5E5E5;\n}\n.acf-admin-3-8 .acf-box .title,\n.acf-admin-3-8 .acf-box .footer {\n  border-color: #E5E5E5;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-notice\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-notice {\n  position: relative;\n  display: block;\n  color: #fff;\n  margin: 5px 0 15px;\n  padding: 3px 12px;\n  background: #2a9bd9;\n  border-left: #1f7db1 solid 3px;\n}\n.acf-notice p {\n  font-size: 13px;\n  line-height: 1.5;\n  margin: 0.5em 0;\n  text-shadow: none;\n  color: inherit;\n}\n.acf-notice .acf-notice-dismiss {\n  position: absolute;\n  top: 9px;\n  right: 12px;\n  background: transparent !important;\n  color: inherit !important;\n  border-color: #fff !important;\n  opacity: 0.75;\n}\n.acf-notice .acf-notice-dismiss:hover {\n  opacity: 1;\n}\n.acf-notice.-dismiss {\n  padding-right: 40px;\n}\n.acf-notice.-error {\n  background: #d94f4f;\n  border-color: #c92c2c;\n}\n.acf-notice.-success {\n  background: #49ad52;\n  border-color: #3a8941;\n}\n.acf-notice.-warning {\n  background: #fd8d3b;\n  border-color: #fc7009;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-table\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-table {\n  border: #ccd0d4 solid 1px;\n  background: #fff;\n  border-spacing: 0;\n  border-radius: 0;\n  table-layout: auto;\n  padding: 0;\n  margin: 0;\n  width: 100%;\n  clear: both;\n  box-sizing: content-box;\n  /* defaults */\n  /* thead */\n  /* tbody */\n  /* -clear */\n}\n.acf-table > tbody > tr > th,\n.acf-table > tbody > tr > td,\n.acf-table > thead > tr > th,\n.acf-table > thead > tr > td {\n  padding: 8px;\n  vertical-align: top;\n  background: #fff;\n  text-align: left;\n  border-style: solid;\n  font-weight: normal;\n}\n.acf-table > tbody > tr > th,\n.acf-table > thead > tr > th {\n  position: relative;\n  color: #333333;\n}\n.acf-table > thead > tr > th {\n  border-color: #d5d9dd;\n  border-width: 0 0 1px 1px;\n}\n.acf-table > thead > tr > th:first-child {\n  border-left-width: 0;\n}\n.acf-table > tbody > tr {\n  z-index: 1;\n}\n.acf-table > tbody > tr > td {\n  border-color: #eeeeee;\n  border-width: 1px 0 0 1px;\n}\n.acf-table > tbody > tr > td:first-child {\n  border-left-width: 0;\n}\n.acf-table > tbody > tr:first-child > td {\n  border-top-width: 0;\n}\n.acf-table.-clear {\n  border: 0 none;\n}\n.acf-table.-clear > tbody > tr > td,\n.acf-table.-clear > tbody > tr > th,\n.acf-table.-clear > thead > tr > td,\n.acf-table.-clear > thead > tr > th {\n  border: 0 none;\n  padding: 4px;\n}\n\n/* remove tr */\n.acf-remove-element {\n  -webkit-transition: all 0.25s ease-out;\n  -moz-transition: all 0.25s ease-out;\n  -o-transition: all 0.25s ease-out;\n  transition: all 0.25s ease-out;\n  transform: translate(50px, 0);\n  opacity: 0;\n}\n\n/* fade-up */\n.acf-fade-up {\n  -webkit-transition: all 0.25s ease-out;\n  -moz-transition: all 0.25s ease-out;\n  -o-transition: all 0.25s ease-out;\n  transition: all 0.25s ease-out;\n  transform: translate(0, -10px);\n  opacity: 0;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Fake table\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-thead,\n.acf-tbody,\n.acf-tfoot {\n  width: 100%;\n  padding: 0;\n  margin: 0;\n}\n.acf-thead > li,\n.acf-tbody > li,\n.acf-tfoot > li {\n  box-sizing: border-box;\n  padding-top: 14px;\n  font-size: 12px;\n  line-height: 14px;\n}\n\n.acf-thead {\n  border-bottom: #ccd0d4 solid 1px;\n  color: #23282d;\n}\n.acf-thead > li {\n  font-size: 14px;\n  line-height: 1.4;\n  font-weight: bold;\n}\n.acf-admin-3-8 .acf-thead {\n  border-color: #dfdfdf;\n}\n\n.acf-tfoot {\n  background: #f5f5f5;\n  border-top: #d5d9dd solid 1px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tSettings\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-settings-wrap #poststuff {\n  padding-top: 15px;\n}\n.acf-settings-wrap .acf-box {\n  margin: 20px 0;\n}\n.acf-settings-wrap table {\n  margin: 0;\n}\n.acf-settings-wrap table .button {\n  vertical-align: middle;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-popup\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-popup {\n  position: fixed;\n  z-index: 900000;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  text-align: center;\n}\n#acf-popup .bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 0;\n  background: rgba(0, 0, 0, 0.25);\n}\n#acf-popup:before {\n  content: \"\";\n  display: inline-block;\n  height: 100%;\n  vertical-align: middle;\n}\n#acf-popup .acf-popup-box {\n  display: inline-block;\n  vertical-align: middle;\n  z-index: 1;\n  min-width: 300px;\n  min-height: 160px;\n  border-color: #aaaaaa;\n  box-shadow: 0 5px 30px -5px rgba(0, 0, 0, 0.25);\n  text-align: left;\n}\nhtml[dir=rtl] #acf-popup .acf-popup-box {\n  text-align: right;\n}\n#acf-popup .acf-popup-box .title {\n  min-height: 15px;\n  line-height: 15px;\n}\n#acf-popup .acf-popup-box .title .acf-icon {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n}\nhtml[dir=rtl] #acf-popup .acf-popup-box .title .acf-icon {\n  right: auto;\n  left: 10px;\n}\n#acf-popup .acf-popup-box .inner {\n  min-height: 50px;\n  padding: 0;\n  margin: 15px;\n}\n#acf-popup .acf-popup-box .loading {\n  position: absolute;\n  top: 45px;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 2;\n  background: rgba(0, 0, 0, 0.1);\n  display: none;\n}\n#acf-popup .acf-popup-box .loading i {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.acf-submit {\n  margin-bottom: 0;\n  line-height: 28px;\n}\n.acf-submit span {\n  float: right;\n  color: #999;\n}\n.acf-submit span.-error {\n  color: #dd4232;\n}\n.acf-submit .button {\n  margin-right: 5px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tupgrade notice\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-upgrade-notice {\n  position: relative;\n  background: #fff;\n  padding: 20px;\n}\n#acf-upgrade-notice:after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n#acf-upgrade-notice .col-content {\n  float: left;\n  width: 55%;\n  padding-left: 90px;\n}\n#acf-upgrade-notice .notice-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  align-content: flex-start;\n}\n#acf-upgrade-notice .col-actions {\n  float: right;\n  text-align: center;\n}\n#acf-upgrade-notice img {\n  float: left;\n  width: 64px;\n  height: 64px;\n  margin: 0 0 0 -90px;\n}\n#acf-upgrade-notice h2 {\n  display: inline-block;\n  font-size: 16px;\n  margin: 2px 0 6.5px;\n}\n#acf-upgrade-notice p {\n  padding: 0;\n  margin: 0;\n}\n#acf-upgrade-notice .button:before {\n  margin-top: 11px;\n}\n@media screen and (max-width: 640px) {\n  #acf-upgrade-notice .col-content,\n  #acf-upgrade-notice .col-actions {\n    float: none;\n    padding-left: 90px;\n    width: auto;\n    text-align: left;\n  }\n}\n\n#acf-upgrade-notice:has(.notice-container)::before,\n#acf-upgrade-notice:has(.notice-container)::after {\n  display: none;\n}\n\n#acf-upgrade-notice:has(.notice-container) {\n  padding-left: 20px !important;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tWelcome\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-wrap h1 {\n  margin-top: 0;\n  padding-top: 20px;\n}\n.acf-wrap .about-text {\n  margin-top: 0.5em;\n  min-height: 50px;\n}\n.acf-wrap .about-headline-callout {\n  font-size: 2.4em;\n  font-weight: 300;\n  line-height: 1.3;\n  margin: 1.1em 0 0.2em;\n  text-align: center;\n}\n.acf-wrap .feature-section {\n  padding: 40px 0;\n}\n.acf-wrap .feature-section h2 {\n  margin-top: 20px;\n}\n.acf-wrap .changelog {\n  list-style: disc;\n  padding-left: 15px;\n}\n.acf-wrap .changelog li {\n  margin: 0 0 0.75em;\n}\n.acf-wrap .acf-three-col {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n.acf-wrap .acf-three-col > div {\n  flex: 1;\n  align-self: flex-start;\n  min-width: 31%;\n  max-width: 31%;\n}\n@media screen and (max-width: 880px) {\n  .acf-wrap .acf-three-col > div {\n    min-width: 48%;\n  }\n}\n@media screen and (max-width: 640px) {\n  .acf-wrap .acf-three-col > div {\n    min-width: 100%;\n  }\n}\n.acf-wrap .acf-three-col h3 .badge {\n  display: inline-block;\n  vertical-align: top;\n  border-radius: 5px;\n  background: #fc9700;\n  color: #fff;\n  font-weight: normal;\n  font-size: 12px;\n  padding: 2px 5px;\n}\n.acf-wrap .acf-three-col img + h3 {\n  margin-top: 0.5em;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-hl cols\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-hl[data-cols] {\n  margin-left: -10px;\n  margin-right: -10px;\n}\n.acf-hl[data-cols] > li {\n  padding: 0 6px 0 10px;\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n\n/* sizes */\n.acf-hl[data-cols=\"2\"] > li {\n  width: 50%;\n}\n\n.acf-hl[data-cols=\"3\"] > li {\n  width: 33.333%;\n}\n\n.acf-hl[data-cols=\"4\"] > li {\n  width: 25%;\n}\n\n/* mobile */\n@media screen and (max-width: 640px) {\n  .acf-hl[data-cols] {\n    flex-wrap: wrap;\n    justify-content: flex-start;\n    align-content: flex-start;\n    align-items: flex-start;\n    margin-left: 0;\n    margin-right: 0;\n    margin-top: -10px;\n  }\n  .acf-hl[data-cols] > li {\n    flex: 1 1 100%;\n    width: 100% !important;\n    padding: 10px 0 0;\n  }\n}\n/*--------------------------------------------------------------------------------------------\n*\n*\tmisc\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-actions {\n  text-align: right;\n  z-index: 1;\n  /* hover */\n  /* rtl */\n}\n.acf-actions.-hover {\n  position: absolute;\n  display: none;\n  top: 0;\n  right: 0;\n  padding: 5px;\n  z-index: 1050;\n}\nhtml[dir=rtl] .acf-actions.-hover {\n  right: auto;\n  left: 0;\n}\n\n/* ul compatibility */\nul.acf-actions li {\n  float: right;\n  margin-left: 4px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRTL\n*\n*--------------------------------------------------------------------------------------------*/\nhtml[dir=rtl] .acf-fl {\n  float: right;\n}\n\nhtml[dir=rtl] .acf-fr {\n  float: left;\n}\n\nhtml[dir=rtl] .acf-hl > li {\n  float: right;\n}\n\nhtml[dir=rtl] .acf-hl > li.acf-fr {\n  float: left;\n}\n\nhtml[dir=rtl] .acf-icon.logo {\n  left: 0;\n  right: auto;\n}\n\nhtml[dir=rtl] .acf-table thead th {\n  text-align: right;\n  border-right-width: 1px;\n  border-left-width: 0px;\n}\n\nhtml[dir=rtl] .acf-table > tbody > tr > td {\n  text-align: right;\n  border-right-width: 1px;\n  border-left-width: 0px;\n}\n\nhtml[dir=rtl] .acf-table > thead > tr > th:first-child,\nhtml[dir=rtl] .acf-table > tbody > tr > td:first-child {\n  border-right-width: 0;\n}\n\nhtml[dir=rtl] .acf-table > tbody > tr > td.order + td {\n  border-right-color: #e1e1e1;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  acf-postbox-columns\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-postbox-columns {\n  position: relative;\n  margin-top: -11px;\n  margin-bottom: -12px;\n  margin-left: -12px;\n  margin-right: 268px;\n}\n.acf-postbox-columns:after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n.acf-postbox-columns .acf-postbox-main,\n.acf-postbox-columns .acf-postbox-side {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n  padding: 0 12px 12px;\n}\n.acf-postbox-columns .acf-postbox-main {\n  float: left;\n  width: 100%;\n}\n.acf-postbox-columns .acf-postbox-side {\n  float: right;\n  width: 280px;\n  margin-right: -280px;\n}\n.acf-postbox-columns .acf-postbox-side:before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: 1px;\n  height: 100%;\n  top: 0;\n  right: 0;\n  background: #d5d9dd;\n}\n.acf-admin-3-8 .acf-postbox-columns .acf-postbox-side:before {\n  background: #dfdfdf;\n}\n\n/* mobile */\n@media only screen and (max-width: 850px) {\n  .acf-postbox-columns {\n    margin: 0;\n  }\n  .acf-postbox-columns .acf-postbox-main,\n  .acf-postbox-columns .acf-postbox-side {\n    float: none;\n    width: auto;\n    margin: 0;\n    padding: 0;\n  }\n  .acf-postbox-columns .acf-postbox-side {\n    margin-top: 1em;\n  }\n  .acf-postbox-columns .acf-postbox-side:before {\n    display: none;\n  }\n}\n/*---------------------------------------------------------------------------------------------\n*\n*  acf-panel\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-panel {\n  margin-top: -1px;\n  border-top: 1px solid #d5d9dd;\n  border-bottom: 1px solid #d5d9dd;\n  /* open */\n  /* inside postbox */\n  /* fields */\n}\n.acf-panel .acf-panel-title {\n  margin: 0;\n  padding: 12px;\n  font-weight: bold;\n  cursor: pointer;\n  font-size: inherit;\n}\n.acf-panel .acf-panel-title i {\n  float: right;\n}\n.acf-panel .acf-panel-inside {\n  margin: 0;\n  padding: 0 12px 12px;\n  display: none;\n}\n.acf-panel.-open .acf-panel-inside {\n  display: block;\n}\n.postbox .acf-panel {\n  margin-left: -12px;\n  margin-right: -12px;\n}\n.acf-panel .acf-field {\n  margin: 20px 0 0;\n}\n.acf-panel .acf-field .acf-label label {\n  color: #555d66;\n  font-weight: normal;\n}\n.acf-panel .acf-field:first-child {\n  margin-top: 0;\n}\n.acf-admin-3-8 .acf-panel {\n  border-color: #dfdfdf;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Tools\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-admin-tools .notice {\n  margin-top: 10px;\n}\n#acf-admin-tools .acf-meta-box-wrap {\n  /* acf-fields */\n}\n#acf-admin-tools .acf-meta-box-wrap .inside {\n  border-top: none;\n}\n#acf-admin-tools .acf-meta-box-wrap .acf-fields {\n  margin-bottom: 24px;\n  border: none;\n  background: #fff;\n  border-radius: 0;\n}\n#acf-admin-tools .acf-meta-box-wrap .acf-fields .acf-field {\n  padding: 0;\n  margin-bottom: 19px;\n  border-top: none;\n}\n#acf-admin-tools .acf-meta-box-wrap .acf-fields .acf-label {\n  margin-bottom: 16px;\n}\n#acf-admin-tools .acf-meta-box-wrap .acf-fields .acf-input {\n  padding-top: 16px;\n  padding-right: 16px;\n  padding-bottom: 16px;\n  padding-left: 16px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #D0D5DD;\n  border-radius: 6px;\n}\n#acf-admin-tools .acf-meta-box-wrap .acf-fields.import-cptui {\n  margin-top: 19px;\n}\n\n.acf-meta-box-wrap .postbox {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.acf-meta-box-wrap .postbox .inside {\n  margin-bottom: 0;\n}\n.acf-meta-box-wrap .postbox .hndle {\n  font-size: 14px;\n  padding: 8px 12px;\n  margin: 0;\n  line-height: 1.4;\n  position: relative;\n  z-index: 1;\n  cursor: default;\n}\n.acf-meta-box-wrap .postbox .handlediv,\n.acf-meta-box-wrap .postbox .handle-order-higher,\n.acf-meta-box-wrap .postbox .handle-order-lower {\n  display: none;\n}\n\n/* grid */\n.acf-meta-box-wrap.-grid {\n  margin-left: 8px;\n  margin-right: 8px;\n}\n.acf-meta-box-wrap.-grid .postbox {\n  float: left;\n  clear: left;\n  width: 50%;\n  margin: 0 0 16px;\n}\n.acf-meta-box-wrap.-grid .postbox:nth-child(odd) {\n  margin-left: -8px;\n}\n.acf-meta-box-wrap.-grid .postbox:nth-child(even) {\n  float: right;\n  clear: right;\n  margin-right: -8px;\n}\n\n/* mobile */\n@media only screen and (max-width: 850px) {\n  .acf-meta-box-wrap.-grid {\n    margin-left: 0;\n    margin-right: 0;\n  }\n  .acf-meta-box-wrap.-grid .postbox {\n    margin-left: 0 !important;\n    margin-right: 0 !important;\n    width: 100%;\n  }\n}\n/* export tool */\n#acf-admin-tool-export {\n  /* panel: selection */\n}\n#acf-admin-tool-export p {\n  max-width: 800px;\n}\n#acf-admin-tool-export ul {\n  display: flex;\n  flex-wrap: wrap;\n  width: 100%;\n}\n#acf-admin-tool-export ul li {\n  flex: 0 1 33.33%;\n}\n@media screen and (max-width: 1600px) {\n  #acf-admin-tool-export ul li {\n    flex: 0 1 50%;\n  }\n}\n@media screen and (max-width: 1200px) {\n  #acf-admin-tool-export ul li {\n    flex: 0 1 100%;\n  }\n}\n#acf-admin-tool-export .acf-postbox-side ul {\n  display: block;\n}\n#acf-admin-tool-export .acf-postbox-side .button {\n  margin: 0;\n  width: 100%;\n}\n#acf-admin-tool-export textarea {\n  display: block;\n  width: 100%;\n  min-height: 500px;\n  background: #F9FAFB;\n  border-color: #D0D5DD;\n  box-shadow: none;\n  padding: 7px;\n  border-radius: 6px;\n}\n#acf-admin-tool-export .acf-panel-selection .acf-label label {\n  font-weight: bold;\n  color: #344054;\n}\n\n#acf-admin-tool-import ul {\n  column-width: 200px;\n}\n\n.acf-css-tooltip {\n  position: relative;\n}\n.acf-css-tooltip:before {\n  content: attr(aria-label);\n  display: none;\n  position: absolute;\n  z-index: 999;\n  bottom: 100%;\n  left: 50%;\n  transform: translate(-50%, -8px);\n  background: #191e23;\n  border-radius: 2px;\n  padding: 5px 10px;\n  color: #fff;\n  font-size: 12px;\n  line-height: 1.4em;\n  white-space: pre;\n}\n.acf-css-tooltip:after {\n  content: \"\";\n  display: none;\n  position: absolute;\n  z-index: 998;\n  bottom: 100%;\n  left: 50%;\n  transform: translate(-50%, 4px);\n  border: solid 6px transparent;\n  border-top-color: #191e23;\n}\n.acf-css-tooltip:hover:before, .acf-css-tooltip:hover:after, .acf-css-tooltip:focus:before, .acf-css-tooltip:focus:after {\n  display: block;\n}\n\n.acf-diff .acf-diff-title {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 40px;\n  padding: 14px 16px;\n  background: #f3f3f3;\n  border-bottom: #dddddd solid 1px;\n}\n.acf-diff .acf-diff-title strong {\n  font-size: 14px;\n  display: block;\n}\n.acf-diff .acf-diff-title .acf-diff-title-left,\n.acf-diff .acf-diff-title .acf-diff-title-right {\n  width: 50%;\n  float: left;\n}\n.acf-diff .acf-diff-content {\n  position: absolute;\n  top: 70px;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  overflow: auto;\n}\n.acf-diff table.diff {\n  border-spacing: 0;\n}\n.acf-diff table.diff col.diffsplit.middle {\n  width: 0;\n}\n.acf-diff table.diff td,\n.acf-diff table.diff th {\n  padding-top: 0.25em;\n  padding-bottom: 0.25em;\n}\n.acf-diff table.diff tr td:nth-child(2) {\n  width: auto;\n}\n.acf-diff table.diff td:nth-child(3) {\n  border-left: #dddddd solid 1px;\n}\n@media screen and (max-width: 600px) {\n  .acf-diff .acf-diff-title {\n    height: 70px;\n  }\n  .acf-diff .acf-diff-content {\n    top: 100px;\n  }\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Modal\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-modal {\n  position: fixed;\n  top: 30px;\n  left: 30px;\n  right: 30px;\n  bottom: 30px;\n  z-index: 160000;\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.7);\n  background: #fcfcfc;\n}\n.acf-modal .acf-modal-title,\n.acf-modal .acf-modal-content,\n.acf-modal .acf-modal-toolbar {\n  box-sizing: border-box;\n  position: absolute;\n  left: 0;\n  right: 0;\n}\n.acf-modal .acf-modal-title {\n  height: 50px;\n  top: 0;\n  border-bottom: 1px solid #ddd;\n}\n.acf-modal .acf-modal-title h2 {\n  margin: 0;\n  padding: 0 16px;\n  line-height: 50px;\n}\n.acf-modal .acf-modal-title .acf-modal-close {\n  position: absolute;\n  top: 0;\n  right: 0;\n  height: 50px;\n  width: 50px;\n  border: none;\n  border-left: 1px solid #ddd;\n  background: transparent;\n  cursor: pointer;\n  color: #666;\n}\n.acf-modal .acf-modal-title .acf-modal-close:hover {\n  color: #00a0d2;\n}\n.acf-modal .acf-modal-content {\n  top: 50px;\n  bottom: 60px;\n  background: #fff;\n  overflow: auto;\n  padding: 16px;\n}\n.acf-modal .acf-modal-feedback {\n  position: absolute;\n  top: 50%;\n  margin: -10px 0;\n  left: 0;\n  right: 0;\n  text-align: center;\n  opacity: 0.75;\n}\n.acf-modal .acf-modal-feedback.error {\n  opacity: 1;\n  color: #b52727;\n}\n.acf-modal .acf-modal-toolbar {\n  height: 60px;\n  bottom: 0;\n  padding: 15px 16px;\n  border-top: 1px solid #ddd;\n}\n.acf-modal .acf-modal-toolbar .button {\n  float: right;\n}\n@media only screen and (max-width: 640px) {\n  .acf-modal {\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n  }\n}\n\n.acf-modal-backdrop {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: #101828;\n  opacity: 0.8;\n  z-index: 159900;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Retina\n*\n*---------------------------------------------------------------------------------------------*/\n@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min--moz-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 2/1), only screen and (min-device-pixel-ratio: 2), only screen and (min-resolution: 192dpi), only screen and (min-resolution: 2dppx) {\n  .acf-loading,\n  .acf-spinner {\n    background-image: url(../../images/<EMAIL>);\n    background-size: 20px 20px;\n  }\n}\n/*--------------------------------------------------------------------------------------------\n*\n*  Wrap\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-page .wrap {\n  margin-top: 48px;\n  margin-right: 32px;\n  margin-bottom: 0;\n  margin-left: 12px;\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page .wrap {\n    margin-right: 8px;\n    margin-left: 8px;\n  }\n}\n.acf-admin-page.rtl .wrap {\n  margin-right: 12px;\n  margin-left: 32px;\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page.rtl .wrap {\n    margin-right: 8px;\n    margin-left: 8px;\n  }\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page #wpcontent {\n    padding-left: 0;\n  }\n}\n\n/*-------------------------------------------------------------------\n*\n*  ACF Admin Page Footer Styles\n*\n*------------------------------------------------------------------*/\n.acf-admin-page #wpfooter {\n  font-style: italic;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Postbox & ACF Postbox\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page .postbox,\n.acf-admin-page .acf-box {\n  border: none;\n  border-radius: 8px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.acf-admin-page .postbox .inside,\n.acf-admin-page .acf-box .inside {\n  padding-top: 24px;\n  padding-right: 24px;\n  padding-bottom: 24px;\n  padding-left: 24px;\n}\n.acf-admin-page .postbox .acf-postbox-inner,\n.acf-admin-page .acf-box .acf-postbox-inner {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 24px;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n.acf-admin-page .postbox .inner,\n.acf-admin-page .postbox .inside,\n.acf-admin-page .acf-box .inner,\n.acf-admin-page .acf-box .inside {\n  margin-top: 0 !important;\n  margin-right: 0 !important;\n  margin-bottom: 0 !important;\n  margin-left: 0 !important;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.acf-admin-page .postbox .postbox-header,\n.acf-admin-page .postbox .title,\n.acf-admin-page .acf-box .postbox-header,\n.acf-admin-page .acf-box .title {\n  display: flex;\n  align-items: center;\n  box-sizing: border-box;\n  min-height: 64px;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 24px;\n  padding-bottom: 0;\n  padding-left: 24px;\n  border-bottom-width: 0;\n  border-bottom-style: none;\n}\n.acf-admin-page .postbox .postbox-header h2,\n.acf-admin-page .postbox .postbox-header h3,\n.acf-admin-page .postbox .title h2,\n.acf-admin-page .postbox .title h3,\n.acf-admin-page .acf-box .postbox-header h2,\n.acf-admin-page .acf-box .postbox-header h3,\n.acf-admin-page .acf-box .title h2,\n.acf-admin-page .acf-box .title h3 {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n  color: #344054;\n}\n.acf-admin-page .postbox .hndle,\n.acf-admin-page .acf-box .hndle {\n  padding-top: 0;\n  padding-right: 24px;\n  padding-bottom: 0;\n  padding-left: 24px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Custom ACF postbox header\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-postbox-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  box-sizing: border-box;\n  min-height: 64px;\n  margin-top: -24px;\n  margin-right: -24px;\n  margin-bottom: 0;\n  margin-left: -24px;\n  padding-top: 0;\n  padding-right: 24px;\n  padding-bottom: 0;\n  padding-left: 24px;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n.acf-postbox-header h2.acf-postbox-title {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 24px;\n  padding-bottom: 0;\n  padding-left: 0;\n  color: #344054;\n}\n.rtl .acf-postbox-header h2.acf-postbox-title {\n  padding-right: 0;\n  padding-left: 24px;\n}\n.acf-postbox-header .acf-icon {\n  background-color: #98A2B3;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Screen options button & screen meta container\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page #screen-meta-links {\n  margin-right: 32px;\n}\n.acf-admin-page #screen-meta-links .show-settings {\n  border-color: #D0D5DD;\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page #screen-meta-links {\n    margin-right: 16px;\n    margin-bottom: 0;\n  }\n}\n.acf-admin-page.rtl #screen-meta-links {\n  margin-right: 0;\n  margin-left: 32px;\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page.rtl #screen-meta-links {\n    margin-right: 0;\n    margin-left: 16px;\n  }\n}\n.acf-admin-page #screen-meta {\n  border-color: #D0D5DD;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Postbox headings\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page #poststuff .postbox-header h2,\n.acf-admin-page #poststuff .postbox-header h3 {\n  justify-content: flex-start;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n  color: #344054 !important;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Postbox drag state\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page.is-dragging-metaboxes .metabox-holder .postbox-container .meta-box-sortables {\n  box-sizing: border-box;\n  padding: 2px;\n  outline: none;\n  background-image: repeating-linear-gradient(0deg, #667085, #667085 5px, transparent 5px, transparent 10px, #667085 10px), repeating-linear-gradient(90deg, #667085, #667085 5px, transparent 5px, transparent 10px, #667085 10px), repeating-linear-gradient(180deg, #667085, #667085 5px, transparent 5px, transparent 10px, #667085 10px), repeating-linear-gradient(270deg, #667085, #667085 5px, transparent 5px, transparent 10px, #667085 10px);\n  background-size: 1.5px 100%, 100% 1.5px, 1.5px 100%, 100% 1.5px;\n  background-position: 0 0, 0 0, 100% 0, 0 100%;\n  background-repeat: no-repeat;\n  border-radius: 8px;\n}\n.acf-admin-page .ui-sortable-placeholder {\n  border: none;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Search summary\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-page .subtitle {\n  display: inline-flex;\n  align-items: center;\n  height: 24px;\n  margin: 0;\n  padding-top: 4px;\n  padding-right: 12px;\n  padding-bottom: 4px;\n  padding-left: 12px;\n  background-color: #EBF5FA;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #A5D2E7;\n  border-radius: 6px;\n}\n.acf-admin-page .subtitle strong {\n  margin-left: 5px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Action strip\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-actions-strip {\n  display: flex;\n}\n.acf-actions-strip .acf-btn {\n  margin-right: 8px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Notices\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-page .acf-notice,\n.acf-admin-page .notice,\n.acf-admin-page #lost-connection-notice {\n  position: relative;\n  box-sizing: border-box;\n  min-height: 48px;\n  margin-top: 0 !important;\n  margin-right: 0 !important;\n  margin-bottom: 16px !important;\n  margin-left: 0 !important;\n  padding-top: 13px !important;\n  padding-right: 16px !important;\n  padding-bottom: 12px !important;\n  padding-left: 50px !important;\n  background-color: #e7eff9;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #9dbaee;\n  border-radius: 8px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  color: #344054;\n}\n.acf-admin-page .acf-notice.update-nag,\n.acf-admin-page .notice.update-nag,\n.acf-admin-page #lost-connection-notice.update-nag {\n  display: block;\n  position: relative;\n  width: calc(100% - 44px);\n  margin-top: 48px !important;\n  margin-right: 44px !important;\n  margin-bottom: -32px !important;\n  margin-left: 12px !important;\n}\n.acf-admin-page .acf-notice .button,\n.acf-admin-page .notice .button,\n.acf-admin-page #lost-connection-notice .button {\n  height: auto;\n  margin-left: 8px;\n  padding: 0;\n  border: none;\n}\n.acf-admin-page .acf-notice > div,\n.acf-admin-page .notice > div,\n.acf-admin-page #lost-connection-notice > div {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.acf-admin-page .acf-notice p,\n.acf-admin-page .notice p,\n.acf-admin-page #lost-connection-notice p {\n  flex: 1 0 auto;\n  max-width: 100%;\n  line-height: 18px;\n  margin: 0;\n  padding: 0;\n}\n.acf-admin-page .acf-notice p.help,\n.acf-admin-page .notice p.help,\n.acf-admin-page #lost-connection-notice p.help {\n  margin-top: 0;\n  padding-top: 0;\n  color: rgba(52, 64, 84, 0.7);\n}\n.acf-admin-page .acf-notice .acf-notice-dismiss,\n.acf-admin-page .acf-notice .notice-dismiss,\n.acf-admin-page .notice .acf-notice-dismiss,\n.acf-admin-page .notice .notice-dismiss,\n.acf-admin-page #lost-connection-notice .acf-notice-dismiss,\n.acf-admin-page #lost-connection-notice .notice-dismiss {\n  position: absolute;\n  top: 4px;\n  right: 8px;\n  padding: 9px;\n  border: none;\n}\n.acf-admin-page .acf-notice .acf-notice-dismiss:before,\n.acf-admin-page .acf-notice .notice-dismiss:before,\n.acf-admin-page .notice .acf-notice-dismiss:before,\n.acf-admin-page .notice .notice-dismiss:before,\n.acf-admin-page #lost-connection-notice .acf-notice-dismiss:before,\n.acf-admin-page #lost-connection-notice .notice-dismiss:before {\n  content: \"\";\n  display: block;\n  position: relative;\n  z-index: 600;\n  width: 20px;\n  height: 20px;\n  background-color: #667085;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-close.svg\");\n  mask-image: url(\"../../images/icons/icon-close.svg\");\n}\n.acf-admin-page .acf-notice .acf-notice-dismiss:hover::before,\n.acf-admin-page .acf-notice .notice-dismiss:hover::before,\n.acf-admin-page .notice .acf-notice-dismiss:hover::before,\n.acf-admin-page .notice .notice-dismiss:hover::before,\n.acf-admin-page #lost-connection-notice .acf-notice-dismiss:hover::before,\n.acf-admin-page #lost-connection-notice .notice-dismiss:hover::before {\n  background-color: #344054;\n}\n.acf-admin-page .acf-notice a.acf-notice-dismiss,\n.acf-admin-page .notice a.acf-notice-dismiss,\n.acf-admin-page #lost-connection-notice a.acf-notice-dismiss {\n  position: absolute;\n  top: 5px;\n  right: 24px;\n}\n.acf-admin-page .acf-notice a.acf-notice-dismiss:before,\n.acf-admin-page .notice a.acf-notice-dismiss:before,\n.acf-admin-page #lost-connection-notice a.acf-notice-dismiss:before {\n  background-color: #475467;\n}\n.acf-admin-page .acf-notice:before,\n.acf-admin-page .notice:before,\n.acf-admin-page #lost-connection-notice:before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 15px;\n  left: 18px;\n  z-index: 600;\n  width: 16px;\n  height: 16px;\n  margin-right: 8px;\n  background-color: #fff;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-info-solid.svg\");\n  mask-image: url(\"../../images/icons/icon-info-solid.svg\");\n}\n.acf-admin-page .acf-notice:after,\n.acf-admin-page .notice:after,\n.acf-admin-page #lost-connection-notice:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 9px;\n  left: 12px;\n  z-index: 500;\n  width: 28px;\n  height: 28px;\n  background-color: #2D69DA;\n  border-radius: 6px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.acf-admin-page .acf-notice .local-restore,\n.acf-admin-page .notice .local-restore,\n.acf-admin-page #lost-connection-notice .local-restore {\n  align-items: center;\n  margin-top: -6px;\n  margin-bottom: 0;\n}\n.acf-admin-page .notice[data-persisted=true] {\n  display: none;\n}\n.acf-admin-page .notice.is-dismissible {\n  padding-right: 56px;\n}\n.acf-admin-page .notice.notice-success {\n  background-color: #edf7ef;\n  border-color: #b6deb9;\n}\n.acf-admin-page .notice.notice-success:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n  mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n}\n.acf-admin-page .notice.notice-success:after {\n  background-color: #52AA59;\n}\n.acf-admin-page .acf-notice.acf-error-message,\n.acf-admin-page .notice.notice-error,\n.acf-admin-page #lost-connection-notice {\n  background-color: #f7eeeb;\n  border-color: #f1b6b3;\n}\n.acf-admin-page .acf-notice.acf-error-message:before,\n.acf-admin-page .notice.notice-error:before,\n.acf-admin-page #lost-connection-notice:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-warning.svg\");\n  mask-image: url(\"../../images/icons/icon-warning.svg\");\n}\n.acf-admin-page .acf-notice.acf-error-message:after,\n.acf-admin-page .notice.notice-error:after,\n.acf-admin-page #lost-connection-notice:after {\n  background-color: #D13737;\n}\n\n.acf-admin-single-taxonomy .notice-success .acf-item-saved-text,\n.acf-admin-single-post-type .notice-success .acf-item-saved-text,\n.acf-admin-single-options-page .notice-success .acf-item-saved-text {\n  font-weight: 600;\n}\n.acf-admin-single-taxonomy .notice-success .acf-item-saved-links,\n.acf-admin-single-post-type .notice-success .acf-item-saved-links,\n.acf-admin-single-options-page .notice-success .acf-item-saved-links {\n  display: flex;\n  gap: 12px;\n}\n.acf-admin-single-taxonomy .notice-success .acf-item-saved-links a,\n.acf-admin-single-post-type .notice-success .acf-item-saved-links a,\n.acf-admin-single-options-page .notice-success .acf-item-saved-links a {\n  text-decoration: none;\n  opacity: 1;\n}\n.acf-admin-single-taxonomy .notice-success .acf-item-saved-links a:after,\n.acf-admin-single-post-type .notice-success .acf-item-saved-links a:after,\n.acf-admin-single-options-page .notice-success .acf-item-saved-links a:after {\n  content: \"\";\n  width: 1px;\n  height: 13px;\n  display: inline-flex;\n  position: relative;\n  top: 2px;\n  left: 6px;\n  background-color: #475467;\n  opacity: 0.3;\n}\n.acf-admin-single-taxonomy .notice-success .acf-item-saved-links a:last-child:after,\n.acf-admin-single-post-type .notice-success .acf-item-saved-links a:last-child:after,\n.acf-admin-single-options-page .notice-success .acf-item-saved-links a:last-child:after {\n  content: none;\n}\n\n.rtl.acf-field-group .notice,\n.rtl.acf-internal-post-type .notice {\n  padding-right: 50px !important;\n}\n.rtl.acf-field-group .notice .notice-dismiss,\n.rtl.acf-internal-post-type .notice .notice-dismiss {\n  left: 8px;\n  right: unset;\n}\n.rtl.acf-field-group .notice:before,\n.rtl.acf-internal-post-type .notice:before {\n  left: unset;\n  right: 10px;\n}\n.rtl.acf-field-group .notice:after,\n.rtl.acf-internal-post-type .notice:after {\n  left: unset;\n  right: 12px;\n}\n.rtl.acf-field-group.acf-admin-single-taxonomy .notice-success .acf-item-saved-links a:after, .rtl.acf-field-group.acf-admin-single-post-type .notice-success .acf-item-saved-links a:after, .rtl.acf-field-group.acf-admin-single-options-page .notice-success .acf-item-saved-links a:after,\n.rtl.acf-internal-post-type.acf-admin-single-taxonomy .notice-success .acf-item-saved-links a:after,\n.rtl.acf-internal-post-type.acf-admin-single-post-type .notice-success .acf-item-saved-links a:after,\n.rtl.acf-internal-post-type.acf-admin-single-options-page .notice-success .acf-item-saved-links a:after {\n  left: unset;\n  right: 6px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  ACF PRO label\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-pro-label {\n  display: inline-flex;\n  align-items: center;\n  min-height: 22px;\n  padding-right: 8px;\n  padding-left: 8px;\n  background: linear-gradient(90.52deg, #3E8BFF 0.44%, #A45CFF 113.3%);\n  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.16);\n  border: none;\n  border-radius: 100px;\n  font-size: 11px;\n  text-transform: uppercase;\n  text-decoration: none;\n  color: #fff;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Inline notice overrides\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-page .acf-field .acf-notice {\n  display: flex;\n  align-items: center;\n  min-height: 40px !important;\n  margin-bottom: 6px !important;\n  padding-top: 6px !important;\n  padding-left: 40px !important;\n  padding-bottom: 6px !important;\n  margin: 0 0 15px;\n  background: #edf2ff;\n  color: #344054 !important;\n  border-color: #2183b9;\n  border-radius: 6px;\n}\n.acf-admin-page .acf-field .acf-notice:after {\n  top: 8px;\n  left: 8px;\n  width: 22px;\n  height: 22px;\n}\n.acf-admin-page .acf-field .acf-notice:before {\n  top: 12px;\n  left: 12px;\n  width: 14px;\n  height: 14px;\n}\n.acf-admin-page .acf-field .acf-notice.-error {\n  background: #f7eeeb;\n  border-color: #f1b6b3;\n}\n.acf-admin-page .acf-field .acf-notice.-success {\n  background: #edf7ef;\n  border-color: #b6deb9;\n}\n.acf-admin-page .acf-field .acf-notice.-warning {\n  background: #fdf8eb;\n  border-color: #f4dbb4;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page #wpcontent {\n  line-height: 140%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Links\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page a {\n  color: #0783BE;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headings\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-h1, .acf-admin-page #tmpl-acf-field-group-pro-features h1,\n.acf-admin-page #acf-field-group-pro-features h1, .acf-admin-page h1,\n.acf-headerbar h1 {\n  font-size: 21px;\n  font-weight: 400;\n}\n\n.acf-h2, .acf-no-field-groups-wrapper .acf-no-field-groups-inner h2,\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner h2,\n.acf-no-field-groups-wrapper .acf-no-post-types-inner h2,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner h2,\n.acf-no-taxonomies-wrapper .acf-no-field-groups-inner h2,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner h2,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner h2,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner h2,\n.acf-no-post-types-wrapper .acf-no-field-groups-inner h2,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner h2,\n.acf-no-post-types-wrapper .acf-no-post-types-inner h2,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner h2,\n.acf-no-options-pages-wrapper .acf-no-field-groups-inner h2,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner h2,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner h2,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner h2, .acf-page-title, .acf-admin-page h2,\n.acf-headerbar h2 {\n  font-size: 18px;\n  font-weight: 400;\n}\n\n.acf-h3, .acf-admin-page h3,\n.acf-headerbar h3, .acf-admin-page .postbox .postbox-header h2,\n.acf-admin-page .postbox .postbox-header h3,\n.acf-admin-page .postbox .title h2,\n.acf-admin-page .postbox .title h3,\n.acf-admin-page .acf-box .postbox-header h2,\n.acf-admin-page .acf-box .postbox-header h3,\n.acf-admin-page .acf-box .title h2,\n.acf-admin-page .acf-box .title h3, .acf-postbox-header h2.acf-postbox-title, .acf-admin-page #poststuff .postbox-header h2,\n.acf-admin-page #poststuff .postbox-header h3 {\n  font-size: 16px;\n  font-weight: 400;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Paragraphs\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page .p1 {\n  font-size: 15px;\n}\n.acf-admin-page .p2, .acf-admin-page .acf-no-field-groups-wrapper .acf-no-field-groups-inner p, .acf-no-field-groups-wrapper .acf-no-field-groups-inner .acf-admin-page p,\n.acf-admin-page .acf-no-field-groups-wrapper .acf-no-taxonomies-inner p,\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner .acf-admin-page p,\n.acf-admin-page .acf-no-field-groups-wrapper .acf-no-post-types-inner p,\n.acf-no-field-groups-wrapper .acf-no-post-types-inner .acf-admin-page p,\n.acf-admin-page .acf-no-field-groups-wrapper .acf-no-options-pages-inner p,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner .acf-admin-page p,\n.acf-admin-page .acf-no-taxonomies-wrapper .acf-no-field-groups-inner p,\n.acf-no-taxonomies-wrapper .acf-no-field-groups-inner .acf-admin-page p,\n.acf-admin-page .acf-no-taxonomies-wrapper .acf-no-taxonomies-inner p,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner .acf-admin-page p,\n.acf-admin-page .acf-no-taxonomies-wrapper .acf-no-post-types-inner p,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner .acf-admin-page p,\n.acf-admin-page .acf-no-taxonomies-wrapper .acf-no-options-pages-inner p,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner .acf-admin-page p,\n.acf-admin-page .acf-no-post-types-wrapper .acf-no-field-groups-inner p,\n.acf-no-post-types-wrapper .acf-no-field-groups-inner .acf-admin-page p,\n.acf-admin-page .acf-no-post-types-wrapper .acf-no-taxonomies-inner p,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner .acf-admin-page p,\n.acf-admin-page .acf-no-post-types-wrapper .acf-no-post-types-inner p,\n.acf-no-post-types-wrapper .acf-no-post-types-inner .acf-admin-page p,\n.acf-admin-page .acf-no-post-types-wrapper .acf-no-options-pages-inner p,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner .acf-admin-page p,\n.acf-admin-page .acf-no-options-pages-wrapper .acf-no-field-groups-inner p,\n.acf-no-options-pages-wrapper .acf-no-field-groups-inner .acf-admin-page p,\n.acf-admin-page .acf-no-options-pages-wrapper .acf-no-taxonomies-inner p,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner .acf-admin-page p,\n.acf-admin-page .acf-no-options-pages-wrapper .acf-no-post-types-inner p,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner .acf-admin-page p,\n.acf-admin-page .acf-no-options-pages-wrapper .acf-no-options-pages-inner p,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner .acf-admin-page p, .acf-admin-page #acf-admin-tools .acf-meta-box-wrap .acf-fields .acf-label, #acf-admin-tools .acf-meta-box-wrap .acf-fields .acf-admin-page .acf-label {\n  font-size: 14px;\n}\n.acf-admin-page .p3, .acf-admin-page .acf-internal-post-type .wp-list-table .post-state, .acf-internal-post-type .wp-list-table .acf-admin-page .post-state, .acf-admin-page .subtitle {\n  font-size: 13.5px;\n}\n.acf-admin-page .p4, .acf-admin-page .acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn p, .acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn .acf-admin-page p, .acf-admin-page #acf-update-information .form-table th, #acf-update-information .form-table .acf-admin-page th,\n.acf-admin-page #acf-update-information .form-table td,\n#acf-update-information .form-table .acf-admin-page td, .acf-admin-page #acf-admin-tools.tool-export .acf-panel h3, #acf-admin-tools.tool-export .acf-panel .acf-admin-page h3, .acf-admin-page .acf-btn.acf-btn-sm, .acf-admin-page .acf-admin-toolbar .acf-tab, .acf-admin-toolbar .acf-admin-page .acf-tab, .acf-admin-page .acf-internal-post-type .subsubsub li, .acf-internal-post-type .subsubsub .acf-admin-page li, .acf-admin-page .acf-internal-post-type .wp-list-table tbody th, .acf-internal-post-type .wp-list-table tbody .acf-admin-page th,\n.acf-admin-page .acf-internal-post-type .wp-list-table tbody td,\n.acf-internal-post-type .wp-list-table tbody .acf-admin-page td, .acf-admin-page .acf-internal-post-type .wp-list-table thead th, .acf-internal-post-type .wp-list-table thead .acf-admin-page th, .acf-admin-page .acf-internal-post-type .wp-list-table thead td, .acf-internal-post-type .wp-list-table thead .acf-admin-page td,\n.acf-admin-page .acf-internal-post-type .wp-list-table tfoot th,\n.acf-internal-post-type .wp-list-table tfoot .acf-admin-page th, .acf-admin-page .acf-internal-post-type .wp-list-table tfoot td, .acf-internal-post-type .wp-list-table tfoot .acf-admin-page td, .acf-admin-page .acf-input .select2-container.-acf .select2-selection__rendered, .acf-admin-page .button, .acf-admin-page input[type=text],\n.acf-admin-page input[type=search],\n.acf-admin-page input[type=number],\n.acf-admin-page textarea,\n.acf-admin-page select {\n  font-size: 13px;\n}\n.acf-admin-page .p5, .acf-admin-page .acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab .acf-field-type .field-type-label, .acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab .acf-field-type .acf-admin-page .field-type-label,\n.acf-admin-page .acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-type-search-results .acf-field-type .field-type-label,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-type-search-results .acf-field-type .acf-admin-page .field-type-label, .acf-admin-page .acf-internal-post-type .row-actions, .acf-internal-post-type .acf-admin-page .row-actions, .acf-admin-page .acf-notice .button,\n.acf-admin-page .notice .button,\n.acf-admin-page #lost-connection-notice .button {\n  font-size: 12.5px;\n}\n.acf-admin-page .p6, .acf-admin-page #acf-update-information .acf-update-changelog p em, #acf-update-information .acf-update-changelog p .acf-admin-page em, .acf-admin-page .acf-no-field-groups-wrapper .acf-no-field-groups-inner p.acf-small, .acf-no-field-groups-wrapper .acf-no-field-groups-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-field-groups-wrapper .acf-no-taxonomies-inner p.acf-small,\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-field-groups-wrapper .acf-no-post-types-inner p.acf-small,\n.acf-no-field-groups-wrapper .acf-no-post-types-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-field-groups-wrapper .acf-no-options-pages-inner p.acf-small,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-taxonomies-wrapper .acf-no-field-groups-inner p.acf-small,\n.acf-no-taxonomies-wrapper .acf-no-field-groups-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-taxonomies-wrapper .acf-no-taxonomies-inner p.acf-small,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-taxonomies-wrapper .acf-no-post-types-inner p.acf-small,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-taxonomies-wrapper .acf-no-options-pages-inner p.acf-small,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-post-types-wrapper .acf-no-field-groups-inner p.acf-small,\n.acf-no-post-types-wrapper .acf-no-field-groups-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-post-types-wrapper .acf-no-taxonomies-inner p.acf-small,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-post-types-wrapper .acf-no-post-types-inner p.acf-small,\n.acf-no-post-types-wrapper .acf-no-post-types-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-post-types-wrapper .acf-no-options-pages-inner p.acf-small,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-options-pages-wrapper .acf-no-field-groups-inner p.acf-small,\n.acf-no-options-pages-wrapper .acf-no-field-groups-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-options-pages-wrapper .acf-no-taxonomies-inner p.acf-small,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-options-pages-wrapper .acf-no-post-types-inner p.acf-small,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner .acf-admin-page p.acf-small,\n.acf-admin-page .acf-no-options-pages-wrapper .acf-no-options-pages-inner p.acf-small,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner .acf-admin-page p.acf-small, .acf-admin-page .acf-internal-post-type .row-actions, .acf-internal-post-type .acf-admin-page .row-actions, .acf-admin-page .acf-small {\n  font-size: 12px;\n}\n.acf-admin-page .p7, .acf-admin-page .acf-tooltip, .acf-admin-page .acf-notice p.help,\n.acf-admin-page .notice p.help,\n.acf-admin-page #lost-connection-notice p.help {\n  font-size: 11.5px;\n}\n.acf-admin-page .p8 {\n  font-size: 11px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Page titles\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-page-title {\n  color: #344054;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide old / native WP titles from pages\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page .acf-settings-wrap h1 {\n  display: none !important;\n}\n.acf-admin-page #acf-admin-tools h1:not(.acf-field-group-pro-features-title, .acf-field-group-pro-features-title-sm) {\n  display: none !important;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small\n*\n*---------------------------------------------------------------------------------------------*/\n/*---------------------------------------------------------------------------------------------\n*\n*  Link focus style\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page a:focus {\n  box-shadow: none;\n  outline: none;\n}\n.acf-admin-page a:focus-visible {\n  box-shadow: 0 0 0 1px #4f94d4, 0 0 2px 1px rgba(79, 148, 212, 0.8);\n  outline: 1px solid transparent;\n}\n\n.acf-admin-page {\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  All Inputs\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Read only text inputs\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Number fields\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Textarea\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Select\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Radio Button & Checkbox base styling\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Radio Buttons\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Checkboxes\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Radio Buttons & Checkbox lists\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  ACF Switch\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  File input button\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Action Buttons\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Edit field group header\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Select2 inputs\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  ACF label\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Tooltip for field name field setting (result of a fix for keyboard navigation)\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /* Field Type Selection select2 */\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  RTL arrow position\n  *\n  *---------------------------------------------------------------------------------------------*/\n}\n.acf-admin-page input[type=text],\n.acf-admin-page input[type=search],\n.acf-admin-page input[type=number],\n.acf-admin-page textarea,\n.acf-admin-page select {\n  box-sizing: border-box;\n  height: 40px;\n  padding-right: 12px;\n  padding-left: 12px;\n  background-color: #fff;\n  border-color: #D0D5DD;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  border-radius: 6px;\n  color: #344054;\n}\n.acf-admin-page input[type=text]:focus,\n.acf-admin-page input[type=search]:focus,\n.acf-admin-page input[type=number]:focus,\n.acf-admin-page textarea:focus,\n.acf-admin-page select:focus {\n  outline: 3px solid #EBF5FA;\n  border-color: #399CCB;\n}\n.acf-admin-page input[type=text]:disabled,\n.acf-admin-page input[type=search]:disabled,\n.acf-admin-page input[type=number]:disabled,\n.acf-admin-page textarea:disabled,\n.acf-admin-page select:disabled {\n  background-color: #F9FAFB;\n  color: #808a9e;\n}\n.acf-admin-page input[type=text]::placeholder,\n.acf-admin-page input[type=search]::placeholder,\n.acf-admin-page input[type=number]::placeholder,\n.acf-admin-page textarea::placeholder,\n.acf-admin-page select::placeholder {\n  color: #98A2B3;\n}\n.acf-admin-page input[type=text]:read-only {\n  background-color: #F9FAFB;\n  color: #98A2B3;\n}\n.acf-admin-page .acf-field.acf-field-number .acf-label,\n.acf-admin-page .acf-field.acf-field-number .acf-input input[type=number] {\n  max-width: 180px;\n}\n.acf-admin-page textarea {\n  box-sizing: border-box;\n  padding-top: 10px;\n  padding-bottom: 10px;\n  height: 80px;\n  min-height: 56px;\n}\n.acf-admin-page select {\n  min-width: 160px;\n  max-width: 100%;\n  padding-right: 40px;\n  padding-left: 12px;\n  background-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  background-position: right 10px top 50%;\n  background-size: 20px;\n}\n.acf-admin-page select:hover, .acf-admin-page select:focus {\n  color: #0783BE;\n}\n.acf-admin-page select::before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 5px;\n  left: 5px;\n  width: 20px;\n  height: 20px;\n}\n.acf-admin-page.rtl select {\n  padding-right: 12px;\n  padding-left: 40px;\n  background-position: left 10px top 50%;\n}\n.acf-admin-page input[type=radio],\n.acf-admin-page input[type=checkbox] {\n  box-sizing: border-box;\n  width: 16px;\n  height: 16px;\n  padding: 0;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #98A2B3;\n  background: #fff;\n  box-shadow: none;\n}\n.acf-admin-page input[type=radio]:hover,\n.acf-admin-page input[type=checkbox]:hover {\n  background-color: #EBF5FA;\n  border-color: #0783BE;\n}\n.acf-admin-page input[type=radio]:checked, .acf-admin-page input[type=radio]:focus-visible,\n.acf-admin-page input[type=checkbox]:checked,\n.acf-admin-page input[type=checkbox]:focus-visible {\n  background-color: #EBF5FA;\n  border-color: #0783BE;\n}\n.acf-admin-page input[type=radio]:checked:before, .acf-admin-page input[type=radio]:focus-visible:before,\n.acf-admin-page input[type=checkbox]:checked:before,\n.acf-admin-page input[type=checkbox]:focus-visible:before {\n  content: \"\";\n  position: relative;\n  top: -1px;\n  left: -1px;\n  width: 16px;\n  height: 16px;\n  margin: 0;\n  padding: 0;\n  background-color: transparent;\n  background-size: cover;\n  background-repeat: no-repeat;\n  background-position: center;\n}\n.acf-admin-page input[type=radio]:active,\n.acf-admin-page input[type=checkbox]:active {\n  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 0px 0px rgba(255, 54, 54, 0.25);\n}\n.acf-admin-page input[type=radio]:disabled,\n.acf-admin-page input[type=checkbox]:disabled {\n  background-color: #F9FAFB;\n  border-color: #D0D5DD;\n}\n.acf-admin-page.rtl input[type=radio]:checked:before, .acf-admin-page.rtl input[type=radio]:focus-visible:before,\n.acf-admin-page.rtl input[type=checkbox]:checked:before,\n.acf-admin-page.rtl input[type=checkbox]:focus-visible:before {\n  left: 1px;\n}\n.acf-admin-page input[type=radio]:checked:before, .acf-admin-page input[type=radio]:focus:before {\n  background-image: url(\"../../images/field-states/radio-active.svg\");\n}\n.acf-admin-page input[type=checkbox]:checked:before, .acf-admin-page input[type=checkbox]:focus:before {\n  background-image: url(\"../../images/field-states/checkbox-active.svg\");\n}\n.acf-admin-page .acf-radio-list li input[type=radio],\n.acf-admin-page .acf-radio-list li input[type=checkbox],\n.acf-admin-page .acf-checkbox-list li input[type=radio],\n.acf-admin-page .acf-checkbox-list li input[type=checkbox] {\n  margin-right: 6px;\n}\n.acf-admin-page .acf-radio-list.acf-bl li,\n.acf-admin-page .acf-checkbox-list.acf-bl li {\n  margin-bottom: 8px;\n}\n.acf-admin-page .acf-radio-list.acf-bl li:last-of-type,\n.acf-admin-page .acf-checkbox-list.acf-bl li:last-of-type {\n  margin-bottom: 0;\n}\n.acf-admin-page .acf-radio-list label,\n.acf-admin-page .acf-checkbox-list label {\n  display: flex;\n  align-items: center;\n  align-content: center;\n}\n.acf-admin-page .acf-switch {\n  width: 42px;\n  height: 24px;\n  border: none;\n  background-color: #D0D5DD;\n  border-radius: 12px;\n}\n.acf-admin-page .acf-switch:hover {\n  background-color: #98A2B3;\n}\n.acf-admin-page .acf-switch:active {\n  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 0px 0px rgba(255, 54, 54, 0.25);\n}\n.acf-admin-page .acf-switch.-on {\n  background-color: #0783BE;\n}\n.acf-admin-page .acf-switch.-on:hover {\n  background-color: #066998;\n}\n.acf-admin-page .acf-switch.-on .acf-switch-slider {\n  left: 20px;\n}\n.acf-admin-page .acf-switch .acf-switch-off,\n.acf-admin-page .acf-switch .acf-switch-on {\n  visibility: hidden;\n}\n.acf-admin-page .acf-switch .acf-switch-slider {\n  width: 20px;\n  height: 20px;\n  border: none;\n  border-radius: 100px;\n  box-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);\n}\n.acf-admin-page .acf-field-true-false {\n  display: flex;\n  align-items: flex-start;\n}\n.acf-admin-page .acf-field-true-false .acf-label {\n  order: 2;\n  display: block;\n  align-items: center;\n  margin-top: 2px;\n  margin-bottom: 0;\n  margin-left: 12px;\n}\n.acf-admin-page .acf-field-true-false .acf-label label {\n  margin-bottom: 0;\n}\n.acf-admin-page .acf-field-true-false .acf-label .acf-tip {\n  margin-left: 12px;\n}\n.acf-admin-page .acf-field-true-false .acf-label .description {\n  display: block;\n  margin-top: 2px;\n  margin-left: 0;\n}\n.acf-admin-page.rtl .acf-field-true-false .acf-label {\n  margin-right: 12px;\n  margin-left: 0;\n}\n.acf-admin-page.rtl .acf-field-true-false .acf-tip {\n  margin-right: 12px;\n  margin-left: 0;\n}\n.acf-admin-page input::file-selector-button {\n  box-sizing: border-box;\n  min-height: 40px;\n  margin-right: 16px;\n  padding-top: 8px;\n  padding-right: 16px;\n  padding-bottom: 8px;\n  padding-left: 16px;\n  background-color: transparent;\n  color: #0783BE !important;\n  border-radius: 6px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #0783BE;\n  text-decoration: none;\n}\n.acf-admin-page input::file-selector-button:hover {\n  border-color: #066998;\n  cursor: pointer;\n  color: #066998 !important;\n}\n.acf-admin-page .button {\n  display: inline-flex;\n  align-items: center;\n  height: 40px;\n  padding-right: 16px;\n  padding-left: 16px;\n  background-color: transparent;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #0783BE;\n  border-radius: 6px;\n  color: #0783BE;\n}\n.acf-admin-page .button:hover {\n  background-color: #f3f9fc;\n  border-color: #0783BE;\n  color: #0783BE;\n}\n.acf-admin-page .button:focus {\n  background-color: #f3f9fc;\n  outline: 3px solid #EBF5FA;\n  color: #0783BE;\n}\n.acf-admin-page .edit-field-group-header {\n  display: block !important;\n}\n.acf-admin-page .acf-input .select2-container.-acf .select2-selection {\n  border: none;\n  line-height: 1;\n}\n.acf-admin-page .acf-input .select2-container.-acf .select2-selection__rendered {\n  box-sizing: border-box;\n  padding-right: 0;\n  padding-left: 0;\n  background-color: #fff;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #D0D5DD;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  border-radius: 6px;\n  color: #344054;\n}\n.acf-admin-page .acf-input .select2-container--focus {\n  outline: 3px solid #EBF5FA;\n  border-color: #399CCB;\n  border-radius: 6px;\n}\n.acf-admin-page .acf-input .select2-container--focus .select2-selection__rendered {\n  border-color: #399CCB !important;\n}\n.acf-admin-page .acf-input .select2-container--focus.select2-container--below.select2-container--open .select2-selection__rendered {\n  border-bottom-right-radius: 0 !important;\n  border-bottom-left-radius: 0 !important;\n}\n.acf-admin-page .acf-input .select2-container--focus.select2-container--above.select2-container--open .select2-selection__rendered {\n  border-top-right-radius: 0 !important;\n  border-top-left-radius: 0 !important;\n}\n.acf-admin-page .acf-input .select2-container .select2-search--inline .select2-search__field {\n  margin: 0;\n  padding-left: 6px;\n}\n.acf-admin-page .acf-input .select2-container .select2-search--inline .select2-search__field:focus {\n  outline: none;\n  border: none;\n}\n.acf-admin-page .acf-input .select2-container--default .select2-selection--multiple .select2-selection__rendered {\n  padding-top: 0;\n  padding-right: 6px;\n  padding-bottom: 0;\n  padding-left: 6px;\n}\n.acf-admin-page .acf-input .select2-selection__clear {\n  width: 18px;\n  height: 18px;\n  margin-top: 12px;\n  margin-right: 1px;\n  text-indent: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  color: #fff;\n}\n.acf-admin-page .acf-input .select2-selection__clear:before {\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  top: 0;\n  left: 0;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-close.svg\");\n  mask-image: url(\"../../images/icons/icon-close.svg\");\n  background-color: #98A2B3;\n}\n.acf-admin-page .acf-input .select2-selection__clear:hover::before {\n  background-color: #0783BE;\n}\n.acf-admin-page .acf-label {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n.acf-admin-page .acf-label .acf-icon-help {\n  width: 18px;\n  height: 18px;\n  background-color: #98A2B3;\n}\n.acf-admin-page .acf-label label {\n  margin-bottom: 0;\n}\n.acf-admin-page .acf-label .description {\n  margin-top: 2px;\n}\n.acf-admin-page .acf-field-setting-name .acf-tip {\n  position: absolute;\n  top: 0;\n  left: 654px;\n  color: #98A2B3;\n}\n.rtl.acf-admin-page .acf-field-setting-name .acf-tip {\n  left: auto;\n  right: 654px;\n}\n\n.acf-admin-page .acf-field-setting-name .acf-tip .acf-icon-help {\n  width: 18px;\n  height: 18px;\n}\n.acf-admin-page .acf-field-setting-type .select2-container.-acf,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container.-acf,\n.acf-admin-page .acf-field-query-var .select2-container.-acf,\n.acf-admin-page .acf-field-capability .select2-container.-acf,\n.acf-admin-page .acf-field-parent-slug .select2-container.-acf,\n.acf-admin-page .acf-field-data-storage .select2-container.-acf,\n.acf-admin-page .acf-field-manage-terms .select2-container.-acf,\n.acf-admin-page .acf-field-edit-terms .select2-container.-acf,\n.acf-admin-page .acf-field-delete-terms .select2-container.-acf,\n.acf-admin-page .acf-field-assign-terms .select2-container.-acf,\n.acf-admin-page .acf-field-meta-box .select2-container.-acf {\n  min-height: 40px;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-query-var .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-capability .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-parent-slug .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-data-storage .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-manage-terms .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-edit-terms .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-delete-terms .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-assign-terms .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-meta-box .select2-container--default .select2-selection--single .select2-selection__rendered {\n  display: flex;\n  align-items: center;\n  position: relative;\n  z-index: 800;\n  min-height: 40px;\n  padding-top: 0;\n  padding-right: 12px;\n  padding-bottom: 0;\n  padding-left: 12px;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-query-var .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-capability .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-parent-slug .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-data-storage .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-manage-terms .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-edit-terms .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-delete-terms .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-assign-terms .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-meta-box .select2-container--default .select2-selection--single .field-type-icon {\n  top: auto;\n  width: 18px;\n  height: 18px;\n  margin-right: 2px;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-query-var .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-capability .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-parent-slug .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-data-storage .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-manage-terms .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-edit-terms .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-delete-terms .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-assign-terms .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-meta-box .select2-container--default .select2-selection--single .field-type-icon:before {\n  width: 9px;\n  height: 9px;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-query-var .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-capability .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-parent-slug .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-data-storage .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-manage-terms .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-edit-terms .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-delete-terms .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-assign-terms .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-meta-box .select2-container--open .select2-selection__rendered {\n  border-color: #6BB5D8 !important;\n  border-bottom-color: #D0D5DD !important;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-query-var .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-capability .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-parent-slug .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-data-storage .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-manage-terms .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-edit-terms .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-delete-terms .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-assign-terms .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-meta-box .select2-container--open.select2-container--below .select2-selection__rendered {\n  border-bottom-right-radius: 0 !important;\n  border-bottom-left-radius: 0 !important;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-query-var .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-capability .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-parent-slug .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-data-storage .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-manage-terms .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-edit-terms .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-delete-terms .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-assign-terms .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-meta-box .select2-container--open.select2-container--above .select2-selection__rendered {\n  border-top-right-radius: 0 !important;\n  border-top-left-radius: 0 !important;\n  border-bottom-color: #6BB5D8 !important;\n  border-top-color: #D0D5DD !important;\n}\n.acf-admin-page .acf-field-setting-type .acf-selection.has-icon,\n.acf-admin-page .acf-field-permalink-rewrite .acf-selection.has-icon,\n.acf-admin-page .acf-field-query-var .acf-selection.has-icon,\n.acf-admin-page .acf-field-capability .acf-selection.has-icon,\n.acf-admin-page .acf-field-parent-slug .acf-selection.has-icon,\n.acf-admin-page .acf-field-data-storage .acf-selection.has-icon,\n.acf-admin-page .acf-field-manage-terms .acf-selection.has-icon,\n.acf-admin-page .acf-field-edit-terms .acf-selection.has-icon,\n.acf-admin-page .acf-field-delete-terms .acf-selection.has-icon,\n.acf-admin-page .acf-field-assign-terms .acf-selection.has-icon,\n.acf-admin-page .acf-field-meta-box .acf-selection.has-icon {\n  margin-left: 6px;\n}\n.rtl.acf-admin-page .acf-field-setting-type .acf-selection.has-icon, .acf-admin-page .acf-field-permalink-rewrite .acf-selection.has-icon, .acf-admin-page .acf-field-query-var .acf-selection.has-icon, .acf-admin-page .acf-field-capability .acf-selection.has-icon, .acf-admin-page .acf-field-parent-slug .acf-selection.has-icon, .acf-admin-page .acf-field-data-storage .acf-selection.has-icon, .acf-admin-page .acf-field-manage-terms .acf-selection.has-icon, .acf-admin-page .acf-field-edit-terms .acf-selection.has-icon, .acf-admin-page .acf-field-delete-terms .acf-selection.has-icon, .acf-admin-page .acf-field-assign-terms .acf-selection.has-icon, .acf-admin-page .acf-field-meta-box .acf-selection.has-icon {\n  margin-right: 6px;\n}\n\n.acf-admin-page .acf-field-setting-type .select2-selection__arrow,\n.acf-admin-page .acf-field-permalink-rewrite .select2-selection__arrow,\n.acf-admin-page .acf-field-query-var .select2-selection__arrow,\n.acf-admin-page .acf-field-capability .select2-selection__arrow,\n.acf-admin-page .acf-field-parent-slug .select2-selection__arrow,\n.acf-admin-page .acf-field-data-storage .select2-selection__arrow,\n.acf-admin-page .acf-field-manage-terms .select2-selection__arrow,\n.acf-admin-page .acf-field-edit-terms .select2-selection__arrow,\n.acf-admin-page .acf-field-delete-terms .select2-selection__arrow,\n.acf-admin-page .acf-field-assign-terms .select2-selection__arrow,\n.acf-admin-page .acf-field-meta-box .select2-selection__arrow {\n  width: 20px;\n  height: 20px;\n  top: calc(50% - 10px);\n  right: 12px;\n  background-color: transparent;\n}\n.acf-admin-page .acf-field-setting-type .select2-selection__arrow:after,\n.acf-admin-page .acf-field-permalink-rewrite .select2-selection__arrow:after,\n.acf-admin-page .acf-field-query-var .select2-selection__arrow:after,\n.acf-admin-page .acf-field-capability .select2-selection__arrow:after,\n.acf-admin-page .acf-field-parent-slug .select2-selection__arrow:after,\n.acf-admin-page .acf-field-data-storage .select2-selection__arrow:after,\n.acf-admin-page .acf-field-manage-terms .select2-selection__arrow:after,\n.acf-admin-page .acf-field-edit-terms .select2-selection__arrow:after,\n.acf-admin-page .acf-field-delete-terms .select2-selection__arrow:after,\n.acf-admin-page .acf-field-assign-terms .select2-selection__arrow:after,\n.acf-admin-page .acf-field-meta-box .select2-selection__arrow:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  z-index: 850;\n  top: 1px;\n  left: 0;\n  width: 20px;\n  height: 20px;\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  background-color: #667085;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.acf-admin-page .acf-field-setting-type .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-permalink-rewrite .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-query-var .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-capability .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-parent-slug .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-data-storage .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-manage-terms .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-edit-terms .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-delete-terms .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-assign-terms .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-meta-box .select2-selection__arrow b[role=presentation] {\n  display: none;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-query-var .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-capability .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-parent-slug .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-data-storage .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-manage-terms .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-edit-terms .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-delete-terms .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-assign-terms .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-meta-box .select2-container--open .select2-selection__arrow:after {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n}\n.acf-admin-page .field-type-select-results {\n  position: relative;\n  top: 4px;\n  z-index: 1002;\n  border-radius: 0 0 6px 6px;\n  box-shadow: 0px 8px 24px 4px rgba(16, 24, 40, 0.12);\n}\n.acf-admin-page .field-type-select-results.select2-dropdown--above {\n  display: flex;\n  flex-direction: column-reverse;\n  top: 0;\n  border-radius: 6px 6px 0 0;\n  z-index: 99999;\n}\n.select2-container.select2-container--open.acf-admin-page .field-type-select-results {\n  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 8px 24px 4px rgba(16, 24, 40, 0.12);\n}\n\n.acf-admin-page .field-type-select-results .acf-selection.has-icon {\n  margin-left: 6px;\n}\n.rtl.acf-admin-page .field-type-select-results .acf-selection.has-icon {\n  margin-right: 6px;\n}\n\n.acf-admin-page .field-type-select-results .select2-search {\n  position: relative;\n  margin: 0;\n  padding: 0;\n}\n.acf-admin-page .field-type-select-results .select2-search--dropdown:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 12px;\n  left: 13px;\n  width: 16px;\n  height: 16px;\n  -webkit-mask-image: url(\"../../images/icons/icon-search.svg\");\n  mask-image: url(\"../../images/icons/icon-search.svg\");\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.rtl.acf-admin-page .field-type-select-results .select2-search--dropdown:after {\n  right: 12px;\n  left: auto;\n}\n\n.acf-admin-page .field-type-select-results .select2-search .select2-search__field {\n  padding-left: 38px;\n  border-right: 0;\n  border-bottom: 0;\n  border-left: 0;\n  border-radius: 0;\n}\n.rtl.acf-admin-page .field-type-select-results .select2-search .select2-search__field {\n  padding-right: 38px;\n  padding-left: 0;\n}\n\n.acf-admin-page .field-type-select-results .select2-search .select2-search__field:focus {\n  border-top-color: #D0D5DD;\n  outline: 0;\n}\n.acf-admin-page .field-type-select-results .select2-results__options {\n  max-height: 440px;\n}\n.acf-admin-page .field-type-select-results .select2-results__option .select2-results__option--highlighted {\n  background-color: #0783BE !important;\n  color: #F9FAFB !important;\n}\n.acf-admin-page .field-type-select-results .select2-results__option .select2-results__option {\n  display: inline-flex;\n  position: relative;\n  width: calc(100% - 24px);\n  min-height: 32px;\n  padding-top: 0;\n  padding-right: 12px;\n  padding-bottom: 0;\n  padding-left: 12px;\n  align-items: center;\n}\n.acf-admin-page .field-type-select-results .select2-results__option .select2-results__option .field-type-icon {\n  top: auto;\n  width: 18px;\n  height: 18px;\n  margin-right: 2px;\n  box-shadow: 0 0 0 1px #F9FAFB;\n}\n.acf-admin-page .field-type-select-results .select2-results__option .select2-results__option .field-type-icon:before {\n  width: 9px;\n  height: 9px;\n}\n.acf-admin-page .field-type-select-results .select2-results__option[aria-selected=true] {\n  background-color: #EBF5FA !important;\n  color: #344054 !important;\n}\n.acf-admin-page .field-type-select-results .select2-results__option[aria-selected=true]:after {\n  content: \"\";\n  right: 13px;\n  position: absolute;\n  width: 16px;\n  height: 16px;\n  -webkit-mask-image: url(\"../../images/icons/icon-check.svg\");\n  mask-image: url(\"../../images/icons/icon-check.svg\");\n  background-color: #0783BE;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.rtl.acf-admin-page .field-type-select-results .select2-results__option[aria-selected=true]:after {\n  left: 13px;\n  right: auto;\n}\n\n.acf-admin-page .field-type-select-results .select2-results__group {\n  display: inline-flex;\n  align-items: center;\n  width: calc(100% - 24px);\n  min-height: 25px;\n  background-color: #F9FAFB;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n  color: #98A2B3;\n  font-size: 11px;\n  margin-bottom: 0;\n  padding-top: 0;\n  padding-right: 12px;\n  padding-bottom: 0;\n  padding-left: 12px;\n  font-weight: normal;\n}\n.acf-admin-page.rtl .acf-field-setting-type .select2-selection__arrow:after,\n.acf-admin-page.rtl .acf-field-permalink-rewrite .select2-selection__arrow:after,\n.acf-admin-page.rtl .acf-field-query-var .select2-selection__arrow:after {\n  right: auto;\n  left: 10px;\n}\n\n.rtl.post-type-acf-field-group .acf-field-setting-name .acf-tip,\n.rtl.acf-internal-post-type .acf-field-setting-name .acf-tip {\n  left: auto;\n  right: 654px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Field Groups\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-internal-post-type .tablenav.top {\n  display: none;\n}\n.acf-internal-post-type .subsubsub {\n  margin-bottom: 3px;\n}\n.acf-internal-post-type .wp-list-table {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  border-radius: 8px;\n  border: none;\n  overflow: hidden;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.acf-internal-post-type .wp-list-table strong {\n  color: #98A2B3;\n  margin: 0;\n}\n.acf-internal-post-type .wp-list-table a.row-title {\n  font-size: 13px !important;\n  font-weight: 500;\n}\n.acf-internal-post-type .wp-list-table th,\n.acf-internal-post-type .wp-list-table td {\n  color: #344054;\n}\n.acf-internal-post-type .wp-list-table th.sortable a,\n.acf-internal-post-type .wp-list-table td.sortable a {\n  padding: 0;\n}\n.acf-internal-post-type .wp-list-table th.check-column,\n.acf-internal-post-type .wp-list-table td.check-column {\n  padding-top: 12px;\n  padding-right: 16px;\n  padding-left: 16px;\n}\n@media screen and (max-width: 880px) {\n  .acf-internal-post-type .wp-list-table th.check-column,\n  .acf-internal-post-type .wp-list-table td.check-column {\n    vertical-align: top;\n    padding-right: 2px;\n    padding-left: 10px;\n  }\n}\n.acf-internal-post-type .wp-list-table th input,\n.acf-internal-post-type .wp-list-table td input {\n  margin: 0;\n  padding: 0;\n}\n.acf-internal-post-type .wp-list-table th .acf-more-items,\n.acf-internal-post-type .wp-list-table td .acf-more-items {\n  display: inline-flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  padding: 0px 6px 1px;\n  gap: 8px;\n  width: 25px;\n  height: 16px;\n  background: #EAECF0;\n  border-radius: 100px;\n  font-weight: 400;\n  font-size: 10px;\n  color: #475467;\n}\n.acf-internal-post-type .wp-list-table th .acf-emdash,\n.acf-internal-post-type .wp-list-table td .acf-emdash {\n  color: #D0D5DD;\n}\n.acf-internal-post-type .wp-list-table thead th, .acf-internal-post-type .wp-list-table thead td,\n.acf-internal-post-type .wp-list-table tfoot th, .acf-internal-post-type .wp-list-table tfoot td {\n  height: 48px;\n  padding-right: 24px;\n  padding-left: 24px;\n  box-sizing: border-box;\n  background-color: #F9FAFB;\n  border-color: #EAECF0;\n  font-weight: 500;\n}\n@media screen and (max-width: 880px) {\n  .acf-internal-post-type .wp-list-table thead th, .acf-internal-post-type .wp-list-table thead td,\n  .acf-internal-post-type .wp-list-table tfoot th, .acf-internal-post-type .wp-list-table tfoot td {\n    padding-right: 16px;\n    padding-left: 8px;\n  }\n}\n@media screen and (max-width: 880px) {\n  .acf-internal-post-type .wp-list-table thead th.check-column, .acf-internal-post-type .wp-list-table thead td.check-column,\n  .acf-internal-post-type .wp-list-table tfoot th.check-column, .acf-internal-post-type .wp-list-table tfoot td.check-column {\n    vertical-align: middle;\n  }\n}\n.acf-internal-post-type .wp-list-table tbody th,\n.acf-internal-post-type .wp-list-table tbody td {\n  box-sizing: border-box;\n  height: 60px;\n  padding-top: 10px;\n  padding-right: 24px;\n  padding-bottom: 10px;\n  padding-left: 24px;\n  vertical-align: top;\n  background-color: #fff;\n  border-bottom-width: 1px;\n  border-bottom-color: #EAECF0;\n  border-bottom-style: solid;\n}\n@media screen and (max-width: 880px) {\n  .acf-internal-post-type .wp-list-table tbody th,\n  .acf-internal-post-type .wp-list-table tbody td {\n    padding-right: 16px;\n    padding-left: 8px;\n  }\n}\n.acf-internal-post-type .wp-list-table .column-acf-key {\n  white-space: nowrap;\n}\n.acf-internal-post-type .wp-list-table .column-acf-key .acf-icon-key-solid {\n  display: inline-block;\n  position: relative;\n  bottom: -2px;\n  width: 15px;\n  height: 15px;\n  margin-right: 4px;\n  color: #98A2B3;\n}\n.acf-internal-post-type .wp-list-table .acf-location .dashicons {\n  position: relative;\n  bottom: -2px;\n  width: 16px;\n  height: 16px;\n  margin-right: 6px;\n  font-size: 16px;\n  color: #98A2B3;\n}\n.acf-internal-post-type .wp-list-table .post-state {\n  color: #667085;\n}\n.acf-internal-post-type .wp-list-table tr:hover,\n.acf-internal-post-type .wp-list-table tr:focus-within {\n  background: #f7f7f7;\n}\n.acf-internal-post-type .wp-list-table tr:hover .row-actions,\n.acf-internal-post-type .wp-list-table tr:focus-within .row-actions {\n  margin-bottom: 0;\n}\n@media screen and (min-width: 782px) {\n  .acf-internal-post-type .wp-list-table .column-acf-count {\n    width: 10%;\n  }\n}\n.acf-internal-post-type .wp-list-table .row-actions span.file {\n  display: block;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n.acf-internal-post-type.rtl .wp-list-table .column-acf-key .acf-icon-key-solid {\n  margin-left: 4px;\n  margin-right: 0;\n}\n.acf-internal-post-type.rtl .wp-list-table .acf-location .dashicons {\n  margin-left: 6px;\n  margin-right: 0;\n}\n.acf-internal-post-type .row-actions {\n  margin-top: 2px;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n  line-height: 14px;\n  color: #D0D5DD;\n}\n.acf-internal-post-type .row-actions .trash a {\n  color: #d94f4f;\n}\n.acf-internal-post-type .widefat thead td.check-column,\n.acf-internal-post-type .widefat tfoot td.check-column {\n  padding-top: 0;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRow actions\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type .row-actions a:hover {\n  color: #044767;\n}\n.acf-internal-post-type .row-actions .trash a {\n  color: #a00;\n}\n.acf-internal-post-type .row-actions .trash a:hover {\n  color: #f00;\n}\n.acf-internal-post-type .row-actions.visible {\n  margin-bottom: 0;\n  opacity: 1;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRow hover\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type #the-list tr:hover td,\n.acf-internal-post-type #the-list tr:hover th {\n  background-color: #f7fbfd;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Table Nav\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-internal-post-type .tablenav {\n  margin-top: 24px;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n  color: #667085;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tSearch box\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type #posts-filter p.search-box {\n  margin-top: 5px;\n  margin-right: 0;\n  margin-bottom: 24px;\n  margin-left: 0;\n}\n.acf-internal-post-type #posts-filter p.search-box #post-search-input {\n  min-width: 280px;\n  margin-top: 0;\n  margin-right: 8px;\n  margin-bottom: 0;\n  margin-left: 0;\n}\n@media screen and (max-width: 768px) {\n  .acf-internal-post-type #posts-filter p.search-box {\n    display: flex;\n    box-sizing: border-box;\n    padding-right: 24px;\n    margin-right: 16px;\n    position: inherit;\n  }\n  .acf-internal-post-type #posts-filter p.search-box #post-search-input {\n    min-width: auto;\n  }\n}\n\n.rtl.acf-internal-post-type #posts-filter p.search-box #post-search-input {\n  margin-right: 0;\n  margin-left: 8px;\n}\n@media screen and (max-width: 768px) {\n  .rtl.acf-internal-post-type #posts-filter p.search-box {\n    padding-left: 24px;\n    padding-right: 0;\n    margin-left: 16px;\n    margin-right: 0;\n  }\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tStatus tabs\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type .subsubsub {\n  display: flex;\n  align-items: flex-end;\n  height: 40px;\n  margin-bottom: 16px;\n}\n.acf-internal-post-type .subsubsub li {\n  margin-top: 0;\n  margin-right: 4px;\n  color: #98A2B3;\n}\n.acf-internal-post-type .subsubsub li .count {\n  color: #667085;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPagination\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type .tablenav-pages {\n  display: flex;\n  align-items: center;\n}\n.acf-internal-post-type .tablenav-pages.no-pages {\n  display: none;\n}\n.acf-internal-post-type .tablenav-pages .displaying-num {\n  margin-top: 0;\n  margin-right: 16px;\n  margin-bottom: 0;\n  margin-left: 0;\n}\n.acf-internal-post-type .tablenav-pages .pagination-links {\n  display: flex;\n  align-items: center;\n}\n.acf-internal-post-type .tablenav-pages .pagination-links #table-paging {\n  margin-top: 0;\n  margin-right: 4px;\n  margin-bottom: 0;\n  margin-left: 8px;\n}\n.acf-internal-post-type .tablenav-pages .pagination-links #table-paging .total-pages {\n  margin-right: 0;\n}\n.acf-internal-post-type .tablenav-pages.one-page .pagination-links {\n  display: none;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPagination buttons & icons\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type .tablenav-pages .pagination-links .button {\n  display: inline-flex;\n  align-items: center;\n  align-content: center;\n  justify-content: center;\n  min-width: 40px;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n  background-color: transparent;\n}\n.acf-internal-post-type .tablenav-pages .pagination-links .button:nth-child(1), .acf-internal-post-type .tablenav-pages .pagination-links .button:nth-child(2), .acf-internal-post-type .tablenav-pages .pagination-links .button:last-child, .acf-internal-post-type .tablenav-pages .pagination-links .button:nth-last-child(2) {\n  display: inline-block;\n  position: relative;\n  text-indent: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  margin-left: 4px;\n}\n.acf-internal-post-type .tablenav-pages .pagination-links .button:nth-child(1):before, .acf-internal-post-type .tablenav-pages .pagination-links .button:nth-child(2):before, .acf-internal-post-type .tablenav-pages .pagination-links .button:last-child:before, .acf-internal-post-type .tablenav-pages .pagination-links .button:nth-last-child(2):before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  background-color: #0783BE;\n  border-radius: 0;\n  -webkit-mask-size: 20px;\n  mask-size: 20px;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n}\n.acf-internal-post-type .tablenav-pages .pagination-links .button:nth-child(1):before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-left-double.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-left-double.svg\");\n}\n.acf-internal-post-type .tablenav-pages .pagination-links .button:nth-child(2):before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-left.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-left.svg\");\n}\n.acf-internal-post-type .tablenav-pages .pagination-links .button:nth-last-child(2):before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n}\n.acf-internal-post-type .tablenav-pages .pagination-links .button:last-child:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-right-double.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-right-double.svg\");\n}\n.acf-internal-post-type .tablenav-pages .pagination-links .button:hover {\n  border-color: #066998;\n  background-color: rgba(7, 131, 190, 0.05);\n}\n.acf-internal-post-type .tablenav-pages .pagination-links .button:hover:before {\n  background-color: #066998;\n}\n.acf-internal-post-type .tablenav-pages .pagination-links .button.disabled {\n  background-color: transparent !important;\n}\n.acf-internal-post-type .tablenav-pages .pagination-links .button.disabled.disabled:before {\n  background-color: #D0D5DD;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Empty state\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-no-field-groups-wrapper,\n.acf-no-taxonomies-wrapper,\n.acf-no-post-types-wrapper,\n.acf-no-options-pages-wrapper {\n  display: flex;\n  justify-content: center;\n  padding-top: 48px;\n  padding-bottom: 48px;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner,\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner,\n.acf-no-field-groups-wrapper .acf-no-post-types-inner,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner,\n.acf-no-taxonomies-wrapper .acf-no-field-groups-inner,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner,\n.acf-no-post-types-wrapper .acf-no-field-groups-inner,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner,\n.acf-no-post-types-wrapper .acf-no-post-types-inner,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner,\n.acf-no-options-pages-wrapper .acf-no-field-groups-inner,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n  align-content: center;\n  align-items: flex-start;\n  text-align: center;\n  max-width: 380px;\n  min-height: 320px;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner img,\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner h2,\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner p,\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner img,\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner h2,\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner p,\n.acf-no-field-groups-wrapper .acf-no-post-types-inner img,\n.acf-no-field-groups-wrapper .acf-no-post-types-inner h2,\n.acf-no-field-groups-wrapper .acf-no-post-types-inner p,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner img,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner h2,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner p,\n.acf-no-taxonomies-wrapper .acf-no-field-groups-inner img,\n.acf-no-taxonomies-wrapper .acf-no-field-groups-inner h2,\n.acf-no-taxonomies-wrapper .acf-no-field-groups-inner p,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner img,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner h2,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner p,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner img,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner h2,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner p,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner img,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner h2,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner p,\n.acf-no-post-types-wrapper .acf-no-field-groups-inner img,\n.acf-no-post-types-wrapper .acf-no-field-groups-inner h2,\n.acf-no-post-types-wrapper .acf-no-field-groups-inner p,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner img,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner h2,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner p,\n.acf-no-post-types-wrapper .acf-no-post-types-inner img,\n.acf-no-post-types-wrapper .acf-no-post-types-inner h2,\n.acf-no-post-types-wrapper .acf-no-post-types-inner p,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner img,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner h2,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner p,\n.acf-no-options-pages-wrapper .acf-no-field-groups-inner img,\n.acf-no-options-pages-wrapper .acf-no-field-groups-inner h2,\n.acf-no-options-pages-wrapper .acf-no-field-groups-inner p,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner img,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner h2,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner p,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner img,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner h2,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner p,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner img,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner h2,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner p {\n  flex: 1 0 100%;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner h2,\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner h2,\n.acf-no-field-groups-wrapper .acf-no-post-types-inner h2,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner h2,\n.acf-no-taxonomies-wrapper .acf-no-field-groups-inner h2,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner h2,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner h2,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner h2,\n.acf-no-post-types-wrapper .acf-no-field-groups-inner h2,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner h2,\n.acf-no-post-types-wrapper .acf-no-post-types-inner h2,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner h2,\n.acf-no-options-pages-wrapper .acf-no-field-groups-inner h2,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner h2,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner h2,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner h2 {\n  margin-top: 32px;\n  margin-bottom: 0;\n  padding: 0;\n  color: #344054;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner p,\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner p,\n.acf-no-field-groups-wrapper .acf-no-post-types-inner p,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner p,\n.acf-no-taxonomies-wrapper .acf-no-field-groups-inner p,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner p,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner p,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner p,\n.acf-no-post-types-wrapper .acf-no-field-groups-inner p,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner p,\n.acf-no-post-types-wrapper .acf-no-post-types-inner p,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner p,\n.acf-no-options-pages-wrapper .acf-no-field-groups-inner p,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner p,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner p,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner p {\n  margin-top: 12px;\n  margin-bottom: 0;\n  padding: 0;\n  color: #667085;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner p.acf-small,\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner p.acf-small,\n.acf-no-field-groups-wrapper .acf-no-post-types-inner p.acf-small,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner p.acf-small,\n.acf-no-taxonomies-wrapper .acf-no-field-groups-inner p.acf-small,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner p.acf-small,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner p.acf-small,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner p.acf-small,\n.acf-no-post-types-wrapper .acf-no-field-groups-inner p.acf-small,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner p.acf-small,\n.acf-no-post-types-wrapper .acf-no-post-types-inner p.acf-small,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner p.acf-small,\n.acf-no-options-pages-wrapper .acf-no-field-groups-inner p.acf-small,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner p.acf-small,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner p.acf-small,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner p.acf-small {\n  display: block;\n  position: relative;\n  margin-top: 32px;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner img,\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner img,\n.acf-no-field-groups-wrapper .acf-no-post-types-inner img,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner img,\n.acf-no-taxonomies-wrapper .acf-no-field-groups-inner img,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner img,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner img,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner img,\n.acf-no-post-types-wrapper .acf-no-field-groups-inner img,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner img,\n.acf-no-post-types-wrapper .acf-no-post-types-inner img,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner img,\n.acf-no-options-pages-wrapper .acf-no-field-groups-inner img,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner img,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner img,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner img {\n  max-width: 284px;\n  margin-bottom: 0;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner .acf-btn,\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner .acf-btn,\n.acf-no-field-groups-wrapper .acf-no-post-types-inner .acf-btn,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner .acf-btn,\n.acf-no-taxonomies-wrapper .acf-no-field-groups-inner .acf-btn,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner .acf-btn,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner .acf-btn,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner .acf-btn,\n.acf-no-post-types-wrapper .acf-no-field-groups-inner .acf-btn,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner .acf-btn,\n.acf-no-post-types-wrapper .acf-no-post-types-inner .acf-btn,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner .acf-btn,\n.acf-no-options-pages-wrapper .acf-no-field-groups-inner .acf-btn,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner .acf-btn,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner .acf-btn,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner .acf-btn {\n  margin-top: 32px;\n}\n.acf-no-field-groups-wrapper .acf-no-post-types-inner img,\n.acf-no-field-groups-wrapper .acf-no-options-pages-inner img,\n.acf-no-taxonomies-wrapper .acf-no-post-types-inner img,\n.acf-no-taxonomies-wrapper .acf-no-options-pages-inner img,\n.acf-no-post-types-wrapper .acf-no-post-types-inner img,\n.acf-no-post-types-wrapper .acf-no-options-pages-inner img,\n.acf-no-options-pages-wrapper .acf-no-post-types-inner img,\n.acf-no-options-pages-wrapper .acf-no-options-pages-inner img {\n  width: 106px;\n  height: 88px;\n}\n.acf-no-field-groups-wrapper .acf-no-taxonomies-inner img,\n.acf-no-taxonomies-wrapper .acf-no-taxonomies-inner img,\n.acf-no-post-types-wrapper .acf-no-taxonomies-inner img,\n.acf-no-options-pages-wrapper .acf-no-taxonomies-inner img {\n  width: 98px;\n  height: 88px;\n}\n\n.acf-no-field-groups #the-list tr:hover td,\n.acf-no-field-groups #the-list tr:hover th,\n.acf-no-field-groups .acf-admin-field-groups .wp-list-table tr:hover,\n.acf-no-field-groups .striped > tbody > :nth-child(odd), .acf-no-field-groups ul.striped > :nth-child(odd), .acf-no-field-groups .alternate,\n.acf-no-post-types #the-list tr:hover td,\n.acf-no-post-types #the-list tr:hover th,\n.acf-no-post-types .acf-admin-field-groups .wp-list-table tr:hover,\n.acf-no-post-types .striped > tbody > :nth-child(odd),\n.acf-no-post-types ul.striped > :nth-child(odd),\n.acf-no-post-types .alternate,\n.acf-no-taxonomies #the-list tr:hover td,\n.acf-no-taxonomies #the-list tr:hover th,\n.acf-no-taxonomies .acf-admin-field-groups .wp-list-table tr:hover,\n.acf-no-taxonomies .striped > tbody > :nth-child(odd),\n.acf-no-taxonomies ul.striped > :nth-child(odd),\n.acf-no-taxonomies .alternate,\n.acf-no-options-pages #the-list tr:hover td,\n.acf-no-options-pages #the-list tr:hover th,\n.acf-no-options-pages .acf-admin-field-groups .wp-list-table tr:hover,\n.acf-no-options-pages .striped > tbody > :nth-child(odd),\n.acf-no-options-pages ul.striped > :nth-child(odd),\n.acf-no-options-pages .alternate {\n  background-color: transparent !important;\n}\n.acf-no-field-groups .wp-list-table thead,\n.acf-no-field-groups .wp-list-table tfoot,\n.acf-no-post-types .wp-list-table thead,\n.acf-no-post-types .wp-list-table tfoot,\n.acf-no-taxonomies .wp-list-table thead,\n.acf-no-taxonomies .wp-list-table tfoot,\n.acf-no-options-pages .wp-list-table thead,\n.acf-no-options-pages .wp-list-table tfoot {\n  display: none;\n}\n\n.acf-internal-post-type #the-list .no-items td {\n  vertical-align: middle;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small screen list table info toggle\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-internal-post-type .wp-list-table .toggle-row:before {\n  top: 4px;\n  left: 16px;\n  border-radius: 0;\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: 16px;\n  height: 16px;\n  background-color: #0783BE;\n  border-radius: 0;\n  -webkit-mask-size: 20px;\n  mask-size: 20px;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  text-indent: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.acf-internal-post-type .wp-list-table .is-expanded .toggle-row:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small screen checkbox\n*\n*---------------------------------------------------------------------------------------------*/\n@media screen and (max-width: 880px) {\n  .acf-internal-post-type .widefat th input[type=checkbox],\n  .acf-internal-post-type .widefat thead td input[type=checkbox],\n  .acf-internal-post-type .widefat tfoot td input[type=checkbox] {\n    margin-bottom: 0;\n  }\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Navigation\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-toolbar {\n  position: unset;\n  top: 32px;\n  height: 72px;\n  z-index: 800;\n  background: #344054;\n  color: #98A2B3;\n}\n.acf-admin-toolbar .acf-admin-toolbar-inner {\n  display: flex;\n  justify-content: space-between;\n  align-content: center;\n  align-items: center;\n  max-width: 100%;\n}\n.acf-admin-toolbar .acf-admin-toolbar-inner .acf-nav-wrap {\n  display: flex;\n  align-items: center;\n}\n@media screen and (max-width: 1250px) {\n  .acf-admin-toolbar .acf-admin-toolbar-inner .acf-nav-wrap .acf-header-tab-acf-post-type,\n  .acf-admin-toolbar .acf-admin-toolbar-inner .acf-nav-wrap .acf-header-tab-acf-taxonomy {\n    display: none;\n  }\n  .acf-admin-toolbar .acf-admin-toolbar-inner .acf-nav-wrap .acf-more .acf-header-tab-acf-post-type,\n  .acf-admin-toolbar .acf-admin-toolbar-inner .acf-nav-wrap .acf-more .acf-header-tab-acf-taxonomy {\n    display: flex;\n  }\n}\n.acf-admin-toolbar .acf-admin-toolbar-inner .acf-nav-upgrade-wrap {\n  display: flex;\n  align-items: center;\n}\n.acf-admin-toolbar .acf-admin-toolbar-inner .acf-nav-wpengine-logo {\n  display: inline-flex;\n  margin-left: 24px;\n}\n@media screen and (max-width: 1000px) {\n  .acf-admin-toolbar .acf-admin-toolbar-inner .acf-nav-wpengine-logo {\n    display: none;\n  }\n}\n@media screen and (max-width: 880px) {\n  .acf-admin-toolbar {\n    position: static;\n  }\n}\n.acf-admin-toolbar .acf-logo {\n  display: flex;\n  margin-right: 24px;\n  text-decoration: none;\n}\n.acf-admin-toolbar .acf-logo .acf-pro-label {\n  margin-left: 8px;\n}\n.acf-admin-toolbar .acf-logo img {\n  display: block;\n  max-width: 55px;\n  line-height: 0%;\n}\n.acf-admin-toolbar h2 {\n  display: none;\n  color: #F9FAFB;\n}\n.acf-admin-toolbar .acf-tab {\n  display: flex;\n  align-items: center;\n  box-sizing: border-box;\n  min-height: 40px;\n  margin-right: 8px;\n  padding-top: 8px;\n  padding-right: 16px;\n  padding-bottom: 8px;\n  padding-left: 16px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: transparent;\n  border-radius: 6px;\n  color: #98A2B3;\n  text-decoration: none;\n}\n.acf-admin-toolbar .acf-tab.is-active {\n  background-color: #475467;\n  color: #fff;\n}\n.acf-admin-toolbar .acf-tab:hover {\n  background-color: #475467;\n  color: #F9FAFB;\n}\n.acf-admin-toolbar .acf-tab:focus-visible {\n  border-width: 1px;\n  border-style: solid;\n  border-color: #667085;\n}\n.acf-admin-toolbar .acf-tab:focus {\n  box-shadow: none;\n}\n.acf-admin-toolbar .acf-more:hover .acf-tab.acf-more-tab {\n  background-color: #475467;\n  color: #F9FAFB;\n}\n.acf-admin-toolbar .acf-more ul {\n  display: none;\n  position: absolute;\n  box-sizing: border-box;\n  background: #fff;\n  z-index: 1051;\n  overflow: hidden;\n  min-width: 280px;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding: 0;\n  border-radius: 8px;\n  box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.04), 0px 8px 23px rgba(0, 0, 0, 0.12);\n}\n.acf-admin-toolbar .acf-more ul .acf-wp-engine {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  min-height: 48px;\n  border-top: 1px solid rgba(0, 0, 0, 0.08);\n  background: #ECFBFC;\n}\n.acf-admin-toolbar .acf-more ul .acf-wp-engine a {\n  display: flex;\n  width: 100%;\n  justify-content: space-between;\n  border-top: none;\n}\n.acf-admin-toolbar .acf-more ul li {\n  margin: 0;\n  padding: 0 16px;\n}\n.acf-admin-toolbar .acf-more ul li .acf-header-tab-acf-post-type,\n.acf-admin-toolbar .acf-more ul li .acf-header-tab-acf-taxonomy {\n  display: none;\n}\n.acf-admin-toolbar .acf-more ul li.acf-more-section-header {\n  background: #F9FAFB;\n  padding: 1px 0 0 0;\n  margin-top: -1px;\n  border-top: 1px solid #EAECF0;\n  border-bottom: 1px solid #EAECF0;\n}\n.acf-admin-toolbar .acf-more ul li.acf-more-section-header span {\n  color: #475467;\n  font-size: 12px;\n  font-weight: bold;\n}\n.acf-admin-toolbar .acf-more ul li.acf-more-section-header span:hover {\n  background: #F9FAFB;\n}\n.acf-admin-toolbar .acf-more ul li a {\n  margin: 0;\n  padding: 0;\n  color: #1D2939;\n  border-radius: 0;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #F2F4F7;\n}\n.acf-admin-toolbar .acf-more ul li a:hover, .acf-admin-toolbar .acf-more ul li a.acf-tab.is-active {\n  background-color: unset;\n  color: #0783BE;\n}\n.acf-admin-toolbar .acf-more ul li a i.acf-icon {\n  display: none !important;\n  width: 16px;\n  height: 16px;\n  -webkit-mask-size: 16px;\n  mask-size: 16px;\n  background-color: #98A2B3 !important;\n}\n.acf-admin-toolbar .acf-more ul li a .acf-requires-pro {\n  justify-content: center;\n  align-items: center;\n  color: white;\n  background: linear-gradient(90.52deg, #3E8BFF 0.44%, #A45CFF 113.3%);\n  background-size: 140% 20%;\n  background-position: 100% 0;\n  border-radius: 100px;\n  font-size: 11px;\n  position: absolute;\n  right: 16px;\n  padding-right: 6px;\n  padding-left: 6px;\n}\n.acf-admin-toolbar .acf-more ul li a img.acf-wp-engine-pro {\n  display: block;\n  height: 16px;\n  width: auto;\n}\n.acf-admin-toolbar .acf-more ul li a .acf-wp-engine-upsell-pill {\n  display: inline-flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 22px;\n  border-radius: 100px;\n  font-size: 11px;\n  padding-right: 8px;\n  padding-left: 8px;\n  background: #0ECAD4;\n  color: #FFFFFF;\n  text-shadow: 0px 1px 0 rgba(0, 0, 0, 0.12);\n  text-transform: uppercase;\n}\n.acf-admin-toolbar .acf-more ul li:first-child a {\n  border-bottom: none;\n}\n.acf-admin-toolbar .acf-more ul:hover, .acf-admin-toolbar .acf-more ul:focus {\n  display: block;\n}\n.acf-admin-toolbar .acf-more:hover ul, .acf-admin-toolbar .acf-more:focus ul {\n  display: block;\n}\n#wpcontent .acf-admin-toolbar {\n  box-sizing: border-box;\n  margin-left: -20px;\n  padding-top: 16px;\n  padding-right: 32px;\n  padding-bottom: 16px;\n  padding-left: 32px;\n}\n@media screen and (max-width: 600px) {\n  .acf-admin-toolbar {\n    display: none;\n  }\n}\n\n.rtl #wpcontent .acf-admin-toolbar {\n  margin-left: 0;\n  margin-right: -20px;\n}\n.rtl #wpcontent .acf-admin-toolbar .acf-tab {\n  margin-left: 8px;\n  margin-right: 0;\n}\n.rtl .acf-logo {\n  margin-right: 0;\n  margin-left: 32px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Toolbar Icons\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-toolbar .acf-tab i.acf-icon,\n.acf-admin-toolbar .acf-more i.acf-icon {\n  display: none;\n  margin-right: 8px;\n  margin-left: -2px;\n}\n.acf-admin-toolbar .acf-tab i.acf-icon.acf-icon-dropdown,\n.acf-admin-toolbar .acf-more i.acf-icon.acf-icon-dropdown {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  width: 16px;\n  height: 16px;\n  -webkit-mask-size: 16px;\n  mask-size: 16px;\n  margin-right: -6px;\n  margin-left: 6px;\n}\n.acf-admin-toolbar .acf-tab.acf-header-tab-acf-field-group i.acf-icon, .acf-admin-toolbar .acf-tab.acf-header-tab-acf-post-type i.acf-icon, .acf-admin-toolbar .acf-tab.acf-header-tab-acf-taxonomy i.acf-icon, .acf-admin-toolbar .acf-tab.acf-header-tab-acf-tools i.acf-icon, .acf-admin-toolbar .acf-tab.acf-header-tab-acf-settings-updates i.acf-icon, .acf-admin-toolbar .acf-tab.acf-header-tab-acf-more i.acf-icon,\n.acf-admin-toolbar .acf-more.acf-header-tab-acf-field-group i.acf-icon,\n.acf-admin-toolbar .acf-more.acf-header-tab-acf-post-type i.acf-icon,\n.acf-admin-toolbar .acf-more.acf-header-tab-acf-taxonomy i.acf-icon,\n.acf-admin-toolbar .acf-more.acf-header-tab-acf-tools i.acf-icon,\n.acf-admin-toolbar .acf-more.acf-header-tab-acf-settings-updates i.acf-icon,\n.acf-admin-toolbar .acf-more.acf-header-tab-acf-more i.acf-icon {\n  display: inline-flex;\n}\n.acf-admin-toolbar .acf-tab.is-active i.acf-icon, .acf-admin-toolbar .acf-tab:hover i.acf-icon,\n.acf-admin-toolbar .acf-more.is-active i.acf-icon,\n.acf-admin-toolbar .acf-more:hover i.acf-icon {\n  background-color: #EAECF0;\n}\n.rtl .acf-admin-toolbar .acf-tab i.acf-icon {\n  margin-right: -2px;\n  margin-left: 8px;\n}\n.acf-admin-toolbar .acf-header-tab-acf-field-group i.acf-icon {\n  -webkit-mask-image: url(\"../../images/icons/icon-field-groups.svg\");\n  mask-image: url(\"../../images/icons/icon-field-groups.svg\");\n}\n.acf-admin-toolbar .acf-header-tab-acf-post-type i.acf-icon {\n  -webkit-mask-image: url(\"../../images/icons/icon-post-type.svg\");\n  mask-image: url(\"../../images/icons/icon-post-type.svg\");\n}\n.acf-admin-toolbar .acf-header-tab-acf-taxonomy i.acf-icon {\n  -webkit-mask-image: url(\"../../images/icons/icon-taxonomies.svg\");\n  mask-image: url(\"../../images/icons/icon-taxonomies.svg\");\n}\n.acf-admin-toolbar .acf-header-tab-acf-tools i.acf-icon {\n  -webkit-mask-image: url(\"../../images/icons/icon-tools.svg\");\n  mask-image: url(\"../../images/icons/icon-tools.svg\");\n}\n.acf-admin-toolbar .acf-header-tab-acf-settings-updates i.acf-icon {\n  -webkit-mask-image: url(\"../../images/icons/icon-updates.svg\");\n  mask-image: url(\"../../images/icons/icon-updates.svg\");\n}\n.acf-admin-toolbar .acf-header-tab-acf-more i.acf-icon-more {\n  -webkit-mask-image: url(\"../../images/icons/icon-extended-menu.svg\");\n  mask-image: url(\"../../images/icons/icon-extended-menu.svg\");\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide WP default controls\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page h1.wp-heading-inline {\n  display: none;\n}\n.acf-admin-page .wrap .wp-heading-inline + .page-title-action {\n  display: none;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headerbar\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-headerbar {\n  display: flex;\n  align-items: center;\n  position: sticky;\n  top: 32px;\n  z-index: 700;\n  box-sizing: border-box;\n  min-height: 72px;\n  margin-left: -20px;\n  padding-top: 8px;\n  padding-right: 32px;\n  padding-bottom: 8px;\n  padding-left: 32px;\n  background-color: #fff;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.acf-headerbar .acf-headerbar-inner {\n  flex: 1 1 auto;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  max-width: 1440px;\n}\n.acf-headerbar .acf-page-title {\n  margin-top: 0;\n  margin-right: 16px;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n.acf-headerbar .acf-page-title .acf-duplicated-from {\n  color: #98A2B3;\n}\n@media screen and (max-width: 880px) {\n  .acf-headerbar {\n    position: static;\n  }\n}\n@media screen and (max-width: 600px) {\n  .acf-headerbar {\n    justify-content: space-between;\n    position: relative;\n    top: 46px;\n    min-height: 64px;\n    padding-right: 12px;\n  }\n}\n.acf-headerbar .acf-headerbar-content {\n  flex: 1 1 auto;\n  display: flex;\n  align-items: center;\n}\n@media screen and (max-width: 880px) {\n  .acf-headerbar .acf-headerbar-content {\n    flex-wrap: wrap;\n  }\n  .acf-headerbar .acf-headerbar-content .acf-headerbar-title,\n  .acf-headerbar .acf-headerbar-content .acf-title-wrap {\n    flex: 1 1 100%;\n  }\n  .acf-headerbar .acf-headerbar-content .acf-title-wrap {\n    margin-top: 8px;\n  }\n}\n.acf-headerbar .acf-input-error {\n  border: 1px rgba(209, 55, 55, 0.5) solid !important;\n  box-shadow: 0px 0px 0px 3px rgba(209, 55, 55, 0.12), 0px 0px 0px rgba(255, 54, 54, 0.25) !important;\n  background-image: url(\"../../images/icons/icon-warning-alt-red.svg\");\n  background-position: right 10px top 50%;\n  background-size: 20px;\n  background-repeat: no-repeat;\n}\n.acf-headerbar .acf-input-error:focus {\n  outline: none !important;\n  border: 1px rgba(209, 55, 55, 0.8) solid !important;\n  box-shadow: 0px 0px 0px 3px rgba(209, 55, 55, 0.16), 0px 0px 0px rgba(255, 54, 54, 0.25) !important;\n}\n.acf-headerbar .acf-headerbar-title-field {\n  min-width: 320px;\n}\n@media screen and (max-width: 880px) {\n  .acf-headerbar .acf-headerbar-title-field {\n    min-width: 100%;\n  }\n}\n.acf-headerbar .acf-headerbar-actions {\n  display: flex;\n}\n.acf-headerbar .acf-headerbar-actions .acf-btn {\n  margin-left: 8px;\n}\n.acf-headerbar .acf-headerbar-actions .disabled {\n  background-color: #F2F4F7;\n  color: #98A2B3 !important;\n  border: 1px #D0D5DD solid;\n  cursor: default;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Edit Field Group Headerbar\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-headerbar-field-editor {\n  position: sticky;\n  top: 32px;\n  z-index: 1020;\n  margin-left: -20px;\n  width: calc(100% + 20px);\n}\n@media screen and (max-width: 880px) {\n  .acf-headerbar-field-editor {\n    position: relative;\n    top: 0;\n    width: 100%;\n    margin-left: 0;\n    padding-right: 8px;\n    padding-left: 8px;\n  }\n}\n@media screen and (max-width: 640px) {\n  .acf-headerbar-field-editor {\n    position: relative;\n    top: 46px;\n  }\n}\n@media screen and (max-width: 880px) {\n  .acf-headerbar-field-editor .acf-headerbar-inner {\n    flex-wrap: wrap;\n    justify-content: flex-start;\n    align-content: flex-start;\n    align-items: flex-start;\n    width: 100%;\n  }\n  .acf-headerbar-field-editor .acf-headerbar-inner .acf-page-title {\n    flex: 1 1 auto;\n  }\n  .acf-headerbar-field-editor .acf-headerbar-inner .acf-headerbar-actions {\n    flex: 1 1 100%;\n    margin-top: 8px;\n    gap: 8px;\n  }\n  .acf-headerbar-field-editor .acf-headerbar-inner .acf-headerbar-actions .acf-btn {\n    width: 100%;\n    display: inline-flex;\n    justify-content: center;\n    margin: 0;\n  }\n}\n.acf-headerbar-field-editor .acf-page-title {\n  margin-right: 16px;\n}\n\n.rtl .acf-headerbar,\n.rtl .acf-headerbar-field-editor {\n  margin-left: 0;\n  margin-right: -20px;\n}\n.rtl .acf-headerbar .acf-page-title,\n.rtl .acf-headerbar-field-editor .acf-page-title {\n  margin-left: 16px;\n  margin-right: 0;\n}\n.rtl .acf-headerbar .acf-headerbar-actions .acf-btn,\n.rtl .acf-headerbar-field-editor .acf-headerbar-actions .acf-btn {\n  margin-left: 0;\n  margin-right: 8px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  ACF Buttons\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-btn {\n  display: inline-flex;\n  align-items: center;\n  box-sizing: border-box;\n  min-height: 40px;\n  padding-top: 8px;\n  padding-right: 16px;\n  padding-bottom: 8px;\n  padding-left: 16px;\n  background-color: #0783BE;\n  border-radius: 6px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: rgba(16, 24, 40, 0.2);\n  text-decoration: none;\n  color: #fff !important;\n  transition: all 0.2s ease-in-out;\n  transition-property: background, border, box-shadow;\n}\n.acf-btn:disabled {\n  background-color: red;\n}\n.acf-btn:hover {\n  background-color: #066998;\n  color: #fff;\n  cursor: pointer;\n}\n.acf-btn.acf-btn-sm {\n  min-height: 32px;\n  padding-top: 4px;\n  padding-right: 12px;\n  padding-bottom: 4px;\n  padding-left: 12px;\n}\n.acf-btn.acf-btn-secondary {\n  background-color: transparent;\n  color: #0783BE !important;\n  border-color: #0783BE;\n}\n.acf-btn.acf-btn-secondary:hover {\n  background-color: #f3f9fc;\n}\n.acf-btn.acf-btn-tertiary {\n  background-color: transparent;\n  color: #667085 !important;\n  border-color: #D0D5DD;\n}\n.acf-btn.acf-btn-tertiary:hover {\n  color: #667085 !important;\n  border-color: #98A2B3;\n}\n.acf-btn.acf-btn-clear {\n  background-color: transparent;\n  color: #667085 !important;\n  border-color: transparent;\n}\n.acf-btn.acf-btn-clear:hover {\n  color: #0783BE !important;\n}\n.acf-btn.acf-btn-pro {\n  background: linear-gradient(90.52deg, #3E8BFF 0.44%, #A45CFF 113.3%);\n  background-size: 180% 80%;\n  background-position: 100% 0;\n  transition: background-position 0.5s;\n}\n.acf-btn.acf-btn-pro:hover {\n  background-position: 0 0;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Button icons\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-btn i.acf-icon {\n  width: 20px;\n  height: 20px;\n  -webkit-mask-size: 20px;\n  mask-size: 20px;\n  margin-right: 6px;\n  margin-left: -4px;\n}\n.acf-btn.acf-btn-sm i.acf-icon {\n  width: 16px;\n  height: 16px;\n  -webkit-mask-size: 16px;\n  mask-size: 16px;\n  margin-right: 6px;\n  margin-left: -2px;\n}\n\n.rtl .acf-btn i.acf-icon {\n  margin-right: -4px;\n  margin-left: 6px;\n}\n.rtl .acf-btn.acf-btn-sm i.acf-icon {\n  margin-right: -4px;\n  margin-left: 2px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Delete field group button\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-btn.acf-delete-field-group:hover {\n  background-color: #fbeded;\n  border-color: #D13737 !important;\n  color: #D13737 !important;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tIcon base styling\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type i.acf-icon,\n.post-type-acf-field-group i.acf-icon {\n  display: inline-flex;\n  width: 20px;\n  height: 20px;\n  background-color: currentColor;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tIcons\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n  /*--------------------------------------------------------------------------------------------\n  *\n  *\tInactive group icon\n  *\n  *--------------------------------------------------------------------------------------------*/\n}\n.acf-admin-page i.acf-field-setting-fc-delete, .acf-admin-page i.acf-field-setting-fc-duplicate {\n  box-sizing: border-box;\n  /* Auto layout */\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  padding: 8px;\n  cursor: pointer;\n  width: 32px;\n  height: 32px;\n  /* Base / White */\n  background: #FFFFFF;\n  /* Gray/300 */\n  border: 1px solid #D0D5DD;\n  /* Elevation/01 */\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  border-radius: 6px;\n  /* Inside auto layout */\n  flex: none;\n  order: 0;\n  flex-grow: 0;\n}\n.acf-admin-page i.acf-icon-plus {\n  -webkit-mask-image: url(\"../../images/icons/icon-add.svg\");\n  mask-image: url(\"../../images/icons/icon-add.svg\");\n}\n.acf-admin-page i.acf-icon-stars {\n  -webkit-mask-image: url(\"../../images/icons/icon-stars.svg\");\n  mask-image: url(\"../../images/icons/icon-stars.svg\");\n}\n.acf-admin-page i.acf-icon-help {\n  -webkit-mask-image: url(\"../../images/icons/icon-help.svg\");\n  mask-image: url(\"../../images/icons/icon-help.svg\");\n}\n.acf-admin-page i.acf-icon-key {\n  -webkit-mask-image: url(\"../../images/icons/icon-key.svg\");\n  mask-image: url(\"../../images/icons/icon-key.svg\");\n}\n.acf-admin-page i.acf-icon-regenerate {\n  -webkit-mask-image: url(\"../../images/icons/icon-regenerate.svg\");\n  mask-image: url(\"../../images/icons/icon-regenerate.svg\");\n}\n.acf-admin-page i.acf-icon-trash, .acf-admin-page button.acf-icon-trash {\n  -webkit-mask-image: url(\"../../images/icons/icon-trash.svg\");\n  mask-image: url(\"../../images/icons/icon-trash.svg\");\n}\n.acf-admin-page i.acf-icon-extended-menu, .acf-admin-page button.acf-icon-extended-menu {\n  -webkit-mask-image: url(\"../../images/icons/icon-extended-menu.svg\");\n  mask-image: url(\"../../images/icons/icon-extended-menu.svg\");\n}\n.acf-admin-page i.acf-icon.-duplicate, .acf-admin-page button.acf-icon-duplicate {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-clone.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-clone.svg\");\n}\n.acf-admin-page i.acf-icon.-duplicate:before, .acf-admin-page i.acf-icon.-duplicate:after, .acf-admin-page button.acf-icon-duplicate:before, .acf-admin-page button.acf-icon-duplicate:after {\n  content: none;\n}\n.acf-admin-page i.acf-icon-arrow-right {\n  -webkit-mask-image: url(\"../../images/icons/icon-arrow-right.svg\");\n  mask-image: url(\"../../images/icons/icon-arrow-right.svg\");\n}\n.acf-admin-page i.acf-icon-arrow-up-right {\n  -webkit-mask-image: url(\"../../images/icons/icon-arrow-up-right.svg\");\n  mask-image: url(\"../../images/icons/icon-arrow-up-right.svg\");\n}\n.acf-admin-page i.acf-icon-arrow-left {\n  -webkit-mask-image: url(\"../../images/icons/icon-arrow-left.svg\");\n  mask-image: url(\"../../images/icons/icon-arrow-left.svg\");\n}\n.acf-admin-page i.acf-icon-chevron-right,\n.acf-admin-page .acf-icon.-right {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n}\n.acf-admin-page i.acf-icon-chevron-left,\n.acf-admin-page .acf-icon.-left {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-left.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-left.svg\");\n}\n.acf-admin-page i.acf-icon-key-solid {\n  -webkit-mask-image: url(\"../../images/icons/icon-key-solid.svg\");\n  mask-image: url(\"../../images/icons/icon-key-solid.svg\");\n}\n.acf-admin-page i.acf-icon-globe,\n.acf-admin-page .acf-icon.-globe {\n  -webkit-mask-image: url(\"../../images/icons/icon-globe.svg\");\n  mask-image: url(\"../../images/icons/icon-globe.svg\");\n}\n.acf-admin-page i.acf-icon-image,\n.acf-admin-page .acf-icon.-picture {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-image.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-image.svg\");\n}\n.acf-admin-page i.acf-icon-warning {\n  -webkit-mask-image: url(\"../../images/icons/icon-warning-alt.svg\");\n  mask-image: url(\"../../images/icons/icon-warning-alt.svg\");\n}\n.acf-admin-page i.acf-icon-warning-red {\n  -webkit-mask-image: url(\"../../images/icons/icon-warning-alt-red.svg\");\n  mask-image: url(\"../../images/icons/icon-warning-alt-red.svg\");\n}\n.acf-admin-page i.acf-icon-dots-grid {\n  -webkit-mask-image: url(\"../../images/icons/icon-dots-grid.svg\");\n  mask-image: url(\"../../images/icons/icon-dots-grid.svg\");\n}\n.acf-admin-page i.acf-icon-play {\n  -webkit-mask-image: url(\"../../images/icons/icon-play.svg\");\n  mask-image: url(\"../../images/icons/icon-play.svg\");\n}\n.acf-admin-page i.acf-icon-lock {\n  -webkit-mask-image: url(\"../../images/icons/icon-lock.svg\");\n  mask-image: url(\"../../images/icons/icon-lock.svg\");\n}\n.acf-admin-page i.acf-icon-document {\n  -webkit-mask-image: url(\"../../images/icons/icon-document.svg\");\n  mask-image: url(\"../../images/icons/icon-document.svg\");\n}\n.acf-admin-page .post-type-acf-field-group .post-state,\n.acf-admin-page .acf-internal-post-type .post-state {\n  font-weight: normal;\n}\n.acf-admin-page .post-type-acf-field-group .post-state .dashicons.dashicons-hidden,\n.acf-admin-page .acf-internal-post-type .post-state .dashicons.dashicons-hidden {\n  display: inline-flex;\n  width: 18px;\n  height: 18px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: 18px;\n  mask-size: 18px;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-hidden.svg\");\n  mask-image: url(\"../../images/icons/icon-hidden.svg\");\n}\n.acf-admin-page .post-type-acf-field-group .post-state .dashicons.dashicons-hidden:before,\n.acf-admin-page .acf-internal-post-type .post-state .dashicons.dashicons-hidden:before {\n  display: none;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tEdit field group page postbox header icons\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-field-group-fields .postbox-header h2,\n#acf-field-group-fields .postbox-header h3,\n#acf-field-group-fields .acf-sub-field-list-header h2,\n#acf-field-group-fields .acf-sub-field-list-header h3,\n#acf-field-group-options .postbox-header h2,\n#acf-field-group-options .postbox-header h3,\n#acf-field-group-options .acf-sub-field-list-header h2,\n#acf-field-group-options .acf-sub-field-list-header h3,\n#acf-advanced-settings .postbox-header h2,\n#acf-advanced-settings .postbox-header h3,\n#acf-advanced-settings .acf-sub-field-list-header h2,\n#acf-advanced-settings .acf-sub-field-list-header h3 {\n  display: inline-flex;\n  justify-content: flex-start;\n  align-content: stretch;\n  align-items: center;\n}\n#acf-field-group-fields .postbox-header h2:before,\n#acf-field-group-fields .postbox-header h3:before,\n#acf-field-group-fields .acf-sub-field-list-header h2:before,\n#acf-field-group-fields .acf-sub-field-list-header h3:before,\n#acf-field-group-options .postbox-header h2:before,\n#acf-field-group-options .postbox-header h3:before,\n#acf-field-group-options .acf-sub-field-list-header h2:before,\n#acf-field-group-options .acf-sub-field-list-header h3:before,\n#acf-advanced-settings .postbox-header h2:before,\n#acf-advanced-settings .postbox-header h3:before,\n#acf-advanced-settings .acf-sub-field-list-header h2:before,\n#acf-advanced-settings .acf-sub-field-list-header h3:before {\n  content: \"\";\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  margin-right: 8px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n}\n\n.rtl #acf-field-group-fields .postbox-header h2:before,\n.rtl #acf-field-group-fields .postbox-header h3:before,\n.rtl #acf-field-group-fields .acf-sub-field-list-header h2:before,\n.rtl #acf-field-group-fields .acf-sub-field-list-header h3:before,\n.rtl #acf-field-group-options .postbox-header h2:before,\n.rtl #acf-field-group-options .postbox-header h3:before,\n.rtl #acf-field-group-options .acf-sub-field-list-header h2:before,\n.rtl #acf-field-group-options .acf-sub-field-list-header h3:before {\n  margin-right: 0;\n  margin-left: 8px;\n}\n\n#acf-field-group-fields .postbox-header h2:before,\nh3.acf-sub-field-list-title:before,\n.acf-link-field-groups-popup h3:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-fields.svg\");\n  mask-image: url(\"../../images/icons/icon-fields.svg\");\n}\n\n.acf-create-options-page-popup h3:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-sliders.svg\");\n  mask-image: url(\"../../images/icons/icon-sliders.svg\");\n}\n\n#acf-field-group-options .postbox-header h2:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-settings.svg\");\n  mask-image: url(\"../../images/icons/icon-settings.svg\");\n}\n\n.acf-field-setting-fc_layout .acf-field-settings-fc_head label:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-layout.svg\");\n  mask-image: url(\"../../images/icons/icon-layout.svg\");\n}\n\n.acf-admin-single-post-type #acf-advanced-settings .postbox-header h2:before,\n.acf-admin-single-taxonomy #acf-advanced-settings .postbox-header h2:before,\n.acf-admin-single-options-page #acf-advanced-settings .postbox-header h2:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-post-type.svg\");\n  mask-image: url(\"../../images/icons/icon-post-type.svg\");\n}\n\n.acf-field-setting-fc_layout .acf-field-settings-fc_head:hover .reorder-layout:before {\n  width: 20px;\n  height: 11px;\n  background-color: #475467 !important;\n  -webkit-mask-image: url(\"../../images/icons/icon-draggable.svg\");\n  mask-image: url(\"../../images/icons/icon-draggable.svg\");\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPostbox expand / collapse icon\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .postbox-header .handle-actions,\n.post-type-acf-field-group #acf-field-group-fields .postbox-header .handle-actions,\n.post-type-acf-field-group #acf-field-group-options .postbox-header .handle-actions,\n.post-type-acf-field-group .postbox .postbox-header .handle-actions,\n.acf-admin-single-post-type #acf-advanced-settings .postbox-header .handle-actions,\n.acf-admin-single-taxonomy #acf-advanced-settings .postbox-header .handle-actions,\n.acf-admin-single-options-page #acf-advanced-settings .postbox-header .handle-actions {\n  display: flex;\n}\n.post-type-acf-field-group .postbox-header .handle-actions .toggle-indicator:before,\n.post-type-acf-field-group #acf-field-group-fields .postbox-header .handle-actions .toggle-indicator:before,\n.post-type-acf-field-group #acf-field-group-options .postbox-header .handle-actions .toggle-indicator:before,\n.post-type-acf-field-group .postbox .postbox-header .handle-actions .toggle-indicator:before,\n.acf-admin-single-post-type #acf-advanced-settings .postbox-header .handle-actions .toggle-indicator:before,\n.acf-admin-single-taxonomy #acf-advanced-settings .postbox-header .handle-actions .toggle-indicator:before,\n.acf-admin-single-options-page #acf-advanced-settings .postbox-header .handle-actions .toggle-indicator:before {\n  content: \"\";\n  display: inline-flex;\n  width: 20px;\n  height: 20px;\n  background-color: currentColor;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n}\n.post-type-acf-field-group.closed .postbox-header .handle-actions .toggle-indicator:before,\n.post-type-acf-field-group #acf-field-group-fields.closed .postbox-header .handle-actions .toggle-indicator:before,\n.post-type-acf-field-group #acf-field-group-options.closed .postbox-header .handle-actions .toggle-indicator:before,\n.post-type-acf-field-group .postbox.closed .postbox-header .handle-actions .toggle-indicator:before,\n.acf-admin-single-post-type #acf-advanced-settings.closed .postbox-header .handle-actions .toggle-indicator:before,\n.acf-admin-single-taxonomy #acf-advanced-settings.closed .postbox-header .handle-actions .toggle-indicator:before,\n.acf-admin-single-options-page #acf-advanced-settings.closed .postbox-header .handle-actions .toggle-indicator:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Tools & updates page heading icons\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-admin-tool-export h2,\n.post-type-acf-field-group #acf-admin-tool-export h3,\n.post-type-acf-field-group #acf-admin-tool-import h2,\n.post-type-acf-field-group #acf-admin-tool-import h3,\n.post-type-acf-field-group #acf-license-information h2,\n.post-type-acf-field-group #acf-license-information h3,\n.post-type-acf-field-group #acf-update-information h2,\n.post-type-acf-field-group #acf-update-information h3 {\n  display: inline-flex;\n  justify-content: flex-start;\n  align-content: stretch;\n  align-items: center;\n}\n.post-type-acf-field-group #acf-admin-tool-export h2:before,\n.post-type-acf-field-group #acf-admin-tool-export h3:before,\n.post-type-acf-field-group #acf-admin-tool-import h2:before,\n.post-type-acf-field-group #acf-admin-tool-import h3:before,\n.post-type-acf-field-group #acf-license-information h2:before,\n.post-type-acf-field-group #acf-license-information h3:before,\n.post-type-acf-field-group #acf-update-information h2:before,\n.post-type-acf-field-group #acf-update-information h3:before {\n  content: \"\";\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  margin-right: 8px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n}\n.post-type-acf-field-group.rtl #acf-admin-tool-export h2:before,\n.post-type-acf-field-group.rtl #acf-admin-tool-export h3:before,\n.post-type-acf-field-group.rtl #acf-admin-tool-import h2:before,\n.post-type-acf-field-group.rtl #acf-admin-tool-import h3:before,\n.post-type-acf-field-group.rtl #acf-license-information h2:before,\n.post-type-acf-field-group.rtl #acf-license-information h3:before,\n.post-type-acf-field-group.rtl #acf-update-information h2:before,\n.post-type-acf-field-group.rtl #acf-update-information h3:before {\n  margin-right: 0;\n  margin-left: 8px;\n}\n\n.post-type-acf-field-group #acf-admin-tool-export h2:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-export.svg\");\n  mask-image: url(\"../../images/icons/icon-export.svg\");\n}\n\n.post-type-acf-field-group #acf-admin-tool-import h2:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-import.svg\");\n  mask-image: url(\"../../images/icons/icon-import.svg\");\n}\n\n.post-type-acf-field-group #acf-license-information h3:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-key.svg\");\n  mask-image: url(\"../../images/icons/icon-key.svg\");\n}\n\n.post-type-acf-field-group #acf-update-information h3:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-info.svg\");\n  mask-image: url(\"../../images/icons/icon-info.svg\");\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tAdmin field icons\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-single-field-group .acf-input .acf-icon {\n  width: 18px;\n  height: 18px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tField type icon base styling\n*\n*--------------------------------------------------------------------------------------------*/\n.field-type-icon {\n  box-sizing: border-box;\n  display: inline-flex;\n  align-content: center;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  width: 24px;\n  height: 24px;\n  top: -4px;\n  background-color: #EBF5FA;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #A5D2E7;\n  border-radius: 100%;\n}\n.field-type-icon:before {\n  content: \"\";\n  width: 14px;\n  height: 14px;\n  position: relative;\n  background-color: #0783BE;\n  -webkit-mask-size: cover;\n  mask-size: cover;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-default.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-default.svg\");\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tField type icons\n*\n*--------------------------------------------------------------------------------------------*/\n.field-type-icon.field-type-icon-text:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-text.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-text.svg\");\n}\n\n.field-type-icon.field-type-icon-textarea:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-textarea.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-textarea.svg\");\n}\n\n.field-type-icon.field-type-icon-textarea:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-textarea.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-textarea.svg\");\n}\n\n.field-type-icon.field-type-icon-number:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-number.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-number.svg\");\n}\n\n.field-type-icon.field-type-icon-range:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-range.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-range.svg\");\n}\n\n.field-type-icon.field-type-icon-email:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-email.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-email.svg\");\n}\n\n.field-type-icon.field-type-icon-url:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-url.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-url.svg\");\n}\n\n.field-type-icon.field-type-icon-password:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-password.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-password.svg\");\n}\n\n.field-type-icon.field-type-icon-image:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-image.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-image.svg\");\n}\n\n.field-type-icon.field-type-icon-file:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-file.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-file.svg\");\n}\n\n.field-type-icon.field-type-icon-wysiwyg:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-wysiwyg.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-wysiwyg.svg\");\n}\n\n.field-type-icon.field-type-icon-oembed:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-oembed.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-oembed.svg\");\n}\n\n.field-type-icon.field-type-icon-gallery:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-gallery.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-gallery.svg\");\n}\n\n.field-type-icon.field-type-icon-select:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-select.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-select.svg\");\n}\n\n.field-type-icon.field-type-icon-checkbox:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-checkbox.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-checkbox.svg\");\n}\n\n.field-type-icon.field-type-icon-radio:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-radio.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-radio.svg\");\n}\n\n.field-type-icon.field-type-icon-button-group:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-button-group.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-button-group.svg\");\n}\n\n.field-type-icon.field-type-icon-true-false:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-true-false.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-true-false.svg\");\n}\n\n.field-type-icon.field-type-icon-link:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-link.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-link.svg\");\n}\n\n.field-type-icon.field-type-icon-post-object:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-post-object.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-post-object.svg\");\n}\n\n.field-type-icon.field-type-icon-page-link:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-page-link.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-page-link.svg\");\n}\n\n.field-type-icon.field-type-icon-relationship:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-relationship.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-relationship.svg\");\n}\n\n.field-type-icon.field-type-icon-taxonomy:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-taxonomy.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-taxonomy.svg\");\n}\n\n.field-type-icon.field-type-icon-user:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-user.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-user.svg\");\n}\n\n.field-type-icon.field-type-icon-google-map:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-google-map.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-google-map.svg\");\n}\n\n.field-type-icon.field-type-icon-date-picker:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-date-picker.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-date-picker.svg\");\n}\n\n.field-type-icon.field-type-icon-date-time-picker:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-date-time-picker.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-date-time-picker.svg\");\n}\n\n.field-type-icon.field-type-icon-time-picker:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-time-picker.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-time-picker.svg\");\n}\n\n.field-type-icon.field-type-icon-color-picker:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-color-picker.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-color-picker.svg\");\n}\n\n.field-type-icon.field-type-icon-message:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-message.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-message.svg\");\n}\n\n.field-type-icon.field-type-icon-accordion:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-accordion.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-accordion.svg\");\n}\n\n.field-type-icon.field-type-icon-tab:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-tab.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-tab.svg\");\n}\n\n.field-type-icon.field-type-icon-group:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-group.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-group.svg\");\n}\n\n.field-type-icon.field-type-icon-repeater:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-repeater.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-repeater.svg\");\n}\n\n.field-type-icon.field-type-icon-flexible-content:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-flexible-content.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-flexible-content.svg\");\n}\n\n.field-type-icon.field-type-icon-clone:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-clone.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-clone.svg\");\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Tools page layout\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-admin-tools .postbox-header {\n  display: none;\n}\n#acf-admin-tools .acf-meta-box-wrap.-grid {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n}\n#acf-admin-tools .acf-meta-box-wrap.-grid .postbox {\n  width: 100%;\n  clear: none;\n  float: none;\n  margin-bottom: 0;\n}\n@media screen and (max-width: 880px) {\n  #acf-admin-tools .acf-meta-box-wrap.-grid .postbox {\n    flex: 1 1 100%;\n  }\n}\n#acf-admin-tools .acf-meta-box-wrap.-grid .postbox:nth-child(odd) {\n  margin-left: 0;\n}\n#acf-admin-tools .meta-box-sortables {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  grid-template-rows: repeat(1, 1fr);\n  grid-column-gap: 32px;\n  grid-row-gap: 32px;\n}\n@media screen and (max-width: 880px) {\n  #acf-admin-tools .meta-box-sortables {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: flex-start;\n    align-content: flex-start;\n    align-items: center;\n    grid-column-gap: 8px;\n    grid-row-gap: 8px;\n  }\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Tools export pages\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-admin-tools.tool-export .inside {\n  margin: 0;\n}\n#acf-admin-tools.tool-export .acf-postbox-header {\n  margin-bottom: 24px;\n}\n#acf-admin-tools.tool-export .acf-postbox-main {\n  border: none;\n  margin: 0;\n  padding-top: 0;\n  padding-right: 24px;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n#acf-admin-tools.tool-export .acf-postbox-columns {\n  margin-top: 0;\n  margin-right: 280px;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding: 0;\n}\n#acf-admin-tools.tool-export .acf-postbox-columns .acf-postbox-side {\n  padding: 0;\n}\n#acf-admin-tools.tool-export .acf-postbox-columns .acf-postbox-side .acf-panel {\n  margin: 0;\n  padding: 0;\n}\n#acf-admin-tools.tool-export .acf-postbox-columns .acf-postbox-side:before {\n  display: none;\n}\n#acf-admin-tools.tool-export .acf-postbox-columns .acf-postbox-side .acf-btn {\n  display: block;\n  width: 100%;\n  text-align: center;\n}\n#acf-admin-tools.tool-export .meta-box-sortables {\n  display: block;\n}\n#acf-admin-tools.tool-export .acf-panel {\n  border: none;\n}\n#acf-admin-tools.tool-export .acf-panel h3 {\n  margin: 0;\n  padding: 0;\n  color: #344054;\n}\n#acf-admin-tools.tool-export .acf-panel h3:before {\n  display: none;\n}\n#acf-admin-tools.tool-export .acf-checkbox-list {\n  margin-top: 16px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #D0D5DD;\n  border-radius: 6px;\n}\n#acf-admin-tools.tool-export .acf-checkbox-list li {\n  display: inline-flex;\n  box-sizing: border-box;\n  width: 100%;\n  height: 48px;\n  align-items: center;\n  margin: 0;\n  padding-right: 12px;\n  padding-left: 12px;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n#acf-admin-tools.tool-export .acf-checkbox-list li:last-child {\n  border-bottom: none;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Updates layout\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-settings-wrap.acf-updates {\n  display: flex;\n  flex-direction: row;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.custom-fields_page_acf-settings-updates .acf-admin-notice,\n.custom-fields_page_acf-settings-updates .acf-upgrade-notice,\n.custom-fields_page_acf-settings-updates .notice {\n  flex: 1 1 100%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  ACF Box\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-settings-wrap.acf-updates .acf-box {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n}\n.acf-settings-wrap.acf-updates .acf-box .inner {\n  padding-top: 24px;\n  padding-right: 24px;\n  padding-bottom: 24px;\n  padding-left: 24px;\n}\n@media screen and (max-width: 880px) {\n  .acf-settings-wrap.acf-updates .acf-box {\n    flex: 1 1 100%;\n  }\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Notices\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-settings-wrap.acf-updates .acf-admin-notice {\n  flex: 1 1 100%;\n  margin-top: 16px;\n  margin-right: 0;\n  margin-left: 0;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* License information\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-license-information {\n  flex: 1 1 65%;\n  margin-right: 32px;\n}\n@media screen and (max-width: 1024px) {\n  #acf-license-information {\n    margin-right: 0;\n    margin-bottom: 32px;\n  }\n}\n#acf-license-information .acf-activation-form {\n  margin-top: 24px;\n}\n#acf-license-information label {\n  font-weight: 500;\n}\n#acf-license-information .acf-input-wrap {\n  margin-top: 8px;\n  margin-bottom: 24px;\n}\n#acf-license-information #acf_pro_license {\n  width: 100%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Update information table\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-update-information {\n  flex: 1 1 35%;\n  max-width: calc(35% - 32px);\n}\n#acf-update-information .form-table th,\n#acf-update-information .form-table td {\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 24px;\n  padding-left: 0;\n  color: #344054;\n}\n#acf-update-information .acf-update-changelog {\n  margin-top: 8px;\n  margin-bottom: 24px;\n  padding-top: 8px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n  color: #344054;\n}\n#acf-update-information .acf-update-changelog h4 {\n  margin-bottom: 0;\n}\n#acf-update-information .acf-update-changelog p {\n  margin-top: 0;\n  margin-bottom: 16px;\n}\n#acf-update-information .acf-update-changelog p:last-of-type {\n  margin-bottom: 0;\n}\n#acf-update-information .acf-update-changelog p em {\n  color: #667085;\n}\n#acf-update-information .acf-btn {\n  display: inline-flex;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tHeader pro upgrade button\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn {\n  display: inline-flex;\n  align-items: center;\n  align-self: stretch;\n  padding-top: 0;\n  padding-right: 16px;\n  padding-bottom: 0;\n  padding-left: 16px;\n  background: linear-gradient(90.52deg, #3E8BFF 0.44%, #A45CFF 113.3%);\n  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.16);\n  background-size: 180% 80%;\n  background-position: 100% 0;\n  transition: background-position 0.5s;\n  border-radius: 6px;\n  text-decoration: none;\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn {\n    display: none;\n  }\n}\n.acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn:hover {\n  background-position: 0 0;\n}\n.acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn:focus {\n  border: none;\n  outline: none;\n  box-shadow: none;\n}\n.acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn p {\n  margin: 0;\n  padding-top: 8px;\n  padding-bottom: 8px;\n  font-weight: normal;\n  text-transform: none;\n  color: #fff;\n}\n.acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn .acf-icon {\n  width: 18px;\n  height: 18px;\n  margin-right: 6px;\n  margin-left: -2px;\n  background-color: #F9FAFB;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Upsell block\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-page #tmpl-acf-field-group-pro-features,\n.acf-admin-page #acf-field-group-pro-features {\n  display: none;\n  align-items: center;\n  min-height: 120px;\n  background-color: #121833;\n  background-image: url(../../images/pro-upgrade-grid-bg.svg), url(../../images/pro-upgrade-overlay.svg);\n  background-repeat: repeat, no-repeat;\n  background-size: 1224px, 1880px;\n  background-position: left top, -520px -680px;\n  color: #EAECF0;\n  border-radius: 8px;\n  margin-top: 24px;\n  margin-bottom: 24px;\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page #tmpl-acf-field-group-pro-features,\n  .acf-admin-page #acf-field-group-pro-features {\n    background-size: 1024px, 980px;\n    background-position: left top, -500px -200px;\n  }\n}\n@media screen and (max-width: 1200px) {\n  .acf-admin-page #tmpl-acf-field-group-pro-features,\n  .acf-admin-page #acf-field-group-pro-features {\n    background-size: 1024px, 1880px;\n    background-position: left top, -520px -300px;\n  }\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .postbox-header,\n.acf-admin-page #acf-field-group-pro-features .postbox-header {\n  display: none;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .inside,\n.acf-admin-page #acf-field-group-pro-features .inside {\n  width: 100%;\n  border: none;\n  padding: 0;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper {\n  display: flex;\n  justify-content: center;\n  align-content: stretch;\n  align-items: center;\n  gap: 96px;\n  height: 358px;\n  max-width: 950px;\n  margin: 0 auto;\n  padding: 0 35px;\n}\n@media screen and (max-width: 1200px) {\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper {\n    gap: 48px;\n  }\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper {\n    gap: 0;\n  }\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title,\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title-sm,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title-sm {\n  font-weight: 590;\n  line-height: 150%;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title .acf-pro-label,\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title-sm .acf-pro-label,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title .acf-pro-label,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title-sm .acf-pro-label {\n  font-weight: normal;\n  margin-top: -4px;\n  margin-left: 2px;\n  vertical-align: middle;\n  height: 22px;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title-sm,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title-sm {\n  display: none !important;\n  font-size: 18px;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title-sm .acf-pro-label,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title-sm .acf-pro-label {\n  font-size: 10px;\n  height: 20px;\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title-sm,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title-sm {\n    width: 100%;\n    text-align: center;\n  }\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper {\n    flex-direction: column;\n    flex-wrap: wrap;\n    justify-content: flex-start;\n    align-content: flex-start;\n    align-items: flex-start;\n    padding: 32px 32px 0 32px;\n    height: unset;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title-sm,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-title-sm {\n    display: block !important;\n    margin-bottom: 24px;\n  }\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content {\n  display: flex;\n  flex-direction: column;\n  width: 416px;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content .acf-field-group-pro-features-desc,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content .acf-field-group-pro-features-desc {\n  margin-top: 8px;\n  margin-bottom: 24px;\n  font-size: 15px;\n  font-weight: 300;\n  color: #D0D5DD;\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content {\n    width: 100%;\n    order: 1;\n    margin-right: 0;\n    margin-bottom: 8px;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content .acf-field-group-pro-features-title,\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content .acf-field-group-pro-features-desc,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content .acf-field-group-pro-features-title,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content .acf-field-group-pro-features-desc {\n    display: none !important;\n  }\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-actions,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-actions {\n  display: flex;\n  flex-direction: row;\n  align-items: flex-start;\n  min-width: 160px;\n  gap: 12px;\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-actions,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-actions {\n    justify-content: flex-start;\n    flex-direction: column;\n    margin-bottom: 24px;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-actions a,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-actions a {\n    justify-content: center;\n    text-align: center;\n    width: 100%;\n  }\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid {\n  display: flex;\n  flex-direction: row;\n  flex-wrap: wrap;\n  gap: 16px;\n  width: 416px;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  width: 128px;\n  height: 124px;\n  background: rgba(255, 255, 255, 0.08);\n  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04), 0px 8px 16px rgba(0, 0, 0, 0.08), inset 0 0 0 1px rgba(255, 255, 255, 0.08);\n  backdrop-filter: blur(6px);\n  border-radius: 8px;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-icon,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-icon {\n  border: none;\n  background: none;\n  width: 24px;\n  opacity: 0.8;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-icon::before,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-icon::before {\n  background-color: #fff;\n  width: 20px;\n  height: 20px;\n}\n@media screen and (max-width: 1200px) {\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-icon::before,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-icon::before {\n    width: 18px;\n    height: 18px;\n  }\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .pro-feature-blocks::before,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .pro-feature-blocks::before {\n  -webkit-mask-image: url(\"../../images/icons/icon-extended-menu.svg\");\n  mask-image: url(\"../../images/icons/icon-extended-menu.svg\");\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .pro-feature-options-pages::before,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .pro-feature-options-pages::before {\n  -webkit-mask-image: url(\"../../images/icons/icon-settings.svg\");\n  mask-image: url(\"../../images/icons/icon-settings.svg\");\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-label,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-label {\n  margin-top: 4px;\n  font-size: 13px;\n  font-weight: 300;\n  text-align: center;\n  color: #fff;\n}\n@media screen and (max-width: 1200px) {\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid {\n    flex-direction: column;\n    gap: 8px;\n    width: 288px;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature {\n    width: 100%;\n    height: 40px;\n    flex-direction: row;\n    justify-content: unset;\n    gap: 8px;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-icon,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-icon {\n    position: initial;\n    margin-left: 16px;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-label,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-label {\n    margin-top: 0;\n  }\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid {\n    gap: 0;\n    width: 100%;\n    height: auto;\n    margin-bottom: 16px;\n    flex-direction: unset;\n    flex-wrap: wrap;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature {\n    flex: 1 0 50%;\n    margin-bottom: 8px;\n    width: auto;\n    height: auto;\n    justify-content: center;\n    background: none;\n    box-shadow: none;\n    backdrop-filter: none;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-label,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-label {\n    margin-left: 2px;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-icon,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-icon {\n    position: initial;\n    margin-left: 0;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-icon:before,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-icon:before {\n    height: 16px;\n    width: 16px;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-label,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-grid .acf-field-group-pro-feature .field-type-label {\n    font-size: 12px;\n    margin-top: 0;\n  }\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features h1,\n.acf-admin-page #acf-field-group-pro-features h1 {\n  margin-top: 0;\n  margin-bottom: 4px;\n  padding-top: 0;\n  padding-bottom: 0;\n  font-weight: bold;\n  color: #F9FAFB;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features h1 .acf-icon,\n.acf-admin-page #acf-field-group-pro-features h1 .acf-icon {\n  margin-right: 8px;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-btn,\n.acf-admin-page #acf-field-group-pro-features .acf-btn {\n  display: inline-flex;\n  background-color: rgba(255, 255, 255, 0.1);\n  border: none;\n  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04), 0px 4px 8px rgba(0, 0, 0, 0.06), inset 0 0 0 1px rgba(255, 255, 255, 0.16);\n  backdrop-filter: blur(6px);\n  padding: 8px 24px;\n  height: 48px;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-btn:hover,\n.acf-admin-page #acf-field-group-pro-features .acf-btn:hover {\n  background-color: rgba(255, 255, 255, 0.2);\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-btn .acf-icon,\n.acf-admin-page #acf-field-group-pro-features .acf-btn .acf-icon {\n  margin-right: -2px;\n  margin-left: 6px;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-btn.acf-pro-features-upgrade,\n.acf-admin-page #acf-field-group-pro-features .acf-btn.acf-pro-features-upgrade {\n  background: linear-gradient(90.52deg, #3E8BFF 0.44%, #A45CFF 113.3%);\n  background-size: 160% 80%;\n  background-position: 100% 0;\n  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04), 0px 4px 8px rgba(0, 0, 0, 0.06), inset 0 0 0 1px rgba(255, 255, 255, 0.16);\n  border-radius: 6px;\n  transition: background-position 0.5s;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-btn.acf-pro-features-upgrade:hover,\n.acf-admin-page #acf-field-group-pro-features .acf-btn.acf-pro-features-upgrade:hover {\n  background-position: 0 0;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap {\n  height: 48px;\n  background: rgba(16, 24, 40, 0.4);\n  backdrop-filter: blur(6px);\n  border-top: 1px solid rgba(255, 255, 255, 0.08);\n  border-bottom-left-radius: 8px;\n  border-bottom-right-radius: 8px;\n  color: #98A2B3;\n  font-size: 13px;\n  font-weight: 300;\n  padding: 0 35px;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap .acf-field-group-pro-features-footer,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap .acf-field-group-pro-features-footer {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  max-width: 950px;\n  height: 48px;\n  margin: 0 auto;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap .acf-field-group-pro-features-wpengine-logo,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap .acf-field-group-pro-features-wpengine-logo {\n  height: 16px;\n  vertical-align: middle;\n  margin-top: -2px;\n  margin-left: 3px;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap a,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap a {\n  color: #98A2B3;\n  text-decoration: none;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap a:hover,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap a:hover {\n  color: #D0D5DD;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap a .acf-icon,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap a .acf-icon {\n  width: 18px;\n  height: 18px;\n  margin-left: 4px;\n}\n.acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap .acf-more-tools-from-wpengine a,\n.acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap .acf-more-tools-from-wpengine a {\n  display: inline-flex;\n  align-items: center;\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap {\n    height: 70px;\n    font-size: 12px;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap .acf-more-tools-from-wpengine,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap .acf-more-tools-from-wpengine {\n    display: none;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap .acf-field-group-pro-features-footer,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap .acf-field-group-pro-features-footer {\n    justify-content: center;\n    height: 70px;\n  }\n  .acf-admin-page #tmpl-acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap .acf-field-group-pro-features-footer .acf-field-group-pro-features-wpengine-logo,\n  .acf-admin-page #acf-field-group-pro-features .acf-field-group-pro-features-footer-wrap .acf-field-group-pro-features-footer .acf-field-group-pro-features-wpengine-logo {\n    clear: both;\n    margin: 6px auto 0 auto;\n    display: block;\n  }\n}\n\n.acf-no-field-groups #tmpl-acf-field-group-pro-features,\n.acf-no-post-types #tmpl-acf-field-group-pro-features,\n.acf-no-taxonomies #tmpl-acf-field-group-pro-features {\n  margin-top: 0;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPost type & taxonomies styles\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-single-post-type label[for=acf-basic-settings-hide],\n.acf-admin-single-taxonomy label[for=acf-basic-settings-hide],\n.acf-admin-single-options-page label[for=acf-basic-settings-hide] {\n  display: none;\n}\n.acf-admin-single-post-type fieldset.columns-prefs,\n.acf-admin-single-taxonomy fieldset.columns-prefs,\n.acf-admin-single-options-page fieldset.columns-prefs {\n  display: none;\n}\n.acf-admin-single-post-type #acf-basic-settings .postbox-header,\n.acf-admin-single-taxonomy #acf-basic-settings .postbox-header,\n.acf-admin-single-options-page #acf-basic-settings .postbox-header {\n  display: none;\n}\n.acf-admin-single-post-type .postbox-container,\n.acf-admin-single-post-type .notice,\n.acf-admin-single-taxonomy .postbox-container,\n.acf-admin-single-taxonomy .notice,\n.acf-admin-single-options-page .postbox-container,\n.acf-admin-single-options-page .notice {\n  max-width: 1440px;\n  clear: left;\n}\n.acf-admin-single-post-type #post-body-content,\n.acf-admin-single-taxonomy #post-body-content,\n.acf-admin-single-options-page #post-body-content {\n  margin: 0;\n}\n.acf-admin-single-post-type .postbox .inside,\n.acf-admin-single-post-type .acf-box .inside,\n.acf-admin-single-taxonomy .postbox .inside,\n.acf-admin-single-taxonomy .acf-box .inside,\n.acf-admin-single-options-page .postbox .inside,\n.acf-admin-single-options-page .acf-box .inside {\n  padding-top: 48px;\n  padding-right: 48px;\n  padding-bottom: 48px;\n  padding-left: 48px;\n}\n.acf-admin-single-post-type #acf-advanced-settings.postbox .inside,\n.acf-admin-single-taxonomy #acf-advanced-settings.postbox .inside,\n.acf-admin-single-options-page #acf-advanced-settings.postbox .inside {\n  padding-bottom: 24px;\n}\n.acf-admin-single-post-type .postbox-container .meta-box-sortables #acf-basic-settings .inside,\n.acf-admin-single-taxonomy .postbox-container .meta-box-sortables #acf-basic-settings .inside,\n.acf-admin-single-options-page .postbox-container .meta-box-sortables #acf-basic-settings .inside {\n  border: none;\n}\n.acf-admin-single-post-type .acf-input-wrap,\n.acf-admin-single-taxonomy .acf-input-wrap,\n.acf-admin-single-options-page .acf-input-wrap {\n  overflow: visible;\n}\n.acf-admin-single-post-type .acf-field,\n.acf-admin-single-taxonomy .acf-field,\n.acf-admin-single-options-page .acf-field {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 24px;\n  margin-left: 0;\n}\n.acf-admin-single-post-type .acf-field .acf-label,\n.acf-admin-single-taxonomy .acf-field .acf-label,\n.acf-admin-single-options-page .acf-field .acf-label {\n  margin-bottom: 6px;\n}\n.acf-admin-single-post-type .acf-field-text,\n.acf-admin-single-post-type .acf-field-textarea,\n.acf-admin-single-post-type .acf-field-select,\n.acf-admin-single-taxonomy .acf-field-text,\n.acf-admin-single-taxonomy .acf-field-textarea,\n.acf-admin-single-taxonomy .acf-field-select,\n.acf-admin-single-options-page .acf-field-text,\n.acf-admin-single-options-page .acf-field-textarea,\n.acf-admin-single-options-page .acf-field-select {\n  max-width: 600px;\n}\n.acf-admin-single-post-type .acf-field-true-false,\n.acf-admin-single-taxonomy .acf-field-true-false,\n.acf-admin-single-options-page .acf-field-true-false {\n  max-width: 700px;\n}\n.acf-admin-single-post-type .acf-field-supports,\n.acf-admin-single-taxonomy .acf-field-supports,\n.acf-admin-single-options-page .acf-field-supports {\n  max-width: 600px;\n}\n.acf-admin-single-post-type .acf-field-supports .acf-label,\n.acf-admin-single-taxonomy .acf-field-supports .acf-label,\n.acf-admin-single-options-page .acf-field-supports .acf-label {\n  display: block;\n}\n.acf-admin-single-post-type .acf-field-supports .acf-label .description,\n.acf-admin-single-taxonomy .acf-field-supports .acf-label .description,\n.acf-admin-single-options-page .acf-field-supports .acf-label .description {\n  margin-top: 4px;\n  margin-bottom: 12px;\n}\n.acf-admin-single-post-type .acf-field-supports .acf_post_type_supports,\n.acf-admin-single-taxonomy .acf-field-supports .acf_post_type_supports,\n.acf-admin-single-options-page .acf-field-supports .acf_post_type_supports {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n.acf-admin-single-post-type .acf-field-supports .acf_post_type_supports:focus-within,\n.acf-admin-single-taxonomy .acf-field-supports .acf_post_type_supports:focus-within,\n.acf-admin-single-options-page .acf-field-supports .acf_post_type_supports:focus-within {\n  border-color: transparent;\n}\n.acf-admin-single-post-type .acf-field-supports .acf_post_type_supports li,\n.acf-admin-single-taxonomy .acf-field-supports .acf_post_type_supports li,\n.acf-admin-single-options-page .acf-field-supports .acf_post_type_supports li {\n  flex: 0 0 25%;\n}\n.acf-admin-single-post-type .acf-field-supports .acf_post_type_supports li a.button,\n.acf-admin-single-taxonomy .acf-field-supports .acf_post_type_supports li a.button,\n.acf-admin-single-options-page .acf-field-supports .acf_post_type_supports li a.button {\n  background-color: transparent;\n  padding: 0;\n  border: 0;\n  height: auto;\n  min-height: auto;\n  margin-top: 0;\n  border-radius: 0;\n  line-height: 22px;\n}\n.acf-admin-single-post-type .acf-field-supports .acf_post_type_supports li a.button:before,\n.acf-admin-single-taxonomy .acf-field-supports .acf_post_type_supports li a.button:before,\n.acf-admin-single-options-page .acf-field-supports .acf_post_type_supports li a.button:before {\n  content: \"\";\n  margin-right: 6px;\n  display: inline-flex;\n  width: 16px;\n  height: 16px;\n  background-color: currentColor;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n  -webkit-mask-image: url(\"../../images/icons/icon-add.svg\");\n  mask-image: url(\"../../images/icons/icon-add.svg\");\n}\n.acf-admin-single-post-type .acf-field-supports .acf_post_type_supports li a.button:hover,\n.acf-admin-single-taxonomy .acf-field-supports .acf_post_type_supports li a.button:hover,\n.acf-admin-single-options-page .acf-field-supports .acf_post_type_supports li a.button:hover {\n  color: #044E71;\n}\n.acf-admin-single-post-type .acf-field-supports .acf_post_type_supports li input[type=text],\n.acf-admin-single-taxonomy .acf-field-supports .acf_post_type_supports li input[type=text],\n.acf-admin-single-options-page .acf-field-supports .acf_post_type_supports li input[type=text] {\n  width: calc(100% - 36px);\n  padding: 0;\n  box-shadow: none;\n  border: none;\n  border-bottom: 1px solid #D0D5DD;\n  border-radius: 0;\n  height: auto;\n  margin: 0;\n  min-height: auto;\n}\n.acf-admin-single-post-type .acf-field-supports .acf_post_type_supports li input[type=text]:focus,\n.acf-admin-single-taxonomy .acf-field-supports .acf_post_type_supports li input[type=text]:focus,\n.acf-admin-single-options-page .acf-field-supports .acf_post_type_supports li input[type=text]:focus {\n  outline: none;\n  border-bottom-color: #399CCB;\n}\n.acf-admin-single-post-type .acf-field-seperator,\n.acf-admin-single-taxonomy .acf-field-seperator,\n.acf-admin-single-options-page .acf-field-seperator {\n  margin-top: 40px;\n  margin-bottom: 40px;\n  border-top: 1px solid #EAECF0;\n  border-right: none;\n  border-bottom: none;\n  border-left: none;\n}\n.acf-admin-single-post-type .acf-field-advanced-configuration,\n.acf-admin-single-taxonomy .acf-field-advanced-configuration,\n.acf-admin-single-options-page .acf-field-advanced-configuration {\n  margin-bottom: 0;\n}\n.acf-admin-single-post-type .postbox-container .acf-tab-wrap,\n.acf-admin-single-post-type .acf-regenerate-labels-bar,\n.acf-admin-single-taxonomy .postbox-container .acf-tab-wrap,\n.acf-admin-single-taxonomy .acf-regenerate-labels-bar,\n.acf-admin-single-options-page .postbox-container .acf-tab-wrap,\n.acf-admin-single-options-page .acf-regenerate-labels-bar {\n  position: relative;\n  top: -48px;\n  left: -48px;\n  width: calc(100% + 96px);\n}\n.acf-admin-single-post-type .acf-regenerate-labels-bar,\n.acf-admin-single-taxonomy .acf-regenerate-labels-bar,\n.acf-admin-single-options-page .acf-regenerate-labels-bar {\n  display: flex;\n  align-items: center;\n  justify-content: right;\n  min-height: 48px;\n  margin-bottom: 0;\n  padding-right: 16px;\n  padding-left: 16px;\n  gap: 8px;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #F2F4F7;\n}\n.acf-admin-single-post-type .acf-labels-tip,\n.acf-admin-single-taxonomy .acf-labels-tip,\n.acf-admin-single-options-page .acf-labels-tip {\n  display: inline-flex;\n  align-items: center;\n  min-height: 24px;\n  margin-right: 8px;\n  padding-left: 16px;\n  border-left-width: 1px;\n  border-left-style: solid;\n  border-left-color: #EAECF0;\n}\n.acf-admin-single-post-type .acf-labels-tip .acf-icon,\n.acf-admin-single-taxonomy .acf-labels-tip .acf-icon,\n.acf-admin-single-options-page .acf-labels-tip .acf-icon {\n  display: inline-flex;\n  align-items: center;\n  width: 16px;\n  height: 16px;\n  -webkit-mask-size: 16px;\n  mask-size: 16px;\n  background-color: #98A2B3;\n}\n\n.acf-select2-default-pill {\n  border-radius: 100px;\n  min-height: 20px;\n  padding-top: 2px;\n  padding-bottom: 2px;\n  padding-left: 8px;\n  padding-right: 8px;\n  font-size: 11px;\n  margin-left: 6px;\n  background-color: #EAECF0;\n  color: #667085;\n}\n\n.acf-menu-position-desc-child {\n  display: none;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Field picker modal\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-modal.acf-browse-fields-modal {\n  width: 1120px;\n  height: 664px;\n  top: 50%;\n  right: auto;\n  bottom: auto;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: row;\n  border-radius: 12px;\n  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04), 0px 8px 16px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker {\n  display: flex;\n  flex-direction: column;\n  flex-grow: 1;\n  width: 760px;\n  background: #fff;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-title,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-toolbar {\n  position: relative;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-title {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: center;\n  background: #F9FAFB;\n  border: none;\n  padding: 35px 32px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-title .acf-search-field-types-wrap {\n  position: relative;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-title .acf-search-field-types-wrap:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 11px;\n  left: 10px;\n  width: 18px;\n  height: 18px;\n  -webkit-mask-image: url(\"../../images/icons/icon-search.svg\");\n  mask-image: url(\"../../images/icons/icon-search.svg\");\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-title .acf-search-field-types-wrap input {\n  width: 280px;\n  height: 40px;\n  margin: 0;\n  padding-left: 32px;\n  box-shadow: none;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content {\n  top: auto;\n  bottom: auto;\n  padding: 0;\n  height: 100%;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-tab-group {\n  padding-left: 32px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab {\n  display: flex;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-type-search-results {\n  flex-direction: row;\n  flex-wrap: wrap;\n  gap: 24px;\n  padding: 32px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab .acf-field-type,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-type-search-results .acf-field-type {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  isolation: isolate;\n  width: 120px;\n  height: 120px;\n  background: #F9FAFB;\n  border: 1px solid #EAECF0;\n  border-radius: 8px;\n  box-sizing: border-box;\n  color: #1D2939;\n  text-decoration: none;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab .acf-field-type:hover, .acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab .acf-field-type:active, .acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab .acf-field-type.selected,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-type-search-results .acf-field-type:hover,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-type-search-results .acf-field-type:active,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-type-search-results .acf-field-type.selected {\n  background: #EBF5FA;\n  border: 1px solid #399CCB;\n  box-shadow: inset 0 0 0 1px #399CCB;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab .acf-field-type .field-type-icon,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-type-search-results .acf-field-type .field-type-icon {\n  border: none;\n  background: none;\n  top: 0;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab .acf-field-type .field-type-icon:before,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-type-search-results .acf-field-type .field-type-icon:before {\n  width: 22px;\n  height: 22px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab .acf-field-type .field-type-label,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-type-search-results .acf-field-type .field-type-label {\n  margin-top: 12px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab .field-type-requires-pro,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-type-search-results .field-type-requires-pro {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: absolute;\n  top: -10px;\n  right: -10px;\n  height: 21px;\n  color: white;\n  background: linear-gradient(90.52deg, #3E8BFF 0.44%, #A45CFF 113.3%);\n  background-size: 140% 20%;\n  background-position: 100% 0;\n  border-radius: 100px;\n  font-size: 11px;\n  padding-right: 6px;\n  padding-left: 6px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-types-tab .field-type-requires-pro i,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-content .acf-field-type-search-results .field-type-requires-pro i {\n  width: 12px;\n  height: 12px;\n  margin-right: 2px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-toolbar {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  height: auto;\n  min-height: 72px;\n  padding-top: 0;\n  padding-right: 32px;\n  padding-bottom: 0;\n  padding-left: 32px;\n  margin: 0;\n  border: none;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-toolbar .acf-select-field,\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-toolbar .acf-btn-pro {\n  min-width: 160px;\n  justify-content: center;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-toolbar .acf-insert-field-label {\n  min-width: 280px;\n  box-shadow: none;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-picker .acf-modal-toolbar .acf-field-picker-actions {\n  display: flex;\n  gap: 8px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview {\n  display: flex;\n  flex-direction: column;\n  width: 360px;\n  background-color: #F9FAFB;\n  background-image: url(\"../../images/field-preview-grid.png\");\n  background-size: 740px;\n  background-repeat: no-repeat;\n  background-position: center bottom;\n  border-left: 1px solid #EAECF0;\n  box-sizing: border-box;\n  padding: 32px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview .field-type-desc {\n  margin: 0;\n  padding: 0;\n  color: #667085;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview .field-type-preview-container {\n  display: inline-flex;\n  justify-content: center;\n  width: 100%;\n  margin-top: 24px;\n  padding-top: 32px;\n  padding-bottom: 32px;\n  background-color: rgba(255, 255, 255, 0.64);\n  border-radius: 8px;\n  box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.04), 0px 8px 24px rgba(0, 0, 0, 0.04);\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview .field-type-image {\n  max-width: 232px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview .field-type-info {\n  flex-grow: 1;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview .field-type-info .field-type-name {\n  font-size: 21px;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 16px;\n  margin-left: 0;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview .field-type-info .field-type-upgrade-to-unlock {\n  display: inline-flex;\n  justify-items: center;\n  align-items: center;\n  min-height: 24px;\n  margin-bottom: 12px;\n  padding-right: 8px;\n  padding-left: 8px;\n  background: linear-gradient(90.52deg, #3E8BFF 0.44%, #A45CFF 113.3%);\n  background-size: 140% 20%;\n  background-position: 100% 0;\n  border-radius: 100px;\n  color: white;\n  text-decoration: none;\n  font-size: 11px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview .field-type-info .field-type-upgrade-to-unlock i.acf-icon {\n  width: 14px;\n  height: 14px;\n  margin-right: 4px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview .field-type-links {\n  display: flex;\n  align-items: center;\n  gap: 24px;\n  min-height: 40px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview .field-type-links .acf-icon {\n  width: 18px;\n  height: 18px;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview .field-type-links:before {\n  display: none;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview .field-type-links a {\n  display: flex;\n  gap: 6px;\n  text-decoration: none;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-preview .field-type-links a:hover {\n  text-decoration: underline;\n}\n.acf-modal.acf-browse-fields-modal .acf-field-type-search-results,\n.acf-modal.acf-browse-fields-modal .acf-field-type-search-no-results {\n  display: none;\n}\n.acf-modal.acf-browse-fields-modal.is-searching .acf-tab-wrap,\n.acf-modal.acf-browse-fields-modal.is-searching .acf-field-types-tab,\n.acf-modal.acf-browse-fields-modal.is-searching .acf-field-type-search-no-results {\n  display: none !important;\n}\n.acf-modal.acf-browse-fields-modal.is-searching .acf-field-type-search-results {\n  display: flex;\n}\n.acf-modal.acf-browse-fields-modal.no-results-found .acf-tab-wrap,\n.acf-modal.acf-browse-fields-modal.no-results-found .acf-field-types-tab,\n.acf-modal.acf-browse-fields-modal.no-results-found .acf-field-type-search-results,\n.acf-modal.acf-browse-fields-modal.no-results-found .field-type-info,\n.acf-modal.acf-browse-fields-modal.no-results-found .field-type-links,\n.acf-modal.acf-browse-fields-modal.no-results-found .acf-field-picker-toolbar {\n  display: none !important;\n}\n.acf-modal.acf-browse-fields-modal.no-results-found .acf-modal-title {\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n.acf-modal.acf-browse-fields-modal.no-results-found .acf-field-type-search-no-results {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  gap: 6px;\n}\n.acf-modal.acf-browse-fields-modal.no-results-found .acf-field-type-search-no-results img {\n  margin-bottom: 19px;\n}\n.acf-modal.acf-browse-fields-modal.no-results-found .acf-field-type-search-no-results p {\n  margin: 0;\n}\n.acf-modal.acf-browse-fields-modal.no-results-found .acf-field-type-search-no-results p.acf-no-results-text {\n  display: flex;\n}\n.acf-modal.acf-browse-fields-modal.no-results-found .acf-field-type-search-no-results .acf-invalid-search-term {\n  max-width: 200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: inline-block;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide browse fields button for smaller screen sizes\n*\n*---------------------------------------------------------------------------------------------*/\n@media only screen and (max-width: 1080px) {\n  .acf-btn.browse-fields {\n    display: none;\n  }\n}", "/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n\n/* colors */\n$acf_blue: #2a9bd9;\n$acf_notice: #2a9bd9;\n$acf_error: #d94f4f;\n$acf_success: #49ad52;\n$acf_warning: #fd8d3b;\n\n/* acf-field */\n$field_padding: 15px 12px;\n$field_padding_x: 12px;\n$field_padding_y: 15px;\n$fp: 15px 12px;\n$fy: 15px;\n$fx: 12px;\n\n/* responsive */\n$md: 880px;\n$sm: 640px;\n\n// Admin.\n$wp-card-border: #ccd0d4;\t\t\t// Card border.\n$wp-card-border-1: #d5d9dd;\t\t  // Card inner border 1: Structural (darker).\n$wp-card-border-2: #eeeeee;\t\t  // Card inner border 2: Fields (lighter).\n$wp-input-border: #7e8993;\t\t   // Input border.\n\n// Admin 3.8\n$wp38-card-border: #E5E5E5;\t\t  // Card border.\n$wp38-card-border-1: #dfdfdf;\t\t// Card inner border 1: Structural (darker).\n$wp38-card-border-2: #eeeeee;\t\t// Card inner border 2: Fields (lighter).\n$wp38-input-border: #dddddd;\t\t // Input border.\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n\n// Grays\n$gray-50:  #F9FAFB;\n$gray-100: #F2F4F7;\n$gray-200: #EAECF0;\n$gray-300: #D0D5DD;\n$gray-400: #98A2B3;\n$gray-500: #667085;\n$gray-600: #475467;\n$gray-700: #344054;\n$gray-800: #1D2939;\n$gray-900: #101828;\n\n// Blues\n$blue-50:  #EBF5FA;\n$blue-100: #D8EBF5;\n$blue-200: #A5D2E7;\n$blue-300: #6BB5D8;\n$blue-400: #399CCB;\n$blue-500: #0783BE;\n$blue-600: #066998;\n$blue-700: #044E71;\n$blue-800: #033F5B;\n$blue-900: #032F45;\n\n// Utility\n$color-info:\t#2D69DA;\n$color-success:\t#52AA59;\n$color-warning:\t#F79009;\n$color-danger:\t#D13737;\n\n$color-primary: $blue-500;\n$color-primary-hover: $blue-600;\n$color-secondary: $gray-500;\n$color-secondary-hover: $gray-400;\n\n// Gradients\n$gradient-pro: linear-gradient(90.52deg, #3E8BFF 0.44%, #A45CFF 113.3%);\n\n// Border radius\n$radius-sm:\t4px;\n$radius-md: 6px;\n$radius-lg: 8px;\n$radius-xl: 12px;\n\n// Elevations / Box shadows\n$elevation-01: 0px 1px 2px rgba($gray-900, 0.10);\n\n// Input & button focus outline\n$outline: 3px solid $blue-50;\n\n// Link colours\n$link-color: $blue-500;\n\n// Responsive\n$max-width: 1440px;", "/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n@mixin clearfix() {\n\t&:after {\n\t\tdisplay: block;\n\t\tclear: both;\n\t\tcontent: \"\";\n\t}\n}\n\n@mixin border-box() {\n\t-webkit-box-sizing: border-box;\n\t-moz-box-sizing: border-box;\n\tbox-sizing: border-box;\n}\n\n@mixin centered() {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n}\n\n@mixin animate( $properties: 'all' ) {\n\t-webkit-transition: $properties 0.3s ease;  // Safari 3.2+, Chrome\n    -moz-transition: $properties 0.3s ease;  \t// Firefox 4-15\n    -o-transition: $properties 0.3s ease;  \t\t// Opera 10.5–12.00\n    transition: $properties 0.3s ease;  \t\t// Firefox 16+, Opera 12.50+\n}\n\n@mixin rtl() {\n\thtml[dir=\"rtl\"] & {\n\t\ttext-align: right;\n\t\t@content;\n\t}\n}\n\n@mixin wp-admin( $version: '3-8' ) {\n\t.acf-admin-#{$version} & {\n\t\t@content;\n\t}\n}", "@use \"sass:math\";\n/*--------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*--------------------------------------------------------------------------------------------*/\n\n/* Horizontal List */\n.acf-hl {\n\tpadding: 0;\n\tmargin: 0;\n\tlist-style: none;\n\tdisplay: block;\n\tposition: relative;\n}\n.acf-hl > li {\n\tfloat: left;\n\tdisplay: block;\n\tmargin: 0;\n\tpadding: 0;\n}\n.acf-hl > li.acf-fr {\n\tfloat: right;\n}\n\n/* Horizontal List: Clearfix */\n.acf-hl:before,\n.acf-hl:after,\n.acf-bl:before,\n.acf-bl:after,\n.acf-cf:before,\n.acf-cf:after {\n\tcontent: \"\";\n\tdisplay: block;\n\tline-height: 0;\n}\n.acf-hl:after,\n.acf-bl:after,\n.acf-cf:after {\n\tclear: both;\n}\n\n/* Block List */\n.acf-bl {\n\tpadding: 0;\n\tmargin: 0;\n\tlist-style: none;\n\tdisplay: block;\n\tposition: relative;\n}\n.acf-bl > li {\n\tdisplay: block;\n\tmargin: 0;\n\tpadding: 0;\n\tfloat: none;\n}\n\n/* Visibility */\n.acf-hidden {\n\tdisplay: none !important;\n}\n.acf-empty {\n\tdisplay: table-cell !important;\n\t* {\n\t\tdisplay: none !important;\n\t}\n}\n\n/* Float */\n.acf-fl {\n\tfloat: left;\n}\n.acf-fr {\n\tfloat: right;\n}\n.acf-fn {\n\tfloat: none;\n}\n\n/* Align */\n.acf-al {\n\ttext-align: left;\n}\n.acf-ar {\n\ttext-align: right;\n}\n.acf-ac {\n\ttext-align: center;\n}\n\n/* loading */\n.acf-loading,\n.acf-spinner {\n\tdisplay: inline-block;\n\theight: 20px;\n\twidth: 20px;\n\tvertical-align: text-top;\n\tbackground: transparent url(../../images/spinner.gif) no-repeat 50% 50%;\n}\n\n/* spinner */\n.acf-spinner {\n\tdisplay: none;\n}\n\n.acf-spinner.is-active {\n\tdisplay: inline-block;\n}\n\n/* WP < 4.2 */\n.spinner.is-active {\n\tdisplay: inline-block;\n}\n\n/* required */\n.acf-required {\n\tcolor: #f00;\n}\n\n/* Allow pointer events in reusable blocks */\n.acf-button,\n.acf-tab-button {\n\tpointer-events: auto !important;\n}\n\n/* show on hover */\n.acf-soh .acf-soh-target {\n\t-webkit-transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n\t-moz-transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n\t-o-transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n\ttransition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n\n\tvisibility: hidden;\n\topacity: 0;\n}\n\n.acf-soh:hover .acf-soh-target {\n\t-webkit-transition-delay: 0s;\n\t-moz-transition-delay: 0s;\n\t-o-transition-delay: 0s;\n\ttransition-delay: 0s;\n\n\tvisibility: visible;\n\topacity: 1;\n}\n\n/* show if value */\n.show-if-value {\n\tdisplay: none;\n}\n.hide-if-value {\n\tdisplay: block;\n}\n\n.has-value .show-if-value {\n\tdisplay: block;\n}\n.has-value .hide-if-value {\n\tdisplay: none;\n}\n\n/* select2 WP animation fix */\n.select2-search-choice-close {\n\t-webkit-transition: none;\n\t-moz-transition: none;\n\t-o-transition: none;\n\ttransition: none;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  tooltip\n*\n*---------------------------------------------------------------------------------------------*/\n\n/* tooltip */\n.acf-tooltip {\n\tbackground: $gray-800;\n\tborder-radius: $radius-md;\n\tcolor: $gray-300;\n\tpadding: {\n\t\ttop: 8px;\n\t\tright: 12px;\n\t\tbottom: 10px;\n\t\tleft: 12px;\n\t}\n\tposition: absolute;\n\t@extend .p7;\n\tz-index: 900000;\n\tmax-width: 280px;\n\tbox-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08),\n\t\t0px 4px 6px -2px rgba(16, 24, 40, 0.03);\n\n\t/* tip */\n\t&:before {\n\t\tborder: solid;\n\t\tborder-color: transparent;\n\t\tborder-width: 6px;\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t}\n\n\t/* positions */\n\t&.top {\n\t\tmargin-top: -8px;\n\n\t\t&:before {\n\t\t\ttop: 100%;\n\t\t\tleft: 50%;\n\t\t\tmargin-left: -6px;\n\t\t\tborder-top-color: #2f353e;\n\t\t\tborder-bottom-width: 0;\n\t\t}\n\t}\n\n\t&.right {\n\t\tmargin-left: 8px;\n\n\t\t&:before {\n\t\t\ttop: 50%;\n\t\t\tmargin-top: -6px;\n\t\t\tright: 100%;\n\t\t\tborder-right-color: #2f353e;\n\t\t\tborder-left-width: 0;\n\t\t}\n\t}\n\n\t&.bottom {\n\t\tmargin-top: 8px;\n\n\t\t&:before {\n\t\t\tbottom: 100%;\n\t\t\tleft: 50%;\n\t\t\tmargin-left: -6px;\n\t\t\tborder-bottom-color: #2f353e;\n\t\t\tborder-top-width: 0;\n\t\t}\n\t}\n\n\t&.left {\n\t\tmargin-left: -8px;\n\n\t\t&:before {\n\t\t\ttop: 50%;\n\t\t\tmargin-top: -6px;\n\t\t\tleft: 100%;\n\t\t\tborder-left-color: #2f353e;\n\t\t\tborder-right-width: 0;\n\t\t}\n\t}\n\n\t.acf-overlay {\n\t\tz-index: -1;\n\t}\n}\n\n/* confirm */\n.acf-tooltip.-confirm {\n\tz-index: 900001; // +1 higher than .acf-tooltip\n\n\ta {\n\t\ttext-decoration: none;\n\t\tcolor: #9ea3a8;\n\n\t\t&:hover {\n\t\t\ttext-decoration: underline;\n\t\t}\n\n\t\t&[data-event=\"confirm\"] {\n\t\t\tcolor: #f55e4f;\n\t\t}\n\t}\n}\n\n.acf-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tcursor: default;\n}\n\n.acf-tooltip-target {\n\tposition: relative;\n\tz-index: 900002; // +1 higher than .acf-tooltip\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  loading\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-loading-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tcursor: default;\n\tz-index: 99;\n\tbackground: rgba(249, 249, 249, 0.5);\n\n\ti {\n\t\t@include centered();\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-icon\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-icon {\n\tdisplay: inline-block;\n\theight: 28px;\n\twidth: 28px;\n\tborder: transparent solid 1px;\n\tborder-radius: 100%;\n\tfont-size: 20px;\n\tline-height: 21px;\n\ttext-align: center;\n\ttext-decoration: none;\n\tvertical-align: top;\n\tbox-sizing: border-box;\n\n\t&:before {\n\t\tfont-family: dashicons;\n\t\tdisplay: inline-block;\n\t\tline-height: 1;\n\t\tfont-weight: 400;\n\t\tfont-style: normal;\n\t\tspeak: none;\n\t\ttext-decoration: inherit;\n\t\ttext-transform: none;\n\t\ttext-rendering: auto;\n\t\t-webkit-font-smoothing: antialiased;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t\twidth: 1em;\n\t\theight: 1em;\n\t\tvertical-align: middle;\n\t\ttext-align: center;\n\t}\n}\n\n// Icon types.\n.acf-icon.-plus:before {\n\tcontent: \"\\f543\";\n}\n.acf-icon.-minus:before {\n\tcontent: \"\\f460\";\n}\n.acf-icon.-cancel:before {\n\tcontent: \"\\f335\";\n\tmargin: -1px 0 0 -1px;\n}\n.acf-icon.-pencil:before {\n\tcontent: \"\\f464\";\n}\n.acf-icon.-location:before {\n\tcontent: \"\\f230\";\n}\n.acf-icon.-up:before {\n\tcontent: \"\\f343\";\n\n\t// Fix position relative to font-size.\n\tmargin-top: math.div(-2em, 20);\n}\n.acf-icon.-down:before {\n\tcontent: \"\\f347\";\n\n\t// Fix position relative to font-size.\n\tmargin-top: math.div(2em, 20);\n}\n.acf-icon.-left:before {\n\tcontent: \"\\f341\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(-2em, 20);\n}\n.acf-icon.-right:before {\n\tcontent: \"\\f345\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(2em, 20);\n}\n.acf-icon.-sync:before {\n\tcontent: \"\\f463\";\n}\n.acf-icon.-globe:before {\n\tcontent: \"\\f319\";\n\n\t// Fix position relative to font-size.\n\tmargin-top: math.div(2em, 20);\n\tmargin-left: math.div(2em, 20);\n}\n.acf-icon.-picture:before {\n\tcontent: \"\\f128\";\n}\n.acf-icon.-check:before {\n\tcontent: \"\\f147\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(-2em, 20);\n}\n.acf-icon.-dot-3:before {\n\tcontent: \"\\f533\";\n\n\t// Fix position relative to font-size.\n\tmargin-top: math.div(-2em, 20);\n}\n.acf-icon.-arrow-combo:before {\n\tcontent: \"\\f156\";\n}\n.acf-icon.-arrow-up:before {\n\tcontent: \"\\f142\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(-2em, 20);\n}\n.acf-icon.-arrow-down:before {\n\tcontent: \"\\f140\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(-2em, 20);\n}\n.acf-icon.-search:before {\n\tcontent: \"\\f179\";\n}\n.acf-icon.-link-ext:before {\n\tcontent: \"\\f504\";\n}\n\n// Duplicate is a custom icon made from pseudo elements.\n.acf-icon.-duplicate {\n\tposition: relative;\n\t&:before,\n\t&:after {\n\t\tcontent: \"\";\n\t\tdisplay: block;\n\t\tbox-sizing: border-box;\n\t\twidth: 46%;\n\t\theight: 46%;\n\t\tposition: absolute;\n\t\ttop: 33%;\n\t\tleft: 23%;\n\t}\n\t&:before {\n\t\tmargin: -1px 0 0 1px;\n\t\tbox-shadow: 2px -2px 0px 0px currentColor;\n\t}\n\t&:after {\n\t\tborder: solid 2px currentColor;\n\t}\n}\n\n.acf-icon.-trash {\n\tposition: relative;\n\t&:before,\n\t&:after {\n\t\tcontent: \"\";\n\t\tdisplay: block;\n\t\tbox-sizing: border-box;\n\t\twidth: 46%;\n\t\theight: 46%;\n\t\tposition: absolute;\n\t\ttop: 33%;\n\t\tleft: 23%;\n\t}\n\t&:before {\n\t\tmargin: -1px 0 0 1px;\n\t\tbox-shadow: 2px -2px 0px 0px currentColor;\n\t}\n\t&:after {\n\t\tborder: solid 2px currentColor;\n\t}\n}\n\n// Collapse icon toggles automatically.\n.acf-icon.-collapse:before {\n\tcontent: \"\\f142\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(-2em, 20);\n}\n.-collapsed .acf-icon.-collapse:before {\n\tcontent: \"\\f140\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(-2em, 20);\n}\n\n// <span> displays with grey border.\nspan.acf-icon {\n\tcolor: #555d66;\n\tborder-color: #b5bcc2;\n\tbackground-color: #fff;\n}\n\n// <a> also displays with grey border.\na.acf-icon {\n\tcolor: #555d66;\n\tborder-color: #b5bcc2;\n\tbackground-color: #fff;\n\tposition: relative;\n\ttransition: none;\n\tcursor: pointer;\n\n\t// State \"hover\".\n\t&:hover {\n\t\tbackground: #f3f5f6;\n\t\tborder-color: #0071a1;\n\t\tcolor: #0071a1;\n\t}\n\t&.-minus:hover,\n\t&.-cancel:hover {\n\t\tbackground: #f7efef;\n\t\tborder-color: #a10000;\n\t\tcolor: #dc3232;\n\t}\n\n\t// Fix: Remove WP outline box-shadow.\n\t&:active,\n\t&:focus {\n\t\toutline: none;\n\t\tbox-shadow: none;\n\t}\n}\n\n// Style \"clear\".\n.acf-icon.-clear {\n\tborder-color: transparent;\n\tbackground: transparent;\n\tcolor: #444;\n}\n\n// Style \"light\".\n.acf-icon.light {\n\tborder-color: transparent;\n\tbackground: #f5f5f5;\n\tcolor: #23282d;\n}\n\n// Style \"dark\".\n.acf-icon.dark {\n\tborder-color: transparent !important;\n\tbackground: #23282d;\n\tcolor: #eee;\n}\na.acf-icon.dark {\n\t&:hover {\n\t\tbackground: #191e23;\n\t\tcolor: #00b9eb;\n\t}\n\t&.-minus:hover,\n\t&.-cancel:hover {\n\t\tcolor: #d54e21;\n\t}\n}\n\n// Style \"grey\".\n.acf-icon.grey {\n\tborder-color: transparent !important;\n\tbackground: #b4b9be;\n\tcolor: #fff !important;\n\n\t&:hover {\n\t\tbackground: #00a0d2;\n\t\tcolor: #fff;\n\t}\n\t&.-minus:hover,\n\t&.-cancel:hover {\n\t\tbackground: #32373c;\n\t}\n}\n\n// Size \"small\".\n.acf-icon.small,\n.acf-icon.-small {\n\twidth: 20px;\n\theight: 20px;\n\tline-height: 14px;\n\tfont-size: 14px;\n\n\t// Apply minor transforms to reduce clarirty of \"duplicate\" icon.\n\t// Helps to unify rendering with dashicons.\n\t&.-duplicate {\n\t\t&:before,\n\t\t&:after {\n\t\t\t//transform: rotate(0.1deg) scale(0.9) translate(-5%, 5%);\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-box\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-box {\n\tbackground: #ffffff;\n\tborder: 1px solid $wp-card-border;\n\tposition: relative;\n\tbox-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);\n\n\t/* title */\n\t.title {\n\t\tborder-bottom: 1px solid $wp-card-border;\n\t\tmargin: 0;\n\t\tpadding: 15px;\n\n\t\th3 {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tfont-size: 14px;\n\t\t\tline-height: 1em;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t}\n\t}\n\n\t.inner {\n\t\tpadding: 15px;\n\t}\n\n\th2 {\n\t\tcolor: #333333;\n\t\tfont-size: 26px;\n\t\tline-height: 1.25em;\n\t\tmargin: 0.25em 0 0.75em;\n\t\tpadding: 0;\n\t}\n\n\th3 {\n\t\tmargin: 1.5em 0 0;\n\t}\n\n\tp {\n\t\tmargin-top: 0.5em;\n\t}\n\n\ta {\n\t\ttext-decoration: none;\n\t}\n\n\ti {\n\t\t&.dashicons-external {\n\t\t\tmargin-top: -1px;\n\t\t}\n\t}\n\n\t/* footer */\n\t.footer {\n\t\tborder-top: 1px solid $wp-card-border;\n\t\tpadding: 12px;\n\t\tfont-size: 13px;\n\t\tline-height: 1.5;\n\n\t\tp {\n\t\t\tmargin: 0;\n\t\t}\n\t}\n\n\t// WP Admin 3.8\n\t@include wp-admin(\"3-8\") {\n\t\tborder-color: $wp38-card-border;\n\t\t.title,\n\t\t.footer {\n\t\t\tborder-color: $wp38-card-border;\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-notice\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-notice {\n\tposition: relative;\n\tdisplay: block;\n\tcolor: #fff;\n\tmargin: 5px 0 15px;\n\tpadding: 3px 12px;\n\tbackground: $acf_notice;\n\tborder-left: darken($acf_notice, 10%) solid 3px;\n\n\tp {\n\t\tfont-size: 13px;\n\t\tline-height: 1.5;\n\t\tmargin: 0.5em 0;\n\t\ttext-shadow: none;\n\t\tcolor: inherit;\n\t}\n\n\t.acf-notice-dismiss {\n\t\tposition: absolute;\n\t\ttop: 9px;\n\t\tright: 12px;\n\t\tbackground: transparent !important;\n\t\tcolor: inherit !important;\n\t\tborder-color: #fff !important;\n\t\topacity: 0.75;\n\t\t&:hover {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t// dismiss\n\t&.-dismiss {\n\t\tpadding-right: 40px;\n\t}\n\n\t// error\n\t&.-error {\n\t\tbackground: $acf_error;\n\t\tborder-color: darken($acf_error, 10%);\n\t}\n\n\t// success\n\t&.-success {\n\t\tbackground: $acf_success;\n\t\tborder-color: darken($acf_success, 10%);\n\t}\n\n\t// warning\n\t&.-warning {\n\t\tbackground: $acf_warning;\n\t\tborder-color: darken($acf_warning, 10%);\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-table\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-table {\n\tborder: $wp-card-border solid 1px;\n\tbackground: #fff;\n\tborder-spacing: 0;\n\tborder-radius: 0;\n\ttable-layout: auto;\n\tpadding: 0;\n\tmargin: 0;\n\twidth: 100%;\n\tclear: both;\n\tbox-sizing: content-box;\n\n\t/* defaults */\n\t> tbody > tr,\n\t> thead > tr {\n\t\t> th,\n\t\t> td {\n\t\t\tpadding: 8px;\n\t\t\tvertical-align: top;\n\t\t\tbackground: #fff;\n\t\t\ttext-align: left;\n\t\t\tborder-style: solid;\n\t\t\tfont-weight: normal;\n\t\t}\n\n\t\t> th {\n\t\t\tposition: relative;\n\t\t\tcolor: #333333;\n\t\t}\n\t}\n\n\t/* thead */\n\t> thead {\n\t\t> tr {\n\t\t\t> th {\n\t\t\t\tborder-color: $wp-card-border-1;\n\t\t\t\tborder-width: 0 0 1px 1px;\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tborder-left-width: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/* tbody */\n\t> tbody {\n\t\t> tr {\n\t\t\tz-index: 1;\n\n\t\t\t> td {\n\t\t\t\tborder-color: $wp-card-border-2;\n\t\t\t\tborder-width: 1px 0 0 1px;\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tborder-left-width: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:first-child > td {\n\t\t\t\tborder-top-width: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* -clear */\n\t&.-clear {\n\t\tborder: 0 none;\n\n\t\t> tbody > tr,\n\t\t> thead > tr {\n\t\t\t> td,\n\t\t\t> th {\n\t\t\t\tborder: 0 none;\n\t\t\t\tpadding: 4px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* remove tr */\n.acf-remove-element {\n\t-webkit-transition: all 0.25s ease-out;\n\t-moz-transition: all 0.25s ease-out;\n\t-o-transition: all 0.25s ease-out;\n\ttransition: all 0.25s ease-out;\n\n\ttransform: translate(50px, 0);\n\topacity: 0;\n}\n\n/* fade-up */\n.acf-fade-up {\n\t-webkit-transition: all 0.25s ease-out;\n\t-moz-transition: all 0.25s ease-out;\n\t-o-transition: all 0.25s ease-out;\n\ttransition: all 0.25s ease-out;\n\n\ttransform: translate(0, -10px);\n\topacity: 0;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Fake table\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-thead,\n.acf-tbody,\n.acf-tfoot {\n\twidth: 100%;\n\tpadding: 0;\n\tmargin: 0;\n\n\t> li {\n\t\tbox-sizing: border-box;\n\t\tpadding: {\n\t\t\ttop: 14px;\n\t\t}\n\t\tfont-size: 12px;\n\t\tline-height: 14px;\n\t}\n}\n\n.acf-thead {\n\tborder-bottom: $wp-card-border solid 1px;\n\tcolor: #23282d;\n\n\t> li {\n\t\tfont-size: 14px;\n\t\tline-height: 1.4;\n\t\tfont-weight: bold;\n\t}\n\n\t// WP Admin 3.8\n\t@include wp-admin(\"3-8\") {\n\t\tborder-color: $wp38-card-border-1;\n\t}\n}\n\n.acf-tfoot {\n\tbackground: #f5f5f5;\n\tborder-top: $wp-card-border-1 solid 1px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tSettings\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-settings-wrap {\n\t#poststuff {\n\t\tpadding-top: 15px;\n\t}\n\n\t.acf-box {\n\t\tmargin: 20px 0;\n\t}\n\n\ttable {\n\t\tmargin: 0;\n\n\t\t.button {\n\t\t\tvertical-align: middle;\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-popup\n*\n*--------------------------------------------------------------------------------------------*/\n\n#acf-popup {\n\tposition: fixed;\n\tz-index: 900000;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\ttext-align: center;\n\n\t// bg\n\t.bg {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tz-index: 0;\n\t\tbackground: rgba(0, 0, 0, 0.25);\n\t}\n\n\t&:before {\n\t\tcontent: \"\";\n\t\tdisplay: inline-block;\n\t\theight: 100%;\n\t\tvertical-align: middle;\n\t}\n\n\t// box\n\t.acf-popup-box {\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\tz-index: 1;\n\t\tmin-width: 300px;\n\t\tmin-height: 160px;\n\t\tborder-color: #aaaaaa;\n\t\tbox-shadow: 0 5px 30px -5px rgba(0, 0, 0, 0.25);\n\t\ttext-align: left;\n\t\t@include rtl();\n\n\t\t// title\n\t\t.title {\n\t\t\tmin-height: 15px;\n\t\t\tline-height: 15px;\n\n\t\t\t// icon\n\t\t\t.acf-icon {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 10px;\n\t\t\t\tright: 10px;\n\n\t\t\t\t// rtl\n\t\t\t\thtml[dir=\"rtl\"] & {\n\t\t\t\t\tright: auto;\n\t\t\t\t\tleft: 10px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.inner {\n\t\t\tmin-height: 50px;\n\n\t\t\t// use margin instead of padding to allow inner elements marin to overlap and avoid large hitespace at top/bottom\n\t\t\tpadding: 0;\n\t\t\tmargin: 15px;\n\t\t}\n\n\t\t// loading\n\t\t.loading {\n\t\t\tposition: absolute;\n\t\t\ttop: 45px;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tz-index: 2;\n\t\t\tbackground: rgba(0, 0, 0, 0.1);\n\t\t\tdisplay: none;\n\n\t\t\ti {\n\t\t\t\t@include centered();\n\t\t\t}\n\t\t}\n\t}\n}\n\n// acf-submit\n.acf-submit {\n\tmargin-bottom: 0;\n\tline-height: 28px; // .button height\n\n\t// message\n\tspan {\n\t\tfloat: right;\n\t\tcolor: #999;\n\n\t\t&.-error {\n\t\t\tcolor: #dd4232;\n\t\t}\n\t}\n\n\t// button (allow margin between loading)\n\t.button {\n\t\tmargin-right: 5px;\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tupgrade notice\n*\n*--------------------------------------------------------------------------------------------*/\n\n#acf-upgrade-notice {\n\tposition: relative;\n\tbackground: #fff;\n\tpadding: 20px;\n\t@include clearfix();\n\n\t.col-content {\n\t\tfloat: left;\n\t\twidth: 55%;\n\t\tpadding-left: 90px;\n\t}\n\n\t.notice-container {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: flex-start;\n\t\talign-content: flex-start;\n\t}\n\n\t.col-actions {\n\t\tfloat: right;\n\t\ttext-align: center;\n\t}\n\n\timg {\n\t\tfloat: left;\n\t\twidth: 64px;\n\t\theight: 64px;\n\t\tmargin: 0 0 0 -90px;\n\t}\n\n\th2 {\n\t\tdisplay: inline-block;\n\t\tfont-size: 16px;\n\t\tmargin: 2px 0 6.5px;\n\t}\n\n\tp {\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t}\n\n\t.button:before {\n\t\tmargin-top: 11px;\n\t}\n\n\t// mobile\n\t@media screen and (max-width: $sm) {\n\t\t.col-content,\n\t\t.col-actions {\n\t\t\tfloat: none;\n\t\t\tpadding-left: 90px;\n\t\t\twidth: auto;\n\t\t\ttext-align: left;\n\t\t}\n\t}\n}\n\n// Hide icons for upgade notice.\n#acf-upgrade-notice:has(.notice-container)::before,\n#acf-upgrade-notice:has(.notice-container)::after {\n\tdisplay: none;\n}\n\n// Match padding of other non-icon notices.\n#acf-upgrade-notice:has(.notice-container) {\n\tpadding-left: 20px !important;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tWelcome\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-wrap {\n\th1 {\n\t\tmargin-top: 0;\n\t\tpadding-top: 20px;\n\t}\n\n\t.about-text {\n\t\tmargin-top: 0.5em;\n\t\tmin-height: 50px;\n\t}\n\n\t.about-headline-callout {\n\t\tfont-size: 2.4em;\n\t\tfont-weight: 300;\n\t\tline-height: 1.3;\n\t\tmargin: 1.1em 0 0.2em;\n\t\ttext-align: center;\n\t}\n\n\t.feature-section {\n\t\tpadding: 40px 0;\n\n\t\th2 {\n\t\t\tmargin-top: 20px;\n\t\t}\n\t}\n\n\t.changelog {\n\t\tlist-style: disc;\n\t\tpadding-left: 15px;\n\n\t\tli {\n\t\t\tmargin: 0 0 0.75em;\n\t\t}\n\t}\n\n\t.acf-three-col {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tjustify-content: space-between;\n\n\t\t> div {\n\t\t\tflex: 1;\n\t\t\talign-self: flex-start;\n\t\t\tmin-width: 31%;\n\t\t\tmax-width: 31%;\n\n\t\t\t@media screen and (max-width: $md) {\n\t\t\t\tmin-width: 48%;\n\t\t\t}\n\n\t\t\t@media screen and (max-width: $sm) {\n\t\t\t\tmin-width: 100%;\n\t\t\t}\n\t\t}\n\n\t\th3 .badge {\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: top;\n\t\t\tborder-radius: 5px;\n\t\t\tbackground: #fc9700;\n\t\t\tcolor: #fff;\n\t\t\tfont-weight: normal;\n\t\t\tfont-size: 12px;\n\t\t\tpadding: 2px 5px;\n\t\t}\n\n\t\timg + h3 {\n\t\t\tmargin-top: 0.5em;\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-hl cols\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-hl[data-cols] {\n\tmargin-left: -10px;\n\tmargin-right: -10px;\n\n\t> li {\n\t\tpadding: 0 6px 0 10px;\n\n\t\t-webkit-box-sizing: border-box;\n\t\t-moz-box-sizing: border-box;\n\t\tbox-sizing: border-box;\n\t}\n}\n\n/* sizes */\n.acf-hl[data-cols=\"2\"] > li {\n\twidth: 50%;\n}\n.acf-hl[data-cols=\"3\"] > li {\n\twidth: 33.333%;\n}\n.acf-hl[data-cols=\"4\"] > li {\n\twidth: 25%;\n}\n\n/* mobile */\n@media screen and (max-width: $sm) {\n\t.acf-hl[data-cols] {\n\t\tflex-wrap: wrap;\n\t\tjustify-content: flex-start;\n\t\talign-content: flex-start;\n\t\talign-items: flex-start;\n\t\tmargin-left: 0;\n\t\tmargin-right: 0;\n\t\tmargin-top: -10px;\n\n\t\t> li {\n\t\t\tflex: 1 1 100%;\n\t\t\twidth: 100% !important;\n\t\t\tpadding: 10px 0 0;\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tmisc\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-actions {\n\ttext-align: right;\n\tz-index: 1;\n\n\t/* hover */\n\t&.-hover {\n\t\tposition: absolute;\n\t\tdisplay: none;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tpadding: 5px;\n\t\tz-index: 1050;\n\t}\n\n\t/* rtl */\n\thtml[dir=\"rtl\"] & {\n\t\t&.-hover {\n\t\t\tright: auto;\n\t\t\tleft: 0;\n\t\t}\n\t}\n}\n\n/* ul compatibility */\nul.acf-actions {\n\tli {\n\t\tfloat: right;\n\t\tmargin-left: 4px;\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRTL\n*\n*--------------------------------------------------------------------------------------------*/\n\nhtml[dir=\"rtl\"] .acf-fl {\n\tfloat: right;\n}\nhtml[dir=\"rtl\"] .acf-fr {\n\tfloat: left;\n}\n\nhtml[dir=\"rtl\"] .acf-hl > li {\n\tfloat: right;\n}\n\nhtml[dir=\"rtl\"] .acf-hl > li.acf-fr {\n\tfloat: left;\n}\n\nhtml[dir=\"rtl\"] .acf-icon.logo {\n\tleft: 0;\n\tright: auto;\n}\n\nhtml[dir=\"rtl\"] .acf-table thead th {\n\ttext-align: right;\n\tborder-right-width: 1px;\n\tborder-left-width: 0px;\n}\n\nhtml[dir=\"rtl\"] .acf-table > tbody > tr > td {\n\ttext-align: right;\n\tborder-right-width: 1px;\n\tborder-left-width: 0px;\n}\n\nhtml[dir=\"rtl\"] .acf-table > thead > tr > th:first-child,\nhtml[dir=\"rtl\"] .acf-table > tbody > tr > td:first-child {\n\tborder-right-width: 0;\n}\n\nhtml[dir=\"rtl\"] .acf-table > tbody > tr > td.order + td {\n\tborder-right-color: #e1e1e1;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  acf-postbox-columns\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-postbox-columns {\n\t@include clearfix();\n\tposition: relative;\n\tmargin-top: -11px;\n\tmargin-bottom: -12px;\n\tmargin-left: -12px;\n\tmargin-right: (280px - 12px);\n\n\t.acf-postbox-main,\n\t.acf-postbox-side {\n\t\t@include border-box();\n\t\tpadding: 0 12px 12px;\n\t}\n\n\t.acf-postbox-main {\n\t\tfloat: left;\n\t\twidth: 100%;\n\t}\n\n\t.acf-postbox-side {\n\t\tfloat: right;\n\t\twidth: 280px;\n\t\tmargin-right: -280px;\n\n\t\t&:before {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\twidth: 1px;\n\t\t\theight: 100%;\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbackground: $wp-card-border-1;\n\t\t}\n\t}\n\n\t// WP Admin 3.8\n\t@include wp-admin(\"3-8\") {\n\t\t.acf-postbox-side:before {\n\t\t\tbackground: $wp38-card-border-1;\n\t\t}\n\t}\n}\n\n/* mobile */\n@media only screen and (max-width: 850px) {\n\t.acf-postbox-columns {\n\t\tmargin: 0;\n\n\t\t.acf-postbox-main,\n\t\t.acf-postbox-side {\n\t\t\tfloat: none;\n\t\t\twidth: auto;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t}\n\n\t\t.acf-postbox-side {\n\t\t\tmargin-top: 1em;\n\n\t\t\t&:before {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  acf-panel\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-panel {\n\tmargin-top: -1px;\n\tborder-top: 1px solid $wp-card-border-1;\n\tborder-bottom: 1px solid $wp-card-border-1;\n\n\t.acf-panel-title {\n\t\tmargin: 0;\n\t\tpadding: 12px;\n\t\tfont-weight: bold;\n\t\tcursor: pointer;\n\t\tfont-size: inherit;\n\n\t\ti {\n\t\t\tfloat: right;\n\t\t}\n\t}\n\n\t.acf-panel-inside {\n\t\tmargin: 0;\n\t\tpadding: 0 12px 12px;\n\t\tdisplay: none;\n\t}\n\n\t/* open */\n\t&.-open {\n\t\t.acf-panel-inside {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n\n\t/* inside postbox */\n\t.postbox & {\n\t\tmargin-left: -12px;\n\t\tmargin-right: -12px;\n\t}\n\n\t/* fields */\n\t.acf-field {\n\t\tmargin: 20px 0 0;\n\n\t\t.acf-label label {\n\t\t\tcolor: #555d66;\n\t\t\tfont-weight: normal;\n\t\t}\n\n\t\t&:first-child {\n\t\t\tmargin-top: 0;\n\t\t}\n\t}\n\n\t// WP Admin 3.8\n\t@include wp-admin(\"3-8\") {\n\t\tborder-color: $wp38-card-border-1;\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Tools\n*\n*---------------------------------------------------------------------------------------------*/\n\n#acf-admin-tools {\n\t.notice {\n\t\tmargin-top: 10px;\n\t}\n\n\t.acf-meta-box-wrap {\n\t\t.inside {\n\t\t\tborder-top: none;\n\t\t}\n\n\t\t/* acf-fields */\n\t\t.acf-fields {\n\t\t\tmargin: {\n\t\t\t\tbottom: 24px;\n\t\t\t}\n\t\t\tborder: none;\n\t\t\tbackground: #fff;\n\t\t\tborder-radius: 0;\n\n\t\t\t.acf-field {\n\t\t\t\tpadding: 0;\n\t\t\t\tmargin-bottom: 19px;\n\t\t\t\tborder-top: none;\n\t\t\t}\n\n\t\t\t.acf-label {\n\t\t\t\t@extend .p2;\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 16px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.acf-input {\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 16px;\n\t\t\t\t\tright: 16px;\n\t\t\t\t\tbottom: 16px;\n\t\t\t\t\tleft: 16px;\n\t\t\t\t}\n\t\t\t\tborder: {\n\t\t\t\t\twidth: 1px;\n\t\t\t\t\tstyle: solid;\n\t\t\t\t\tcolor: $gray-300;\n\t\t\t\t}\n\t\t\t\tborder-radius: $radius-md;\n\t\t\t}\n\n\t\t\t&.import-cptui {\n\t\t\t\tmargin-top: 19px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.acf-meta-box-wrap {\n\t.postbox {\n\t\t@include border-box();\n\n\t\t.inside {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\n\t\t.hndle {\n\t\t\tfont-size: 14px;\n\t\t\tpadding: 8px 12px;\n\t\t\tmargin: 0;\n\t\t\tline-height: 1.4;\n\n\t\t\t// Prevent .acf-panel border overlapping.\n\t\t\tposition: relative;\n\t\t\tz-index: 1;\n\t\t\tcursor: default;\n\t\t}\n\n\t\t.handlediv,\n\t\t.handle-order-higher,\n\t\t.handle-order-lower {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n}\n\n/* grid */\n.acf-meta-box-wrap.-grid {\n\tmargin-left: 8px;\n\tmargin-right: 8px;\n\n\t.postbox {\n\t\tfloat: left;\n\t\tclear: left;\n\t\twidth: 50%;\n\t\tmargin: 0 0 16px;\n\n\t\t&:nth-child(odd) {\n\t\t\tmargin-left: -8px;\n\t\t}\n\n\t\t&:nth-child(even) {\n\t\t\tfloat: right;\n\t\t\tclear: right;\n\t\t\tmargin-right: -8px;\n\t\t}\n\t}\n}\n\n/* mobile */\n@media only screen and (max-width: 850px) {\n\t.acf-meta-box-wrap.-grid {\n\t\tmargin-left: 0;\n\t\tmargin-right: 0;\n\n\t\t.postbox {\n\t\t\tmargin-left: 0 !important;\n\t\t\tmargin-right: 0 !important;\n\t\t\twidth: 100%;\n\t\t}\n\t}\n}\n\n/* export tool */\n#acf-admin-tool-export {\n\tp {\n\t\tmax-width: 800px;\n\t}\n\n\tul {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\twidth: 100%;\n\t\tli {\n\t\t\tflex: 0 1 33.33%;\n\t\t\t@media screen and (max-width: 1600px) {\n\t\t\t\tflex: 0 1 50%;\n\t\t\t}\n\t\t\t@media screen and (max-width: 1200px) {\n\t\t\t\tflex: 0 1 100%;\n\t\t\t}\n\t\t}\n\t}\n\n\t.acf-postbox-side {\n\t\tul {\n\t\t\tdisplay: block;\n\t\t}\n\n\t\t.button {\n\t\t\tmargin: 0;\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\ttextarea {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t\tmin-height: 500px;\n\t\tbackground: $gray-50;\n\t\tborder-color: $gray-300;\n\t\tbox-shadow: none;\n\t\tpadding: 7px;\n\t\tborder-radius: $radius-md;\n\t}\n\n\t/* panel: selection */\n\t.acf-panel-selection {\n\t\t.acf-label label {\n\t\t\tfont-weight: bold;\n\t\t\tcolor: $gray-700;\n\t\t}\n\t}\n}\n\n#acf-admin-tool-import {\n\tul {\n\t\tcolumn-width: 200px;\n\t}\n}\n\n// CSS only Tooltip.\n.acf-css-tooltip {\n\tposition: relative;\n\t&:before {\n\t\tcontent: attr(aria-label);\n\t\tdisplay: none;\n\t\tposition: absolute;\n\t\tz-index: 999;\n\n\t\tbottom: 100%;\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, -8px);\n\n\t\tbackground: #191e23;\n\t\tborder-radius: 2px;\n\t\tpadding: 5px 10px;\n\n\t\tcolor: #fff;\n\t\tfont-size: 12px;\n\t\tline-height: 1.4em;\n\t\twhite-space: pre;\n\t}\n\t&:after {\n\t\tcontent: \"\";\n\t\tdisplay: none;\n\t\tposition: absolute;\n\t\tz-index: 998;\n\n\t\tbottom: 100%;\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, 4px);\n\n\t\tborder: solid 6px transparent;\n\t\tborder-top-color: #191e23;\n\t}\n\n\t&:hover,\n\t&:focus {\n\t\t&:before,\n\t\t&:after {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n}\n\n// Diff modal.\n.acf-diff {\n\t.acf-diff-title {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 40px;\n\t\tpadding: 14px 16px;\n\t\tbackground: #f3f3f3;\n\t\tborder-bottom: #dddddd solid 1px;\n\n\t\tstrong {\n\t\t\tfont-size: 14px;\n\t\t\tdisplay: block;\n\t\t}\n\n\t\t.acf-diff-title-left,\n\t\t.acf-diff-title-right {\n\t\t\twidth: 50%;\n\t\t\tfloat: left;\n\t\t}\n\t}\n\n\t.acf-diff-content {\n\t\tposition: absolute;\n\t\ttop: 70px;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\toverflow: auto;\n\t}\n\n\ttable.diff {\n\t\tborder-spacing: 0;\n\n\t\tcol.diffsplit.middle {\n\t\t\twidth: 0;\n\t\t}\n\n\t\ttd,\n\t\tth {\n\t\t\tpadding-top: 0.25em;\n\t\t\tpadding-bottom: 0.25em;\n\t\t}\n\n\t\t// Fix WP 5.7 conflicting CSS.\n\t\ttr td:nth-child(2) {\n\t\t\twidth: auto;\n\t\t}\n\n\t\ttd:nth-child(3) {\n\t\t\tborder-left: #dddddd solid 1px;\n\t\t}\n\t}\n\n\t// Mobile\n\t@media screen and (max-width: 600px) {\n\t\t.acf-diff-title {\n\t\t\theight: 70px;\n\t\t}\n\t\t.acf-diff-content {\n\t\t\ttop: 100px;\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Modal\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-modal {\n\tposition: fixed;\n\ttop: 30px;\n\tleft: 30px;\n\tright: 30px;\n\tbottom: 30px;\n\tz-index: 160000;\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.7);\n\tbackground: #fcfcfc;\n\n\t.acf-modal-title,\n\t.acf-modal-content,\n\t.acf-modal-toolbar {\n\t\tbox-sizing: border-box;\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\tright: 0;\n\t}\n\n\t.acf-modal-title {\n\t\theight: 50px;\n\t\ttop: 0;\n\t\tborder-bottom: 1px solid #ddd;\n\n\t\th2 {\n\t\t\tmargin: 0;\n\t\t\tpadding: 0 16px;\n\t\t\tline-height: 50px;\n\t\t}\n\t\t.acf-modal-close {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\theight: 50px;\n\t\t\twidth: 50px;\n\t\t\tborder: none;\n\t\t\tborder-left: 1px solid #ddd;\n\t\t\tbackground: transparent;\n\t\t\tcursor: pointer;\n\t\t\tcolor: #666;\n\t\t\t&:hover {\n\t\t\t\tcolor: #00a0d2;\n\t\t\t}\n\t\t}\n\t}\n\n\t.acf-modal-content {\n\t\ttop: 50px;\n\t\tbottom: 60px;\n\t\tbackground: #fff;\n\t\toverflow: auto;\n\t\tpadding: 16px;\n\t}\n\n\t.acf-modal-feedback {\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\tmargin: -10px 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\ttext-align: center;\n\t\topacity: 0.75;\n\n\t\t&.error {\n\t\t\topacity: 1;\n\t\t\tcolor: #b52727;\n\t\t}\n\t}\n\n\t.acf-modal-toolbar {\n\t\theight: 60px;\n\t\tbottom: 0;\n\t\tpadding: 15px 16px;\n\t\tborder-top: 1px solid #ddd;\n\n\t\t.button {\n\t\t\tfloat: right;\n\t\t}\n\t}\n\n\t// Responsive.\n\t@media only screen and (max-width: 640px) {\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t}\n}\n.acf-modal-backdrop {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: $gray-900;\n\topacity: 0.8;\n\tz-index: 159900;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Retina\n*\n*---------------------------------------------------------------------------------------------*/\n\n@media only screen and (-webkit-min-device-pixel-ratio: 2),\n\tonly screen and (min--moz-device-pixel-ratio: 2),\n\tonly screen and (-o-min-device-pixel-ratio: 2/1),\n\tonly screen and (min-device-pixel-ratio: 2),\n\tonly screen and (min-resolution: 192dpi),\n\tonly screen and (min-resolution: 2dppx) {\n\t.acf-loading,\n\t.acf-spinner {\n\t\tbackground-image: url(../../images/<EMAIL>);\n\t\tbackground-size: 20px 20px;\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Wrap\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-admin-page {\n\t.wrap {\n\t\tmargin: {\n\t\t\ttop: 48px;\n\t\t\tright: 32px;\n\t\t\tbottom: 0;\n\t\t\tleft: 12px;\n\t\t}\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t\tleft: 8px;\n\t\t\t}\n\t\t}\n\t}\n\n\t&.rtl .wrap {\n\t\tmargin: {\n\t\t\tright: 12px;\n\t\t\tleft: 32px;\n\t\t}\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t\tleft: 8px;\n\t\t\t}\n\t\t}\n\t}\n\n\t#wpcontent {\n\t\t@media screen and (max-width: 768px) {\n\t\t\tpadding: {\n\t\t\t\tleft: 0;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*-------------------------------------------------------------------\n*\n*  ACF Admin Page Footer Styles\n*\n*------------------------------------------------------------------*/\n.acf-admin-page {\n\t#wpfooter {\n\t\tfont-style: italic;\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Postbox & ACF Postbox\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\t.postbox,\n\t.acf-box {\n\t\tborder: none;\n\t\tborder-radius: $radius-lg;\n\t\tbox-shadow: $elevation-01;\n\n\t\t.inside {\n\t\t\tpadding: {\n\t\t\t\ttop: 24px;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t}\n\t\t}\n\n\t\t.acf-postbox-inner {\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t}\n\t\t\tpadding: {\n\t\t\t\ttop: 24px;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t}\n\t\t}\n\n\t\t.inner,\n\t\t.inside {\n\t\t\tmargin: {\n\t\t\t\ttop: 0 !important;\n\t\t\t\tright: 0 !important;\n\t\t\t\tbottom: 0 !important;\n\t\t\t\tleft: 0 !important;\n\t\t\t}\n\t\t\tborder-top: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t}\n\t\t}\n\n\t\t.postbox-header,\n\t\t.title {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tbox-sizing: border-box;\n\t\t\tmin-height: 64px;\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t}\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 24px;\n\t\t\t}\n\t\t\tborder-bottom: {\n\t\t\t\twidth: 0;\n\t\t\t\tstyle: none;\n\t\t\t}\n\n\t\t\th2,\n\t\t\th3 {\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t}\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t}\n\t\t\t\t@extend .acf-h3;\n\t\t\t\tcolor: $gray-700;\n\t\t\t}\n\t\t}\n\n\t\t.hndle {\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 24px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Custom ACF postbox header\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-postbox-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tbox-sizing: border-box;\n\tmin-height: 64px;\n\tmargin: {\n\t\ttop: -24px;\n\t\tright: -24px;\n\t\tbottom: 0;\n\t\tleft: -24px;\n\t}\n\tpadding: {\n\t\ttop: 0;\n\t\tright: 24px;\n\t\tbottom: 0;\n\t\tleft: 24px;\n\t}\n\tborder-bottom: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $gray-200;\n\t}\n\n\th2.acf-postbox-title {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t}\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 24px;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t}\n\t\t@extend .acf-h3;\n\t\tcolor: $gray-700;\n\t}\n\n\t.rtl & h2.acf-postbox-title {\n\t\tpadding: {\n\t\t\tright: 0;\n\t\t\tleft: 24px;\n\t\t}\n\t}\n\n\t.acf-icon {\n\t\tbackground-color: $gray-400;\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Screen options button & screen meta container\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\t#screen-meta-links {\n\t\tmargin: {\n\t\t\tright: 32px;\n\t\t}\n\n\t\t.show-settings {\n\t\t\tborder-color: $gray-300;\n\t\t}\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tmargin: {\n\t\t\t\tright: 16px;\n\t\t\t\tbottom: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t&.rtl #screen-meta-links {\n\t\tmargin: {\n\t\t\tright: 0;\n\t\t\tleft: 32px;\n\t\t}\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tmargin: {\n\t\t\t\tright: 0;\n\t\t\t\tleft: 16px;\n\t\t\t}\n\t\t}\n\t}\n\n\t#screen-meta {\n\t\tborder-color: $gray-300;\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Postbox headings\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\t#poststuff {\n\t\t.postbox-header {\n\t\t\th2,\n\t\t\th3 {\n\t\t\t\tjustify-content: flex-start;\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t}\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t}\n\t\t\t\t@extend .acf-h3;\n\t\t\t\tcolor: $gray-700 !important;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Postbox drag state\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\t&.is-dragging-metaboxes\n\t\t.metabox-holder\n\t\t.postbox-container\n\t\t.meta-box-sortables {\n\t\tbox-sizing: border-box;\n\t\tpadding: 2px;\n\t\toutline: none;\n\t\tbackground-image: repeating-linear-gradient(\n\t\t\t\t0deg,\n\t\t\t\t$gray-500,\n\t\t\t\t$gray-500 5px,\n\t\t\t\ttransparent 5px,\n\t\t\t\ttransparent 10px,\n\t\t\t\t$gray-500 10px\n\t\t\t),\n\t\t\trepeating-linear-gradient(\n\t\t\t\t90deg,\n\t\t\t\t$gray-500,\n\t\t\t\t$gray-500 5px,\n\t\t\t\ttransparent 5px,\n\t\t\t\ttransparent 10px,\n\t\t\t\t$gray-500 10px\n\t\t\t),\n\t\t\trepeating-linear-gradient(\n\t\t\t\t180deg,\n\t\t\t\t$gray-500,\n\t\t\t\t$gray-500 5px,\n\t\t\t\ttransparent 5px,\n\t\t\t\ttransparent 10px,\n\t\t\t\t$gray-500 10px\n\t\t\t),\n\t\t\trepeating-linear-gradient(\n\t\t\t\t270deg,\n\t\t\t\t$gray-500,\n\t\t\t\t$gray-500 5px,\n\t\t\t\ttransparent 5px,\n\t\t\t\ttransparent 10px,\n\t\t\t\t$gray-500 10px\n\t\t\t);\n\t\tbackground-size: 1.5px 100%, 100% 1.5px, 1.5px 100%, 100% 1.5px;\n\t\tbackground-position: 0 0, 0 0, 100% 0, 0 100%;\n\t\tbackground-repeat: no-repeat;\n\t\tborder-radius: $radius-lg;\n\t}\n\n\t.ui-sortable-placeholder {\n\t\tborder: none;\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Search summary\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\t.subtitle {\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\theight: 24px;\n\t\tmargin: 0;\n\t\tpadding: {\n\t\t\ttop: 4px;\n\t\t\tright: 12px;\n\t\t\tbottom: 4px;\n\t\t\tleft: 12px;\n\t\t}\n\t\tbackground-color: $blue-50;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $blue-200;\n\t\t}\n\t\tborder-radius: $radius-md;\n\t\t@extend .p3;\n\n\t\tstrong {\n\t\t\tmargin: {\n\t\t\t\tleft: 5px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Action strip\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-actions-strip {\n\tdisplay: flex;\n\n\t.acf-btn {\n\t\tmargin: {\n\t\t\tright: 8px;\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Notices\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\t.acf-notice,\n\t.notice,\n\t#lost-connection-notice {\n\t\tposition: relative;\n\t\tbox-sizing: border-box;\n\t\tmin-height: 48px;\n\t\tmargin: {\n\t\t\ttop: 0 !important;\n\t\t\tright: 0 !important;\n\t\t\tbottom: 16px !important;\n\t\t\tleft: 0 !important;\n\t\t}\n\t\tpadding: {\n\t\t\ttop: 13px !important;\n\t\t\tright: 16px !important;\n\t\t\tbottom: 12px !important;\n\t\t\tleft: 50px !important;\n\t\t}\n\t\tbackground-color: #e7eff9;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: #9dbaee;\n\t\t}\n\t\tborder-radius: $radius-lg;\n\t\tbox-shadow: $elevation-01;\n\t\tcolor: $gray-700;\n\n\t\t&.update-nag {\n\t\t\tdisplay: block;\n\t\t\tposition: relative;\n\t\t\twidth: calc(100% - 44px);\n\t\t\tmargin: {\n\t\t\t\ttop: 48px !important;\n\t\t\t\tright: 44px !important;\n\t\t\t\tbottom: -32px !important;\n\t\t\t\tleft: 12px !important;\n\t\t\t}\n\t\t}\n\n\t\t.button {\n\t\t\theight: auto;\n\t\t\tmargin: {\n\t\t\t\tleft: 8px;\n\t\t\t}\n\t\t\tpadding: 0;\n\t\t\tborder: none;\n\t\t\t@extend .p5;\n\t\t}\n\n\t\t> div {\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tbottom: 0;\n\t\t\t}\n\t\t}\n\n\t\tp {\n\t\t\tflex: 1 0 auto;\n\t\t\tmax-width: 100%;\n\t\t\tline-height: 18px;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\n\t\t\t&.help {\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t}\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t}\n\t\t\t\t@extend .p7;\n\t\t\t\tcolor: rgba($gray-700, 0.7);\n\t\t\t}\n\t\t}\n\n\t\t// Dismiss button\n\t\t.acf-notice-dismiss,\n\t\t.notice-dismiss {\n\t\t\tposition: absolute;\n\t\t\ttop: 4px;\n\t\t\tright: 8px;\n\t\t\tpadding: 9px;\n\t\t\tborder: none;\n\n\t\t\t&:before {\n\t\t\t\tcontent: \"\";\n\t\t\t\t$icon-size: 20px;\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: relative;\n\t\t\t\tz-index: 600;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tbackground-color: $gray-500;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-close.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-close.svg\");\n\t\t\t}\n\n\t\t\t&:hover::before {\n\t\t\t\tbackground-color: $gray-700;\n\t\t\t}\n\t\t}\n\n\t\ta.acf-notice-dismiss {\n\t\t\tposition: absolute;\n\t\t\ttop: 5px;\n\t\t\tright: 24px;\n\n\t\t\t&:before {\n\t\t\t\tbackground-color: $gray-600;\n\t\t\t}\n\t\t}\n\n\t\t// Icon base styling\n\t\t&:before {\n\t\t\tcontent: \"\";\n\t\t\t$icon-size: 16px;\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\ttop: 15px;\n\t\t\tleft: 18px;\n\t\t\tz-index: 600;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t}\n\t\t\tbackground-color: #fff;\n\t\t\tborder: none;\n\t\t\tborder-radius: 0;\n\t\t\t-webkit-mask-size: contain;\n\t\t\tmask-size: contain;\n\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\tmask-repeat: no-repeat;\n\t\t\t-webkit-mask-position: center;\n\t\t\tmask-position: center;\n\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-info-solid.svg\");\n\t\t\tmask-image: url(\"../../images/icons/icon-info-solid.svg\");\n\t\t}\n\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\ttop: 9px;\n\t\t\tleft: 12px;\n\t\t\tz-index: 500;\n\t\t\twidth: 28px;\n\t\t\theight: 28px;\n\t\t\tbackground-color: $color-info;\n\t\t\tborder-radius: $radius-md;\n\t\t\tbox-shadow: $elevation-01;\n\t\t}\n\n\t\t.local-restore {\n\t\t\talign-items: center;\n\t\t\tmargin: {\n\t\t\t\ttop: -6px;\n\t\t\t\tbottom: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Persisted notices should be hidden by default as they will be shown by JS if required.\n\t.notice[data-persisted=\"true\"] {\n\t\tdisplay: none;\n\t}\n\n\t.notice.is-dismissible {\n\t\tpadding: {\n\t\t\tright: 56px;\n\t\t}\n\t}\n\n\t// Success notice\n\t.notice.notice-success {\n\t\tbackground-color: #edf7ef;\n\t\tborder-color: #b6deb9;\n\n\t\t&:before {\n\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n\t\t\tmask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n\t\t}\n\n\t\t&:after {\n\t\t\tbackground-color: $color-success;\n\t\t}\n\t}\n\n\t// Error notice\n\t.acf-notice.acf-error-message,\n\t.notice.notice-error,\n\t#lost-connection-notice {\n\t\tbackground-color: #f7eeeb;\n\t\tborder-color: #f1b6b3;\n\n\t\t&:before {\n\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-warning.svg\");\n\t\t\tmask-image: url(\"../../images/icons/icon-warning.svg\");\n\t\t}\n\n\t\t&:after {\n\t\t\tbackground-color: $color-danger;\n\t\t}\n\t}\n}\n\n.acf-admin-single-taxonomy,\n.acf-admin-single-post-type,\n.acf-admin-single-options-page {\n\t.notice-success {\n\t\t.acf-item-saved-text {\n\t\t\tfont-weight: 600;\n\t\t}\n\n\t\t.acf-item-saved-links {\n\t\t\tdisplay: flex;\n\t\t\tgap: 12px;\n\n\t\t\ta {\n\t\t\t\ttext-decoration: none;\n\t\t\t\topacity: 1;\n\n\t\t\t\t&:after {\n\t\t\t\t\tcontent: \"\";\n\t\t\t\t\twidth: 1px;\n\t\t\t\t\theight: 13px;\n\t\t\t\t\tdisplay: inline-flex;\n\t\t\t\t\tposition: relative;\n\t\t\t\t\ttop: 2px;\n\t\t\t\t\tleft: 6px;\n\t\t\t\t\tbackground-color: $gray-600;\n\t\t\t\t\topacity: 0.3;\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\t&:after {\n\t\t\t\t\t\tcontent: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.rtl.acf-field-group,\n.rtl.acf-internal-post-type {\n\t.notice {\n\t\tpadding-right: 50px !important;\n\n\t\t.notice-dismiss {\n\t\t\tleft: 8px;\n\t\t\tright: unset;\n\t\t}\n\n\t\t&:before {\n\t\t\tleft: unset;\n\t\t\tright: 10px;\n\t\t}\n\n\t\t&:after {\n\t\t\tleft: unset;\n\t\t\tright: 12px;\n\t\t}\n\t}\n\n\t&.acf-admin-single-taxonomy,\n\t&.acf-admin-single-post-type,\n\t&.acf-admin-single-options-page {\n\t\t.notice-success .acf-item-saved-links a {\n\t\t\t&:after {\n\t\t\t\tleft: unset;\n\t\t\t\tright: 6px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  ACF PRO label\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-pro-label {\n\tdisplay: inline-flex;\n\talign-items: center;\n\tmin-height: 22px;\n\tpadding: {\n\t\tright: 8px;\n\t\tleft: 8px;\n\t}\n\tbackground: $gradient-pro;\n\tbox-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.16);\n\tborder: none;\n\tborder-radius: 100px;\n\tfont-size: 11px;\n\ttext-transform: uppercase;\n\ttext-decoration: none;\n\tcolor: #fff;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Inline notice overrides\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\t.acf-field {\n\t\t// notice\n\t\t.acf-notice {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmin-height: 40px !important;\n\t\t\tmargin: {\n\t\t\t\tbottom: 6px !important;\n\t\t\t}\n\t\t\tpadding: {\n\t\t\t\ttop: 6px !important;\n\t\t\t\tleft: 40px !important;\n\t\t\t\tbottom: 6px !important;\n\t\t\t}\n\t\t\tmargin: 0 0 15px;\n\t\t\tbackground: #edf2ff;\n\t\t\tcolor: $gray-700 !important;\n\t\t\tborder-color: #2183b9;\n\t\t\tborder-radius: $radius-md;\n\n\t\t\t&:after {\n\t\t\t\ttop: 8px;\n\t\t\t\tleft: 8px;\n\t\t\t\twidth: 22px;\n\t\t\t\theight: 22px;\n\t\t\t}\n\n\t\t\t&:before {\n\t\t\t\ttop: 12px;\n\t\t\t\tleft: 12px;\n\t\t\t\twidth: 14px;\n\t\t\t\theight: 14px;\n\t\t\t}\n\n\t\t\t// error\n\t\t\t&.-error {\n\t\t\t\tbackground: #f7eeeb;\n\t\t\t\tborder-color: #f1b6b3;\n\t\t\t}\n\n\t\t\t// success\n\t\t\t&.-success {\n\t\t\t\tbackground: #edf7ef;\n\t\t\t\tborder-color: #b6deb9;\n\t\t\t}\n\n\t\t\t// warning\n\t\t\t&.-warning {\n\t\t\t\tbackground: #fdf8eb;\n\t\t\t\tborder-color: #f4dbb4;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\t#wpcontent {\n\t\tline-height: 140%;\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Links\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\n\ta {\n\t\tcolor: $blue-500;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headings\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-h1 {\n\tfont-size: 21px;\n\tfont-weight: 400;\n}\n\n.acf-h2 {\n\tfont-size: 18px;\n\tfont-weight: 400;\n}\n\n.acf-h3 {\n\tfont-size: 16px;\n\tfont-weight: 400;\n}\n\n.acf-admin-page,\n.acf-headerbar {\n\n\th1 {\n\t\t@extend .acf-h1;\n\t}\n\n\th2 {\n\t\t@extend .acf-h2;\n\t}\n\n\th3 {\n\t\t@extend .acf-h3;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Paragraphs\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-admin-page {\n\n\t.p1 {\n\t\tfont-size: 15px;\n\t}\n\n\t.p2 {\n\t\tfont-size: 14px;\n\t}\n\n\t.p3 {\n\t\tfont-size: 13.5px;\n\t}\n\n\t.p4 {\n\t\tfont-size: 13px;\n\t}\n\n\t.p5 {\n\t\tfont-size: 12.5px;\n\t}\n\n\t.p6 {\n\t\tfont-size: 12px;\n\t}\n\n\t.p7 {\n\t\tfont-size: 11.5px;\n\t}\n\n\t.p8 {\n\t\tfont-size: 11px;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Page titles\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-page-title {\n\t@extend .acf-h2;\n\tcolor: $gray-700;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide old / native WP titles from pages\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\n\t.acf-settings-wrap h1 {\n\t\tdisplay: none !important;\n\t}\n\n\t#acf-admin-tools h1:not(.acf-field-group-pro-features-title, .acf-field-group-pro-features-title-sm) {\n\t\tdisplay: none !important;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-small {\n\t@extend .p6;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Link focus style\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\ta:focus {\n\t\tbox-shadow: none;\n\t\toutline: none;\n\t}\n\n\ta:focus-visible {\n\t\tbox-shadow: 0 0 0 1px #4f94d4, 0 0 2px 1px rgb(79 148 212 / 80%);\n\t\toutline: 1px solid transparent;\n\t}\n}\n", ".acf-admin-page {\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  All Inputs\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"text\"],\n\tinput[type=\"search\"],\n\tinput[type=\"number\"],\n\ttextarea,\n\tselect {\n\t\tbox-sizing: border-box;\n\t\theight: 40px;\n\t\tpadding: {\n\t\t\tright: 12px;\n\t\t\tleft: 12px;\n\t\t};\n\t\tbackground-color: #fff;\n\t\tborder-color: $gray-300;\n\t\tbox-shadow: $elevation-01;\n\t\tborder-radius: $radius-md;\n\t\t@extend .p4;\n\t\tcolor: $gray-700;\n\n\t\t&:focus {\n\t\t\toutline: $outline;\n\t\t\tborder-color: $blue-400;\n\t\t}\n\n\t\t&:disabled {\n\t\t\tbackground-color: $gray-50;\n\t\t\tcolor: lighten($gray-500, 10%);\n\t\t}\n\n\t\t&::placeholder {\n\t\t\tcolor: $gray-400;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Read only text inputs\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"text\"] {\n\n\t\t&:read-only {\n\t\t\tbackground-color: $gray-50;\n\t\t\tcolor: $gray-400;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Number fields\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-field.acf-field-number {\n\n\t\t.acf-label,\n\t\t.acf-input input[type=\"number\"] {\n\t\t\tmax-width: 180px;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Textarea\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\ttextarea {\n\t\tbox-sizing: border-box;\n\t\tpadding: {\n\t\t\ttop: 10px;\n\t\t\tbottom: 10px;\n\t\t};\n\t\theight: 80px;\n\t\tmin-height: 56px;\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Select\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tselect {\n\t\tmin-width: 160px;\n\t\tmax-width: 100%;\n\t\tpadding: {\n\t\t\tright: 40px;\n\t\t\tleft: 12px;\n\t\t};\n\t\tbackground-image: url('../../images/icons/icon-chevron-down.svg');\n\t\tbackground-position: right 10px top 50%;\n\t\tbackground-size: 20px;\n\t\t@extend .p4;\n\n\t\t&:hover,\n\t\t&:focus {\n\t\t\tcolor: $blue-500;\n\t\t}\n\n\t\t&::before {\n\t\t\tcontent: '';\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\ttop: 5px;\n\t\t\tleft: 5px;\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t}\n\n\t}\n\n\t&.rtl {\n\t\tselect {\n\t\t\tpadding: {\n\t\t\t\tright: 12px;\n\t\t\t\tleft: 40px;\n\t\t\t};\n\t\t\tbackground-position: left 10px top 50%;\n\t\t}\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Radio Button & Checkbox base styling\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"radio\"],\n\tinput[type=\"checkbox\"] {\n\t\tbox-sizing: border-box;\n\t\twidth: 16px;\n\t\theight: 16px;\n\t\tpadding: 0;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-400;\n\t\t};\n\t\tbackground: #fff;\n\t\tbox-shadow: none;\n\n\t\t&:hover {\n\t\t\tbackground-color: $blue-50;\n\t\t\tborder-color: $blue-500;\n\t\t}\n\n\t\t&:checked,\n\t\t&:focus-visible {\n\t\t\tbackground-color: $blue-50;\n\t\t\tborder-color: $blue-500;\n\n\t\t\t&:before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: relative;\n\t\t\t\ttop: -1px;\n\t\t\t\tleft: -1px;\n\t\t\t\twidth: 16px;\n\t\t\t\theight: 16px;\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0;\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tbackground-size: cover;\n\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\tbackground-position: center;\n\t\t\t}\n\n\t\t}\n\n\t\t&:active {\n\t\t\tbox-shadow: 0px 0px 0px 3px $blue-50, 0px 0px 0px rgba(255, 54, 54, 0.25);\n\t\t}\n\n\t\t&:disabled {\n\t\t\tbackground-color: $gray-50;\n\t\t\tborder-color: $gray-300;\n\t\t}\n\n\t}\n\n\t&.rtl {\n\t\tinput[type=\"radio\"],\n\t\tinput[type=\"checkbox\"] {\n\t\t\t&:checked,\n\t\t\t&:focus-visible {\n\t\t\t\t&:before {\n\t\t\t\t\tleft: 1px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Radio Buttons\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"radio\"] {\n\n\t\t&:checked,\n\t\t&:focus {\n\n\t\t\t&:before {\n\t\t\t\tbackground-image: url('../../images/field-states/radio-active.svg');\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Checkboxes\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"checkbox\"] {\n\n\t\t&:checked,\n\t\t&:focus {\n\n\t\t\t&:before {\n\t\t\t\tbackground-image: url('../../images/field-states/checkbox-active.svg');\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Radio Buttons & Checkbox lists\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-radio-list,\n\t.acf-checkbox-list {\n\n\t\tli input[type=\"radio\"],\n\t\tli input[type=\"checkbox\"] {\n\t\t\tmargin: {\n\t\t\t\tright: 6px;\n\t\t\t};\n\t\t}\n\n\t\t&.acf-bl li {\n\t\t\tmargin: {\n\t\t\t\tbottom: 8px;\n\t\t\t};\n\n\t\t\t&:last-of-type {\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\n\t\t}\n\n\t\tlabel {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\talign-content: center;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  ACF Switch\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-switch {\n\t\twidth: 42px;\n\t\theight: 24px;\n\t\tborder: none;\n\t\tbackground-color: $gray-300;\n\t\tborder-radius: 12px;\n\n\t\t&:hover {\n\t\t\tbackground-color: $gray-400;\n\t\t}\n\n\t\t&:active {\n\t\t\tbox-shadow: 0px 0px 0px 3px $blue-50, 0px 0px 0px rgba(255, 54, 54, 0.25);\n\t\t}\n\n\t\t&.-on {\n\t\t\tbackground-color: $color-primary;\n\n\t\t\t&:hover {\n\t\t\t\tbackground-color: $color-primary-hover;\n\t\t\t}\n\n\t\t\t.acf-switch-slider {\n\t\t\t\tleft: 20px;\n\t\t\t}\n\n\t\t}\n\n\t\t.acf-switch-off,\n\t\t.acf-switch-on {\n\t\t\tvisibility: hidden;\n\t\t}\n\n\t\t.acf-switch-slider {\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t\tborder: none;\n\t\t\tborder-radius: 100px;\n\t\t\tbox-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);\n\t\t}\n\n\t}\n\n\t.acf-field-true-false {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\n\t\t.acf-label {\n\t\t\torder: 2;\n\t\t\tdisplay: block;\n\t\t\talign-items: center;\n\t\t\tmargin: {\n\t\t\t\ttop: 2px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 12px;\n\t\t\t};\n\n\t\t\tlabel {\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-tip {\n\t\t\t\tmargin: {\n\t\t\t\t\tleft: 12px;\n\t\t\t\t};\n\t\t\t}\n\t\t\t\n\t\t\t.description {\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 2px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\t\t\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t&.rtl {\n\t\t.acf-field-true-false {\n\t\t\t.acf-label {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 12px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-tip {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 12px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  File input button\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\n\tinput::file-selector-button {\n\t\tbox-sizing: border-box;\n\t\tmin-height: 40px;\n\t\tmargin: {\n\t\t\tright: 16px;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 8px;\n\t\t\tright: 16px;\n\t\t\tbottom: 8px;\n\t\t\tleft: 16px;\n\t\t};\n\t\tbackground-color: transparent;\n\t\tcolor: $color-primary !important;\n\t\tborder-radius: $radius-md;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $color-primary;\n\t\t};\n\t\ttext-decoration: none;\n\n\t\t&:hover {\n\t\t\tborder-color: $color-primary-hover;\n\t\t\tcursor: pointer;\n\t\t\tcolor: $color-primary-hover !important;\n\t\t}\n\n\t}\n\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Action Buttons\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.button {\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\theight: 40px;\n\t\tpadding: {\n\t\t\tright: 16px;\n\t\t\tleft: 16px;\n\t\t};\n\t\tbackground-color: transparent;\n\t\tborder-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: $blue-500;\n\t\tborder-radius: $radius-md;\n\t\t@extend .p4;\n\t\tcolor: $blue-500;\n\n\t\t&:hover {\n\t\t\tbackground-color: lighten($blue-50, 2%);\n\t\t\tborder-color: $color-primary;\n\t\t\tcolor: $color-primary;\n\t\t}\n\t\t&:focus {\n\t\t\tbackground-color: lighten($blue-50, 2%);\n\t\t\toutline: $outline;\n\t\t\tcolor: $color-primary;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Edit field group header\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.edit-field-group-header {\n\t\tdisplay: block !important;\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Select2 inputs\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-input {\n\n\t\t.select2-container.-acf .select2-selection {\n\t\t\tborder: none;\n\t\t\tline-height: 1;\n\t\t}\n\n\t\t.select2-container.-acf .select2-selection__rendered {\n\t\t\tbox-sizing: border-box;\n\t\t\tpadding: {\n\t\t\t\tright: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\tbackground-color: #fff;\n\t\t\tborder: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-300;\n\t\t\t};\n\t\t\tbox-shadow: $elevation-01;\n\t\t\tborder-radius: $radius-md;\n\t\t\t@extend .p4;\n\t\t\tcolor: $gray-700;\n\t\t}\n\n\t\t.select2-container--focus {\n\t\t\toutline: $outline;\n\t\t\tborder-color: $blue-400;\n\t\t\tborder-radius: $radius-md;\n\n\t\t\t.select2-selection__rendered {\n\t\t\t\tborder-color: $blue-400 !important;\n\t\t\t}\n\n\t\t\t&.select2-container--below.select2-container--open {\n\n\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\tborder-bottom-right-radius: 0 !important;\n\t\t\t\t\tborder-bottom-left-radius: 0 !important;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t&.select2-container--above.select2-container--open {\n\n\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\tborder-top-right-radius: 0 !important;\n\t\t\t\t\tborder-top-left-radius: 0 !important;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t.select2-container .select2-search--inline .select2-search__field {\n\t\t\tmargin: 0;\n\t\t\tpadding: {\n\t\t\t\tleft: 6px;\n\t\t\t};\n\n\t\t\t&:focus {\n\t\t\t\toutline: none;\n\t\t\t\tborder: none;\n\t\t\t}\n\n\t\t}\n\n\t\t.select2-container--default .select2-selection--multiple .select2-selection__rendered {\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 6px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 6px;\n\t\t\t};\n\t\t}\n\n\t\t.select2-selection__clear {\n\t\t\twidth: 18px;\n\t\t\theight: 18px;\n\t\t\tmargin: {\n\t\t\t\ttop: 12px;\n\t\t\t\tright: 1px;\n\t\t\t};\n\t\t\ttext-indent: 100%;\n\t\t\twhite-space: nowrap;\n\t\t\toverflow: hidden;\n\t\t\tcolor: #fff;\n\n\t\t\t&:before {\n\t\t\t\tcontent: '';\n\t\t\t\t$icon-size: 16px;\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 0;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-close.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-close.svg');\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t}\n\n\t\t\t&:hover::before {\n\t\t\t\tbackground-color: $blue-500;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  ACF label\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\n\t\t.acf-icon-help {\n\t\t\t$icon-size: 18px;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tbackground-color: $gray-400;\n\t\t}\n\n\t\tlabel {\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t}\n\t\t\n\t\t.description {\n\t\t\tmargin: {\n\t\t\t\ttop: 2px;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Tooltip for field name field setting (result of a fix for keyboard navigation)\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-field-setting-name .acf-tip {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 654px;\n\t\tcolor: #98A2B3;\n\n\t\t@at-root .rtl#{&} {\n\t\t\tleft: auto;\n\t\t\tright: 654px;\n\t\t}\n\n\t\t.acf-icon-help {\n\t\t\twidth: 18px;\n\t\t\theight: 18px;\n\t\t}\n\t}\n\n\t/* Field Type Selection select2 */\n\t.acf-field-setting-type,\n\t.acf-field-permalink-rewrite,\n\t.acf-field-query-var,\n\t.acf-field-capability,\n\t.acf-field-parent-slug,\n\t.acf-field-data-storage,\n\t.acf-field-manage-terms,\n\t.acf-field-edit-terms,\n\t.acf-field-delete-terms,\n\t.acf-field-assign-terms,\n\t.acf-field-meta-box {\n\t\t\n\t\t.select2-container.-acf {\n\t\t\tmin-height: 40px;\n\t\t}\n\n\t\t.select2-container--default .select2-selection--single {\n\t\t\t.select2-selection__rendered {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tposition: relative;\n\t\t\t\tz-index: 800;\n\t\t\t\tmin-height: 40px;\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 12px;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 12px;\n\t\t\t\t};\n\t\t\t}\n\t\t\t.field-type-icon {\n\t\t\t\ttop: auto;\n\t\t\t\twidth: 18px;\n\t\t\t\theight: 18px;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 2px;\n\t\t\t\t};\n\n\t\t\t\t&:before {\n\t\t\t\t\twidth: 9px;\n\t\t\t\t\theight: 9px;\n\t\t\t\t}\n\n\t\t\t}\n\t\t}\n\n\t\t.select2-container--open .select2-selection__rendered {\n\t\t\tborder-color: $blue-300 !important;\n\t\t\tborder-bottom-color: $gray-300 !important;\n\t\t}\n\n\t\t.select2-container--open.select2-container--below .select2-selection__rendered {\n\t\t\tborder-bottom-right-radius: 0 !important;\n\t\t\tborder-bottom-left-radius: 0 !important;\n\t\t}\n\n\t\t.select2-container--open.select2-container--above .select2-selection__rendered {\n\t\t\tborder-top-right-radius: 0 !important;\n\t\t\tborder-top-left-radius: 0 !important;\n\t\t\tborder-bottom-color: $blue-300 !important;\n\t\t\tborder-top-color: $gray-300 !important;\n\t\t}\n\n\t\t// icon margins\n\t\t.acf-selection.has-icon {\n\t\t\tmargin-left: 6px;\n\t\n\t\t\t@at-root .rtl#{&} {\n\t\t\t\tmargin-right: 6px;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// Dropdown icon\n\t\t.select2-selection__arrow {\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t\ttop: calc(50% - 10px);\n\t\t\tright: 12px;\n\t\t\tbackground-color: transparent;\n\t\t\t\n\t\t\t&:after {\n\t\t\t\tcontent: \"\";\n\t\t\t\t$icon-size: 20px;\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: absolute;\n\t\t\t\tz-index: 850;\n\t\t\t\ttop: 1px;\n\t\t\t\tleft: 0;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t\tbackground-color: $gray-500;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\ttext-indent: 500%;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\toverflow: hidden;\t\n\t\t\t}\n\t\t\t\n\t\t\tb[role=\"presentation\"] {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t\t\n\t\t}\n\n\t\t// Open state\n\t\t.select2-container--open {\n\t\t\t\n\t\t\t// Swap chevron icon\n\t\t\t.select2-selection__arrow:after {\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n\t\t\t}\n\t\t\t\n\t\t}\n\n\t}\n\n\t.field-type-select-results {\n\t\tposition: relative;\n\t\ttop: 4px;\n\t\tz-index: 1002;\n\t\tborder-radius: 0 0 $radius-md $radius-md;\n\t\tbox-shadow: 0px 8px 24px 4px rgba(16, 24, 40, 0.12);\n\t\t&.select2-dropdown--above {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column-reverse;\t  \n\t\t\ttop: 0;\n\t\t\tborder-radius: $radius-md $radius-md 0 0;\n\t\t\tz-index: 99999;\n\t\t}\n\t\t\n\t\t@at-root .select2-container.select2-container--open#{&} {\n\t\t\t// outline: 3px solid $blue-50;\n\t\t\tbox-shadow: 0px 0px 0px 3px #EBF5FA, 0px 8px 24px 4px rgba(16, 24, 40, 0.12);\n\t\t}\n\n\t\t// icon margins\n\t\t.acf-selection.has-icon {\n\t\t\tmargin-left: 6px;\n\n\t\t\t@at-root .rtl#{&} {\n\t\t\t\tmargin-right: 6px;\n\t\t\t}\n\t\t}\n\n\t\t// Search field\n\t\t.select2-search {\n\t\t\tposition: relative;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\n\t\t\t&--dropdown {\n\t\t\t\t&:after {\n\t\t\t\t\tcontent: \"\";\n\t\t\t\t\t$icon-size: 16px;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 12px;\n\t\t\t\t\tleft: 13px;\n\t\t\t\t\twidth: $icon-size;\n\t\t\t\t\theight: $icon-size;\n\t\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-search.svg\");\n\t\t\t\t\tmask-image: url(\"../../images/icons/icon-search.svg\");\n\t\t\t\t\tbackground-color: $gray-400;\n\t\t\t\t\tborder: none;\n\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\t\tmask-size: contain;\n\t\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t\t-webkit-mask-position: center;\n\t\t\t\t\tmask-position: center;\n\t\t\t\t\ttext-indent: 500%;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\toverflow: hidden;\n\n\t\t\t\t\t@at-root .rtl#{&} {\n\t\t\t\t\t\tright: 12px;\n\t\t\t\t\t\tleft: auto;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.select2-search__field {\n\t\t\t\tpadding-left: 38px;\n\n\t\t\t\tborder-right: 0;\n\t\t\t\tborder-bottom: 0;\n\t\t\t\tborder-left: 0;\n\t\t\t\tborder-radius: 0;\n\n\t\t\t\t@at-root .rtl#{&} {\n\t\t\t\t\tpadding-right: 38px;\n\t\t\t\t\tpadding-left: 0;\n\t\t\t\t}\n\n\t\t\t\t&:focus {\n\t\t\t\t\tborder-top-color: $gray-300;\n\t\t\t\t\toutline: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t.select2-results__options {\n\t\t\tmax-height: 440px;\n\t\t}\n\t\t\n\t\t.select2-results__option {\n\t\t\t.select2-results__option--highlighted {\n\t\t\t\tbackground-color: $blue-500 !important;\n\t\t\t\tcolor: $gray-50 !important;\n\t\t\t}\n\t\t}\n\n\t\t// List items\n\t\t.select2-results__option .select2-results__option {\n\t\t\tdisplay: inline-flex;\n\t\t\tposition: relative;\n\t\t\twidth: calc(100% - 24px);\n\t\t\tmin-height: 32px;\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 12px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 12px;\n\t\t\t}\n\t\t\talign-items: center;\n\t\t\t\n\t\t\t.field-type-icon {\n\t\t\t\ttop: auto;\n\t\t\t\twidth: 18px;\n\t\t\t\theight: 18px;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 2px;\n\t\t\t\t};\n\t\t\t\tbox-shadow: 0 0 0 1px $gray-50;\n\t\t\t\n\t\t\t\t&:before {\n\t\t\t\t\twidth: 9px;\n\t\t\t\t\theight: 9px;\n\t\t\t\t}\n\t\t\t\n\t\t\t}\n\t\t\t\n\t\t}\n\n\t\t.select2-results__option[aria-selected=\"true\"] {\n\t\t\tbackground-color: $blue-50 !important;\n\t\t\tcolor: $gray-700 !important;\n\t\t\t\n\t\t\t&:after {\n\t\t\t\tcontent: \"\";\n\t\t\t\t$icon-size: 16px;\n\t\t\t\tright: 13px;\n\t\t\t\tposition: absolute;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-check.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-check.svg\");\n\t\t\t\tbackground-color: $blue-500;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\ttext-indent: 500%;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\toverflow: hidden;\n\n\t\t\t\t@at-root .rtl#{&} {\n\t\t\t\t\tleft: 13px;\n\t\t\t\t\tright: auto;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.select2-results__group {\n\t\t\tdisplay: inline-flex;\n\t\t\talign-items: center;\n\t\t\twidth: calc(100% - 24px);\n\t\t\tmin-height: 25px;\n\t\t\tbackground-color: $gray-50;\n\t\t\tborder-top: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\t\t\tborder-bottom: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\t\t\tcolor: $gray-400;\n\t\t\tfont-size: 11px;\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 12px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 12px;\n\t\t\t};\n\t\t\tfont-weight: normal;\n\t\t}\n\t}\n\t\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  RTL arrow position\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t&.rtl  {\n\t\t\n\t\t.acf-field-setting-type,\n\t\t.acf-field-permalink-rewrite,\n\t\t.acf-field-query-var {\n\t\t\t\n\t\t\t.select2-selection__arrow:after {\n\t\t\tright: auto;\n\t\t\tleft: 10px;\n\t\t\t}\n\t\t}\n\t\t\n\t}\n\t\n}\n\n.rtl.post-type-acf-field-group,\n.rtl.acf-internal-post-type {\n\t.acf-field-setting-name .acf-tip {\n\t\tleft: auto;\n\t\tright: 654px;\n\t}\n}", "/*---------------------------------------------------------------------------------------------\n*\n*  Field Groups\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-internal-post-type {\n\n\t// Hide tablenav top.\n\t.tablenav.top {\n\t\tdisplay: none;\n\t}\n\n\t// Fix margin due to hidden tablenav.\n\t.subsubsub {\n\t\tmargin-bottom: 3px;\n\t}\n\n\t// table.\n\t.wp-list-table {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t}\n\t\tborder-radius: $radius-lg;\n\t\tborder: none;\n\t\toverflow: hidden;\n\t\tbox-shadow: $elevation-01;\n\n\t\tstrong {\n\t\t\tcolor: $gray-400;\n\t\t\tmargin: 0;\n\t\t}\n\n\t\ta.row-title {\n\t\t\tfont-size: 13px !important;\n\t\t\tfont-weight: 500;\n\t\t}\n\n\t\tth,\n\t\ttd {\n\t\t\tcolor: $gray-700;\n\n\t\t\t&.sortable a {\n\t\t\t\tpadding: 0;\n\t\t\t}\n\n\t\t\t&.check-column {\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 12px;\n\t\t\t\t\tright: 16px;\n\t\t\t\t\tleft: 16px;\n\t\t\t\t};\n\n\t\t\t\t@media screen and (max-width: $md) {\n\t\t\t\t\tvertical-align: top;\n\t\t\t\t\tpadding: {\n\t\t\t\t\t\tright: 2px;\n\t\t\t\t\t\tleft: 10px;\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tinput {\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0;\n\t\t\t}\n\n\t\t\t.acf-more-items {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\tflex-direction: row;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 0px 6px 1px;\n\t\t\t\tgap: 8px;\n\t\t\t\twidth: 25px;\n\t\t\t\theight: 16px;\n\t\t\t\tbackground: $gray-200;\n\t\t\t\tborder-radius: 100px;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tfont-size: 10px;\n\t\t\t\tcolor: $gray-600;\n\t\t\t}\n\n\t\t\t.acf-emdash {\n\t\t\t\tcolor: $gray-300;\n\t\t\t}\n\t\t}\n\n\t\t// Table headers\n\t\tthead th, thead td,\n\t\ttfoot th, tfoot td {\n\t\t\theight: 48px;\n\t\t\tpadding: {\n\t\t\t\tright: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t};\n\t\t\tbox-sizing: border-box;\n\t\t\tbackground-color: $gray-50;\n\t\t\tborder-color: $gray-200;\n\t\t\t@extend .p4;\n\t\t\tfont-weight: 500;\n\n\t\t\t@media screen and (max-width: $md) {\n\t\t\t\tpadding: {\n\t\t\t\t\tright: 16px;\n\t\t\t\t\tleft: 8px;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t&.check-column {\n\t\t\t\t@media screen and (max-width: $md) {\n\t\t\t\t\tvertical-align: middle;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t// Table body\n\t\ttbody th,\n\t\ttbody td {\n\t\t\tbox-sizing: border-box;\n\t\t\theight: 60px;\n\t\t\tpadding: {\n\t\t\t\ttop: 10px;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 10px;\n\t\t\t\tleft: 24px;\n\t\t\t};\n\t\t\tvertical-align: top;\n\t\t\tbackground-color: #fff;\n\t\t\tborder-bottom: {\n\t\t\t\twidth: 1px;\n\t\t\t\tcolor: $gray-200;\n\t\t\t\tstyle: solid;\n\t\t\t};\n\t\t\t@extend .p4;\n\n\t\t\t@media screen and (max-width: $md) {\n\t\t\t\tpadding: {\n\t\t\t\t\tright: 16px;\n\t\t\t\t\tleft: 8px;\n\t\t\t\t};\n\t\t\t}\n\n\t\t}\n\n\t\t.column-acf-key {\n\t\t\twhite-space: nowrap;\n\t\t}\n\n\t\t// SVG icons\n\t\t.column-acf-key .acf-icon-key-solid {\n\t\t\tdisplay: inline-block;\n\t\t\tposition: relative;\n\t\t\tbottom: -2px;\n\t\t\twidth: 15px;\n\t\t\theight: 15px;\n\t\t\tmargin: {\n\t\t\t\tright: 4px;\n\t\t\t};\n\t\t\tcolor: $gray-400;\n\t\t}\n\n\t\t// Post location icon\n\t\t.acf-location .dashicons {\n\t\t\tposition: relative;\n\t\t\tbottom: -2px;\n\t\t\twidth: 16px;\n\t\t\theight: 16px;\n\t\t\tmargin: {\n\t\t\t\tright: 6px;\n\t\t\t};\n\t\t\tfont-size: 16px;\n\t\t\tcolor: $gray-400;\n\t\t}\n\n\t\t.post-state {\n\t\t\t@extend .p3;\n\t\t\tcolor: $gray-500;\n\t\t}\n\n\t\t// Add subtle hover background to define row.\n\t\ttr:hover,\n\t\ttr:focus-within {\n\t\t\tbackground: #f7f7f7;\n\n\t\t\t.row-actions {\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t};\n\n\t\t}\n\n\t\t// Use less specific identifier to inherit mobile styling.\n\t\t@media screen and ( min-width: 782px ) {\n\t\t\t.column-acf-count { width: 10%; }\n\t\t}\n\n\t\t.row-actions {\n\t\t\tspan.file {\n\t\t\t\tdisplay: block;\n\t\t\t\toverflow: hidden;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t}\n\t\t}\n\t}\n\n\t&.rtl {\n\t\t.wp-list-table {\n\t\t\t.column-acf-key .acf-icon-key-solid {\n\t\t\t\tmargin: {\n\t\t\t\t\tleft: 4px;\n\t\t\t\t\tright: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-location .dashicons {\n\t\t\t\tmargin: {\n\t\t\t\t\tleft: 6px;\n\t\t\t\t\tright: 0;\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\t// Actions\n\t.row-actions {\n\t\tmargin: {\n\t\t\ttop: 2px;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t}\n\t\t@extend .p5;\n\t\tline-height: 14px;\n\t\tcolor: $gray-300;\n\n\t\t.trash a {\n\t\t\tcolor: $acf_error;\n\t\t}\n\n\t}\n\n\n\t// Remove padding from checkbox column\n\t.widefat thead td.check-column,\n\t.widefat tfoot td.check-column {\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t};\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRow actions\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type {\n\n\t.row-actions {\n\t\t@extend .p6;\n\n\t\ta:hover {\n\t\t\tcolor: darken($color-primary-hover, 10%);\n\t\t}\n\n\t\t.trash a {\n\t\t\tcolor: #a00;\n\t\t\t&:hover { color: #f00; }\n\t\t}\n\n\t\t&.visible {\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t\topacity: 1;\n\t\t}\n\n\t}\n\n}\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRow hover\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type {\n\n\t#the-list tr:hover td,\n\t#the-list tr:hover th {\n\t\tbackground-color: lighten($blue-50, 3%);\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Table Nav\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-internal-post-type {\n\n\t.tablenav {\n\t\tmargin: {\n\t\t\ttop: 24px;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\tcolor: $gray-500;\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tSearch box\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type #posts-filter p.search-box {\n\tmargin: {\n\t\ttop: 5px;\n\t\tright: 0;\n\t\tbottom: 24px;\n\t\tleft: 0;\n\t};\n\n\t#post-search-input {\n\t\tmin-width: 280px;\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 8px;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t}\n\n\t@media screen and (max-width: 768px) {\n\t\tdisplay: flex;\n\t\tbox-sizing: border-box;\n\t\tpadding-right: 24px;\n\t\tmargin-right: 16px;\n\t\tposition: inherit;\n\n\t\t#post-search-input {\n\t\t\tmin-width: auto;\n\t\t}\n\n\t}\n\n}\n\n.rtl.acf-internal-post-type #posts-filter p.search-box {\n\t#post-search-input {\n\t\tmargin: {\n\t\t\tright: 0;\n\t\t\tleft: 8px;\n\t\t};\n\t}\n\n\t@media screen and (max-width: 768px) {\n\t\tpadding-left: 24px;\n\t\tpadding-right: 0;\n\t\tmargin-left: 16px;\n\t\tmargin-right: 0;\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tStatus tabs\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type .subsubsub {\n\tdisplay: flex;\n\talign-items: flex-end;\n\theight: 40px;\n\tmargin: {\n\t\tbottom: 16px;\n\t};\n\n\tli {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 4px;\n\t\t};\n\t\tcolor: $gray-400;\n\t\t@extend .p4;\n\n\t\t.count {\n\t\t\tcolor: $gray-500;\n\t\t}\n\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPagination\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type {\n\n\t.tablenav-pages {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t&.no-pages{\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t.displaying-num {\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 16px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t}\n\n\t\t.pagination-links {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t#table-paging {\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 4px;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 8px;\n\t\t\t\t};\n\n\t\t\t\t.total-pages {\n\t\t\t\t\tmargin: {\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Hide pagination if there's only 1 page\n\t\t&.one-page .pagination-links {\n\t\t\tdisplay: none;\n\t\t}\n\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPagination buttons & icons\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type .tablenav-pages .pagination-links .button {\n\tdisplay: inline-flex;\n\talign-items: center;\n\talign-content: center;\n\tjustify-content: center;\n\tmin-width: 40px;\n\tmargin: {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t};\n\tpadding: {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t};\n\tbackground-color: transparent;\n\n\t// Pagination Buttons\n\t&:nth-child(1),\n\t&:nth-child(2),\n\t&:last-child,\n\t&:nth-last-child(2) {\n\t\tdisplay: inline-block;\n\t\tposition: relative;\n\t\ttext-indent: 100%;\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\tmargin: {\n\t\t\tleft: 4px;\n\t\t}\n\n\t\t// Pagination Button Icons\n\t\t&:before {\n\t\t\t$icon-size: 20px;\n\t\t\tcontent: \"\";\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\t$icon-size: $icon-size;\n\t\t\tbackground-color: $link-color;\n\t\t\tborder-radius: 0;\n\t\t\t-webkit-mask-size: $icon-size;\n\t\t\tmask-size: $icon-size;\n\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\tmask-repeat: no-repeat;\n\t\t\t-webkit-mask-position: center;\n\t\t\tmask-position: center;\n\t\t}\n\n\t}\n\n\t// First Page Icon\n\t&:nth-child(1):before {\n\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-left-double.svg');\n\t\tmask-image: url('../../images/icons/icon-chevron-left-double.svg');\n\t}\n\n\t// Previous Page Icon\n\t&:nth-child(2):before {\n\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-left.svg');\n\t\tmask-image: url('../../images/icons/icon-chevron-left.svg');\n\t}\n\n\t// Next Page Icon\n\t&:nth-last-child(2):before {\n\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-right.svg');\n\t\tmask-image: url('../../images/icons/icon-chevron-right.svg');\n\t}\n\n\t// Last Page Icon\n\t&:last-child:before {\n\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-right-double.svg');\n\t\tmask-image: url('../../images/icons/icon-chevron-right-double.svg');\n\t}\n\n\t// Pagination Button Hover State\n\t&:hover {\n\t\tborder-color: $blue-600;\n\t\tbackground-color: rgba($link-color, .05);\n\n\t\t&:before {\n\t\t\tbackground-color: $blue-600;\n\t\t}\n\n\t}\n\n\t// Pagination Button Disabled State\n\t&.disabled {\n\t\tbackground-color: transparent !important;\n\n\t\t&.disabled:before {\n\t\t\tbackground-color: $gray-300;\n\t\t}\n\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Empty state\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-no-field-groups-wrapper,\n.acf-no-taxonomies-wrapper,\n.acf-no-post-types-wrapper,\n.acf-no-options-pages-wrapper {\n\tdisplay: flex;\n\tjustify-content: center;\n\tpadding: {\n\t\ttop: 48px;\n\t\tbottom: 48px;\n\t};\n\n\t.acf-no-field-groups-inner,\n\t.acf-no-taxonomies-inner,\n\t.acf-no-post-types-inner,\n\t.acf-no-options-pages-inner {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tjustify-content: center;\n\t\talign-content: center;\n\t\talign-items: flex-start;\n\t\ttext-align: center;\n\t\tmax-width: 380px;\n\t\tmin-height: 320px;\n\n\t\timg,\n\t\th2,\n\t\tp {\n\t\t\tflex: 1 0 100%;\n\t\t}\n\n\t\th2 {\n\t\t\t@extend .acf-h2;\n\t\t\tmargin: {\n\t\t\t\ttop: 32px;\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t\tpadding: 0;\n\t\t\tcolor: $gray-700;\n\t\t}\n\n\t\tp {\n\t\t\t@extend .p2;\n\t\t\tmargin: {\n\t\t\t\ttop: 12px;\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t\tpadding: 0;\n\t\t\tcolor: $gray-500;\n\n\t\t\t&.acf-small {\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: relative;\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 32px;\n\t\t\t\t};\n\t\t\t\t@extend .p6;\n\t\t\t}\n\n\t\t}\n\n\n\t\timg {\n\t\t\tmax-width: 284px;\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t}\n\n\t\t.acf-btn {\n\t\t\tmargin: {\n\t\t\t\ttop: 32px;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t.acf-no-post-types-inner,\n\t.acf-no-options-pages-inner {\n\t\timg {\n\t\t\twidth: 106px;\n\t\t\theight: 88px;\n\t\t}\n\t}\n\n\t.acf-no-taxonomies-inner {\n\t\timg {\n\t\t\twidth: 98px;\n\t\t\theight: 88px;\n\t\t}\n\t}\n\n};\n\n.acf-no-field-groups,\n.acf-no-post-types,\n.acf-no-taxonomies,\n.acf-no-options-pages {\n\n\t#the-list tr:hover td,\n\t#the-list tr:hover th,\n\t.acf-admin-field-groups .wp-list-table tr:hover,\n\t.striped > tbody > :nth-child(odd), ul.striped > :nth-child(odd), .alternate {\n\t\tbackground-color: transparent !important;\n\t}\n\n\t.wp-list-table {\n\n\t\tthead,\n\t\ttfoot {\n\t\t\tdisplay: none;\n\t\t}\n\n\t}\n\n}\n\n.acf-internal-post-type #the-list .no-items td {\n\tvertical-align: middle;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small screen list table info toggle\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-internal-post-type {\n\n\t.wp-list-table .toggle-row:before {\n\t\ttop: 4px;\n\t\tleft: 16px;\n\t\tborder-radius: 0;\n\t\t$icon-size: 20px;\n\t\tcontent: \"\";\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t\twidth: 16px;\n\t\theight: 16px;\n\t\t$icon-size: $icon-size;\n\t\tbackground-color: $link-color;\n\t\tborder-radius: 0;\n\t\t-webkit-mask-size: $icon-size;\n\t\tmask-size: $icon-size;\n\t\t-webkit-mask-repeat: no-repeat;\n\t\tmask-repeat: no-repeat;\n\t\t-webkit-mask-position: center;\n\t\tmask-position: center;\n\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-down.svg');\n\t\tmask-image: url('../../images/icons/icon-chevron-down.svg');\n\t\ttext-indent: 100%;\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t}\n\n\t.wp-list-table .is-expanded .toggle-row:before {\n\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-up.svg');\n\t\tmask-image: url('../../images/icons/icon-chevron-up.svg');\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small screen checkbox\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-internal-post-type {\n\n\t@media screen and (max-width: $md) {\n\n\t\t.widefat th input[type=\"checkbox\"],\n\t\t.widefat thead td input[type=\"checkbox\"],\n\t\t.widefat tfoot td input[type=\"checkbox\"] {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\n\t}\n\n}", "/*---------------------------------------------------------------------------------------------\n*\n*  Admin Navigation\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-toolbar {\n\tposition: unset;\n\ttop: 32px;\n\theight: 72px;\n\tz-index: 800;\n\tbackground: $gray-700;\n\tcolor: $gray-400;\n\n\t.acf-admin-toolbar-inner {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-content: center;\n\t\talign-items: center;\n\t\tmax-width: 100%;\n\n\t\t.acf-nav-wrap {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t@media screen and (max-width: 1250px) {\n\t\t\t\t.acf-header-tab-acf-post-type,\n\t\t\t\t.acf-header-tab-acf-taxonomy {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t\t.acf-more {\n\t\t\t\t\t.acf-header-tab-acf-post-type,\n\t\t\t\t\t.acf-header-tab-acf-taxonomy {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\t\t\n\t\t.acf-nav-upgrade-wrap {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t.acf-nav-wpengine-logo {\n\t\t\tdisplay: inline-flex;\n\t\t\tmargin-left: 24px;\n\n\t\t\t@media screen and (max-width: 1000px) {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\t}\n\n\t@media screen and (max-width: $md) {\n\t\tposition: static;\n\t}\n\n\t.acf-logo {\n\t\tdisplay: flex;\n\t\tmargin: {\n\t\t\tright: 24px;\n\t\t}\n\t\ttext-decoration: none;\n\t\t\n\t\t.acf-pro-label {\n\t\t\tmargin: {\n\t\t\t\tleft: 8px;\n\t\t\t};\n\t\t}\n\n\t\timg {\n\t\t\tdisplay: block;\n\t\t\tmax-width: 55px;\n\t\t\tline-height: 0%;\n\t\t}\n\t}\n\n\th2 {\n\t\tdisplay: none;\n\t\tcolor: $gray-50;\n\t}\n\n\t.acf-tab {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbox-sizing: border-box;\n\t\tmin-height: 40px;\n\t\tmargin: {\n\t\t\tright: 8px;\n\t\t}\n\t\tpadding: {\n\t\t\ttop: 8px;\n\t\t\tright: 16px;\n\t\t\tbottom: 8px;\n\t\t\tleft: 16px;\n\t\t}\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: transparent;\n\t\t}\n\t\tborder-radius: $radius-md;\n\t\t@extend .p4;\n\t\tcolor: $gray-400;\n\t\ttext-decoration: none;\n\n\t\t&.is-active {\n\t\t\tbackground-color: $gray-600;\n\t\t\tcolor: #fff;\n\t\t}\n\t\t&:hover {\n\t\t\tbackground-color: $gray-600;\n\t\t\tcolor: $gray-50;\n\t\t}\n\t\t&:focus-visible {\n\t\t\tborder: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-500;\n\t\t\t}\n\t\t}\n\t\t&:focus {\n\t\t\tbox-shadow: none;\n\t\t}\n\t}\n\n\t.acf-more {\n\t\t&:hover {\n\t\t\t.acf-tab.acf-more-tab {\n\t\t\t\tbackground-color: $gray-600;\n\t\t\t\tcolor: $gray-50;\n\t\t\t}\n\t\t}\n\t\t\n\t\tul {\n\t\t\tdisplay: none;\n\t\t\tposition: absolute;\n\t\t\tbox-sizing: border-box;\n\t\t\tbackground: #fff;\n\t\t\tz-index: 1051;\n\t\t\toverflow: hidden;\n\t\t\tmin-width: 280px;\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\tpadding: 0;\n\t\t\tborder-radius: $radius-lg;\n\t\t\tbox-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.04), 0px 8px 23px rgba(0, 0, 0, 0.12);\n\t\t\t\n\t\t\t.acf-wp-engine {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\tmin-height: 48px;\n\t\t\t\tborder-top: 1px solid rgba(0, 0, 0, 0.08);\n\t\t\t\tbackground: #ECFBFC;\n\t\t\t\t\n\t\t\t\ta {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\tborder-top: none;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t}\n\n\t\t\tli {\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0 16px;\n\n\t\t\t\t.acf-header-tab-acf-post-type,\n\t\t\t\t.acf-header-tab-acf-taxonomy {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t\t&.acf-more-section-header {\n\t\t\t\t\tbackground: $gray-50;\n\t\t\t\t\tpadding: 1px 0 0 0;\n\t\t\t\t\tmargin-top: -1px;\n\t\t\t\t\tborder-top: 1px solid $gray-200;\n\t\t\t\t\tborder-bottom: 1px solid $gray-200;\n\n\t\t\t\t\tspan {\n\t\t\t\t\t\tcolor: $gray-600;\n\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\tfont-weight: bold;\n\n\t\t\t\t\t\t&:hover {\n\t\t\t\t\t\t\tbackground: $gray-50;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// Links\n\t\t\t\ta {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t\tcolor: $gray-800;\n\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\tborder-top: {\n\t\t\t\t\t\twidth: 1px;\n\t\t\t\t\t\tstyle: solid;\n\t\t\t\t\t\tcolor: $gray-100;\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&.acf-tab.is-active {\n\t\t\t\t\t\tbackground-color: unset;\n\t\t\t\t\t\tcolor: $blue-500;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\ti.acf-icon {\n\t\t\t\t\t\tdisplay: none !important;\n\t\t\t\t\t\t$icon-size: 16px;\n\t\t\t\t\t\twidth: $icon-size;\n\t\t\t\t\t\theight: $icon-size;\n\t\t\t\t\t\t-webkit-mask-size: $icon-size;\n\t\t\t\t\t\tmask-size: $icon-size;\n\t\t\t\t\t\tbackground-color: $gray-400 !important;\n\t\t\t\t\t}\n\n\t\t\t\t\t.acf-requires-pro {\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tcolor: white;\n\t\t\t\t\t\tbackground: $gradient-pro;\n\t\t\t\t\t\tbackground-size: 140% 20%;\n\t\t\t\t\t\tbackground-position: 100% 0;\n\t\t\t\t\t\tborder-radius: 100px;\n\t\t\t\t\t\tfont-size: 11px;\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\tright: 16px;\n\t\t\t\t\t\tpadding: {\n\t\t\t\t\t\t\tright: 6px;\n\t\t\t\t\t\t\tleft: 6px;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\timg.acf-wp-engine-pro {\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\theight: 16px;\n\t\t\t\t\t\twidth: auto;\n\t\t\t\t\t}\n\n\t\t\t\t\t.acf-wp-engine-upsell-pill {\n\t\t\t\t\t\tdisplay: inline-flex;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tmin-height: 22px;\n\t\t\t\t\t\tborder-radius: 100px;\n\t\t\t\t\t\tfont-size: 11px;\n\t\t\t\t\t\tpadding: {\n\t\t\t\t\t\t\tright: 8px;\n\t\t\t\t\t\t\tleft: 8px;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbackground: #0ECAD4;\n\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t\ttext-shadow: 0px 1px 0 rgba(0, 0, 0, 0.12);\n\t\t\t\t\t\ttext-transform: uppercase;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// First list item\n\t\t\t\t&:first-child {\n\t\t\t\t\ta {\n\t\t\t\t\t\tborder-bottom: none;\n\t\t\t\t\t}\t\n\t\t\t\t}\n\t\t\t\t\n\t\t\t}\n\t\t\t\n\t\t\t&:hover,\n\t\t\t&:focus {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\t\t&:hover,\n\t\t&:focus {\n\t\t\tul {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Within wpcontent.\n\t#wpcontent & {\n\t\tbox-sizing: border-box;\n\t\tmargin-left: -20px;\n\t\tpadding: {\n\t\t\ttop: 16px;\n\t\t\tright: 32px;\n\t\t\tbottom: 16px;\n\t\t\tleft: 32px;\n\t\t}\n\t}\n\n\t// Mobile\n\t@media screen and (max-width: 600px) {\n\t\t& {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n}\n\n.rtl {\n\t#wpcontent .acf-admin-toolbar {\n\t\tmargin-left: 0;\n\t\tmargin-right: -20px;\n\n\t\t.acf-tab {\n\t\t\tmargin: {\n\t\t\t\tleft: 8px;\n\t\t\t\tright: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t.acf-logo {\n\t\tmargin: {\n\t\t\tright: 0;\n\t\t\tleft: 32px;\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Toolbar Icons\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-toolbar {\n\t.acf-tab,\n\t.acf-more {\n\t\ti.acf-icon {\n\t\t\tdisplay: none; // Icons only shown for specified nav items below\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t\tleft: -2px;\n\t\t\t}\n\t\t\t\n\t\t\t&.acf-icon-dropdown {\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t\t$icon-size: 16px;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\t-webkit-mask-size: $icon-size;\n\t\t\t\tmask-size: $icon-size;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: -6px;\n\t\t\t\t\tleft: 6px;\n\t\t\t\t};\n\t\t\t}\n\t\t\t\n\t\t}\n\n\t\t// Only show icons for specified nav items, stops third party plugin items with no icon appearing broken\n\t\t&.acf-header-tab-acf-field-group,\n\t\t&.acf-header-tab-acf-post-type,\n\t\t&.acf-header-tab-acf-taxonomy,\n\t\t&.acf-header-tab-acf-tools,\n\t\t&.acf-header-tab-acf-settings-updates,\n\t\t&.acf-header-tab-acf-more {\n\t\t\ti.acf-icon {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t}\n\t\t}\n\n\t\t&.is-active,\n\t\t&:hover {\n\t\t\ti.acf-icon {\n\t\t\t\tbackground-color: $gray-200;\n\t\t\t}\n\t\t}\n\t}\n\n\t.rtl & .acf-tab {\n\t\ti.acf-icon {\n\t\t\tmargin: {\n\t\t\t\tright: -2px;\n\t\t\t\tleft: 8px;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Field groups tab\n\t.acf-header-tab-acf-field-group {\n\t\ti.acf-icon {\n\t\t\t$icon-url: url(\"../../images/icons/icon-field-groups.svg\");\n\t\t\t-webkit-mask-image: $icon-url;\n\t\t\tmask-image: $icon-url;\n\t\t}\n\t}\n\n\t// Post types tab\n\t.acf-header-tab-acf-post-type {\n\t\ti.acf-icon {\n\t\t\t$icon-url: url(\"../../images/icons/icon-post-type.svg\");\n\t\t\t-webkit-mask-image: $icon-url;\n\t\t\tmask-image: $icon-url;\n\t\t}\n\t}\n\n\t// Taxonomies tab\n\t.acf-header-tab-acf-taxonomy {\n\t\ti.acf-icon {\n\t\t\t$icon-url: url(\"../../images/icons/icon-taxonomies.svg\");\n\t\t\t-webkit-mask-image: $icon-url;\n\t\t\tmask-image: $icon-url;\n\t\t}\n\t}\n\n\t// Tools tab\n\t.acf-header-tab-acf-tools {\n\t\ti.acf-icon {\n\t\t\t$icon-url: url(\"../../images/icons/icon-tools.svg\");\n\t\t\t-webkit-mask-image: $icon-url;\n\t\t\tmask-image: $icon-url;\n\t\t}\n\t}\n\n\t// Updates tab\n\t.acf-header-tab-acf-settings-updates {\n\t\ti.acf-icon {\n\t\t\t$icon-url: url(\"../../images/icons/icon-updates.svg\");\n\t\t\t-webkit-mask-image: $icon-url;\n\t\t\tmask-image: $icon-url;\n\t\t}\n\t}\n\t\n\t// More tab\n\t.acf-header-tab-acf-more {\n\t\ti.acf-icon-more {\n\t\t\t$icon-url: url(\"../../images/icons/icon-extended-menu.svg\");\n\t\t\t-webkit-mask-image: $icon-url;\n\t\t\tmask-image: $icon-url;\n\t\t}\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n*\n*  Hide WP default controls\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\n\th1.wp-heading-inline {\n\t\tdisplay: none;\n\t}\n\n\t.wrap .wp-heading-inline + .page-title-action {\n\t\tdisplay: none;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headerbar\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-headerbar {\n\tdisplay: flex;\n\talign-items: center;\n\tposition: sticky;\n\ttop: 32px;\n\tz-index: 700;\n\tbox-sizing: border-box;\n\tmin-height: 72px;\n\tmargin: {\n\t\tleft: -20px;\n\t};\n\tpadding: {\n\t\ttop: 8px;\n\t\tright: 32px;\n\t\tbottom: 8px;\n\t\tleft: 32px;\n\t};\n\tbackground-color: #fff;\n\tbox-shadow: $elevation-01;\n\n\t.acf-headerbar-inner {\n\t\tflex: 1 1 auto;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmax-width: $max-width;\n\t}\n\n\t.acf-page-title {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 16px;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\n\t\t.acf-duplicated-from {\n\t\t\tcolor: $gray-400;\n\t\t}\n\t}\n\n\t@media screen and (max-width: $md) {\n\t\tposition: static;\n\t}\n\n\t@media screen and (max-width: 600px) {\n\t\tjustify-content: space-between;\n\t\tposition: relative;\n\t\ttop: 46px;\n\t\tmin-height: 64px;\n\t\tpadding: {\n\t\t\tright: 12px;\n\t\t};\n\t}\n\n\t.acf-headerbar-content {\n\t\tflex: 1 1 auto;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t@media screen and (max-width: $md) {\n\t\t\tflex-wrap: wrap;\n\n\t\t\t.acf-headerbar-title,\n\t\t\t.acf-title-wrap {\n\t\t\t\tflex: 1 1 100%;\n\t\t\t}\n\n\t\t\t.acf-title-wrap {\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 8px;\n\t\t\t\t};\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t.acf-input-error {\n\t\tborder: 1px rgba($color-danger, .5) solid !important;\n\t\tbox-shadow: 0px 0px 0px 3px rgba(209, 55, 55, 0.12), 0px 0px 0px rgba(255, 54, 54, 0.25) !important;\n\t\tbackground-image: url('../../images/icons/icon-warning-alt-red.svg');\n\t\tbackground-position: right 10px top 50%;\n\t\tbackground-size: 20px;\n\t\tbackground-repeat: no-repeat;\n\n\t\t&:focus {\n\t\t\toutline: none !important;\n\t\t\tborder: 1px rgba($color-danger, .8) solid !important;\n\t\t\tbox-shadow: 0px 0px 0px 3px rgba(209, 55, 55, 0.16), 0px 0px 0px rgba(255, 54, 54, 0.25) !important;\n\t\t}\n\t}\n\n\t.acf-headerbar-title-field {\n\t\tmin-width: 320px;\n\n\t\t@media screen and (max-width: $md) {\n\t\t\tmin-width: 100%;\n\t\t}\n\t}\n\n\t.acf-headerbar-actions {\n\t\tdisplay: flex;\n\n\t\t.acf-btn {\n\t\t\tmargin: {\n\t\t\t\tleft: 8px;\n\t\t\t};\n\t\t};\n\n\t\t.disabled {\n\t\t\tbackground-color: $gray-100;\n\t\t\tcolor: $gray-400 !important;\n\t\t\tborder: 1px $gray-300 solid;\n\t\t\tcursor: default;\n\t\t}\n\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Edit Field Group Headerbar\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-headerbar-field-editor {\n\tposition: sticky;\n\ttop: 32px;\n\tz-index: 1020;\n\tmargin: {\n\t\tleft: -20px;\n\t};\n\twidth: calc(100% + 20px);\n\n\t@media screen and (max-width: $md) {\n\t\tposition: relative;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\tmargin: {\n\t\t\tleft: 0;\n\t\t};\n\t\tpadding: {\n\t\t\tright: 8px;\n\t\t\tleft: 8px;\n\t\t};\n\t}\n\n\t@media screen and (max-width: $sm) {\n\t\tposition: relative;\n\t\ttop: 46px;\n\t}\n\n\n\t.acf-headerbar-inner {\n\n\t\t@media screen and (max-width: $md) {\n\t\t\tflex-wrap: wrap;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-content: flex-start;\n\t\t\talign-items: flex-start;\n\t\t\twidth: 100%;\n\n\t\t\t.acf-page-title {\n\t\t\t\tflex: 1 1 auto;\n\t\t\t}\n\n\t\t\t.acf-headerbar-actions {\n\t\t\t\tflex: 1 1 100%;\n\t\t\t\tmargin-top: 8px;\n\t\t\t\tgap: 8px;\n\n\t\t\t\t.acf-btn {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tdisplay: inline-flex;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t.acf-page-title {\n\t\tmargin: {\n\t\t\tright: 16px;\n\t\t};\n\t}\n\n}\n\n.rtl .acf-headerbar,\n.rtl .acf-headerbar-field-editor {\n\tmargin-left: 0;\n\tmargin-right: -20px;\n\n\t.acf-page-title {\n\t\tmargin: {\n\t\t\tleft: 16px;\n\t\t\tright: 0;\n\t\t};\n\t}\n\n\t.acf-headerbar-actions {\n\t\t.acf-btn {\n\t\t\tmargin: {\n\t\t\t\tleft: 0;\n\t\t\t\tright: 8px;\n\t\t\t};\n\t\t};\n\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n*\n*  ACF Buttons\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-btn {\n\tdisplay: inline-flex;\n\talign-items: center;\n\tbox-sizing: border-box;\n\tmin-height: 40px;\n\tpadding: {\n\t\ttop: 8px;\n\t\tright: 16px;\n\t\tbottom: 8px;\n\t\tleft: 16px;\n\t}\n\tbackground-color: $color-primary;\n\tborder-radius: $radius-md;\n\tborder: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: rgba($gray-900, 20%);\n\t}\n\ttext-decoration: none;\n\tcolor: #fff !important;\n\ttransition: all 0.2s ease-in-out;\n\ttransition-property: background, border, box-shadow;\n\n\t&:disabled {\n\t\tbackground-color: red;\n\t}\n\n\t&:hover {\n\t\tbackground-color: $color-primary-hover;\n\t\tcolor: #fff;\n\t\tcursor: pointer;\n\t}\n\n\t&.acf-btn-sm {\n\t\tmin-height: 32px;\n\t\tpadding: {\n\t\t\ttop: 4px;\n\t\t\tright: 12px;\n\t\t\tbottom: 4px;\n\t\t\tleft: 12px;\n\t\t}\n\t\t@extend .p4;\n\t}\n\n\t&.acf-btn-secondary {\n\t\tbackground-color: transparent;\n\t\tcolor: $color-primary !important;\n\t\tborder-color: $color-primary;\n\n\t\t&:hover {\n\t\t\tbackground-color: lighten($blue-50, 2%);\n\t\t}\n\t}\n\n\t&.acf-btn-tertiary {\n\t\tbackground-color: transparent;\n\t\tcolor: $gray-500 !important;\n\t\tborder-color: $gray-300;\n\n\t\t&:hover {\n\t\t\tcolor: $gray-500 !important;\n\t\t\tborder-color: $gray-400;\n\t\t}\n\t}\n\n\t&.acf-btn-clear {\n\t\tbackground-color: transparent;\n\t\tcolor: $gray-500 !important;\n\t\tborder-color: transparent;\n\n\t\t&:hover {\n\t\t\tcolor: $blue-500 !important;\n\t\t}\n\t}\n\n\t&.acf-btn-pro {\n\t\tbackground: $gradient-pro;\n\t\tbackground-size: 180% 80%;\n\t\tbackground-position: 100% 0;\n\t\ttransition: background-position 0.5s;\n\n\t\t&:hover {\n\t\t\tbackground-position: 0 0;\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Button icons\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-btn {\n\ti.acf-icon {\n\t\t$icon-size: 20px;\n\t\twidth: $icon-size;\n\t\theight: $icon-size;\n\t\t-webkit-mask-size: $icon-size;\n\t\tmask-size: $icon-size;\n\t\tmargin: {\n\t\t\tright: 6px;\n\t\t\tleft: -4px;\n\t\t}\n\t}\n\n\t&.acf-btn-sm {\n\t\ti.acf-icon {\n\t\t\t$icon-size: 16px;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\t-webkit-mask-size: $icon-size;\n\t\t\tmask-size: $icon-size;\n\t\t\tmargin: {\n\t\t\t\tright: 6px;\n\t\t\t\tleft: -2px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.rtl .acf-btn {\n\ti.acf-icon {\n\t\tmargin: {\n\t\t\tright: -4px;\n\t\t\tleft: 6px;\n\t\t}\n\t}\n\n\t&.acf-btn-sm {\n\t\ti.acf-icon {\n\t\t\tmargin: {\n\t\t\t\tright: -4px;\n\t\t\t\tleft: 2px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Delete field group button\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-btn.acf-delete-field-group {\n\t&:hover {\n\t\tbackground-color: lighten($color-danger, 44%);\n\t\tborder-color: $color-danger !important;\n\t\tcolor: $color-danger !important;\n\t}\n}\n", "/*--------------------------------------------------------------------------------------------\n*\n*\tIcon base styling\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-internal-post-type,\n.post-type-acf-field-group {\n\ti.acf-icon {\n\t\t$icon-size: 20px;\n\t\tdisplay: inline-flex;\n\t\twidth: $icon-size;\n\t\theight: $icon-size;\n\t\tbackground-color: currentColor;\n\t\tborder: none;\n\t\tborder-radius: 0;\n\t\t-webkit-mask-size: contain;\n\t\tmask-size: contain;\n\t\t-webkit-mask-repeat: no-repeat;\n\t\tmask-repeat: no-repeat;\n\t\t-webkit-mask-position: center;\n\t\tmask-position: center;\n\t\ttext-indent: 500%;\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tIcons\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\n\t// Action icons for Flexible Content Field\n\ti.acf-field-setting-fc-delete, i.acf-field-setting-fc-duplicate {\n\t\tbox-sizing: border-box;\n\n\t\t/* Auto layout */\n\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 8px;\n\t\tcursor: pointer;\n\n\t\twidth: 32px;\n\t\theight: 32px;\n\n\t\t/* Base / White */\n\n\t\tbackground: #FFFFFF;\n\t\t/* Gray/300 */\n\n\t\tborder: 1px solid $gray-300;\n\t\t/* Elevation/01 */\n\n\t\tbox-shadow: $elevation-01;\n\t\tborder-radius: 6px;\n\n\t\t/* Inside auto layout */\n\n\t\tflex: none;\n\t\torder: 0;\n\t\tflex-grow: 0;\n\t}\n\n\ti.acf-icon-plus {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-add.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-add.svg\");\n\t}\n\n\ti.acf-icon-stars {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-stars.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-stars.svg\");\n\t}\n\n\ti.acf-icon-help {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-help.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-help.svg\");\n\t}\n\n\ti.acf-icon-key {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-key.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-key.svg\");\n\t}\n\n\ti.acf-icon-regenerate {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-regenerate.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-regenerate.svg\");\n\t}\n\n\ti.acf-icon-trash, button.acf-icon-trash {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-trash.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-trash.svg\");\n\t}\n\t\n\ti.acf-icon-extended-menu, button.acf-icon-extended-menu {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-extended-menu.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-extended-menu.svg\");\n\t}\n\n\ti.acf-icon.-duplicate, button.acf-icon-duplicate {\n\t\t-webkit-mask-image: url(\"../../images/field-type-icons/icon-field-clone.svg\");\n\t\tmask-image: url(\"../../images/field-type-icons/icon-field-clone.svg\");\n\n\t\t&:before,\n\t\t&:after {\n\t\t\tcontent: none;\n\t\t}\n\t}\n\n\ti.acf-icon-arrow-right {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-arrow-right.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-arrow-right.svg\");\n\t}\n\n\ti.acf-icon-arrow-up-right {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-arrow-up-right.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-arrow-up-right.svg\");\n\t}\n\n\ti.acf-icon-arrow-left {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-arrow-left.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-arrow-left.svg\");\n\t}\n\n\ti.acf-icon-chevron-right,\n\t.acf-icon.-right {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n\t}\n\n\ti.acf-icon-chevron-left,\n\t.acf-icon.-left {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-left.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-chevron-left.svg\");\n\t}\n\n\ti.acf-icon-key-solid {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-key-solid.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-key-solid.svg\");\n\t}\n\n\ti.acf-icon-globe,\n\t.acf-icon.-globe {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-globe.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-globe.svg\");\n\t}\n\n\ti.acf-icon-image,\n\t.acf-icon.-picture {\n\t\t-webkit-mask-image: url(\"../../images/field-type-icons/icon-field-image.svg\");\n\t\tmask-image: url(\"../../images/field-type-icons/icon-field-image.svg\");\n\t}\n\t\n\ti.acf-icon-warning {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-warning-alt.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-warning-alt.svg\");\n\t}\n\t\n\ti.acf-icon-warning-red {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-warning-alt-red.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-warning-alt-red.svg\");\n\t}\n\n\ti.acf-icon-dots-grid {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-dots-grid.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-dots-grid.svg\");\n\t}\n\n\ti.acf-icon-play {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-play.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-play.svg\");\n\t}\n\t\n\ti.acf-icon-lock {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-lock.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-lock.svg\");\n\t}\n\n\ti.acf-icon-document {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-document.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-document.svg\");\n\t}\n\t/*--------------------------------------------------------------------------------------------\n\t*\n\t*\tInactive group icon\n\t*\n\t*--------------------------------------------------------------------------------------------*/\n\t.post-type-acf-field-group,\n\t.acf-internal-post-type {\n\t\t.post-state {\n\t\t\tfont-weight: normal;\n\n\t\t\t.dashicons.dashicons-hidden {\n\t\t\t\t$icon-size: 18px;\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: $icon-size;\n\t\t\t\tmask-size: $icon-size;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-hidden.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-hidden.svg\");\n\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tEdit field group page postbox header icons\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-field-group-fields,\n#acf-field-group-options,\n#acf-advanced-settings {\n\t.postbox-header,\n\t.acf-sub-field-list-header {\n\t\th2,\n\t\th3 {\n\t\t\tdisplay: inline-flex;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-content: stretch;\n\t\t\talign-items: center;\n\n\t\t\t&:before {\n\t\t\t\tcontent: \"\";\n\t\t\t\t$icon-size: 20px;\n\t\t\t\tdisplay: inline-block;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 8px;\n\t\t\t\t}\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.rtl #acf-field-group-fields,\n.rtl #acf-field-group-options {\n\t.postbox-header,\n\t.acf-sub-field-list-header {\n\t\th2,\n\t\th3 {\n\t\t\t&:before {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 0;\n\t\t\t\t\tleft: 8px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// Field icon\n#acf-field-group-fields .postbox-header h2:before,\nh3.acf-sub-field-list-title:before,\n.acf-link-field-groups-popup h3:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-fields.svg\");\n\tmask-image: url(\"../../images/icons/icon-fields.svg\");\n}\n\n// Create options page modal icon\n.acf-create-options-page-popup h3:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-sliders.svg\");\n\tmask-image: url(\"../../images/icons/icon-sliders.svg\");\n}\n\n// Settings icon\n#acf-field-group-options .postbox-header h2:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-settings.svg\");\n\tmask-image: url(\"../../images/icons/icon-settings.svg\");\n}\n\n// Layout icon\n.acf-field-setting-fc_layout .acf-field-settings-fc_head label:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-layout.svg\");\n\tmask-image: url(\"../../images/icons/icon-layout.svg\");\n}\n\n// Advanced post type and taxonomies settings icon\n.acf-admin-single-post-type,\n.acf-admin-single-taxonomy,\n.acf-admin-single-options-page {\n\n\t#acf-advanced-settings .postbox-header h2:before {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-post-type.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-post-type.svg\");\n\t}\n\n}\n\n// Flexible Content reorder\n.acf-field-setting-fc_layout .acf-field-settings-fc_head:hover .reorder-layout:before {\n\twidth: 20px;\n\theight: 11px;\n\tbackground-color: $gray-600 !important;\n\t-webkit-mask-image: url(\"../../images/icons/icon-draggable.svg\");\n\tmask-image: url(\"../../images/icons/icon-draggable.svg\");\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPostbox expand / collapse icon\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group, \n.post-type-acf-field-group #acf-field-group-fields,\n.post-type-acf-field-group #acf-field-group-options,\n.post-type-acf-field-group .postbox,\n.acf-admin-single-post-type #acf-advanced-settings,\n.acf-admin-single-taxonomy #acf-advanced-settings,\n.acf-admin-single-options-page #acf-advanced-settings{\n\t\n\t.postbox-header .handle-actions {\n\t\tdisplay: flex;\n\n\t\t.toggle-indicator:before {\n\t\t\tcontent: \"\";\n\t\t\t$icon-size: 20px;\n\t\t\tdisplay: inline-flex;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tbackground-color: currentColor;\n\t\t\tborder: none;\n\t\t\tborder-radius: 0;\n\t\t\t-webkit-mask-size: contain;\n\t\t\tmask-size: contain;\n\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\tmask-repeat: no-repeat;\n\t\t\t-webkit-mask-position: center;\n\t\t\tmask-position: center;\n\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n\t\t\tmask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n\t\t}\n\t}\n\n\t// Closed state\n\t&.closed {\n\t\t.postbox-header .handle-actions {\n\t\t\t.toggle-indicator:before {\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Tools & updates page heading icons\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\t#acf-admin-tool-export,\n\t#acf-admin-tool-import,\n\t#acf-license-information,\n\t#acf-update-information {\n\t\th2,\n\t\th3 {\n\t\t\tdisplay: inline-flex;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-content: stretch;\n\t\t\talign-items: center;\n\n\t\t\t&:before {\n\t\t\t\tcontent: \"\";\n\t\t\t\t$icon-size: 20px;\n\t\t\t\tdisplay: inline-block;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 8px;\n\t\t\t\t}\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t}\n\t\t}\n\t}\n\n\t&.rtl {\n\t\t#acf-admin-tool-export,\n\t\t#acf-admin-tool-import,\n\t\t#acf-license-information,\n\t\t#acf-update-information {\n\t\t\th2,\n\t\t\th3 {\n\t\t\t\t&:before {\n\t\t\t\t\tmargin: {\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\tleft: 8px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// Export icon\n.post-type-acf-field-group #acf-admin-tool-export h2:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-export.svg\");\n\tmask-image: url(\"../../images/icons/icon-export.svg\");\n}\n\n// Import icon\n.post-type-acf-field-group #acf-admin-tool-import h2:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-import.svg\");\n\tmask-image: url(\"../../images/icons/icon-import.svg\");\n}\n\n// License information icon\n.post-type-acf-field-group #acf-license-information h3:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-key.svg\");\n\tmask-image: url(\"../../images/icons/icon-key.svg\");\n}\n\n// Update information icon\n.post-type-acf-field-group #acf-update-information h3:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-info.svg\");\n\tmask-image: url(\"../../images/icons/icon-info.svg\");\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tAdmin field icons\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-single-field-group .acf-input {\n\t.acf-icon {\n\t\t$icon-size: 18px;\n\t\twidth: $icon-size;\n\t\theight: $icon-size;\n\t}\n}\n", "/*--------------------------------------------------------------------------------------------\n*\n*\tField type icon base styling\n*\n*--------------------------------------------------------------------------------------------*/\n.field-type-icon {\n\tbox-sizing: border-box;\n\tdisplay: inline-flex;\n\talign-content: center;\n\talign-items: center;\n\tjustify-content: center;\n\tposition: relative;\n\twidth: 24px;\n\theight: 24px;\n\ttop: -4px;\n\tbackground-color: $blue-50;\n\tborder: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $blue-200;\n\t};\n\tborder-radius: 100%;\n\n\t&:before {\n\t\t$icon-size: 14px;\n\t\tcontent: \"\";\n\t\twidth: $icon-size;\n\t\theight: $icon-size;\n\t\tposition: relative;\n\t\tbackground-color: $blue-500;\n\t\t-webkit-mask-size: cover;\n\t\tmask-size: cover;\n\t\t-webkit-mask-repeat: no-repeat;\n\t\tmask-repeat: no-repeat;\n\t\t-webkit-mask-position: center;\n\t\tmask-position: center;\n\t\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-default.svg');\n\t\tmask-image: url('../../images/field-type-icons/icon-field-default.svg');\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tField type icons\n*\n*--------------------------------------------------------------------------------------------*/\n\n// Text field\n.field-type-icon.field-type-icon-text:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-text.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-text.svg');\n}\n\n// Textarea\n.field-type-icon.field-type-icon-textarea:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-textarea.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-textarea.svg');\n}\n\n// Textarea\n.field-type-icon.field-type-icon-textarea:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-textarea.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-textarea.svg');\n}\n\n// Number\n.field-type-icon.field-type-icon-number:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-number.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-number.svg');\n}\n\n// Range\n.field-type-icon.field-type-icon-range:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-range.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-range.svg');\n}\n\n// Email\n.field-type-icon.field-type-icon-email:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-email.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-email.svg');\n}\n\n// URL\n.field-type-icon.field-type-icon-url:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-url.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-url.svg');\n}\n\n// Password\n.field-type-icon.field-type-icon-password:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-password.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-password.svg');\n}\n\n// Image\n.field-type-icon.field-type-icon-image:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-image.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-image.svg');\n}\n\n// File\n.field-type-icon.field-type-icon-file:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-file.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-file.svg');\n}\n\n// WYSIWYG\n.field-type-icon.field-type-icon-wysiwyg:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-wysiwyg.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-wysiwyg.svg');\n}\n\n// oEmbed\n.field-type-icon.field-type-icon-oembed:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-oembed.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-oembed.svg');\n}\n\n// Gallery\n.field-type-icon.field-type-icon-gallery:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-gallery.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-gallery.svg');\n}\n\n// Select\n.field-type-icon.field-type-icon-select:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-select.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-select.svg');\n}\n\n// Checkbox\n.field-type-icon.field-type-icon-checkbox:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-checkbox.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-checkbox.svg');\n}\n\n// Radio Button\n.field-type-icon.field-type-icon-radio:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-radio.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-radio.svg');\n}\n\n// Button Group\n.field-type-icon.field-type-icon-button-group:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-button-group.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-button-group.svg');\n}\n\n// True / False\n.field-type-icon.field-type-icon-true-false:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-true-false.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-true-false.svg');\n}\n\n// Link\n.field-type-icon.field-type-icon-link:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-link.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-link.svg');\n}\n\n// Post Object\n.field-type-icon.field-type-icon-post-object:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-post-object.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-post-object.svg');\n}\n\n// Page Link\n.field-type-icon.field-type-icon-page-link:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-page-link.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-page-link.svg');\n}\n\n// Relationship\n.field-type-icon.field-type-icon-relationship:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-relationship.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-relationship.svg');\n}\n\n// Taxonomy\n.field-type-icon.field-type-icon-taxonomy:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-taxonomy.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-taxonomy.svg');\n}\n\n// User\n.field-type-icon.field-type-icon-user:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-user.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-user.svg');\n}\n\n// Google Map\n.field-type-icon.field-type-icon-google-map:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-google-map.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-google-map.svg');\n}\n\n// Date Picker\n.field-type-icon.field-type-icon-date-picker:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-date-picker.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-date-picker.svg');\n}\n\n// Date / Time Picker\n.field-type-icon.field-type-icon-date-time-picker:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-date-time-picker.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-date-time-picker.svg');\n}\n\n// Time Picker\n.field-type-icon.field-type-icon-time-picker:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-time-picker.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-time-picker.svg');\n}\n\n// Color Picker\n.field-type-icon.field-type-icon-color-picker:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-color-picker.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-color-picker.svg');\n}\n\n// Message\n.field-type-icon.field-type-icon-message:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-message.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-message.svg');\n}\n\n// Accordion\n.field-type-icon.field-type-icon-accordion:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-accordion.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-accordion.svg');\n}\n\n// Tab\n.field-type-icon.field-type-icon-tab:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-tab.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-tab.svg');\n}\n\n// Group\n.field-type-icon.field-type-icon-group:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-group.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-group.svg');\n}\n\n// Repeater\n.field-type-icon.field-type-icon-repeater:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-repeater.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-repeater.svg');\n}\n\n\n// Flexible Content\n.field-type-icon.field-type-icon-flexible-content:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-flexible-content.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-flexible-content.svg');\n}\n\n// Clone\n.field-type-icon.field-type-icon-clone:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-clone.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-clone.svg');\n}", "/*---------------------------------------------------------------------------------------------\n*\n* Tools page layout\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-admin-tools {\n\n\t.postbox-header {\n\t\tdisplay: none; // Hide native WP postbox headers\n\t}\n\n\t.acf-meta-box-wrap.-grid {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\n\t\t.postbox {\n\t\t\twidth: 100%;\n\t\t\tclear: none;\n\t\t\tfloat: none;\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\n\t\t\t@media screen and (max-width: $md) {\n\t\t\t\tflex: 1 1 100%;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t.acf-meta-box-wrap.-grid .postbox:nth-child(odd) {\n\t\tmargin: {\n\t\t\tleft: 0;\n\t\t};\n\t}\n\n\t.meta-box-sortables {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(2, 1fr);\n\t\tgrid-template-rows: repeat(1, 1fr);\n\t\tgrid-column-gap: 32px;\n\t\tgrid-row-gap: 32px;\n\n\t\t@media screen and (max-width: $md) {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-content: flex-start;\n\t\t\talign-items: center;\n\t\t\tgrid-column-gap: 8px;\n\t\t\tgrid-row-gap: 8px;\n\t\t}\n\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Tools export pages\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-admin-tools {\n\n\t&.tool-export {\n\n\t\t.inside {\n\t\t\tmargin: 0;\n\t\t}\n\n\t\t// ACF custom postbox header\n\t\t.acf-postbox-header {\n\t\t\tmargin: {\n\t\t\t\tbottom: 24px;\n\t\t\t};\n\t\t}\n\n\t\t// Main postbox area\n\t\t.acf-postbox-main {\n\t\t\tborder: none;\n\t\t\tmargin: 0;\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t}\n\n\t\t.acf-postbox-columns {\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 280px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\tpadding: 0;\n\n\t\t\t.acf-postbox-side {\n\t\t\t\tpadding: 0;\n\n\t\t\t\t.acf-panel {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t}\n\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t\t.acf-btn {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t.meta-box-sortables {\n\t\t\tdisplay: block;\n\t\t}\n\n\t\t.acf-panel {\n\t\t\tborder: none;\n\n\t\t\th3 {\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0;\n\t\t\t\tcolor: $gray-700;\n\t\t\t\t@extend .p4;\n\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t.acf-checkbox-list {\n\t\t\tmargin: {\n\t\t\t\ttop: 16px;\n\t\t\t};\n\t\t\tborder: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-300;\n\t\t\t};\n\t\t\tborder-radius: $radius-md;\n\n\t\t\tli {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 48px;\n\t\t\t\talign-items: center;\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: {\n\t\t\t\t\tright: 12px;\n\t\t\t\t\tleft: 12px;\n\t\t\t\t};\n\t\t\t\tborder-bottom: {\n\t\t\t\t\twidth: 1px;\n\t\t\t\t\tstyle: solid;\n\t\t\t\t\tcolor: $gray-200;\n\t\t\t\t};\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tborder-bottom: none;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}", "/*---------------------------------------------------------------------------------------------\n*\n*  Updates layout\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-settings-wrap.acf-updates {\n\tdisplay: flex;\n\tflex-direction: row;\n\tflex-wrap: wrap;\n\tjustify-content: flex-start;\n\talign-content: flex-start;\n\talign-items: flex-start;\n}\n\n.custom-fields_page_acf-settings-updates .acf-admin-notice,\n.custom-fields_page_acf-settings-updates .acf-upgrade-notice,\n.custom-fields_page_acf-settings-updates .notice {\n\tflex: 1 1 100%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  ACF Box\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-settings-wrap.acf-updates {\n\n\t.acf-box {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\n\t\t.inner {\n\t\t\tpadding: {\n\t\t\t\ttop: 24px;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t};\n\t\t}\n\n\t\t@media screen and (max-width: $md) {\n\t\t\tflex: 1 1 100%;\n\t\t}\n\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Notices\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-settings-wrap.acf-updates {\n\n\t.acf-admin-notice {\n\t\tflex: 1 1 100%;\n\t\tmargin: {\n\t\t\ttop: 16px;\n\t\t\tright: 0;\n\t\t\tleft: 0;\n\t\t};\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* License information\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-license-information {\n\tflex: 1 1 65%;\n\tmargin: {\n\t\tright: 32px;\n\t};\n\t\n\t@media screen and (max-width: 1024px) {\n\t\tmargin: {\n\t\t\tright: 0;\n\t\t\tbottom: 32px;\n\t\t};\n\t}\n\n\t.acf-activation-form {\n\t\tmargin: {\n\t\t\ttop: 24px;\n\t\t};\n\t}\n\n\tlabel {\n\t\tfont-weight: 500;\n\t}\n\n\t.acf-input-wrap {\n\t\tmargin: {\n\t\t\ttop: 8px;\n\t\t\tbottom: 24px;\n\t\t};\n\t}\n\n\t#acf_pro_license {\n\t\twidth: 100%;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Update information table\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-update-information {\n\tflex: 1 1 35%;\n\tmax-width: calc(35% - 32px);\n\n\t.form-table {\n\n\t\tth,\n\t\ttd {\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 24px;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\t@extend .p4;\n\t\t\tcolor: $gray-700;\n\t\t}\n\n\t}\n\n\t.acf-update-changelog {\n\t\tmargin: {\n\t\t\ttop: 8px;\n\t\t\tbottom: 24px;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 8px;\n\t\t};\n\t\tborder-top: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t};\n\t\tcolor: $gray-700;\n\n\t\th4 {\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t}\n\n\t\tp {\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tbottom: 16px;\n\t\t\t};\n\n\t\t\t&:last-of-type {\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tem {\n\t\t\t\t@extend .p6;\n\t\t\t\tcolor: $gray-500;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t.acf-btn {\n\t\tdisplay: inline-flex;\n\t}\n\n}", "/*--------------------------------------------------------------------------------------------\n*\n*\tHeader pro upgrade button\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-toolbar {\n\n\ta.acf-admin-toolbar-upgrade-btn {\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\talign-self: stretch;\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 16px;\n\t\t\tbottom: 0;\n\t\t\tleft: 16px;\n\t\t};\n\t\tbackground: $gradient-pro;\n\t\tbox-shadow: inset 0 0 0 1px rgba(255,255,255,.16);\n\t\tbackground-size: 180% 80%;\n\t\tbackground-position: 100% 0;\n\t\ttransition: background-position .5s;\n\t\tborder-radius: $radius-md;\n\t\ttext-decoration: none;\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t&:hover {\n\t\t\tbackground-position: 0 0;\n\t\t}\n\n\t\t&:focus {\n\t\t\tborder: none;\n\t\t\toutline: none;\n\t\t\tbox-shadow: none;\n\t\t}\n\n\t\tp {\n\t\t\tmargin: 0;\n\t\t\tpadding: {\n\t\t\t\ttop: 8px;\n\t\t\t\tbottom: 8px;\n\t\t\t}\n\t\t\t@extend .p4;\n\t\t\tfont-weight: normal;\n\t\t\ttext-transform: none;\n\t\t\tcolor: #fff;\n\t\t}\n\n\t\t.acf-icon {\n\t\t\t$icon-size: 18px;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tmargin: {\n\t\t\t\tright: 6px;\n\t\t\t\tleft: -2px;\n\t\t\t};\n\t\t\tbackground-color: $gray-50;\n\t\t}\n\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Upsell block\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-page #tmpl-acf-field-group-pro-features,\n.acf-admin-page #acf-field-group-pro-features {\n\tdisplay: none;\n\talign-items: center;\n\tmin-height: 120px;\n\tbackground-color: #121833;\n\tbackground-image: url(../../images/pro-upgrade-grid-bg.svg), url(../../images/pro-upgrade-overlay.svg);\n\tbackground-repeat: repeat, no-repeat;\n\tbackground-size: 1224px, 1880px;\n\tbackground-position: left top, -520px -680px;\n\tcolor: $gray-200;\n\tborder-radius: 8px;\n\tmargin-top: 24px;\n\tmargin-bottom: 24px;\n\n\t@media screen and (max-width: 768px) {\n\t\tbackground-size: 1024px, 980px;\n\t\tbackground-position: left top, -500px -200px;\n\t}\n\n\t@media screen and (max-width: 1200px) {\n\t\tbackground-size: 1024px, 1880px;\n\t\tbackground-position: left top, -520px -300px;\n\t}\n\n\t.postbox-header {\n\t\tdisplay: none;\n\t}\n\n\t.inside {\n\t\twidth: 100%;\n\t\tborder: none;\n\t\tpadding: 0;\n\t}\n\n\t.acf-field-group-pro-features-wrapper {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-content: stretch;\n\t\talign-items: center;\n\t\tgap: 96px;\n\t\theight: 358px;\n\t\tmax-width: 950px;\n\t\tmargin: 0 auto;\n\t\tpadding: 0 35px;\n\t\t\n\t\t@media screen and (max-width: 1200px) {\n\t\t\tgap: 48px;\n\t\t}\n\t\t\n\t\t@media screen and (max-width: 768px) {\n\t\t\tgap: 0;\n\t\t}\n\n\t\t.acf-field-group-pro-features-title,\n\t\t.acf-field-group-pro-features-title-sm {\n\t\t\tfont-weight: 590;\n\t\t\tline-height: 150%;\n\n\t\t\t.acf-pro-label {\n\t\t\t\tfont-weight: normal;\n\t\t\t\tmargin-top: -4px;\n\t\t\t\tmargin-left: 2px;\n\t\t\t\tvertical-align: middle;\n\t\t\t\theight: 22px;\n\t\t\t}\n\t\t\t\n\t\t}\n\n\t\t.acf-field-group-pro-features-title-sm {\n\t\t\tdisplay: none !important;\n\t\t\tfont-size: 18px;\n\n\t\t\t.acf-pro-label {\n\t\t\t\tfont-size: 10px;\n\t\t\t\theight: 20px;\n\t\t\t}\n\t\t\t\n\t\t\t@media screen and (max-width: 768px) {\n\t\t\t\twidth: 100%;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t\t\n\t\t}\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tflex-direction: column;\n\t\t\tflex-wrap: wrap;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-content: flex-start;\n\t\t\talign-items: flex-start;\n\t\t\tpadding: 32px 32px 0 32px;\n\t\t\theight: unset;\n\n\t\t\t.acf-field-group-pro-features-title-sm {\n\t\t\t\tdisplay: block !important;\n\t\t\t\tmargin-bottom: 24px;\n\t\t\t}\n\t\t}\n\n\t\t.acf-field-group-pro-features-content {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\twidth: 416px;\n\n\t\t\t.acf-field-group-pro-features-desc {\n\t\t\t\tmargin-top: 8px;\n\t\t\t\tmargin-bottom: 24px;\n\t\t\t\tfont-size: 15px;\n\t\t\t\tfont-weight: 300;\n\t\t\t\tcolor: $gray-300;\n\t\t\t}\n\n\t\t\t@media screen and (max-width: 768px) {\n\t\t\t\twidth: 100%;\n\t\t\t\torder: 1;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 8px;\n\t\t\t\t};\n\n\t\t\t\t.acf-field-group-pro-features-title,\n\t\t\t\t.acf-field-group-pro-features-desc {\n\t\t\t\t\tdisplay: none !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t.acf-field-group-pro-features-actions {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\talign-items: flex-start;\n\t\t\tmin-width: 160px;\n\t\t\tgap: 12px;\n\n\t\t\t@media screen and (max-width: 768px) {\n\t\t\t\tjustify-content: flex-start;\n\t\t\t\tflex-direction: column;\n\t\t\t\tmargin-bottom: 24px;\n\n\t\t\t\ta {\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t.acf-field-group-pro-features-grid {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\tflex-wrap: wrap;\n\t\t\tgap: 16px;\n\t\t\twidth: 416px;\n\n\t\t\t.acf-field-group-pro-feature {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t\twidth: 128px;\n\t\t\t\theight: 124px;\n\t\t\t\tbackground: rgba(255, 255, 255, 0.08);\n\t\t\t\tbox-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04), 0px 8px 16px rgba(0, 0, 0, 0.08), inset 0 0 0 1px rgba(255,255,255,.08);\n\t\t\t\tbackdrop-filter: blur(6px);\n\t\t\t\tborder-radius: 8px;\n\n\t\t\t\t.field-type-icon {\n\t\t\t\t\tborder: none;\n\t\t\t\t\tbackground: none;\n\t\t\t\t\twidth: 24px;\n\t\t\t\t\topacity: .8;\n\n\t\t\t\t\t&::before {\n\t\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t\t\twidth: 20px;\n\t\t\t\t\t\theight: 20px;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t@media screen and (max-width: 1200px) {\n\t\t\t\t\t\t&::before { width: 18px; height: 18px; }\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t}\n\n\t\t\t\t.pro-feature-blocks::before {\n\t\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-extended-menu.svg\");\n\t\t\t\t\tmask-image: url(\"../../images/icons/icon-extended-menu.svg\");\n\t\t\t\t}\n\n\t\t\t\t.pro-feature-options-pages::before {\n\t\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-settings.svg\");\n\t\t\t\t\tmask-image: url(\"../../images/icons/icon-settings.svg\");\n\t\t\t\t}\n\n\t\t\t\t.field-type-label {\n\t\t\t\t\tmargin-top: 4px;\n\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\tfont-weight: 300;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@media screen and (max-width: 1200px) {\n\t\t\t\tflex-direction: column;\n\t\t\t\tgap: 8px;\n\t\t\t\twidth: 288px;\n\n\t\t\t\t.acf-field-group-pro-feature {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 40px;\n\t\t\t\t\tflex-direction: row;\n\t\t\t\t\tjustify-content: unset;\n\t\t\t\t\tgap: 8px;\n\n\n\t\t\t\t\t.field-type-icon {\n\t\t\t\t\t\tposition: initial;\n\t\t\t\t\t\tmargin-left: 16px;\n\t\t\t\t\t}\n\n\t\t\t\t\t.field-type-label {\n\t\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@media screen and (max-width: 768px) {\n\t\t\t\tgap: 0;\n\t\t\t\twidth: 100%;\n\t\t\t\theight: auto;\n\t\t\t\tmargin-bottom: 16px;\n\t\t\t\tflex-direction: unset;\n\t\t\t\tflex-wrap: wrap;\n\n\t\t\t\t.acf-field-group-pro-feature {\n\t\t\t\t\tflex: 1 0 50%;\n\t\t\t\t\tmargin-bottom: 8px;\n\t\t\t\t\twidth: auto;\n\t\t\t\t\theight: auto;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tbackground: none;\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t\tbackdrop-filter: none;\n\t\t\t\t\t\n\t\t\t\t\t.field-type-label {\n\t\t\t\t\t\tmargin-left: 2px;\n\t\t\t\t\t}\n\n\t\t\t\t\t.field-type-icon {\n\t\t\t\t\t\tposition: initial;\n\t\t\t\t\t\tmargin-left: 0;\n\n\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\theight: 16px;\n\t\t\t\t\t\t\twidth: 16px;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.field-type-label {\n\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t}\n\n\th1 {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tbottom: 4px;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tbottom: 0;\n\t\t};\n\t\t@extend .acf-h1;\n\t\tfont-weight: bold;\n\t\tcolor: $gray-50;\n\n\t\t.acf-icon {\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t// Upsell block btn\n\t.acf-btn {\n\t\tdisplay: inline-flex;\n\t\tbackground-color: rgba(#fff,.1);\n\t\tborder: none;\n\t\tbox-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04), 0px 4px 8px rgba(0, 0, 0, 0.06), inset 0 0 0 1px rgba(255,255,255,.16);\n\t\tbackdrop-filter: blur(6px);\n\t\tpadding: 8px 24px;\n\t\theight: 48px;\n\n\t\t&:hover {\n\t\t\tbackground-color: rgba(#fff,.2);\n\t\t}\n\n\t\t.acf-icon {\n\t\t\tmargin: {\n\t\t\t\tright: -2px;\n\t\t\t\tleft: 6px;\n\t\t\t};\n\t\t}\n\n\t\t&.acf-pro-features-upgrade {\n\t\t\tbackground: $gradient-pro;\n\t\t\tbackground-size: 160% 80%;\n\t\t\tbackground-position: 100% 0;\n\t\t\tbox-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04), 0px 4px 8px rgba(0, 0, 0, 0.06), inset 0 0 0 1px rgba(255,255,255,.16);\n\t\t\tborder-radius: 6px;\n\t\t\ttransition: background-position .5s;\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\tbackground-position: 0 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t.acf-field-group-pro-features-footer-wrap {\n\t\theight: 48px;\n\t\tbackground: rgba(16, 24, 40, 0.4);\n\t\tbackdrop-filter: blur(6px);\n\t\tborder-top: 1px solid rgba(255, 255, 255, 0.08);\n\t\tborder-bottom-left-radius: 8px;\n\t\tborder-bottom-right-radius: 8px;\n\t\tcolor: $gray-400;\n\t\tfont-size: 13px;\n\t\tfont-weight: 300;\n\t\tpadding: 0 35px;\n\n\t\t.acf-field-group-pro-features-footer {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\t\t\tmax-width: 950px;\n\t\t\theight: 48px;\n\t\t\tmargin: 0 auto;\n\t\t}\n\n\t\t.acf-field-group-pro-features-wpengine-logo {\n\t\t\theight: 16px;\n\t\t\tvertical-align: middle;\n\t\t\tmargin-top: -2px;\n\t\t\tmargin-left: 3px;\n\t\t}\n\n\t\ta {\n\t\t\tcolor: $gray-400;\n\t\t\ttext-decoration: none;\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\tcolor: $gray-300;\n\t\t\t}\n\t\t\t\n\t\t\t.acf-icon {\n\t\t\t\twidth: 18px;\n\t\t\t\theight: 18px;\n\t\t\t\tmargin-left: 4px;\n\t\t\t}\n\t\t\t\n\t\t}\n\t\t\n\t\t.acf-more-tools-from-wpengine {\n\t\t\t\n\t\t\ta {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\talign-items: center;\n\t\t\t}\n\t\t\t\n\t\t}\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\theight: 70px;\n\t\t\tfont-size: 12px;\n\n\t\t\t.acf-more-tools-from-wpengine {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\n\t\t\t.acf-field-group-pro-features-footer {\n\t\t\t\tjustify-content: center;\n\t\t\t\theight: 70px;\n\n\t\t\t\t.acf-field-group-pro-features-wpengine-logo {\n\t\t\t\t\tclear: both;\n\t\t\t\t\tmargin: 6px auto 0 auto;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n}\n\n.acf-no-field-groups,\n.acf-no-post-types,\n.acf-no-taxonomies {\n\t#tmpl-acf-field-group-pro-features {\n\t\tmargin-top: 0;\n\t}\n}\n", "/*--------------------------------------------------------------------------------------------\n*\n*\tPost type & taxonomies styles\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-admin-single-post-type,\n.acf-admin-single-taxonomy,\n.acf-admin-single-options-page {\n\tlabel[for=\"acf-basic-settings-hide\"] {\n\t\tdisplay: none;\n\t}\n\tfieldset.columns-prefs {\n\t\tdisplay: none;\n\t}\n\n\t#acf-basic-settings {\n\t\t.postbox-header {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\n\t.postbox-container,\n\t.notice {\n\t\tmax-width: $max-width;\n\t\tclear: left;\n\t}\n\n\t#post-body-content {\n\t\tmargin: 0;\n\t}\n\n\t// Main postbox\n\t.postbox,\n\t.acf-box {\n\t\t.inside {\n\t\t\tpadding: {\n\t\t\t\ttop: 48px;\n\t\t\t\tright: 48px;\n\t\t\t\tbottom: 48px;\n\t\t\t\tleft: 48px;\n\t\t\t}\n\t\t}\n\t}\n\n\t#acf-advanced-settings.postbox {\n\t\t.inside {\n\t\t\tpadding: {\n\t\t\t\tbottom: 24px;\n\t\t\t}\n\t\t}\n\t}\n\n\t.postbox-container .meta-box-sortables #acf-basic-settings .inside {\n\t\tborder: none;\n\t}\n\n\t// Input wrap\n\t.acf-input-wrap {\n\t\toverflow: visible;\n\t}\n\n\t// Field & label margins & paddings\n\t.acf-field {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 24px;\n\t\t\tleft: 0;\n\t\t}\n\n\t\t.acf-label {\n\t\t\tmargin: {\n\t\t\t\tbottom: 6px;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Specific field overrides\n\t.acf-field-text,\n\t.acf-field-textarea,\n\t.acf-field-select {\n\t\tmax-width: 600px;\n\t}\n\n\t.acf-field-true-false {\n\t\tmax-width: 700px;\n\t}\n\n\t.acf-field-supports {\n\t\tmax-width: 600px;\n\n\t\t.acf-label {\n\t\t\tdisplay: block;\n\n\t\t\t.description {\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 4px;\n\t\t\t\t\tbottom: 12px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.acf_post_type_supports {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-content: flex-start;\n\t\t\talign-items: flex-start;\n\n\t\t\t&:focus-within {\n\t\t\t\tborder-color: transparent;\n\t\t\t}\n\n\t\t\tli {\n\t\t\t\tflex: 0 0 25%;\n\n\t\t\t\ta.button {\n\t\t\t\t\tbackground-color: transparent;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t\tborder: 0;\n\t\t\t\t\theight: auto;\n\t\t\t\t\tmin-height: auto;\n\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\tline-height: 22px;\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tcontent: '';\n\t\t\t\t\t\t$icon-size: 16px;\n\t\t\t\t\t\tmargin-right: 6px;\n\t\t\t\t\t\tdisplay: inline-flex;\n\t\t\t\t\t\twidth: $icon-size;\n\t\t\t\t\t\theight: $icon-size;\n\t\t\t\t\t\tbackground-color: currentColor;\n\t\t\t\t\t\tborder: none;\n\t\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\t\t\tmask-size: contain;\n\t\t\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t\t\t-webkit-mask-position: center;\n\t\t\t\t\t\tmask-position: center;\n\t\t\t\t\t\ttext-indent: 500%;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-add.svg\");\n\t\t\t\t\t\tmask-image: url(\"../../images/icons/icon-add.svg\");\n\t\t\t\t\t}\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tcolor: $blue-700;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tinput[type=text] {\n\t\t\t\t\twidth: calc(100% - 36px);\n\t\t\t\t\tpadding: 0;\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t\tborder: none;\n\t\t\t\t\tborder-bottom: 1px solid $gray-300;\n\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\theight: auto;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tmin-height: auto;\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\toutline: none;\n\t\t\t\t\t\tborder-bottom-color: $blue-400;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Dividers\n\t.acf-field-seperator {\n\t\tmargin: {\n\t\t\ttop: 40px;\n\t\t\tbottom: 40px;\n\t\t}\n\t\tborder: {\n\t\t\ttop: 1px solid $gray-200;\n\t\t\tright: none;\n\t\t\tbottom: none;\n\t\t\tleft: none;\n\t\t}\n\t}\n\n\t// Remove margin from last fields in postbox\n\t.acf-field-advanced-configuration {\n\t\tmargin: {\n\t\t\tbottom: 0;\n\t\t}\n\t}\n\n\t// Tabbed navigation & labels utility bar\n\t.postbox-container .acf-tab-wrap,\n\t.acf-regenerate-labels-bar {\n\t\tposition: relative;\n\t\ttop: -48px;\n\t\tleft: -48px;\n\t\twidth: calc(100% + 96px);\n\t}\n\n\t// Labels utility bar\n\t.acf-regenerate-labels-bar {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: right;\n\t\tmin-height: 48px;\n\t\tmargin: {\n\t\t\tbottom: 0;\n\t\t}\n\t\tpadding: {\n\t\t\tright: 16px;\n\t\t\tleft: 16px;\n\t\t}\n\t\tgap: 8px;\n\t\tborder-bottom: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-100;\n\t\t}\n\t}\n\n\t// Labels utility bar help/tip icon\n\t.acf-labels-tip {\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\tmin-height: 24px;\n\t\tmargin: {\n\t\t\tright: 8px;\n\t\t}\n\t\tpadding: {\n\t\t\tleft: 16px;\n\t\t}\n\t\tborder-left: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t}\n\n\t\t.acf-icon {\n\t\t\tdisplay: inline-flex;\n\t\t\talign-items: center;\n\t\t\t$icon-size: 16px;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\t-webkit-mask-size: $icon-size;\n\t\t\tmask-size: $icon-size;\n\t\t\tbackground-color: $gray-400;\n\t\t}\n\t}\n}\n\n// Select2 for default values in permalink rewrite\n.acf-select2-default-pill {\n\tborder-radius: 100px;\n\tmin-height: 20px;\n\tpadding: {\n\t\ttop: 2px;\n\t\tbottom: 2px;\n\t\tleft: 8px;\n\t\tright: 8px;\n\t}\n\tfont-size: 11px;\n\tmargin-left: 6px;\n\tbackground-color: $gray-200;\n\tcolor: $gray-500;\n}\n\n.acf-menu-position-desc-child {\n\tdisplay: none;\n}", "/*---------------------------------------------------------------------------------------------\n*\n*  Field picker modal\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-modal.acf-browse-fields-modal {\n\twidth: 1120px;\n\theight: 664px;\n\ttop: 50%;\n\tright: auto;\n\tbottom: auto;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\tdisplay: flex;\n\tflex-direction: row;\n\tborder-radius: $radius-xl;\n\tbox-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04),\n\t\t0px 8px 16px rgba(0, 0, 0, 0.08);\n\toverflow: hidden;\n\n\t.acf-field-picker {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tflex-grow: 1;\n\t\twidth: 760px;\n\t\tbackground: #fff;\n\n\t\t.acf-modal-title,\n\t\t.acf-modal-content,\n\t\t.acf-modal-toolbar {\n\t\t\tposition: relative;\n\t\t}\n\n\t\t.acf-modal-title {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tbackground: $gray-50;\n\t\t\tborder: none;\n\t\t\tpadding: 35px 32px;\n\n\t\t\t.acf-search-field-types-wrap {\n\t\t\t\tposition: relative;\n\n\t\t\t\t&:after {\n\t\t\t\t\tcontent: \"\";\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 11px;\n\t\t\t\t\tleft: 10px;\n\t\t\t\t\t$icon-size: 18px;\n\t\t\t\t\twidth: $icon-size;\n\t\t\t\t\theight: $icon-size;\n\t\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-search.svg\");\n\t\t\t\t\tmask-image: url(\"../../images/icons/icon-search.svg\");\n\t\t\t\t\tbackground-color: $gray-400;\n\t\t\t\t\tborder: none;\n\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\t\tmask-size: contain;\n\t\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t\t-webkit-mask-position: center;\n\t\t\t\t\tmask-position: center;\n\t\t\t\t\ttext-indent: 500%;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t}\n\n\t\t\t\tinput {\n\t\t\t\t\twidth: 280px;\n\t\t\t\t\theight: 40px;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tpadding-left: 32px;\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.acf-modal-content {\n\t\t\ttop: auto;\n\t\t\tbottom: auto;\n\t\t\tpadding: 0;\n\t\t\theight: 100%;\n\n\t\t\t.acf-tab-group {\n\t\t\t\tpadding-left: 32px;\n\t\t\t}\n\n\t\t\t.acf-field-types-tab {\n\t\t\t\tdisplay: flex;\n\t\t\t}\n\n\t\t\t.acf-field-types-tab,\n\t\t\t.acf-field-type-search-results {\n\t\t\t\tflex-direction: row;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tgap: 24px;\n\t\t\t\tpadding: 32px;\n\n\t\t\t\t.acf-field-type {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tisolation: isolate;\n\t\t\t\t\twidth: 120px;\n\t\t\t\t\theight: 120px;\n\t\t\t\t\tbackground: $gray-50;\n\t\t\t\t\tborder: 1px solid $gray-200;\n\t\t\t\t\tborder-radius: 8px;\n\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\tcolor: $gray-800;\n\t\t\t\t\ttext-decoration: none;\n\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&:active,\n\t\t\t\t\t&.selected {\n\t\t\t\t\t\tbackground: $blue-50;\n\t\t\t\t\t\tborder: 1px solid $blue-400;\n\t\t\t\t\t\tbox-shadow: inset 0 0 0 1px $blue-400;\n\t\t\t\t\t}\n\n\t\t\t\t\t.field-type-icon {\n\t\t\t\t\t\tborder: none;\n\t\t\t\t\t\tbackground: none;\n\t\t\t\t\t\ttop: 0;\n\n\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\twidth: 22px;\n\t\t\t\t\t\t\theight: 22px;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.field-type-label {\n\t\t\t\t\t\tmargin-top: 12px;\n\t\t\t\t\t\t@extend .p5;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.field-type-requires-pro {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: -10px;\n\t\t\t\t\tright: -10px;\n\t\t\t\t\theight: 21px;\n\t\t\t\t\tcolor: white;\n\t\t\t\t\tbackground: $gradient-pro;\n\t\t\t\t\tbackground-size: 140% 20%;\n\t\t\t\t\tbackground-position: 100% 0;\n\t\t\t\t\tborder-radius: 100px;\n\t\t\t\t\tfont-size: 11px;\n\t\t\t\t\tpadding: {\n\t\t\t\t\t\tright: 6px;\n\t\t\t\t\t\tleft: 6px;\n\t\t\t\t\t}\n\t\t\t\t\ti {\n\t\t\t\t\t\twidth: 12px;\n\t\t\t\t\t\theight: 12px;\n\t\t\t\t\t\tmargin-right: 2px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.acf-modal-toolbar {\n\t\t\tdisplay: flex;\n\t\t\talign-items: flex-start;\n\t\t\tjustify-content: space-between;\n\t\t\theight: auto;\n\t\t\tmin-height: 72px;\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 32px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 32px;\n\t\t\t}\n\t\t\tmargin: 0;\n\t\t\tborder: none;\n\n\t\t\t.acf-select-field,\n\t\t\t.acf-btn-pro {\n\t\t\t\tmin-width: 160px;\n\t\t\t\tjustify-content: center;\n\t\t\t}\n\n\t\t\t.acf-insert-field-label {\n\t\t\t\tmin-width: 280px;\n\t\t\t\tbox-shadow: none;\n\t\t\t}\n\n\t\t\t.acf-field-picker-actions {\n\t\t\t\tdisplay: flex;\n\t\t\t\tgap: 8px;\n\t\t\t}\n\t\t}\n\t}\n\n\t.acf-field-type-preview {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\twidth: 360px;\n\t\tbackground-color: $gray-50;\n\t\tbackground-image: url(\"../../images/field-preview-grid.png\");\n\t\tbackground-size: 740px;\n\t\tbackground-repeat: no-repeat;\n\t\tbackground-position: center bottom;\n\t\tborder-left: 1px solid $gray-200;\n\t\tbox-sizing: border-box;\n\t\tpadding: 32px;\n\n\t\t.field-type-desc {\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\tcolor: $gray-500;\n\t\t}\n\n\t\t.field-type-preview-container {\n\t\t\tdisplay: inline-flex;\n\t\t\tjustify-content: center;\n\t\t\twidth: 100%;\n\t\t\tmargin: {\n\t\t\t\ttop: 24px;\n\t\t\t}\n\t\t\tpadding: {\n\t\t\t\ttop: 32px;\n\t\t\t\tbottom: 32px;\n\t\t\t}\n\t\t\tbackground-color: rgba(#fff, 0.64);\n\t\t\tborder-radius: $radius-lg;\n\t\t\tbox-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.04),\n\t\t\t\t0px 8px 24px rgba(0, 0, 0, 0.04);\n\t\t}\n\n\t\t.field-type-image {\n\t\t\tmax-width: 232px;\n\t\t}\n\n\t\t.field-type-info {\n\t\t\tflex-grow: 1;\n\n\t\t\t.field-type-name {\n\t\t\t\tfont-size: 21px;\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 16px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.field-type-upgrade-to-unlock {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\tjustify-items: center;\n\t\t\t\talign-items: center;\n\t\t\t\tmin-height: 24px;\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 12px;\n\t\t\t\t}\n\t\t\t\tpadding: {\n\t\t\t\t\tright: 8px;\n\t\t\t\t\tleft: 8px;\n\t\t\t\t}\n\t\t\t\tbackground: $gradient-pro;\n\t\t\t\tbackground-size: 140% 20%;\n\t\t\t\tbackground-position: 100% 0;\n\t\t\t\tborder-radius: 100px;\n\t\t\t\tcolor: white;\n\t\t\t\ttext-decoration: none;\n\t\t\t\tfont-size: 11px;\n\n\t\t\t\ti.acf-icon {\n\t\t\t\t\twidth: 14px;\n\t\t\t\t\theight: 14px;\n\t\t\t\t\tmargin: {\n\t\t\t\t\t\tright: 4px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.field-type-links {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tgap: 24px;\n\t\t\tmin-height: 40px;\n\n\t\t\t.acf-icon {\n\t\t\t\t$icon-size: 18px;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t}\n\n\t\t\t&:before {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\n\t\t\ta {\n\t\t\t\tdisplay: flex;\n\t\t\t\tgap: 6px;\n\t\t\t\ttext-decoration: none;\n\n\t\t\t\t&:hover {\n\t\t\t\t\ttext-decoration: underline;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.acf-field-type-search-results,\n\t.acf-field-type-search-no-results {\n\t\tdisplay: none;\n\t}\n\n\t&.is-searching {\n\t\t.acf-tab-wrap,\n\t\t.acf-field-types-tab,\n\t\t.acf-field-type-search-no-results {\n\t\t\tdisplay: none !important;\n\t\t}\n\n\t\t.acf-field-type-search-results {\n\t\t\tdisplay: flex;\n\t\t}\n\t}\n\n\t&.no-results-found {\n\t\t.acf-tab-wrap,\n\t\t.acf-field-types-tab,\n\t\t.acf-field-type-search-results,\n\t\t.field-type-info,\n\t\t.field-type-links,\n\t\t.acf-field-picker-toolbar {\n\t\t\tdisplay: none !important;\n\t\t}\n\n\t\t.acf-modal-title {\n\t\t\tborder-bottom: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t}\n\t\t}\n\n\t\t.acf-field-type-search-no-results {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\theight: 100%;\n\t\t\tgap: 6px;\n\n\t\t\timg {\n\t\t\t\tmargin-bottom: 19px;\n\t\t\t}\n\n\t\t\tp {\n\t\t\t\tmargin: 0;\n\n\t\t\t\t&.acf-no-results-text {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.acf-invalid-search-term {\n\t\t\t\tmax-width: 200px;\n\t\t\t\toverflow: hidden;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\tdisplay: inline-block;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide browse fields button for smaller screen sizes\n*\n*---------------------------------------------------------------------------------------------*/\n@media only screen and (max-width: 1080px) {\n\t.acf-btn.browse-fields {\n\t\tdisplay: none;\n\t}\n}\n"], "names": [], "sourceRoot": ""}