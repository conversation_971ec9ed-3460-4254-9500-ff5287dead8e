/* reset.css */
/* Comment out/delete the reset rules where appropriate */

/* container */
.elfinder,

/* toolbar */

/* navbar */
.elfinder .elfinder-navbar *,

/* current working directory */
.elfinder .elfinder-cwd,
.elfinder .elfinder-cwd table tr td.ui-state-active,
.elfinder .elfinder-cwd table tr td.ui-state-hover,
.elfinder .elfinder-cwd table tr td.ui-state-selected,
.elfinder .elfinder-cwd table thead tr,
.elfinder .elfinder-cwd table tbody tr,
.elfinder .elfinder-cwd-file .ui-state-hover,
.elfinder .elfinder-cwd-file .elfinder-cwd-icon-directory,
.elfinder .elfinder-cwd-file .elfinder-cwd-filename,
.elfinder .elfinder-cwd-file .elfinder-cwd-filename.ui-state-hover,

/* general states */
.elfinder .ui-state-default,
.elfinder .ui-state-active,
.elfinder .ui-state-hover,
.elfinder .ui-selected,

/* ui-widgets (normally for dialogs) */
.elfinder .ui-widget,
.elfinder .ui-widget-content,

/* icons */
.elfinder-button-icon,
.elfinder-navbar-icon,
.elfinder .ui-icon,
.elfinder-cwd-icon-directory,

/* statusbar */
.elfinder .elfinder-statusbar,
.elfinder .elfinder-statusbar *,

/* context menu (outside of elfinder div */
.elfinder-contextmenu,
.elfinder-contextmenu-sub,
.elfinder-contextmenu-item,
.elfinder-contextmenu-separator,
.elfinder-contextmenu .ui-state-hover {

}
.elfinder .elfinder-toolbar,
.elfinder .elfinder-buttonset,
.elfinder .elfinder-button,
.elfinder .elfinder-toolbar-button-separator,
.elfinder .elfinder-navbar,
.elfinder .ui-widget-header,
.elfinder-dialog-confirm .ui-icon,
.elfinder-dialog-confirm .ui-widget-content,
.std42-dialog .ui-dialog-titlebar .ui-dialog-titlebar-close:hover .ui-icon {
 background: none;
  border: none;
}
