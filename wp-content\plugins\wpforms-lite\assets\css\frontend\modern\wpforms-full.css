.wpforms-container .wpforms-error-container,
.wpforms-container .wpforms-error-noscript {
  color: #D63637;
}

.wpforms-container .wpforms-error-styled-container {
  padding: 15px 0;
}

.wpforms-container .wpforms-error-styled-container p {
  margin: 0;
}

.wpforms-container .wpforms-error-styled-container + .wpforms-submit-container {
  margin-top: 10px;
}

.wpforms-container label.wpforms-error,
.wpforms-container em.wpforms-error {
  display: block;
  color: #D63637;
  font-size: 0.9em;
  font-style: normal;
  cursor: default;
  min-width: 120px;
}

.wpforms-container .wpforms-field input.wpforms-error, .wpforms-container .wpforms-field input.user-invalid,
.wpforms-container .wpforms-field textarea.wpforms-error,
.wpforms-container .wpforms-field textarea.user-invalid,
.wpforms-container .wpforms-field select.wpforms-error,
.wpforms-container .wpforms-field select.user-invalid {
  border: 1px solid #D63637;
}

.wpforms-container .wpforms-field input[type=checkbox].wpforms-error, .wpforms-container .wpforms-field input[type=checkbox].user-invalid,
.wpforms-container .wpforms-field input[type=radio].wpforms-error,
.wpforms-container .wpforms-field input[type=radio].user-invalid {
  border: none;
}

.wpforms-container .wpforms-field.wpforms-has-error .choices__inner {
  border: 1px solid #D63637;
}

.wpforms-container .wpforms-error-alert {
  border: 1px solid rgba(0, 0, 0, 0.25);
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  padding: 10px 15px;
  font-size: 14px;
  margin: 0 0 10px 0;
}

.wpforms-container .wpforms-error-alert {
  color: #D63637;
  background-color: #f2dede;
  border-color: #f2dede;
}

div[style*="z-index: 2147483647"] div[style*="border-width: 11px"][style*="position: absolute"][style*="pointer-events: none"] {
  border-style: none;
}

.wpforms-container .wpforms-screen-reader-element {
  position: absolute !important;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  width: 1px;
  border: 0;
  overflow: hidden;
  word-wrap: normal !important;
}

.wpforms-container .wpforms-field-hp {
  display: none !important;
  position: absolute !important;
  left: -9000px !important;
}

.wpforms-container .wpforms-recaptcha-container {
  padding: 0;
  clear: both;
}

.wpforms-container .wpforms-recaptcha-container iframe {
  display: block;
  width: 100%;
  max-width: 100%;
}

.wpforms-container .wpforms-recaptcha-container .g-recaptcha {
  padding: 10px 0 0;
}

.wpforms-container .wpforms-recaptcha-container.wpforms-is-turnstile.wpforms-is-turnstile-invisible {
  padding: 0;
  height: 0;
}

.wpforms-container .wpforms-recaptcha-container.wpforms-is-turnstile iframe {
  position: relative !important;
  visibility: inherit !important;
}

.wpforms-container .wpforms-recaptcha-container.wpforms-is-recaptcha-type-v3 .g-recaptcha, .wpforms-container .wpforms-recaptcha-container.wpforms-is-turnstile-invisible .g-recaptcha, .wpforms-container .wpforms-recaptcha-container.wpforms-is-recaptcha-type-invisible .g-recaptcha {
  padding: 0;
}

.wpforms-container .wpforms-recaptcha-container.wpforms-is-recaptcha-type-v3 .wpforms-error:first-of-type, .wpforms-container .wpforms-recaptcha-container.wpforms-is-turnstile-invisible .wpforms-error:first-of-type, .wpforms-container .wpforms-recaptcha-container.wpforms-is-recaptcha-type-invisible .wpforms-error:first-of-type {
  margin-top: 10px;
}

.wpforms-container amp-img > img {
  position: absolute;
}

.wpforms-container .amp-form-submit-success .wpforms-field-container,
.wpforms-container .amp-form-submit-success .wpforms-submit-container {
  display: none;
}

.wpforms-container .wpforms-preview-notice-links {
  line-height: 2.4;
}

body.rtl .wpforms-container .wpforms-form input[type=tel] {
  direction: ltr;
  text-align: right;
}

body.rtl .wpforms-container .wpforms-form input[type=url] {
  direction: ltr;
  text-align: right;
}

body.rtl .wpforms-container .wpforms-form input[type=url]::-webkit-textfield-decoration-container {
  display: flex;
  flex-direction: row-reverse;
}

body.rtl .wpforms-container .wpforms-form input[type=email] {
  direction: ltr;
  text-align: right;
}

body.rtl .wpforms-container .wpforms-form input[type=email]::-webkit-textfield-decoration-container {
  display: flex;
  flex-direction: row-reverse;
}

body.rtl .wpforms-container .wpforms-form input[type=number]::-webkit-textfield-decoration-container {
  flex-direction: row-reverse;
}

body.rtl .wpforms-container .wpforms-form .wpforms-field-date-time .wpforms-datepicker-wrap .wpforms-datepicker-clear {
  right: auto;
  left: 10px;
}

body.rtl .wpforms-container .wpforms-form .wpforms-field-date-time .ui-timepicker-list li {
  padding: 3px 5px 3px 0 !important;
}

body.rtl .wpforms-container .wpforms-form .wpforms-field-date-time .wpforms-field-medium + .wpforms-datepicker-clear {
  left: calc( 40% + 10px);
  right: auto;
}

body.rtl .wpforms-container .wpforms-form .wpforms-field-file-upload .dz-remove {
  right: auto;
  left: 0;
}

body.rtl .wpforms-container .wpforms-form .wpforms-image-choices-none .wpforms-image-choices-item .wpforms-image-choices-label {
  margin-left: 0;
  margin-right: 10px;
}

body.rtl .ui-timepicker-list li {
  padding: 3px 5px 3px 0;
}

.wpforms-container .wpforms-form .wpforms-field.wpforms-field-email .wpforms-field-row, .wpforms-container .wpforms-form .wpforms-field.wpforms-field-address .wpforms-field-row, .wpforms-container .wpforms-form .wpforms-field.wpforms-field-password .wpforms-field-row {
  flex-wrap: wrap;
  container-type: inline-size;
  container-name: wpforms-field-row-responsive;
}

.wpforms-container .wpforms-form .wpforms-field.wpforms-field-name .wpforms-field-row {
  flex-wrap: wrap;
  container-type: inline-size;
  container-name: wpforms-field-row-responsive-name-field;
}

.wpforms-container .wpforms-form .wpforms-field .wpforms-field-row-responsive {
  flex-wrap: wrap;
  container-type: inline-size;
  container-name: wpforms-field-row-responsive;
}

@container wpforms-field-row-responsive (max-width: 200px) {
  .wpforms-container .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block {
    width: 100%;
    padding: 0;
  }
  .wpforms-container .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block:only-child {
    padding: 0;
  }
  .wpforms-container .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block:not(:last-child) {
    margin-bottom: 15px;
  }
}

@container wpforms-field-row-responsive-name-field (max-width: 260px) {
  .wpforms-container .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block {
    width: 100%;
    padding: 0;
  }
  .wpforms-container .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block:only-child {
    padding: 0;
  }
  .wpforms-container .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block:not(:last-child) {
    margin-bottom: 15px;
  }
}

.wpforms-container .wpforms-form .wpforms-checkbox-2-columns,
.wpforms-container .wpforms-form .wpforms-multiplechoice-2-columns,
.wpforms-container .wpforms-form .wpforms-list-2-columns {
  container-type: inline-size;
  container-name: wpforms-field-2-columns-responsive;
}

@container wpforms-field-2-columns-responsive (max-width: 320px) {
  .wpforms-container .wpforms-form .wpforms-checkbox-2-columns ul,
  .wpforms-container .wpforms-form .wpforms-multiplechoice-2-columns ul,
  .wpforms-container .wpforms-form .wpforms-list-2-columns ul {
    grid-template-columns: 1fr !important;
  }
  .wpforms-container .wpforms-form .wpforms-checkbox-2-columns ul li,
  .wpforms-container .wpforms-form .wpforms-multiplechoice-2-columns ul li,
  .wpforms-container .wpforms-form .wpforms-list-2-columns ul li {
    width: 100%;
  }
}

.wpforms-container .wpforms-form .wpforms-checkbox-3-columns,
.wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns,
.wpforms-container .wpforms-form .wpforms-list-3-columns {
  container-type: inline-size;
  container-name: wpforms-field-3-columns-responsive;
}

@container wpforms-field-3-columns-responsive (max-width: 480px) {
  .wpforms-container .wpforms-form .wpforms-checkbox-3-columns ul,
  .wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns ul,
  .wpforms-container .wpforms-form .wpforms-list-3-columns ul {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
  .wpforms-container .wpforms-form .wpforms-checkbox-3-columns ul li,
  .wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns ul li,
  .wpforms-container .wpforms-form .wpforms-list-3-columns ul li {
    width: 100%;
  }
}

@container wpforms-field-3-columns-responsive (max-width: 320px) {
  .wpforms-container .wpforms-form .wpforms-checkbox-3-columns ul,
  .wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns ul,
  .wpforms-container .wpforms-form .wpforms-list-3-columns ul {
    grid-template-columns: 1fr !important;
  }
  .wpforms-container .wpforms-form .wpforms-checkbox-3-columns ul li,
  .wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns ul li,
  .wpforms-container .wpforms-form .wpforms-list-3-columns ul li {
    width: 100%;
  }
}

@media only screen and (max-width: 600px) {
  div.wpforms-container .wpforms-form .wpforms-field > * {
    max-width: 100%;
  }
  div.wpforms-container .wpforms-form .wpforms-field {
    padding-right: 1px;
    padding-left: 1px;
  }
  div.wpforms-container .wpforms-form .wpforms-field input.wpforms-field-small, div.wpforms-container .wpforms-form .wpforms-field input.wpforms-field-medium, div.wpforms-container .wpforms-form .wpforms-field input.wpforms-field-large,
  div.wpforms-container .wpforms-form .wpforms-field select.wpforms-field-small,
  div.wpforms-container .wpforms-form .wpforms-field select.wpforms-field-medium,
  div.wpforms-container .wpforms-form .wpforms-field select.wpforms-field-large,
  div.wpforms-container .wpforms-form .wpforms-field .wpforms-field-row.wpforms-field-small,
  div.wpforms-container .wpforms-form .wpforms-field .wpforms-field-row.wpforms-field-medium,
  div.wpforms-container .wpforms-form .wpforms-field .wpforms-field-row.wpforms-field-large {
    max-width: 100%;
  }
  div.wpforms-container .wpforms-form .wpforms-mobile-full {
    width: 100%;
    margin-left: 0;
  }
  div.wpforms-container .wpforms-form .wpforms-field:not(.wpforms-field-phone):not(.wpforms-field-select-style-modern):not(.wpforms-field-radio):not(.wpforms-field-checkbox):not(.wpforms-field-layout):not(.wpforms-field-repeater) {
    overflow-x: hidden;
  }
  div.wpforms-container .wpforms-form .wpforms-checkbox-2-columns ul,
  div.wpforms-container .wpforms-form .wpforms-multiplechoice-2-columns ul,
  div.wpforms-container .wpforms-form .wpforms-list-2-columns ul,
  div.wpforms-container .wpforms-form .wpforms-checkbox-3-columns ul,
  div.wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns ul,
  div.wpforms-container .wpforms-form .wpforms-list-3-columns ul {
    grid-template-columns: 1fr !important;
  }
  div.wpforms-container .wpforms-form .wpforms-checkbox-2-columns ul li,
  div.wpforms-container .wpforms-form .wpforms-multiplechoice-2-columns ul li,
  div.wpforms-container .wpforms-form .wpforms-list-2-columns ul li,
  div.wpforms-container .wpforms-form .wpforms-checkbox-3-columns ul li,
  div.wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns ul li,
  div.wpforms-container .wpforms-form .wpforms-list-3-columns ul li {
    width: 100%;
  }
  div.wpforms-container .wpforms-form .wpforms-page-indicator.circles .wpforms-page-indicator-page {
    display: block;
    margin: 0 0 10px 0;
  }
  div.wpforms-container .wpforms-form .wpforms-page-indicator.circles .wpforms-page-indicator-page-number {
    width: 30px;
    height: 30px;
    line-height: 30px;
  }
  div.wpforms-container .wpforms-form .wpforms-page-indicator.connector .wpforms-page-indicator-page {
    width: 100% !important;
    padding: 5px 10px;
  }
  div.wpforms-container .wpforms-form .wpforms-page-indicator.connector .wpforms-page-indicator-page-number {
    display: none;
  }
  div.wpforms-container .wpforms-form .wpforms-page-indicator.connector .wpforms-page-indicator-page.active {
    font-weight: 700;
  }
  div.wpforms-container .wpforms-form.inline-fields .wpforms-field-container,
  div.wpforms-container .wpforms-form.inline-fields .wpforms-field {
    display: block;
    width: 100%;
  }
  div.wpforms-container .wpforms-form.inline-fields .wpforms-submit-container {
    width: 100%;
  }
}

.wpforms-container {
  margin-bottom: 26px;
}

.wpforms-container .wpforms-form * {
  word-break: break-word;
  box-sizing: border-box;
}

.wpforms-container .wpforms-form .wpforms-field-label,
.wpforms-container .wpforms-form .wpforms-field-sublabel,
.wpforms-container .wpforms-form .wpforms-field-description,
.wpforms-container .wpforms-form textarea,
.wpforms-container .wpforms-form li,
.wpforms-container .wpforms-form th {
  hyphens: auto;
}

.wpforms-container ul,
.wpforms-container ul li {
  background: none;
  border: 0;
  margin: 0;
}

.wpforms-container .wpforms-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 10px 0;
}

.wpforms-container .wpforms-submit-container {
  clear: both;
  position: relative;
}

.wpforms-container .wpforms-submit-spinner {
  margin-inline-start: 15px;
  display: inline-block;
  vertical-align: middle;
}

.wpforms-container .wpforms-hidden {
  display: none !important;
}

.wpforms-clear:before {
  content: " ";
  display: table;
}

.wpforms-clear:after {
  clear: both;
  content: " ";
  display: table;
}

.wpforms-container .wpforms-notice {
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-left-width: 12px;
  color: #333333;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 30px;
  padding: 20px 36px 20px 26px;
  position: relative;
}

.wpforms-container .wpforms-notice .wpforms-delete {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  background-color: rgba(10, 10, 10, 0.2);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: inline-block;
  height: 20px;
  margin: 0;
  padding: 0;
  vertical-align: top;
  width: 20px;
  position: absolute;
  right: 10px;
  top: 10px;
}

.wpforms-container .wpforms-notice .wpforms-delete:before, .wpforms-container .wpforms-notice .wpforms-delete:after {
  background-color: #ffffff;
  content: "";
  display: block;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform-origin: center center;
}

.wpforms-container .wpforms-notice .wpforms-delete:before {
  height: 2px;
  width: 50%;
}

.wpforms-container .wpforms-notice .wpforms-delete:after {
  height: 50%;
  width: 2px;
}

.wpforms-container .wpforms-notice .wpforms-delete:hover, .wpforms-container .wpforms-notice .wpforms-delete:focus {
  background-color: rgba(10, 10, 10, 0.3);
}

.wpforms-container .wpforms-notice a {
  text-decoration: underline;
}

.wpforms-container .wpforms-notice p {
  margin: 0 0 20px 0;
}

.wpforms-container .wpforms-notice p:last-of-type {
  margin-bottom: 0;
}

.wpforms-container .wpforms-notice .wpforms-notice-actions {
  margin-top: 20px;
}

.wpforms-container .wpforms-notice .wpforms-notice-action {
  border: 2px solid;
  margin-right: 20px;
  padding: 5px;
  text-decoration: none;
}

.wpforms-container .wpforms-notice .wpforms-notice-action:hover, .wpforms-container .wpforms-notice .wpforms-notice-action:focus, .wpforms-container .wpforms-notice .wpforms-notice-action:active {
  color: #ffffff;
}

.wpforms-container .wpforms-notice.wpforms-info {
  border-color: #3273dc;
}

.wpforms-container .wpforms-notice.wpforms-info .wpforms-notice-action {
  border-color: #3273dc;
}

.wpforms-container .wpforms-notice.wpforms-info .wpforms-notice-action:hover, .wpforms-container .wpforms-notice.wpforms-info .wpforms-notice-action:focus, .wpforms-container .wpforms-notice.wpforms-info .wpforms-notice-action:active {
  background-color: #3273dc;
}

.wpforms-container .wpforms-notice.wpforms-success {
  border-color: #23d160;
}

.wpforms-container .wpforms-notice.wpforms-success .wpforms-notice-action {
  border-color: #23d160;
}

.wpforms-container .wpforms-notice.wpforms-success .wpforms-notice-action:hover, .wpforms-container .wpforms-notice.wpforms-success .wpforms-notice-action:focus, .wpforms-container .wpforms-notice.wpforms-success .wpforms-notice-action:active {
  background-color: #23d160;
}

.wpforms-container .wpforms-notice.wpforms-warning {
  border-color: #ffdd57;
}

.wpforms-container .wpforms-notice.wpforms-warning .wpforms-notice-action {
  border-color: #ffdd57;
}

.wpforms-container .wpforms-notice.wpforms-warning .wpforms-notice-action:hover, .wpforms-container .wpforms-notice.wpforms-warning .wpforms-notice-action:focus, .wpforms-container .wpforms-notice.wpforms-warning .wpforms-notice-action:active {
  background-color: #ffdd57;
  color: inherit;
}

.wpforms-container .wpforms-notice.wpforms-error {
  border-color: #D63637;
}

.wpforms-container .wpforms-notice.wpforms-error .wpforms-notice-action {
  border-color: #D63637;
}

.wpforms-container .wpforms-notice.wpforms-error .wpforms-notice-action:hover, .wpforms-container .wpforms-notice.wpforms-error .wpforms-notice-action:focus, .wpforms-container .wpforms-notice.wpforms-error .wpforms-notice-action:active {
  background-color: #D63637;
}

.wpforms-container .wpforms-preview-notice-links {
  line-height: 2.4;
}

.wpforms-container input.wpforms-field-medium,
.wpforms-container select.wpforms-field-medium,
.wpforms-container .wpforms-field-row.wpforms-field-medium,
.wp-core-ui div.wpforms-container input.wpforms-field-medium,
.wp-core-ui div.wpforms-container select.wpforms-field-medium,
.wp-core-ui div.wpforms-container .wpforms-field-row.wpforms-field-medium {
  max-width: 60%;
}

.wpforms-container input.wpforms-field-small,
.wpforms-container select.wpforms-field-small,
.wpforms-container .wpforms-field-row.wpforms-field-small,
.wp-core-ui div.wpforms-container input.wpforms-field-small,
.wp-core-ui div.wpforms-container select.wpforms-field-small,
.wp-core-ui div.wpforms-container .wpforms-field-row.wpforms-field-small {
  max-width: 25%;
}

.wpforms-container input.wpforms-field-large,
.wpforms-container select.wpforms-field-large,
.wpforms-container .wpforms-field-row.wpforms-field-large,
.wp-core-ui div.wpforms-container input.wpforms-field-large,
.wp-core-ui div.wpforms-container select.wpforms-field-large,
.wp-core-ui div.wpforms-container .wpforms-field-row.wpforms-field-large {
  max-width: 100%;
}

.wpforms-container fieldset,
.wp-core-ui div.wpforms-container fieldset {
  display: block;
  border: none;
  margin: 0;
  padding: 0;
}

.wpforms-container .wpforms-field,
.wp-core-ui div.wpforms-container .wpforms-field {
  padding: 15px 0;
  position: relative;
}

.wpforms-container .wpforms-field.wpforms-field-hidden,
.wp-core-ui div.wpforms-container .wpforms-field.wpforms-field-hidden {
  display: none;
  padding: 0;
}

.wpforms-container .wpforms-field-description,
.wpforms-container .wpforms-field-limit-text,
.wp-core-ui div.wpforms-container .wpforms-field-description,
.wp-core-ui div.wpforms-container .wpforms-field-limit-text {
  font-size: 0.8em;
  margin: 5px 0 0 0;
  word-break: break-word;
  word-wrap: break-word;
  line-height: 1.3;
}

.wpforms-container .wpforms-field-description.wpforms-disclaimer-description,
.wp-core-ui div.wpforms-container .wpforms-field-description.wpforms-disclaimer-description {
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.25);
  padding: 15px 15px 0;
  height: 125px;
  overflow-y: scroll;
  overflow-x: hidden;
}

.wpforms-container .wpforms-field-description.wpforms-disclaimer-description p,
.wp-core-ui div.wpforms-container .wpforms-field-description.wpforms-disclaimer-description p {
  margin: 0 0 15px 0;
}

.wpforms-container .wpforms-field-description-before,
.wpforms-container .wpforms-field-description.before,
.wp-core-ui div.wpforms-container .wpforms-field-description-before,
.wp-core-ui div.wpforms-container .wpforms-field-description.before {
  font-size: 0.85em;
  margin: 0 0 5px 0;
}

.wpforms-container .wpforms-field-label,
.wp-core-ui div.wpforms-container .wpforms-field-label {
  display: block;
  font-weight: 700;
  font-style: normal;
  word-break: break-word;
  word-wrap: break-word;
}

.wpforms-container .wpforms-field-label-inline,
.wp-core-ui div.wpforms-container .wpforms-field-label-inline {
  display: inline;
  vertical-align: baseline;
  font-weight: 400;
  font-style: normal;
  word-break: break-word;
  word-wrap: break-word;
}

.wpforms-container .wpforms-field-sublabel,
.wp-core-ui div.wpforms-container .wpforms-field-sublabel {
  display: block;
  font-size: 0.8em;
  font-weight: 400;
  font-style: normal;
  min-width: 120px;
}

.wpforms-container .wpforms-field-label.wpforms-label-hide,
.wpforms-container .wpforms-field-sublabel.wpforms-sublabel-hide,
.wp-core-ui div.wpforms-container .wpforms-field-label.wpforms-label-hide,
.wp-core-ui div.wpforms-container .wpforms-field-sublabel.wpforms-sublabel-hide {
  position: absolute;
  clip: rect(0 0 0 0);
  width: 1px;
  height: 1px;
  margin: -1px;
  overflow: hidden;
}

.wpforms-container .wpforms-required-label,
.wp-core-ui div.wpforms-container .wpforms-required-label {
  color: var(--wpforms-label-error-color);
  font-weight: normal;
}

.wpforms-container input[type=date],
.wpforms-container input[type=datetime],
.wpforms-container input[type=datetime-local],
.wpforms-container input[type=email],
.wpforms-container input[type=month],
.wpforms-container input[type=number],
.wpforms-container input[type=password],
.wpforms-container input[type=range],
.wpforms-container input[type=search],
.wpforms-container input[type=tel],
.wpforms-container input[type=text],
.wpforms-container input[type=time],
.wpforms-container input[type=url],
.wpforms-container input[type=week],
.wpforms-container select,
.wpforms-container textarea,
.wp-core-ui div.wpforms-container input[type=date],
.wp-core-ui div.wpforms-container input[type=datetime],
.wp-core-ui div.wpforms-container input[type=datetime-local],
.wp-core-ui div.wpforms-container input[type=email],
.wp-core-ui div.wpforms-container input[type=month],
.wp-core-ui div.wpforms-container input[type=number],
.wp-core-ui div.wpforms-container input[type=password],
.wp-core-ui div.wpforms-container input[type=range],
.wp-core-ui div.wpforms-container input[type=search],
.wp-core-ui div.wpforms-container input[type=tel],
.wp-core-ui div.wpforms-container input[type=text],
.wp-core-ui div.wpforms-container input[type=time],
.wp-core-ui div.wpforms-container input[type=url],
.wp-core-ui div.wpforms-container input[type=week],
.wp-core-ui div.wpforms-container select,
.wp-core-ui div.wpforms-container textarea {
  display: block;
  width: 100%;
  box-sizing: border-box;
  font-family: inherit;
  font-style: normal;
  font-weight: 400;
  margin: 0;
}

.wpforms-container input[type=date]:read-only,
.wpforms-container input[type=datetime]:read-only,
.wpforms-container input[type=datetime-local]:read-only,
.wpforms-container input[type=email]:read-only,
.wpforms-container input[type=month]:read-only,
.wpforms-container input[type=number]:read-only,
.wpforms-container input[type=password]:read-only,
.wpforms-container input[type=range]:read-only,
.wpforms-container input[type=search]:read-only,
.wpforms-container input[type=tel]:read-only,
.wpforms-container input[type=text]:read-only,
.wpforms-container input[type=time]:read-only,
.wpforms-container input[type=url]:read-only,
.wpforms-container input[type=week]:read-only,
.wpforms-container select:read-only,
.wpforms-container textarea:read-only,
.wp-core-ui div.wpforms-container input[type=date]:read-only,
.wp-core-ui div.wpforms-container input[type=datetime]:read-only,
.wp-core-ui div.wpforms-container input[type=datetime-local]:read-only,
.wp-core-ui div.wpforms-container input[type=email]:read-only,
.wp-core-ui div.wpforms-container input[type=month]:read-only,
.wp-core-ui div.wpforms-container input[type=number]:read-only,
.wp-core-ui div.wpforms-container input[type=password]:read-only,
.wp-core-ui div.wpforms-container input[type=range]:read-only,
.wp-core-ui div.wpforms-container input[type=search]:read-only,
.wp-core-ui div.wpforms-container input[type=tel]:read-only,
.wp-core-ui div.wpforms-container input[type=text]:read-only,
.wp-core-ui div.wpforms-container input[type=time]:read-only,
.wp-core-ui div.wpforms-container input[type=url]:read-only,
.wp-core-ui div.wpforms-container input[type=week]:read-only,
.wp-core-ui div.wpforms-container select:read-only,
.wp-core-ui div.wpforms-container textarea:read-only {
  cursor: default;
}

.wpforms-container textarea,
.wp-core-ui div.wpforms-container textarea {
  resize: vertical;
}

.wpforms-container input[type=checkbox],
.wpforms-container input[type=radio],
.wp-core-ui div.wpforms-container input[type=checkbox],
.wp-core-ui div.wpforms-container input[type=radio] {
  width: 16px;
  height: 16px;
  margin: 2px 10px 0 3px;
  display: inline-block;
  vertical-align: baseline;
  font-style: normal;
  font-weight: 400;
}

.wpforms-container .wpforms-five-sixths,
.wpforms-container .wpforms-four-sixths,
.wpforms-container .wpforms-four-fifths,
.wpforms-container .wpforms-one-fifth,
.wpforms-container .wpforms-one-fourth,
.wpforms-container .wpforms-one-half,
.wpforms-container .wpforms-one-sixth,
.wpforms-container .wpforms-one-third,
.wpforms-container .wpforms-three-fourths,
.wpforms-container .wpforms-three-fifths,
.wpforms-container .wpforms-three-sixths,
.wpforms-container .wpforms-two-fourths,
.wpforms-container .wpforms-two-fifths,
.wpforms-container .wpforms-two-sixths,
.wpforms-container .wpforms-two-thirds {
  float: left;
  margin-left: 20px;
  clear: none;
}

.wpforms-container .wpforms-one-half,
.wpforms-container .wpforms-three-sixths,
.wpforms-container .wpforms-two-fourths {
  width: calc( 50% - 10px);
}

.wpforms-container .wpforms-one-third,
.wpforms-container .wpforms-two-sixths {
  width: calc( 100% / 3 - 20px);
}

.wpforms-container .wpforms-one-third.wpforms-first,
.wpforms-container .wpforms-two-sixths.wpforms-first {
  width: calc( 100% / 3);
}

.wpforms-container .wpforms-four-sixths,
.wpforms-container .wpforms-two-thirds {
  width: calc( 2 * 100% / 3 - 20px);
}

.wpforms-container .wpforms-four-sixths.wpforms-first,
.wpforms-container .wpforms-two-thirds.wpforms-first {
  width: calc( 2 * 100% / 3);
}

.wpforms-container .wpforms-one-fourth {
  width: calc( 25% - 20px);
}

.wpforms-container .wpforms-one-fourth.wpforms-first {
  width: 25%;
}

.wpforms-container .wpforms-three-fourths {
  width: calc( 75% - 20px);
}

.wpforms-container .wpforms-three-fourths.wpforms-first {
  width: 75%;
}

.wpforms-container .wpforms-one-fifth {
  width: calc( 100% / 5 - 20px);
}

.wpforms-container .wpforms-one-fifth.wpforms-first {
  width: calc( 100% / 5);
}

.wpforms-container .wpforms-two-fifths {
  width: calc( 2 * 100% / 5 - 20px);
}

.wpforms-container .wpforms-two-fifths.wpforms-first {
  width: calc( 2 * 100% / 5);
}

.wpforms-container .wpforms-three-fifths {
  width: calc( 3 * 100% / 5 - 20px);
}

.wpforms-container .wpforms-three-fifths.wpforms-first {
  width: calc( 3 * 100% / 5);
}

.wpforms-container .wpforms-four-fifths {
  width: calc( 4 * 100% / 5 - 20px);
}

.wpforms-container .wpforms-four-fifths.wpforms-first {
  width: calc( 4 * 100% / 5);
}

.wpforms-container .wpforms-one-sixth {
  width: calc( 100% / 6 - 20px);
}

.wpforms-container .wpforms-one-sixth.wpforms-first {
  width: calc( 100% / 6);
}

.wpforms-container .wpforms-five-sixths {
  width: calc( 5 * 100% / 6 - 20px);
}

.wpforms-container .wpforms-five-sixths.wpforms-first {
  width: calc( 5 * 100% / 6);
}

.wpforms-container .wpforms-first {
  clear: both !important;
  margin-left: 0 !important;
}

.wpforms-container .wpforms-field {
  float: none;
  clear: both;
}

.wpforms-container .wpforms-field.wpforms-five-sixths, .wpforms-container .wpforms-field.wpforms-four-sixths, .wpforms-container .wpforms-field.wpforms-four-fifths, .wpforms-container .wpforms-field.wpforms-one-fifth, .wpforms-container .wpforms-field.wpforms-one-fourth, .wpforms-container .wpforms-field.wpforms-one-half, .wpforms-container .wpforms-field.wpforms-one-sixth, .wpforms-container .wpforms-field.wpforms-one-third, .wpforms-container .wpforms-field.wpforms-three-fourths, .wpforms-container .wpforms-field.wpforms-three-fifths, .wpforms-container .wpforms-field.wpforms-three-sixths, .wpforms-container .wpforms-field.wpforms-two-fourths, .wpforms-container .wpforms-field.wpforms-two-fifths, .wpforms-container .wpforms-field.wpforms-two-sixths, .wpforms-container .wpforms-field.wpforms-two-thirds {
  float: left;
  margin-left: 20px;
  clear: none;
}

.wpforms-container .wpforms-field .wpforms-field-row {
  align-items: start;
  position: relative;
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-field-row-block {
  padding: 0 10px;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-field-row-block:first-child {
  padding-inline-start: 0;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-field-row-block:last-child {
  padding-inline-end: 0;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-field-row-block:only-child {
  margin-right: auto;
  padding-right: 10px;
}

.wpforms-container .wpforms-field .wpforms-field-row:before {
  content: "";
  display: table;
}

.wpforms-container .wpforms-field .wpforms-field-row:after {
  clear: both;
  content: "";
  display: table;
}

.wpforms-container .wpforms-field .wpforms-field-row:last-of-type {
  margin-bottom: 0;
}

.wpforms-container .wpforms-field .wpforms-field-row > :only-child {
  width: 100%;
}

.wpforms-container .wpforms-field .wpforms-field-row.wpforms-no-columns {
  display: block;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-five-sixths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-four-sixths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-four-fifths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-fifth,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-fourth,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-half,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-sixth,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-third,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-three-fourths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-three-fifths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-three-sixths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-fourths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-fifths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-sixths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-thirds {
  float: none;
  margin-left: 0;
  clear: initial;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-half,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-three-sixths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-fourths {
  width: 50%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-third,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-sixths {
  width: 33.33333%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-four-sixths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-thirds {
  width: 66.66667%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-fourth {
  width: 25%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-three-fourths {
  width: 75%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-fifth {
  width: 20%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-fifths {
  width: 40%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-three-fifths {
  width: 60%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-four-fifths {
  width: 80%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-sixth {
  width: 16.66667%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-five-sixths {
  width: 83.33333%;
}

.wpforms-container .wpforms-field .wpforms-checkbox-2-columns ul,
.wpforms-container .wpforms-field .wpforms-multiplechoice-2-columns ul,
.wpforms-container .wpforms-field .wpforms-list-2-columns ul,
.wpforms-container .wpforms-field .wpforms-checkbox-3-columns ul,
.wpforms-container .wpforms-field .wpforms-multiplechoice-3-columns ul,
.wpforms-container .wpforms-field .wpforms-list-3-columns ul {
  display: grid;
  gap: 15px 30px;
}

.wpforms-container .wpforms-field .wpforms-checkbox-2-columns ul,
.wpforms-container .wpforms-field .wpforms-multiplechoice-2-columns ul,
.wpforms-container .wpforms-field .wpforms-list-2-columns ul {
  grid-template-columns: repeat(2, 1fr);
}

.wpforms-container .wpforms-field .wpforms-checkbox-3-columns ul,
.wpforms-container .wpforms-field .wpforms-multiplechoice-3-columns ul,
.wpforms-container .wpforms-field .wpforms-list-3-columns ul {
  grid-template-columns: repeat(3, 1fr);
}

.wpforms-container .wpforms-field .wpforms-list-inline ul li {
  display: inline-block;
  vertical-align: top;
  margin-right: 20px;
}

.wpforms-container.inline-fields {
  overflow: visible;
}

.wpforms-container.inline-fields .wpforms-form {
  display: flex;
  justify-content: space-between;
}

.wpforms-container.inline-fields .wpforms-field-container {
  display: flex;
  justify-content: space-between;
  width: calc( 100% - 175px);
}

.wpforms-container.inline-fields .wpforms-field-container .wpforms-field {
  padding-right: 7px;
  padding-left: 8px;
}

.wpforms-container.inline-fields .wpforms-field-container .wpforms-field:first-of-type {
  padding-left: 0;
}

.wpforms-container.inline-fields .wpforms-field-container .wpforms-field:last-of-type {
  padding-right: 0;
}

.wpforms-container.inline-fields .wpforms-field-row:first-of-type .wpforms-field-row-block:first-child {
  padding-left: 0;
}

.wpforms-container.inline-fields .wpforms-submit-container {
  width: 160px;
  padding-bottom: 16px;
  align-self: flex-end;
}

.wpforms-container.inline-fields .wpforms-submit {
  display: block;
  width: 100%;
}

.wpforms-container.inline-fields input.wpforms-field-medium,
.wpforms-container.inline-fields select.wpforms-field-medium,
.wpforms-container.inline-fields .wpforms-field-row.wpforms-field-medium {
  max-width: 100%;
}

.wpforms-container ul.wpforms-image-choices label:not(.wpforms-error) {
  cursor: pointer;
  position: relative;
}

.wpforms-container ul.wpforms-image-choices label input {
  top: 50%;
}

.wpforms-container .wpforms-image-choices-modern img {
  display: inline-block;
  margin: 0 auto;
  max-width: 100%;
}

.wpforms-container .wpforms-image-choices-modern label:not(.wpforms-error) {
  background: none;
  display: inline-block;
  margin: 0 auto;
  cursor: pointer;
  border: 1px solid #ffffff;
  border-radius: 3px;
  padding: 20px;
  transition: all 0.5s;
  text-align: center;
}

.wpforms-container .wpforms-image-choices-modern label:not(.wpforms-error):hover {
  border: 1px solid rgba(0, 0, 0, 0.25);
}

.wpforms-container .wpforms-image-choices-modern label:not(.wpforms-error):focus, .wpforms-container .wpforms-image-choices-modern label:not(.wpforms-error):focus-within {
  border: 1px solid rgba(0, 0, 0, 0.25);
}

.wpforms-container .wpforms-image-choices-modern .wpforms-selected label, .wpforms-container .wpforms-image-choices-modern li:has(input:checked) label {
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);
}

.wpforms-container .wpforms-image-choices-modern .wpforms-selected .wpforms-image-choices-label, .wpforms-container .wpforms-image-choices-modern li:has(input:checked) .wpforms-image-choices-label {
  font-weight: 700;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-selected .wpforms-image-choices-image:after, .wpforms-container .wpforms-image-choices-modern li:has(input:checked) .wpforms-image-choices-image:after {
  opacity: 1;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-image-choices-image {
  display: block;
  position: relative;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-image-choices-image:after {
  content: "\2714";
  font-size: 22px;
  line-height: 32px;
  color: #ffffff;
  background: var(--wpforms-button-background-color, #066aab);
  opacity: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -16px 0 0 -16px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.5s;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-image-choices-label {
  display: block;
  margin-top: 12px;
}

.wpforms-container .wpforms-list-inline .wpforms-image-choices-modern li {
  margin: 5px !important;
}

.wpforms-container .wpforms-image-choices-classic img {
  display: inline-block;
  margin: 0 auto;
  max-width: 100%;
}

.wpforms-container .wpforms-image-choices-classic label:not(.wpforms-error) {
  background: none;
  display: inline-block;
  margin: 0 auto;
  cursor: pointer;
  border: 1px solid transparent;
  padding: 10px;
  text-align: center;
}

.wpforms-container .wpforms-image-choices-classic label:not(.wpforms-error):hover {
  border: 1px solid rgba(0, 0, 0, 0.25);
}

.wpforms-container .wpforms-image-choices-classic label:not(.wpforms-error):focus {
  border: 1px solid rgba(0, 0, 0, 0.25);
}

.wpforms-container .wpforms-image-choices-classic .wpforms-image-choices-image {
  display: block;
}

.wpforms-container .wpforms-image-choices-classic .wpforms-selected label, .wpforms-container .wpforms-image-choices-classic li:has(input:checked) label {
  border-color: rgba(0, 0, 0, 0.7);
}

.wpforms-container .wpforms-image-choices-classic .wpforms-image-choices-label {
  display: block;
  margin-top: 8px;
}

.wpforms-container .wpforms-list-inline .wpforms-image-choices-classic li {
  margin: 0 10px 10px 0 !important;
}

.wpforms-container .wpforms-image-choices-none .wpforms-image-choices-item img {
  display: inline-block;
  margin: 0 auto;
  max-width: 100%;
}

.wpforms-container .wpforms-image-choices-none .wpforms-image-choices-item input {
  vertical-align: middle;
}

.wpforms-container .wpforms-image-choices-none .wpforms-image-choices-item .wpforms-image-choices-label {
  display: inline-block;
  margin-top: 5px;
  margin-left: 10px;
  vertical-align: middle;
}

.wpforms-container-full ul.wpforms-icon-choices,
.wpforms-container-full ul.wpforms-icon-choices * {
  box-sizing: border-box;
}

.wpforms-container-full ul.wpforms-icon-choices {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 1px 0 1px !important;
  margin: 12px 0 -20px 0 !important;
  /* Style: Default */
  /* Style: Modern */
  /* Style: Classic */
}

.wpforms-container-full ul.wpforms-icon-choices + .wpforms-field-description,
.wpforms-container-full ul.wpforms-icon-choices + .wpforms-error {
  margin-top: 15px;
}

.wpforms-container-full ul.wpforms-icon-choices li {
  min-width: 120px;
  padding-right: 0 !important;
  margin: 0 0 20px 0 !important;
}

.wpforms-container-full ul.wpforms-icon-choices label {
  position: relative;
  display: block;
  margin: 0;
  cursor: pointer;
}

.wpforms-container-full ul.wpforms-icon-choices .wpforms-icon-choices-icon {
  display: block;
}

.wpforms-container-full ul.wpforms-icon-choices svg {
  margin: 0 auto;
  fill: var(--wpforms-icon-choices-color);
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-none svg {
  margin: 0;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  margin: 0 0 22px 0 !important;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-default label {
  text-align: center;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-default label:focus-within .wpforms-icon-choices-icon {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon {
  position: relative;
  z-index: 1;
  margin-bottom: 10px;
  padding: 15px 20px 45px 20px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 6px;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon:hover {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon svg {
  position: relative;
  z-index: 2;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon-bg {
  display: block;
  position: absolute;
  z-index: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 6px;
  background-color: #ffffff;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon, .wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon {
  background-color: transparent;
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
  box-sizing: border-box;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon-bg, .wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon-bg {
  background-color: var(--wpforms-icon-choices-color);
  opacity: .1;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-modern li label {
  background-color: #ffffff !important;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 6px;
  height: 100%;
  padding: 20px 20px 15px 20px;
  text-align: center;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-modern li label:hover {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-modern li:focus-within label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-modern li.wpforms-selected label, .wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-modern li:has(input:checked) label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color), 0 2px 10px rgba(0, 0, 0, 0.15);
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-modern li .wpforms-icon-choices-icon {
  margin-bottom: 10px;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-classic li label {
  background-color: #ffffff !important;
  height: 100%;
  padding: 20px 20px 15px 20px;
  text-align: center;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-classic li label:hover {
  box-shadow: 0 0 0 1px #999999;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-classic li:focus-within label {
  box-shadow: 0 0 0 1px #999999;
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-classic li.wpforms-selected label, .wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-classic li:has(input:checked) label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container-full ul.wpforms-icon-choices.wpforms-icon-choices-classic li .wpforms-icon-choices-icon {
  margin-bottom: 10px;
}

.wpforms-container-full .wpforms-field-radio ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before,
.wpforms-container-full .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: 15px;
  left: calc(50% - 8px);
  display: block;
  width: 16px;
  height: 16px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 50%;
  margin: 15px auto 0;
}

.wpforms-container-full .wpforms-field-radio ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before, .wpforms-container-full .wpforms-field-radio ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before,
.wpforms-container-full .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before,
.wpforms-container-full .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container-full .wpforms-field-radio ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after, .wpforms-container-full .wpforms-field-radio ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after,
.wpforms-container-full .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after,
.wpforms-container-full .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: calc(15px + 4px);
  left: calc(50% - 4px);
  display: block;
  width: 8px;
  height: 8px;
  background-color: var(--wpforms-icon-choices-color);
  border-radius: 50%;
}

.wpforms-container-full .wpforms-field-checkbox ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before,
.wpforms-container-full .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: 15px;
  left: calc(50% - 8px);
  display: block;
  width: 16px;
  height: 16px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 3px;
  margin: 15px auto 0;
}

.wpforms-container-full .wpforms-field-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before, .wpforms-container-full .wpforms-field-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before,
.wpforms-container-full .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before,
.wpforms-container-full .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container-full .wpforms-field-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after, .wpforms-container-full .wpforms-field-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after,
.wpforms-container-full .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after,
.wpforms-container-full .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after {
  content: "";
  box-sizing: border-box;
  display: block;
  position: absolute;
  z-index: 2;
  bottom: 23px;
  left: calc(50% - 6px);
  width: 6px;
  height: 10px;
  border-style: solid;
  border-color: var(--wpforms-icon-choices-color);
  border-width: 0 2px 2px 0;
  transform-origin: bottom left;
  transform: rotate(45deg);
}

.wpforms-container-full .wpforms-list-2-columns ul.wpforms-icon-choices,
.wpforms-container-full .wpforms-list-3-columns ul.wpforms-icon-choices,
.wpforms-container-full .wpforms-list-inline ul.wpforms-icon-choices {
  flex-direction: row;
  flex-wrap: wrap;
}

.wpforms-container-full .wpforms-list-2-columns ul.wpforms-icon-choices li,
.wpforms-container-full .wpforms-list-3-columns ul.wpforms-icon-choices li,
.wpforms-container-full .wpforms-list-inline ul.wpforms-icon-choices li {
  margin-right: 20px !important;
}

.wpforms-container-full .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-container-full .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-container-full .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-container-full .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-container-full .wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-container-full .wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  margin-right: 22px !important;
  margin-bottom: 22px !important;
}

.wpforms-container-full .wpforms-list-2-columns ul.wpforms-icon-choices li {
  width: calc( 100% / 2 - 20px / 2);
}

.wpforms-container-full .wpforms-list-2-columns ul.wpforms-icon-choices li:nth-child(2n) {
  margin-right: 0 !important;
}

.wpforms-container-full .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-container-full .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  width: calc( 100% / 2 - 22px / 2);
}

.wpforms-container-full .wpforms-list-3-columns ul.wpforms-icon-choices li {
  width: calc( 100% / 3 - 20px * 2 / 3);
}

.wpforms-container-full .wpforms-list-3-columns ul.wpforms-icon-choices li:nth-child(3n) {
  margin-right: 0 !important;
}

.wpforms-container-full .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-container-full .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  width: calc( 100% / 3 - 22px * 2 / 3);
}

.wpforms-container-full .wpforms-list-inline ul.wpforms-icon-choices li {
  width: auto;
  max-width: calc( 100% / 4 - 20px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price {
  width: calc(60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price.wpforms-field-small {
  text-wrap: balance;
  width: calc(25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price.wpforms-field-large {
  width: calc(100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content select.wpforms-payment-quantity {
  margin-left: 0;
  height: 30px;
  min-height: 30px;
  font-size: 14px;
  padding-top: 0;
  padding-bottom: 0;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price {
  display: inline-block;
  max-width: calc( 60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-small {
  max-width: calc( 25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-large {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-quantity {
  display: inline-block;
  margin-inline-start: 15px;
  width: 70px;
  min-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern {
  display: flex;
  flex-wrap: wrap;
  column-gap: 15px;
  align-items: flex-start;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-label {
  min-width: 100%;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .choices {
  margin-bottom: 5px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row {
  flex-grow: 1;
  max-width: calc( 60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row.wpforms-field-small {
  max-width: calc( 25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row.wpforms-field-large {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-payment-quantity {
  flex-basis: 70px;
  max-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-payment-quantity .choices__list--dropdown {
  min-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-description {
  flex-basis: 100%;
  margin-top: 0;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-error {
  flex-basis: 100%;
}

@media only screen and (max-width: 600px) {
  .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price {
    width: calc( 100% - 85px) !important;
    max-width: 100% !important;
  }
  .wpforms-form .wpforms-payment-quantities-enabled .wpforms-single-item-price-content .wpforms-single-item-price {
    width: calc( 100% - 70px) !important;
  }
  .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row {
    width: calc( 100% - 85px) !important;
    max-width: 100% !important;
  }
}

#wpforms-form-page-page .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-medium, #wpforms-form-page-page .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-small {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-form .choices .choices__inner {
  border-radius: 3px;
  min-height: 35px;
}

.wpforms-container .wpforms-form .choices .choices__inner .choices__list--single {
  height: auto;
}

.wpforms-container .wpforms-form .choices .choices__inner .choices__list--multiple .choices__item {
  line-height: 1.3;
}

.wpforms-container .wpforms-form .choices.is-open.is-flipped .choices__inner,
.wpforms-container .wpforms-form .choices.is-open .choices__list--dropdown {
  border-radius: 0 0 3px 3px;
}

.wpforms-container .wpforms-form .choices.is-open.is-flipped .choices__list--dropdown,
.wpforms-container .wpforms-form .choices.is-open .choices__inner {
  border-radius: 3px 3px 0 0;
}

.wpforms-container textarea {
  line-height: 1.3;
}

.wpforms-container textarea.wpforms-field-small {
  height: 70px;
}

.wpforms-container textarea.wpforms-field-medium {
  height: 120px;
}

.wpforms-container textarea.wpforms-field-large {
  height: 220px;
}

.wpforms-container .wpforms-order-summary-container {
  display: block;
  max-width: 60%;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview {
  width: 100%;
  table-layout: fixed;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  text-align: center;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: left;
}

.rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: right;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
  width: 8ch;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  width: 6ch;
  text-align: right;
}

.rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  text-align: left;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: left;
}

.rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: right;
}

.wpforms-container .size-large > .wpforms-order-summary-container,
.wpforms-container .wpforms-field-large > .wpforms-order-summary-container {
  max-width: 100%;
}

.wpforms-container .size-medium > .wpforms-order-summary-container,
.wpforms-container .wpforms-field-medium > .wpforms-order-summary-container {
  max-width: 60%;
}

.wpforms-container .wpforms-order-summary-container tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: #D63637;
}

div.wpforms-container-full,
div.wpforms-container-full * {
  background: none;
  border: 0 none;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  float: none;
  font-size: 100%;
  height: auto;
  letter-spacing: normal;
  outline: none;
  position: static;
  text-indent: 0;
  text-shadow: none;
  text-transform: none;
  width: auto;
  visibility: visible;
  overflow: visible;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  -ms-box-shadow: none;
  -o-box-shadow: none;
  box-shadow: none;
}

div.wpforms-container-full img, div.wpforms-container-full video, div.wpforms-container-full canvas, div.wpforms-container-full svg {
  overflow: clip;
}

div.wpforms-container-full {
  margin-left: auto;
  margin-right: auto;
}

div.wpforms-container-full:not(:empty) {
  margin: 24px auto;
  padding: var(--wpforms-container-padding);
  background-clip: padding-box;
  background-color: var(--wpforms-background-color);
  background-image: var(--wpforms-background-url);
  background-position: var(--wpforms-background-position);
  background-repeat: var(--wpforms-background-repeat);
  background-size: var(--wpforms-background-size);
  border-style: var(--wpforms-container-border-style);
  border-width: var(--wpforms-container-border-width);
  border-color: var(--wpforms-container-border-color);
  border-radius: var(--wpforms-container-border-radius);
  box-shadow: var(--wpforms-container-shadow-size-box-shadow);
}

div.wpforms-container-full input,
div.wpforms-container-full label,
div.wpforms-container-full select,
div.wpforms-container-full button,
div.wpforms-container-full textarea {
  margin: 0;
  border: 0;
  padding: 0;
  vertical-align: middle;
  background: none;
  height: auto;
  box-sizing: border-box;
}

div.wpforms-container-full h1,
div.wpforms-container-full h2,
div.wpforms-container-full h3,
div.wpforms-container-full h4,
div.wpforms-container-full h5,
div.wpforms-container-full h6,
div.wpforms-container-full small,
div.wpforms-container-full sup,
div.wpforms-container-full sub,
div.wpforms-container-full dl,
div.wpforms-container-full dt,
div.wpforms-container-full dd,
div.wpforms-container-full time,
div.wpforms-container-full address,
div.wpforms-container-full pre,
div.wpforms-container-full code,
div.wpforms-container-full blockquote,
div.wpforms-container-full sup,
div.wpforms-container-full sub,
div.wpforms-container-full del {
  font-size: revert;
  font-weight: revert;
  margin: revert;
  padding: revert;
}

div.wpforms-container-full sup,
div.wpforms-container-full sub {
  position: relative;
}

div.wpforms-container-full del {
  text-decoration: line-through;
}

div.wpforms-container-full blockquote {
  padding-left: 20px;
  border-left: 4px solid;
}

div.wpforms-container-full blockquote p {
  font-size: revert;
  font-weight: revert;
  font-style: italic;
}

div.wpforms-container-full ul,
div.wpforms-container-full ul li {
  background: none;
  border: 0;
  margin: 0;
  padding: 0;
}

div.wpforms-container-full ul li {
  margin-bottom: 15px;
}

div.wpforms-container-full ul li:last-of-type {
  margin-bottom: 0;
}

div.wpforms-container-full hr {
  border-top-width: var(--wpforms-field-border-size);
  border-top-style: var(--wpforms-field-border-style);
  border-top-color: var(--wpforms-field-border-color);
  margin: 0.5em auto;
}

div.wpforms-container-full fieldset {
  min-width: 0;
}

div.wpforms-container-full .wpforms-form .wpforms-head-container {
  margin: 0;
  padding: 0 0 35px 0;
}

div.wpforms-container-full .wpforms-form .wpforms-title {
  font-weight: 700;
  line-height: 29px;
  color: var(--wpforms-label-color);
}

div.wpforms-container-full .wpforms-form .wpforms-description {
  font-style: normal;
  font-weight: 300;
  font-size: var(--wpforms-label-size-font-size);
  line-height: var(--wpforms-label-size-line-height);
  color: var(--wpforms-label-color);
}

div.wpforms-container-full .wpforms-form .wpforms-submit-container {
  margin-top: var(--wpforms-button-size-margin-top);
}

div.wpforms-container-full .wpforms-form .wpforms-submit-spinner {
  max-width: 26px;
}

body .wpforms-test {
  outline: 2px solid red !important;
}

div.wpforms-container-full .wpforms-form label.wpforms-error,
div.wpforms-container-full .wpforms-form em.wpforms-error {
  font-weight: 400;
  font-size: var(--wpforms-label-size-sublabel-font-size);
  line-height: var(--wpforms-label-size-sublabel-line-height);
  margin-top: var(--wpforms-field-size-input-spacing);
  color: var(--wpforms-label-error-color);
  padding: 0 0 0 5px;
  position: relative;
}

div.wpforms-container-full .wpforms-form label.wpforms-error:before,
div.wpforms-container-full .wpforms-form em.wpforms-error:before {
  -webkit-mask-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2217%22%20height%3D%2215%22%20viewBox%3D%220%200%2017%2015%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Cpath%20d%3D%22M16.0264%2012.3086L9.46387%200.90625C8.97168%200.0585938%207.68652%200.03125%207.19434%200.90625L0.631836%2012.3086C0.139648%2013.1562%200.768555%2014.25%201.78027%2014.25H14.8779C15.8896%2014.25%2016.5186%2013.1836%2016.0264%2012.3086ZM8.34277%209.92969C9.02637%209.92969%209.60059%2010.5039%209.60059%2011.1875C9.60059%2011.8984%209.02637%2012.4453%208.34277%2012.4453C7.63184%2012.4453%207.08496%2011.8984%207.08496%2011.1875C7.08496%2010.5039%207.63184%209.92969%208.34277%209.92969ZM7.13965%205.41797C7.1123%205.22656%207.27637%205.0625%207.46777%205.0625H9.19043C9.38184%205.0625%209.5459%205.22656%209.51855%205.41797L9.32715%209.13672C9.2998%209.32812%209.16309%209.4375%208.99902%209.4375H7.65918C7.49512%209.4375%207.3584%209.32812%207.33105%209.13672L7.13965%205.41797Z%22%20fill%3D%22currentColor%22%2F%3E%0A%3C%2Fsvg%3E%0A");
  mask-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2217%22%20height%3D%2215%22%20viewBox%3D%220%200%2017%2015%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Cpath%20d%3D%22M16.0264%2012.3086L9.46387%200.90625C8.97168%200.0585938%207.68652%200.03125%207.19434%200.90625L0.631836%2012.3086C0.139648%2013.1562%200.768555%2014.25%201.78027%2014.25H14.8779C15.8896%2014.25%2016.5186%2013.1836%2016.0264%2012.3086ZM8.34277%209.92969C9.02637%209.92969%209.60059%2010.5039%209.60059%2011.1875C9.60059%2011.8984%209.02637%2012.4453%208.34277%2012.4453C7.63184%2012.4453%207.08496%2011.8984%207.08496%2011.1875C7.08496%2010.5039%207.63184%209.92969%208.34277%209.92969ZM7.13965%205.41797C7.1123%205.22656%207.27637%205.0625%207.46777%205.0625H9.19043C9.38184%205.0625%209.5459%205.22656%209.51855%205.41797L9.32715%209.13672C9.2998%209.32812%209.16309%209.4375%208.99902%209.4375H7.65918C7.49512%209.4375%207.3584%209.32812%207.33105%209.13672L7.13965%205.41797Z%22%20fill%3D%22currentColor%22%2F%3E%0A%3C%2Fsvg%3E%0A");
  content: '';
  position: relative;
  display: inline-block;
  right: 5px;
  top: 1.5px;
  width: 16px;
  height: 14px;
  background-color: var(--wpforms-label-error-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field-address .wpforms-field-sublabel + .wpforms-error,
div.wpforms-container-full .wpforms-form .wpforms-field-credit-card .wpforms-field-sublabel + .wpforms-error {
  margin-top: calc( 1.5 * var( --wpforms-field-size-sublabel-spacing ));
}

div.wpforms-container-full .wpforms-form .wpforms-field input.wpforms-error, div.wpforms-container-full .wpforms-form .wpforms-field input.user-invalid,
div.wpforms-container-full .wpforms-form .wpforms-field textarea.wpforms-error,
div.wpforms-container-full .wpforms-form .wpforms-field textarea.user-invalid,
div.wpforms-container-full .wpforms-form .wpforms-field select.wpforms-error,
div.wpforms-container-full .wpforms-form .wpforms-field select.user-invalid {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field input.wpforms-error:hover, div.wpforms-container-full .wpforms-form .wpforms-field input.user-invalid:hover,
div.wpforms-container-full .wpforms-form .wpforms-field textarea.wpforms-error:hover,
div.wpforms-container-full .wpforms-form .wpforms-field textarea.user-invalid:hover,
div.wpforms-container-full .wpforms-form .wpforms-field select.wpforms-error:hover,
div.wpforms-container-full .wpforms-form .wpforms-field select.user-invalid:hover {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  box-shadow: 0 0 2px 0 var(--wpforms-label-error-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field input.wpforms-error:focus, div.wpforms-container-full .wpforms-form .wpforms-field input.user-invalid:focus,
div.wpforms-container-full .wpforms-form .wpforms-field textarea.wpforms-error:focus,
div.wpforms-container-full .wpforms-form .wpforms-field textarea.user-invalid:focus,
div.wpforms-container-full .wpforms-form .wpforms-field select.wpforms-error:focus,
div.wpforms-container-full .wpforms-form .wpforms-field select.user-invalid:focus {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  box-shadow: 0 0 0 1px var(--wpforms-label-error-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field input[type=checkbox].wpforms-error, div.wpforms-container-full .wpforms-form .wpforms-field input[type=checkbox].user-invalid,
div.wpforms-container-full .wpforms-form .wpforms-field input[type=radio].wpforms-error,
div.wpforms-container-full .wpforms-form .wpforms-field input[type=radio].user-invalid {
  border: none;
  box-shadow: none;
}

div.wpforms-container-full .wpforms-form .wpforms-field input[type=checkbox].wpforms-error:hover, div.wpforms-container-full .wpforms-form .wpforms-field input[type=checkbox].wpforms-error:focus, div.wpforms-container-full .wpforms-form .wpforms-field input[type=checkbox].user-invalid:hover, div.wpforms-container-full .wpforms-form .wpforms-field input[type=checkbox].user-invalid:focus,
div.wpforms-container-full .wpforms-form .wpforms-field input[type=radio].wpforms-error:hover,
div.wpforms-container-full .wpforms-form .wpforms-field input[type=radio].wpforms-error:focus,
div.wpforms-container-full .wpforms-form .wpforms-field input[type=radio].user-invalid:hover,
div.wpforms-container-full .wpforms-form .wpforms-field input[type=radio].user-invalid:focus {
  border: none;
  box-shadow: none;
}

div.wpforms-container-full .wpforms-form .wpforms-error-container {
  color: var(--wpforms-label-error-color);
  font-size: var(--wpforms-label-size-font-size);
  line-height: var(--wpforms-label-size-line-height);
}

div.wpforms-container-full .wpforms-form .wpforms-error-container ul li {
  list-style: inside !important;
}

div.wpforms-container-full .wpforms-form .wpforms-error-container ol li {
  list-style: inside decimal !important;
}

div.wpforms-container-full .wpforms-form .wpforms-error-container a {
  color: var(--wpforms-label-error-color);
  text-decoration: underline !important;
}

div.wpforms-container-full .wpforms-form .wpforms-error-container a:hover {
  text-decoration: none !important;
}

div.wpforms-container-full .wpforms-form .wpforms-error-container del {
  text-decoration: line-through !important;
}

div.wpforms-container-full .wpforms-form .wpforms-error-container blockquote {
  padding-left: 20px;
  border-left: 4px solid;
  font-style: italic;
}

div.wpforms-container-full .wpforms-form .wpforms-error-container.wpforms-error-styled-container {
  padding: 15px 0;
}

div.wpforms-container-full .wpforms-form .wpforms-error-container.wpforms-error-styled-container .wpforms-error {
  padding: 11px;
  border: 1px solid var(--wpforms-label-error-color);
  border-left: 5px solid;
}

div.wpforms-container-full .wpforms-form .wpforms-error-alert {
  color: var(--wpforms-label-error-color);
  border-color: transparent;
  border-radius: var(--wpforms-field-border-radius);
  font-size: var(--wpforms-label-size-sublabel-font-size);
  padding: var(--wpforms-field-size-input-spacing);
  background: linear-gradient(90deg, var(--wpforms-label-error-color) -3000%, transparent 500%);
}

div.wpforms-container-full .wpforms-confirmation-container-full,
div[submit-success] > .wpforms-confirmation-container-full {
  color: #333333;
  margin: 0 auto 24px;
  padding: 15px;
  overflow-wrap: break-word;
}

div.wpforms-container-full .wpforms-confirmation-container-full ul, div.wpforms-container-full .wpforms-confirmation-container-full ol,
div[submit-success] > .wpforms-confirmation-container-full ul,
div[submit-success] > .wpforms-confirmation-container-full ol {
  padding-left: 30px;
}

div.wpforms-container-full .wpforms-confirmation-container-full ul li, div.wpforms-container-full .wpforms-confirmation-container-full ol li,
div[submit-success] > .wpforms-confirmation-container-full ul li,
div[submit-success] > .wpforms-confirmation-container-full ol li {
  margin-bottom: 5px;
}

div.wpforms-container-full .wpforms-confirmation-container-full p,
div[submit-success] > .wpforms-confirmation-container-full p {
  margin: 0 0 15px 0;
}

div.wpforms-container-full .wpforms-confirmation-container-full p:last-child,
div[submit-success] > .wpforms-confirmation-container-full p:last-child {
  margin: 0;
}

div.wpforms-container-full .wpforms-confirmation-container-full iframe,
div[submit-success] > .wpforms-confirmation-container-full iframe {
  width: 100%;
  border: 0;
}

div.wpforms-container-full .wpforms-confirmation-container-full,
div[submit-success] > .wpforms-confirmation-container-full:not(.wpforms-redirection-message) {
  background: #e0ffc7;
  border: 1px solid #b4d39b;
  box-sizing: border-box;
}

div.wpforms-container-full .wpforms-confirmation-container-full p,
div[submit-success] > .wpforms-confirmation-container-full:not(.wpforms-redirection-message) p {
  color: #333333;
}

div.wpforms-container-full .wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview,
div[submit-success] > .wpforms-confirmation-container-full:not(.wpforms-redirection-message) .wpforms-order-summary-container table.wpforms-order-summary-preview {
  color: inherit;
  border-color: #b4d39b;
}

div.wpforms-container-full .wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview td, div.wpforms-container-full .wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview th,
div[submit-success] > .wpforms-confirmation-container-full:not(.wpforms-redirection-message) .wpforms-order-summary-container table.wpforms-order-summary-preview td,
div[submit-success] > .wpforms-confirmation-container-full:not(.wpforms-redirection-message) .wpforms-order-summary-container table.wpforms-order-summary-preview th {
  border-top-color: #b4d39b;
}

div.wpforms-container-full .wpforms-form amp-img > img {
  position: absolute;
}

div.wpforms-container-full .wpforms-form .wpforms-limit-text {
  font-size: 13px;
  display: block;
}

.wpforms-screen-reader-announce {
  color: transparent !important;
  position: absolute !important;
  bottom: 0 !important;
}

body.rtl div.wpforms-container-full .wpforms-form .wpforms-page-indicator.progress .wpforms-page-indicator-page-progress-wrap {
  transform: rotate(180deg);
}

body.rtl div.wpforms-container-full .wpforms-form .wpforms-page-indicator.circles .wpforms-page-indicator-page-number {
  margin: 0 0 0 10px;
}

body.rtl div.wpforms-container-full .wpforms-form .wpforms-page-indicator.circles .wpforms-page-indicator-page {
  margin: 0 0 0 15px;
}

body.rtl div.wpforms-container-full .wpforms-form em.wpforms-error {
  padding: 0 5px 0 0;
}

body.rtl div.wpforms-container-full .wpforms-form em.wpforms-error:before {
  left: 5px;
  right: auto;
}

body.rtl div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices__list--single .choices__item {
  padding-right: 0;
  padding-left: 15px;
}

body.rtl div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__inner {
  padding: 0 7px 0 24px;
}

body.rtl div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__inner .choices__list--single {
  padding: 0 4px 0 16px;
}

body.rtl div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices[data-type*="select-multiple"]:after {
  right: auto;
  left: 12px;
}

body.rtl div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices[data-type*="select-one"]:after {
  right: auto;
  left: 12px;
}

body.rtl div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices[data-type*="select-one"] .choices__button {
  right: auto;
  left: 0;
  margin-right: 0;
  margin-left: 25px;
}

body.rtl div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices[data-type*="select-multiple"] .choices__button {
  margin-right: 5px;
  border-right: none;
}

body.rtl div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-classic select {
  background-position: calc(0% + 12px) 50%, calc(0% + 17px) 50%;
  padding: 0 12px 0 24px;
}

div.wpforms-container-full .wpforms-field-description,
div.wpforms-container-full .wpforms-field-limit-text,
.wp-core-ui div.wpforms-container-full .wpforms-field-description,
.wp-core-ui div.wpforms-container-full .wpforms-field-limit-text {
  font-size: var(--wpforms-label-size-sublabel-font-size);
  line-height: var(--wpforms-label-size-sublabel-line-height);
  color: var(--wpforms-label-sublabel-color);
  margin: var(--wpforms-field-size-sublabel-spacing) 0 0 0;
}

div.wpforms-container-full .wpforms-field-description ul, div.wpforms-container-full .wpforms-field-description ol,
.wp-core-ui div.wpforms-container-full .wpforms-field-description ul,
.wp-core-ui div.wpforms-container-full .wpforms-field-description ol {
  padding-left: 15px;
}

div.wpforms-container-full .wpforms-field-description.wpforms-disclaimer-description,
.wp-core-ui div.wpforms-container-full .wpforms-field-description.wpforms-disclaimer-description {
  color: var(--wpforms-field-text-color);
  margin-top: var(--wpforms-field-size-input-spacing);
  font-size: var(--wpforms-label-size-sublabel-font-size);
  line-height: var(--wpforms-label-size-sublabel-line-height);
  background-color: var(--wpforms-field-background-color);
  border-radius: var(--wpforms-field-border-radius);
  border-width: var(--wpforms-field-border-size);
  border-style: var(--wpforms-field-border-style);
  border-color: var(--wpforms-field-border-color);
  padding: var(--wpforms-field-size-padding-h);
}

div.wpforms-container-full .wpforms-field-description.wpforms-disclaimer-description p,
.wp-core-ui div.wpforms-container-full .wpforms-field-description.wpforms-disclaimer-description p {
  color: inherit;
  font-size: inherit;
  line-height: inherit;
  margin-bottom: 15px;
}

div.wpforms-container-full .wpforms-field-description-before, div.wpforms-container-full .wpforms-field-description.before,
.wp-core-ui div.wpforms-container-full .wpforms-field-description-before,
.wp-core-ui div.wpforms-container-full .wpforms-field-description.before {
  margin: 0 0 var(--wpforms-field-size-input-spacing) 0;
}

div.wpforms-container-full .wpforms-field-row + .wpforms-field-description,
.wp-core-ui div.wpforms-container-full .wpforms-field-row + .wpforms-field-description {
  margin-top: 0;
}

div.wpforms-container-full .wpforms-field-row + .wpforms-field-description.wpforms-disclaimer-description,
.wp-core-ui div.wpforms-container-full .wpforms-field-row + .wpforms-field-description.wpforms-disclaimer-description {
  margin-top: 0;
}

div.wpforms-container-full .wpforms-field-label,
.wp-core-ui div.wpforms-container-full .wpforms-field-label {
  margin: 0 0 var(--wpforms-field-size-input-spacing) 0;
  padding: 0;
  font-size: var(--wpforms-label-size-font-size);
  line-height: var(--wpforms-label-size-line-height);
  color: var(--wpforms-label-color);
}

div.wpforms-container-full .wpforms-field-label-inline,
.wp-core-ui div.wpforms-container-full .wpforms-field-label-inline {
  font-size: var(--wpforms-label-size-font-size);
  line-height: var(--wpforms-label-size-line-height);
}

div.wpforms-container-full .wpforms-field-sublabel,
.wp-core-ui div.wpforms-container-full .wpforms-field-sublabel {
  font-size: var(--wpforms-label-size-sublabel-font-size);
  line-height: var(--wpforms-label-size-sublabel-line-height);
  margin: var(--wpforms-field-size-sublabel-spacing) 0 0 0;
  padding: 0;
  color: var(--wpforms-label-sublabel-color);
}

div.wpforms-container-full .wpforms-field-sublabel.before,
.wp-core-ui div.wpforms-container-full .wpforms-field-sublabel.before {
  margin: 0 0 var(--wpforms-field-size-sublabel-spacing) 0;
}

div.wpforms-container-full .wpforms-field-label-inline,
.wp-core-ui div.wpforms-container-full .wpforms-field-label-inline {
  color: var(--wpforms-label-color);
}

div.wpforms-container-full .wpforms-required-label,
.wp-core-ui div.wpforms-container-full .wpforms-required-label {
  font-weight: 400;
}

div.wpforms-container-full input[type=date],
div.wpforms-container-full input[type=datetime],
div.wpforms-container-full input[type=datetime-local],
div.wpforms-container-full input[type=email],
div.wpforms-container-full input[type=month],
div.wpforms-container-full input[type=number],
div.wpforms-container-full input[type=password],
div.wpforms-container-full input[type=range],
div.wpforms-container-full input[type=search],
div.wpforms-container-full input[type=tel],
div.wpforms-container-full input[type=text],
div.wpforms-container-full input[type=time],
div.wpforms-container-full input[type=url],
div.wpforms-container-full input[type=week],
div.wpforms-container-full select,
div.wpforms-container-full textarea,
.wp-core-ui div.wpforms-container-full input[type=date],
.wp-core-ui div.wpforms-container-full input[type=datetime],
.wp-core-ui div.wpforms-container-full input[type=datetime-local],
.wp-core-ui div.wpforms-container-full input[type=email],
.wp-core-ui div.wpforms-container-full input[type=month],
.wp-core-ui div.wpforms-container-full input[type=number],
.wp-core-ui div.wpforms-container-full input[type=password],
.wp-core-ui div.wpforms-container-full input[type=range],
.wp-core-ui div.wpforms-container-full input[type=search],
.wp-core-ui div.wpforms-container-full input[type=tel],
.wp-core-ui div.wpforms-container-full input[type=text],
.wp-core-ui div.wpforms-container-full input[type=time],
.wp-core-ui div.wpforms-container-full input[type=url],
.wp-core-ui div.wpforms-container-full input[type=week],
.wp-core-ui div.wpforms-container-full select,
.wp-core-ui div.wpforms-container-full textarea {
  background-color: var(--wpforms-field-background-color);
  background-clip: padding-box;
  border-radius: var(--wpforms-field-border-radius);
  color: var(--wpforms-field-text-color);
  border-width: var(--wpforms-field-border-size);
  border-style: var(--wpforms-field-border-style);
  border-color: var(--wpforms-field-border-color);
  padding: 0 var(--wpforms-field-size-padding-h);
  font-size: var(--wpforms-field-size-font-size);
  line-height: 100%;
  box-shadow: none;
  transition: border 0.15s, box-shadow 0.15s;
}

div.wpforms-container-full input[type=date]:focus,
div.wpforms-container-full input[type=datetime]:focus,
div.wpforms-container-full input[type=datetime-local]:focus,
div.wpforms-container-full input[type=email]:focus,
div.wpforms-container-full input[type=month]:focus,
div.wpforms-container-full input[type=number]:focus,
div.wpforms-container-full input[type=password]:focus,
div.wpforms-container-full input[type=range]:focus,
div.wpforms-container-full input[type=search]:focus,
div.wpforms-container-full input[type=tel]:focus,
div.wpforms-container-full input[type=text]:focus,
div.wpforms-container-full input[type=time]:focus,
div.wpforms-container-full input[type=url]:focus,
div.wpforms-container-full input[type=week]:focus,
div.wpforms-container-full select:focus,
div.wpforms-container-full textarea:focus,
.wp-core-ui div.wpforms-container-full input[type=date]:focus,
.wp-core-ui div.wpforms-container-full input[type=datetime]:focus,
.wp-core-ui div.wpforms-container-full input[type=datetime-local]:focus,
.wp-core-ui div.wpforms-container-full input[type=email]:focus,
.wp-core-ui div.wpforms-container-full input[type=month]:focus,
.wp-core-ui div.wpforms-container-full input[type=number]:focus,
.wp-core-ui div.wpforms-container-full input[type=password]:focus,
.wp-core-ui div.wpforms-container-full input[type=range]:focus,
.wp-core-ui div.wpforms-container-full input[type=search]:focus,
.wp-core-ui div.wpforms-container-full input[type=tel]:focus,
.wp-core-ui div.wpforms-container-full input[type=text]:focus,
.wp-core-ui div.wpforms-container-full input[type=time]:focus,
.wp-core-ui div.wpforms-container-full input[type=url]:focus,
.wp-core-ui div.wpforms-container-full input[type=week]:focus,
.wp-core-ui div.wpforms-container-full select:focus,
.wp-core-ui div.wpforms-container-full textarea:focus {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
}

div.wpforms-container-full input[type=date]:focus:invalid,
div.wpforms-container-full input[type=datetime]:focus:invalid,
div.wpforms-container-full input[type=datetime-local]:focus:invalid,
div.wpforms-container-full input[type=email]:focus:invalid,
div.wpforms-container-full input[type=month]:focus:invalid,
div.wpforms-container-full input[type=number]:focus:invalid,
div.wpforms-container-full input[type=password]:focus:invalid,
div.wpforms-container-full input[type=range]:focus:invalid,
div.wpforms-container-full input[type=search]:focus:invalid,
div.wpforms-container-full input[type=tel]:focus:invalid,
div.wpforms-container-full input[type=text]:focus:invalid,
div.wpforms-container-full input[type=time]:focus:invalid,
div.wpforms-container-full input[type=url]:focus:invalid,
div.wpforms-container-full input[type=week]:focus:invalid,
div.wpforms-container-full select:focus:invalid,
div.wpforms-container-full textarea:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=date]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=datetime]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=datetime-local]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=email]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=month]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=number]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=password]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=range]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=search]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=tel]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=text]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=time]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=url]:focus:invalid,
.wp-core-ui div.wpforms-container-full input[type=week]:focus:invalid,
.wp-core-ui div.wpforms-container-full select:focus:invalid,
.wp-core-ui div.wpforms-container-full textarea:focus:invalid {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
  color: var(--wpforms-field-text-color);
}

div.wpforms-container-full input[type=date][disabled],
div.wpforms-container-full input[type=datetime][disabled],
div.wpforms-container-full input[type=datetime-local][disabled],
div.wpforms-container-full input[type=email][disabled],
div.wpforms-container-full input[type=month][disabled],
div.wpforms-container-full input[type=number][disabled],
div.wpforms-container-full input[type=password][disabled],
div.wpforms-container-full input[type=range][disabled],
div.wpforms-container-full input[type=search][disabled],
div.wpforms-container-full input[type=tel][disabled],
div.wpforms-container-full input[type=text][disabled],
div.wpforms-container-full input[type=time][disabled],
div.wpforms-container-full input[type=url][disabled],
div.wpforms-container-full input[type=week][disabled],
div.wpforms-container-full select[disabled],
div.wpforms-container-full textarea[disabled],
.wp-core-ui div.wpforms-container-full input[type=date][disabled],
.wp-core-ui div.wpforms-container-full input[type=datetime][disabled],
.wp-core-ui div.wpforms-container-full input[type=datetime-local][disabled],
.wp-core-ui div.wpforms-container-full input[type=email][disabled],
.wp-core-ui div.wpforms-container-full input[type=month][disabled],
.wp-core-ui div.wpforms-container-full input[type=number][disabled],
.wp-core-ui div.wpforms-container-full input[type=password][disabled],
.wp-core-ui div.wpforms-container-full input[type=range][disabled],
.wp-core-ui div.wpforms-container-full input[type=search][disabled],
.wp-core-ui div.wpforms-container-full input[type=tel][disabled],
.wp-core-ui div.wpforms-container-full input[type=text][disabled],
.wp-core-ui div.wpforms-container-full input[type=time][disabled],
.wp-core-ui div.wpforms-container-full input[type=url][disabled],
.wp-core-ui div.wpforms-container-full input[type=week][disabled],
.wp-core-ui div.wpforms-container-full select[disabled],
.wp-core-ui div.wpforms-container-full textarea[disabled] {
  color: var(--wpforms-field-text-color);
  opacity: 0.5;
}

div.wpforms-container-full input[type=date]::-webkit-input-placeholder,
div.wpforms-container-full input[type=datetime]::-webkit-input-placeholder,
div.wpforms-container-full input[type=datetime-local]::-webkit-input-placeholder,
div.wpforms-container-full input[type=email]::-webkit-input-placeholder,
div.wpforms-container-full input[type=month]::-webkit-input-placeholder,
div.wpforms-container-full input[type=number]::-webkit-input-placeholder,
div.wpforms-container-full input[type=password]::-webkit-input-placeholder,
div.wpforms-container-full input[type=range]::-webkit-input-placeholder,
div.wpforms-container-full input[type=search]::-webkit-input-placeholder,
div.wpforms-container-full input[type=tel]::-webkit-input-placeholder,
div.wpforms-container-full input[type=text]::-webkit-input-placeholder,
div.wpforms-container-full input[type=time]::-webkit-input-placeholder,
div.wpforms-container-full input[type=url]::-webkit-input-placeholder,
div.wpforms-container-full input[type=week]::-webkit-input-placeholder,
div.wpforms-container-full select::-webkit-input-placeholder,
div.wpforms-container-full textarea::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=date]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=datetime]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=datetime-local]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=email]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=month]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=number]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=password]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=range]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=search]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=tel]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=text]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=time]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=url]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full input[type=week]::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full select::-webkit-input-placeholder,
.wp-core-ui div.wpforms-container-full textarea::-webkit-input-placeholder {
  color: var(--wpforms-field-text-color);
  opacity: 0.5;
  pointer-events: none;
}

div.wpforms-container-full input[type=date]::-moz-placeholder,
div.wpforms-container-full input[type=datetime]::-moz-placeholder,
div.wpforms-container-full input[type=datetime-local]::-moz-placeholder,
div.wpforms-container-full input[type=email]::-moz-placeholder,
div.wpforms-container-full input[type=month]::-moz-placeholder,
div.wpforms-container-full input[type=number]::-moz-placeholder,
div.wpforms-container-full input[type=password]::-moz-placeholder,
div.wpforms-container-full input[type=range]::-moz-placeholder,
div.wpforms-container-full input[type=search]::-moz-placeholder,
div.wpforms-container-full input[type=tel]::-moz-placeholder,
div.wpforms-container-full input[type=text]::-moz-placeholder,
div.wpforms-container-full input[type=time]::-moz-placeholder,
div.wpforms-container-full input[type=url]::-moz-placeholder,
div.wpforms-container-full input[type=week]::-moz-placeholder,
div.wpforms-container-full select::-moz-placeholder,
div.wpforms-container-full textarea::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=date]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=datetime]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=datetime-local]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=email]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=month]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=number]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=password]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=range]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=search]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=tel]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=text]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=time]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=url]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=week]::-moz-placeholder,
.wp-core-ui div.wpforms-container-full select::-moz-placeholder,
.wp-core-ui div.wpforms-container-full textarea::-moz-placeholder {
  color: var(--wpforms-field-text-color);
  opacity: 0.5;
  pointer-events: none;
}

div.wpforms-container-full input[type=date]:-moz-placeholder,
div.wpforms-container-full input[type=datetime]:-moz-placeholder,
div.wpforms-container-full input[type=datetime-local]:-moz-placeholder,
div.wpforms-container-full input[type=email]:-moz-placeholder,
div.wpforms-container-full input[type=month]:-moz-placeholder,
div.wpforms-container-full input[type=number]:-moz-placeholder,
div.wpforms-container-full input[type=password]:-moz-placeholder,
div.wpforms-container-full input[type=range]:-moz-placeholder,
div.wpforms-container-full input[type=search]:-moz-placeholder,
div.wpforms-container-full input[type=tel]:-moz-placeholder,
div.wpforms-container-full input[type=text]:-moz-placeholder,
div.wpforms-container-full input[type=time]:-moz-placeholder,
div.wpforms-container-full input[type=url]:-moz-placeholder,
div.wpforms-container-full input[type=week]:-moz-placeholder,
div.wpforms-container-full select:-moz-placeholder,
div.wpforms-container-full textarea:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=date]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=datetime]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=datetime-local]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=email]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=month]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=number]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=password]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=range]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=search]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=tel]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=text]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=time]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=url]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full input[type=week]:-moz-placeholder,
.wp-core-ui div.wpforms-container-full select:-moz-placeholder,
.wp-core-ui div.wpforms-container-full textarea:-moz-placeholder {
  color: var(--wpforms-field-text-color);
  opacity: 0.5;
  pointer-events: none;
}

div.wpforms-container-full textarea,
.wp-core-ui div.wpforms-container-full textarea {
  width: 100%;
}

div.wpforms-container-full input,
div.wpforms-container-full select,
.wp-core-ui div.wpforms-container-full input,
.wp-core-ui div.wpforms-container-full select {
  height: var(--wpforms-field-size-input-height);
}

div.wpforms-container-full select,
.wp-core-ui div.wpforms-container-full select {
  appearance: none;
  display: block;
  max-width: 100%;
  width: 100%;
  text-transform: none;
  text-shadow: none;
  white-space: nowrap;
  line-height: unset;
  padding-block: 0;
  padding-inline-end: 24px;
  padding-inline-start: 12px;
  min-height: var(--wpforms-field-size-input-height);
  vertical-align: middle;
  cursor: pointer;
}

div.wpforms-container-full select, div.wpforms-container-full select:disabled,
.wp-core-ui div.wpforms-container-full select,
.wp-core-ui div.wpforms-container-full select:disabled {
  background-image: linear-gradient(45deg, transparent 50%, var(--wpforms-field-border-color-spare) 50%), linear-gradient(135deg, var(--wpforms-field-border-color-spare) 50%, transparent 50%);
  background-position: calc( 100% - 17px) 50%, calc( 100% - 12px) 50%;
  background-size: 5px 5px, 5px 5px;
  background-repeat: no-repeat;
}

.rtl div.wpforms-container-full select, .rtl div.wpforms-container-full select:disabled, .rtl
.wp-core-ui div.wpforms-container-full select, .rtl
.wp-core-ui div.wpforms-container-full select:disabled {
  background-position: 12px 50%, 17px 50%;
}

div.wpforms-container-full select > option,
.wp-core-ui div.wpforms-container-full select > option {
  color: var(--wpforms-field-text-color);
}

div.wpforms-container-full select > option.placeholder, div.wpforms-container-full select > option[disabled],
.wp-core-ui div.wpforms-container-full select > option.placeholder,
.wp-core-ui div.wpforms-container-full select > option[disabled] {
  color: var(--wpforms-field-text-color);
  opacity: 0.5;
}

div.wpforms-container-full select:not([multiple]) > option,
.wp-core-ui div.wpforms-container-full select:not([multiple]) > option {
  background: var(--wpforms-field-menu-color);
}

div.wpforms-container-full select:not([multiple]) > option:not(.placeholder):checked,
.wp-core-ui div.wpforms-container-full select:not([multiple]) > option:not(.placeholder):checked {
  font-weight: bold;
}

div.wpforms-container-full select[multiple],
.wp-core-ui div.wpforms-container-full select[multiple] {
  height: auto;
  overflow-y: scroll;
  background-image: none;
}

div.wpforms-container-full select[multiple] > option:not(.placeholder):checked,
.wp-core-ui div.wpforms-container-full select[multiple] > option:not(.placeholder):checked {
  background: var(--wpforms-button-background-color);
  color: var(--wpforms-button-text-color-alt, var(--wpforms-button-text-color));
}

div.wpforms-container-full input[type=number]:read-only,
.wp-core-ui div.wpforms-container-full input[type=number]:read-only {
  appearance: textfield;
  -moz-appearance: textfield;
  -webkit-appearance: textfield;
}

div.wpforms-container-full input[type=number]:read-only::-webkit-inner-spin-button,
.wp-core-ui div.wpforms-container-full input[type=number]:read-only::-webkit-inner-spin-button {
  visibility: hidden;
}

div.wpforms-container-full input[type=submit],
div.wpforms-container-full button[type=submit],
div.wpforms-container-full .wpforms-page-button,
.wp-core-ui div.wpforms-container-full input[type=submit],
.wp-core-ui div.wpforms-container-full button[type=submit],
.wp-core-ui div.wpforms-container-full .wpforms-page-button {
  height: var(--wpforms-button-size-height);
  background-color: var(--wpforms-button-background-color-alt, var(--wpforms-button-background-color));
  border-radius: var(--wpforms-button-border-radius);
  border-style: var(--wpforms-button-border-style);
  border-color: var(--wpforms-button-border-color);
  border-width: var(--wpforms-button-border-size);
  box-shadow: none;
  color: var(--wpforms-button-text-color);
  padding: 0 var(--wpforms-button-size-padding-h);
  font-family: inherit;
  font-weight: 500;
  font-size: var(--wpforms-button-size-font-size);
  line-height: 100%;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  position: relative;
  text-decoration: none;
}

div.wpforms-container-full input[type=submit]:not(:hover):not(:active),
div.wpforms-container-full button[type=submit]:not(:hover):not(:active),
div.wpforms-container-full .wpforms-page-button:not(:hover):not(:active),
.wp-core-ui div.wpforms-container-full input[type=submit]:not(:hover):not(:active),
.wp-core-ui div.wpforms-container-full button[type=submit]:not(:hover):not(:active),
.wp-core-ui div.wpforms-container-full .wpforms-page-button:not(:hover):not(:active) {
  background-color: var(--wpforms-button-background-color-alt, var(--wpforms-button-background-color));
  color: var(--wpforms-button-text-color);
}

div.wpforms-container-full input[type=submit]:hover, div.wpforms-container-full input[type=submit]:active,
div.wpforms-container-full button[type=submit]:hover,
div.wpforms-container-full button[type=submit]:active,
div.wpforms-container-full .wpforms-page-button:hover,
div.wpforms-container-full .wpforms-page-button:active,
.wp-core-ui div.wpforms-container-full input[type=submit]:hover,
.wp-core-ui div.wpforms-container-full input[type=submit]:active,
.wp-core-ui div.wpforms-container-full button[type=submit]:hover,
.wp-core-ui div.wpforms-container-full button[type=submit]:active,
.wp-core-ui div.wpforms-container-full .wpforms-page-button:hover,
.wp-core-ui div.wpforms-container-full .wpforms-page-button:active {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), var(--wpforms-button-background-color-alt, var(--wpforms-button-background-color));
}

div.wpforms-container-full input[type=submit].wpforms-opacity-hover:hover, div.wpforms-container-full input[type=submit].wpforms-opacity-hover:active,
div.wpforms-container-full button[type=submit].wpforms-opacity-hover:hover,
div.wpforms-container-full button[type=submit].wpforms-opacity-hover:active,
div.wpforms-container-full .wpforms-page-button.wpforms-opacity-hover:hover,
div.wpforms-container-full .wpforms-page-button.wpforms-opacity-hover:active,
.wp-core-ui div.wpforms-container-full input[type=submit].wpforms-opacity-hover:hover,
.wp-core-ui div.wpforms-container-full input[type=submit].wpforms-opacity-hover:active,
.wp-core-ui div.wpforms-container-full button[type=submit].wpforms-opacity-hover:hover,
.wp-core-ui div.wpforms-container-full button[type=submit].wpforms-opacity-hover:active,
.wp-core-ui div.wpforms-container-full .wpforms-page-button.wpforms-opacity-hover:hover,
.wp-core-ui div.wpforms-container-full .wpforms-page-button.wpforms-opacity-hover:active {
  background: none;
  opacity: .75;
}

div.wpforms-container-full input[type=submit]:focus,
div.wpforms-container-full button[type=submit]:focus,
div.wpforms-container-full .wpforms-page-button:focus,
.wp-core-ui div.wpforms-container-full input[type=submit]:focus,
.wp-core-ui div.wpforms-container-full button[type=submit]:focus,
.wp-core-ui div.wpforms-container-full .wpforms-page-button:focus {
  outline: none;
}

div.wpforms-container-full input[type=submit]:focus:after,
div.wpforms-container-full button[type=submit]:focus:after,
div.wpforms-container-full .wpforms-page-button:focus:after,
.wp-core-ui div.wpforms-container-full input[type=submit]:focus:after,
.wp-core-ui div.wpforms-container-full button[type=submit]:focus:after,
.wp-core-ui div.wpforms-container-full .wpforms-page-button:focus:after {
  content: "";
  position: absolute;
  border: 2px solid var(--wpforms-button-background-color);
  border-radius: calc( var( --wpforms-button-border-radius ) + 2px);
  top: calc( -4px - var( --wpforms-button-border-size, 1px ));
  right: calc( -4px - var( --wpforms-button-border-size, 1px ));
  bottom: calc( -4px - var( --wpforms-button-border-size, 1px ));
  left: calc( -4px - var( --wpforms-button-border-size, 1px ));
}

div.wpforms-container-full input[type=submit]:disabled, div.wpforms-container-full input[type=submit]:disabled:hover, div.wpforms-container-full input[type=submit].wpforms-disabled,
div.wpforms-container-full button[type=submit]:disabled,
div.wpforms-container-full button[type=submit]:disabled:hover,
div.wpforms-container-full button[type=submit].wpforms-disabled,
div.wpforms-container-full .wpforms-page-button:disabled,
div.wpforms-container-full .wpforms-page-button:disabled:hover,
div.wpforms-container-full .wpforms-page-button.wpforms-disabled,
.wp-core-ui div.wpforms-container-full input[type=submit]:disabled,
.wp-core-ui div.wpforms-container-full input[type=submit]:disabled:hover,
.wp-core-ui div.wpforms-container-full input[type=submit].wpforms-disabled,
.wp-core-ui div.wpforms-container-full button[type=submit]:disabled,
.wp-core-ui div.wpforms-container-full button[type=submit]:disabled:hover,
.wp-core-ui div.wpforms-container-full button[type=submit].wpforms-disabled,
.wp-core-ui div.wpforms-container-full .wpforms-page-button:disabled,
.wp-core-ui div.wpforms-container-full .wpforms-page-button:disabled:hover,
.wp-core-ui div.wpforms-container-full .wpforms-page-button.wpforms-disabled {
  background-color: var(--wpforms-button-background-color-alt, var(--wpforms-button-background-color));
  cursor: default;
}

div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=date]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=datetime]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=datetime-local]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=email]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=month]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=number]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=password]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=range]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=search]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=tel]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=text]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=time]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=url]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=week]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) select:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) textarea:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=checkbox]:disabled:before, div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=checkbox]:disabled:after,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=radio]:disabled:before,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=radio]:disabled:after {
  cursor: not-allowed;
  opacity: 0.5;
}

div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=submit]:disabled, div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=submit]:disabled:hover, div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=submit].wpforms-disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) button[type=submit]:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) button[type=submit]:disabled:hover,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) button[type=submit].wpforms-disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) .wpforms-page-button:disabled,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) .wpforms-page-button:disabled:hover,
div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) .wpforms-page-button.wpforms-disabled {
  opacity: 0.5;
}

div.wpforms-container-full .wpforms-form .wpforms-field .wpforms-field-row {
  margin-bottom: var(--wpforms-field-size-input-spacing);
}

div.wpforms-container-full .wpforms-form .wpforms-field .wpforms-field-row:last-child, div.wpforms-container-full .wpforms-form .wpforms-field .wpforms-field-row:only-child {
  margin-bottom: 0 !important;
}

div.wpforms-container-full input[type=checkbox],
div.wpforms-container-full input[type=radio] {
  position: relative;
  display: inline-block;
  background: none;
  height: calc( var( --wpforms-field-size-checkbox-size ) - 2px + calc( var( --wpforms-field-border-size, 1px ) * 2 ));
  width: calc( var( --wpforms-field-size-checkbox-size ) - 2px + calc( var( --wpforms-field-border-size, 1px ) * 2 ));
  margin: 0 0 0 2px;
  border: none;
  box-shadow: none;
  vertical-align: middle;
  opacity: 1;
  appearance: none;
}

div.wpforms-container-full input[type=checkbox]:before, div.wpforms-container-full input[type=checkbox]:after,
div.wpforms-container-full input[type=radio]:before,
div.wpforms-container-full input[type=radio]:after {
  content: '';
  position: absolute;
  left: -2px;
  top: -2px;
  width: var(--wpforms-field-size-checkbox-size);
  height: var(--wpforms-field-size-checkbox-size);
  box-sizing: content-box;
  cursor: pointer;
}

div.wpforms-container-full input[type=checkbox]:before,
div.wpforms-container-full input[type=radio]:before {
  border-width: var(--wpforms-field-border-size);
  border-style: var(--wpforms-field-border-style);
  border-color: var(--wpforms-field-border-color);
  background-color: var(--wpforms-field-background-color);
  background-clip: padding-box;
  background-image: none;
  border-radius: 3px;
}

div.wpforms-container-full input[type=checkbox] + label,
div.wpforms-container-full input[type=radio] + label {
  display: inline;
  position: relative;
  padding-inline-start: 12px;
  font-size: var(--wpforms-label-size-font-size);
  cursor: pointer;
  vertical-align: middle;
}

div.wpforms-container-full input[type=checkbox]:checked:before,
div.wpforms-container-full input[type=radio]:checked:before {
  margin: 0;
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
}

div.wpforms-container-full input[type=checkbox]:focus,
div.wpforms-container-full input[type=radio]:focus {
  outline: none;
}

div.wpforms-container-full input[type=checkbox]:focus:before,
div.wpforms-container-full input[type=radio]:focus:before {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
}

div.wpforms-container-full input[type=checkbox]:checked:after {
  border-top: none;
  border-right: none;
  height: calc( var( --wpforms-field-size-checkbox-size ) * 0.6);
  border-left: 4px solid var(--wpforms-button-background-color);
  border-bottom: 4px solid var(--wpforms-button-background-color);
  background-color: transparent;
  transform: translate(0, 1px) scale(0.5) rotate(-45deg);
  left: calc( -4px + var( --wpforms-field-border-size, 1px ));
  top: calc( -3px + var( --wpforms-field-border-size, 1px ));
}

div.wpforms-container-full input[type=radio] {
  border-radius: 50%;
}

div.wpforms-container-full input[type=radio]:before {
  border-radius: 50%;
}

div.wpforms-container-full input[type=radio]:checked:after {
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: var(--wpforms-button-background-color);
  transform: scale(0.5);
  -moz-transform: scale(0.47);
  left: calc( -3px + var( --wpforms-field-border-size, 1px ));
  top: calc( -3px + var( --wpforms-field-border-size, 1px ));
}

div.wpforms-container-full .wpforms-field.wpforms-field-checkbox ul, div.wpforms-container-full .wpforms-field.wpforms-field-radio ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple ul, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox ul {
  display: grid;
  grid-template-columns: repeat(1, auto);
  padding: 0;
  gap: var(--wpforms-field-size-input-spacing);
}

div.wpforms-container-full .wpforms-field.wpforms-field-checkbox ul li, div.wpforms-container-full .wpforms-field.wpforms-field-radio ul li, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox ul li, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple ul li, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox ul li {
  height: unset;
  line-height: var(--wpforms-field-size-checkbox-size);
  display: flex;
  align-items: flex-start;
  margin: 0;
}

div.wpforms-container-full .wpforms-field.wpforms-field-checkbox ul li input, div.wpforms-container-full .wpforms-field.wpforms-field-radio ul li input, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox ul li input, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple ul li input, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox ul li input {
  min-width: var(--wpforms-field-size-checkbox-size);
  margin-top: calc((var(--wpforms-label-size-font-size) * 1.3 - var(--wpforms-field-size-checkbox-size)) / 1.5);
}

div.wpforms-container-full .wpforms-field.wpforms-field-checkbox ul li input + label, div.wpforms-container-full .wpforms-field.wpforms-field-radio ul li input + label, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox ul li input + label, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple ul li input + label, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox ul li input + label {
  padding-inline-start: 12px;
  margin: 0;
  line-height: 1.3;
}

div.wpforms-container-full .wpforms-field.wpforms-field-checkbox ul li input + label .wpforms-currency-symbol, div.wpforms-container-full .wpforms-field.wpforms-field-radio ul li input + label .wpforms-currency-symbol, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox ul li input + label .wpforms-currency-symbol, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple ul li input + label .wpforms-currency-symbol, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox ul li input + label .wpforms-currency-symbol {
  white-space: nowrap;
}

div.wpforms-container-full .wpforms-field.wpforms-field-checkbox .wpforms-field-description ul, div.wpforms-container-full .wpforms-field.wpforms-field-checkbox .wpforms-field-description ol, div.wpforms-container-full .wpforms-field.wpforms-field-radio .wpforms-field-description ul, div.wpforms-container-full .wpforms-field.wpforms-field-radio .wpforms-field-description ol, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox .wpforms-field-description ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox .wpforms-field-description ol, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple .wpforms-field-description ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple .wpforms-field-description ol, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox .wpforms-field-description ul, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox .wpforms-field-description ol {
  display: block;
  padding-left: 15px;
}

div.wpforms-container-full .wpforms-field.wpforms-field-checkbox .wpforms-field-description ul li, div.wpforms-container-full .wpforms-field.wpforms-field-checkbox .wpforms-field-description ol li, div.wpforms-container-full .wpforms-field.wpforms-field-radio .wpforms-field-description ul li, div.wpforms-container-full .wpforms-field.wpforms-field-radio .wpforms-field-description ol li, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox .wpforms-field-description ul li, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox .wpforms-field-description ol li, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple .wpforms-field-description ul li, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple .wpforms-field-description ol li, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox .wpforms-field-description ul li, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox .wpforms-field-description ol li {
  display: list-item;
  margin-bottom: 10px;
}

div.wpforms-container-full .wpforms-field.wpforms-field-checkbox.wpforms-checkbox-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-checkbox.wpforms-multiplechoice-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-checkbox.wpforms-list-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-radio.wpforms-checkbox-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-radio.wpforms-multiplechoice-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-radio.wpforms-list-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox.wpforms-checkbox-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox.wpforms-multiplechoice-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox.wpforms-list-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple.wpforms-checkbox-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple.wpforms-multiplechoice-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple.wpforms-list-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox.wpforms-checkbox-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox.wpforms-multiplechoice-2-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox.wpforms-list-2-columns ul {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

div.wpforms-container-full .wpforms-field.wpforms-field-checkbox.wpforms-checkbox-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-checkbox.wpforms-multiplechoice-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-checkbox.wpforms-list-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-radio.wpforms-checkbox-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-radio.wpforms-multiplechoice-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-radio.wpforms-list-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox.wpforms-checkbox-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox.wpforms-multiplechoice-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox.wpforms-list-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple.wpforms-checkbox-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple.wpforms-multiplechoice-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple.wpforms-list-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox.wpforms-checkbox-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox.wpforms-multiplechoice-3-columns ul, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox.wpforms-list-3-columns ul {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

div.wpforms-container-full .wpforms-field.wpforms-field-checkbox.wpforms-list-inline ul, div.wpforms-container-full .wpforms-field.wpforms-field-radio.wpforms-list-inline ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-checkbox.wpforms-list-inline ul, div.wpforms-container-full .wpforms-field.wpforms-field-payment-multiple.wpforms-list-inline ul, div.wpforms-container-full .wpforms-field.wpforms-field-gdpr-checkbox.wpforms-list-inline ul {
  display: inline-flex;
  flex-wrap: wrap;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern .wpforms-image-choices-item label,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic .wpforms-image-choices-item label,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-none .wpforms-image-choices-item label {
  display: block;
  overflow: hidden;
  position: relative;
  border-width: var(--wpforms-field-border-size);
  border-color: transparent;
  border-style: solid;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern .wpforms-image-choices-item label .wpforms-image-choices-label,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic .wpforms-image-choices-item label .wpforms-image-choices-label,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-none .wpforms-image-choices-item label .wpforms-image-choices-label {
  font-size: var(--wpforms-field-size-font-size);
  color: var(--wpforms-label-color);
  margin-top: var(--wpforms-field-size-input-spacing);
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern .wpforms-image-choices-item img,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic .wpforms-image-choices-item img {
  border-radius: calc( var( --wpforms-field-border-radius ) / 2);
  overflow: hidden;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern .wpforms-image-choices-item label,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic .wpforms-image-choices-item label {
  border-radius: var(--wpforms-field-border-radius);
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern .wpforms-image-choices-item label:hover,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic .wpforms-image-choices-item label:hover {
  border-color: var(--wpforms-button-background-color);
}

div.wpforms-container-full .wpforms-form .wpforms-image-choices-modern .wpforms-image-choices-item label {
  border-width: var(--wpforms-field-border-size);
}

div.wpforms-container-full .wpforms-form .wpforms-image-choices-modern .wpforms-image-choices-item label:hover {
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color);
}

div.wpforms-container-full .wpforms-form .wpforms-image-choices-modern .wpforms-image-choices-item label:focus, div.wpforms-container-full .wpforms-form .wpforms-image-choices-modern .wpforms-image-choices-item label:focus-within {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
}

div.wpforms-container-full .wpforms-form .wpforms-image-choices-modern .wpforms-image-choices-item label.wpforms-field-label-inline-empty .wpforms-image-choices-label {
  margin-top: 0;
}

div.wpforms-container-full .wpforms-form .wpforms-image-choices-modern .wpforms-image-choices-item.wpforms-selected .wpforms-image-choices-image:after, div.wpforms-container-full .wpforms-form .wpforms-image-choices-modern .wpforms-image-choices-item:has(input:checked) .wpforms-image-choices-image:after {
  background-color: var(--wpforms-button-background-color);
}

div.wpforms-container-full .wpforms-form .wpforms-image-choices-modern .wpforms-image-choices-item.wpforms-selected label:hover, div.wpforms-container-full .wpforms-form .wpforms-image-choices-modern .wpforms-image-choices-item:has(input:checked) label:hover {
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1), 0 0 0 1px var(--wpforms-button-background-color);
}

div.wpforms-container-full .wpforms-form .wpforms-image-choices-classic .wpforms-image-choices-item .wpforms-image-choices-image img {
  border-radius: calc( var( --wpforms-field-border-radius ) / 2);
}

div.wpforms-container-full .wpforms-form .wpforms-image-choices-classic .wpforms-image-choices-item label:focus, div.wpforms-container-full .wpforms-form .wpforms-image-choices-classic .wpforms-image-choices-item label:focus-within {
  border-color: var(--wpforms-button-background-color);
}

div.wpforms-container-full .wpforms-form .wpforms-image-choices-classic .wpforms-image-choices-item.wpforms-selected label, div.wpforms-container-full .wpforms-form .wpforms-image-choices-classic .wpforms-image-choices-item:has(input:checked) label {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-none .wpforms-image-choices-item label {
  padding: 2px;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-none .wpforms-image-choices-item label .wpforms-image-choices-image {
  display: inline-block;
  margin-bottom: var(--wpforms-field-size-input-spacing);
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-none .wpforms-image-choices-item label input {
  margin-top: 0;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-none .wpforms-image-choices-item label .wpforms-image-choices-label {
  margin-top: 0;
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices {
  margin: 0 !important;
  gap: 22px;
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-small svg {
  height: calc( 32px * var( --wpforms-field-size-icon-size ));
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-medium svg {
  height: calc( 48px * var( --wpforms-field-size-icon-size ));
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-large svg {
  height: calc( 64px * var( --wpforms-field-size-icon-size ));
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices .wpforms-icon-choices-item {
  width: auto;
  margin: 0 !important;
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices .wpforms-icon-choices-item label {
  width: 100%;
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices .wpforms-icon-choices-item svg {
  fill: var(--wpforms-icon-choices-color);
  max-width: 100%;
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices .wpforms-icon-choices-item .wpforms-icon-choices-label {
  font-size: var(--wpforms-label-size-font-size);
  line-height: var(--wpforms-label-size-line-height);
  margin-top: var(--wpforms-field-size-input-spacing);
  color: var(--wpforms-label-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon {
  background-color: transparent;
  border-radius: var(--wpforms-field-border-radius);
  border-width: clamp(1px, var(--wpforms-field-border-size), 6px);
  border-style: solid;
  border-color: var(--wpforms-field-border-color);
  margin-bottom: calc( var( --wpforms-field-size-input-spacing ) - 5px);
  overflow: hidden;
  box-shadow: none;
  padding: var(--wpforms-field-size-input-spacing) calc( var( --wpforms-field-size-input-spacing ) + 5px) calc( 2 * var( --wpforms-field-size-input-spacing ) + var( --wpforms-field-size-checkbox-size )) calc( var( --wpforms-field-size-input-spacing ) + 5px);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon:before, div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon:after {
  width: var(--wpforms-field-size-checkbox-size);
  height: var(--wpforms-field-size-checkbox-size);
  margin-top: var(--wpforms-field-size-input-spacing);
  bottom: var(--wpforms-field-size-input-spacing);
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--wpforms-field-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-field-border-color);
  opacity: 1 !important;
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon:hover {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
  border-color: var(--wpforms-icon-choices-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon-bg {
  background-color: transparent;
  border-radius: calc( var( --wpforms-field-border-radius ) / 1.5);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon, div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
  border-color: var(--wpforms-icon-choices-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon-bg, div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon-bg {
  background-color: var(--wpforms-icon-choices-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-default label:focus-within .wpforms-icon-choices-icon {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
  border-color: var(--wpforms-icon-choices-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-modern li label {
  box-shadow: none;
  border-radius: var(--wpforms-field-border-radius);
  border-width: clamp(1px, var(--wpforms-field-border-size), 6px);
  border-style: solid;
  border-color: var(--wpforms-field-border-color);
  padding: var(--wpforms-field-size-input-spacing);
  background-color: transparent !important;
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-modern li label:hover {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
  border-color: var(--wpforms-icon-choices-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-modern li label.wpforms-field-label-inline-empty .wpforms-icon-choices-icon {
  margin-bottom: 0;
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-modern li:focus-within label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
  border-color: var(--wpforms-icon-choices-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-modern li.wpforms-selected label, div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-modern li:has(input:checked) label {
  box-shadow: 0 0 0 clamp(1px, var(--wpforms-field-border-size), 6px) var(--wpforms-icon-choices-color), 0 1px 10px rgba(0, 0, 0, 0.15);
  border-color: var(--wpforms-icon-choices-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-classic li label {
  border-radius: var(--wpforms-field-border-radius);
  background-color: transparent !important;
  padding: 20px 20px 15px 20px;
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-classic li label:hover {
  box-shadow: 0 0 0 clamp(2px, var(--wpforms-field-border-size), 6px) var(--wpforms-icon-choices-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-classic li label.wpforms-field-label-inline-empty .wpforms-icon-choices-icon {
  margin-bottom: 5px;
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-classic li:focus-within label {
  box-shadow: 0 0 0 clamp(2px, var(--wpforms-field-border-size), 6px) var(--wpforms-icon-choices-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-classic li.wpforms-selected label, div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-classic li:has(input:checked) label {
  box-shadow: 0 0 0 clamp(2px, var(--wpforms-field-border-size), 6px) var(--wpforms-icon-choices-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-none .wpforms-icon-choices-icon {
  margin-bottom: var(--wpforms-field-size-input-spacing);
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-none input {
  margin-top: 0;
  vertical-align: middle;
}

div.wpforms-container.wpforms-container-full .wpforms-form ul.wpforms-icon-choices.wpforms-icon-choices-none .wpforms-icon-choices-label {
  padding-left: 8px;
  vertical-align: middle;
  margin-top: 2px;
}

div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-radio .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:before, div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-radio .wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before,
div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-payment-multiple .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:before,
div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-payment-multiple .wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-radio .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:after, div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-radio .wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after,
div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-payment-multiple .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:after,
div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-payment-multiple .wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after {
  transform: translateX(-50%) scale(0.5);
  background-color: var(--wpforms-icon-choices-color);
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
  -moz-transform: translateX(-50%) scale(0.49);
}

div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-checkbox .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:before, div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-checkbox .wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before,
div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-payment-checkbox .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:before,
div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-payment-checkbox .wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-checkbox .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:after, div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-checkbox .wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after,
div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-payment-checkbox .wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon:after,
div.wpforms-container.wpforms-container-full .wpforms-form .wpforms-field-payment-checkbox .wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after {
  border-top: none;
  border-right: none;
  height: calc( var( --wpforms-field-size-checkbox-size ) * 0.6);
  border-left: 4px solid var(--wpforms-icon-choices-color);
  border-bottom: 4px solid var(--wpforms-icon-choices-color);
  background-color: transparent;
  transform: scale(0.6) translate(-10%, -50%) rotate(-45deg);
  box-shadow: none;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price {
  width: calc(60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price.wpforms-field-small {
  text-wrap: balance;
  width: calc(25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price.wpforms-field-large {
  width: calc(100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content select.wpforms-payment-quantity {
  margin-left: 0;
  height: 30px;
  min-height: 30px;
  font-size: 14px;
  padding-top: 0;
  padding-bottom: 0;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price {
  display: inline-block;
  max-width: calc( 60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-small {
  max-width: calc( 25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-large {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-quantity {
  display: inline-block;
  margin-inline-start: 15px;
  width: 70px;
  min-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern {
  display: flex;
  flex-wrap: wrap;
  column-gap: 15px;
  align-items: flex-start;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-label {
  min-width: 100%;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .choices {
  margin-bottom: 5px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row {
  flex-grow: 1;
  max-width: calc( 60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row.wpforms-field-small {
  max-width: calc( 25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row.wpforms-field-large {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-payment-quantity {
  flex-basis: 70px;
  max-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-payment-quantity .choices__list--dropdown {
  min-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-description {
  flex-basis: 100%;
  margin-top: 0;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-error {
  flex-basis: 100%;
}

@media only screen and (max-width: 600px) {
  .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price {
    width: calc( 100% - 85px) !important;
    max-width: 100% !important;
  }
  .wpforms-form .wpforms-payment-quantities-enabled .wpforms-single-item-price-content .wpforms-single-item-price {
    width: calc( 100% - 70px) !important;
  }
  .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row {
    width: calc( 100% - 85px) !important;
    max-width: 100% !important;
  }
}

#wpforms-form-page-page .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-medium, #wpforms-form-page-page .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-small {
  max-width: calc( 100% - 85px);
}

div.wpforms-container-full .wpforms-form .wpforms-field-divider {
  border-top-width: var(--wpforms-field-border-size);
  border-top-style: solid;
  border-top-color: var(--wpforms-field-border-color);
  margin-top: 30px;
  padding: 0px;
}

div.wpforms-container-full .wpforms-form .wpforms-field-divider:first-child {
  margin-top: 0;
  border-top: none;
}

div.wpforms-container-full .wpforms-form .wpforms-field-divider h3 {
  color: var(--wpforms-label-color);
  padding-top: var(--wpforms-field-size-input-spacing);
  font-size: calc( 1.5 * var( --wpforms-label-size-font-size ));
  font-weight: bold;
}

div.wpforms-container-full .wpforms-form .wpforms-field-divider:has(> .wpforms-field-description) h3 {
  margin-bottom: 5px;
}

div.wpforms-container-full .wpforms-form .wpforms-field-divider + .wpforms-field-divider {
  margin-top: 45px;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-classic select {
  padding-inline-start: 14px;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-classic select[multiple] {
  padding: 0;
  overflow: auto;
}

@supports (font: -apple-system-body) and (-webkit-appearance: none) and (-webkit-hyphens: none) {
  div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-classic select[multiple] {
    padding: 10px 12px;
    line-height: 1;
  }
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-classic select[multiple] > option {
  padding: 10px 14px;
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1);
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-classic select[multiple] > option.placeholder, div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-classic select[multiple] > option[disabled] {
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.2);
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices {
  font-size: var(--wpforms-field-size-font-size);
  line-height: 19px;
  color: var(--wpforms-field-text-color);
  margin-bottom: 5px;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__input--cloned {
  background-color: transparent;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__inner {
  background-color: var(--wpforms-field-background-color);
  background-clip: padding-box;
  min-height: var(--wpforms-field-size-input-height);
  line-height: var(--wpforms-field-size-input-height);
  padding: 0 24px 0 7px;
  border-width: var(--wpforms-field-border-size);
  border-style: var(--wpforms-field-border-style);
  border-color: var(--wpforms-field-border-color);
  border-radius: var(--wpforms-field-border-radius);
  cursor: pointer;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__inner .choices__list--single {
  padding: 0 16px 0 4px;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__inner .choices__list--single .choices__item--selectable {
  background-color: transparent;
  font-size: var(--wpforms-field-size-font-size);
  color: var(--wpforms-field-text-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__inner .choices__list--single .choices__item--selectable.choices__placeholder {
  opacity: 0.5;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__inner .choices__list--multiple {
  display: inline !important;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__inner .choices__list--multiple:empty {
  display: none;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__inner .choices__list--multiple:empty + .choices__input {
  margin-left: 4px !important;
  min-width: 100% !important;
  text-overflow: ellipsis;
  padding-right: 20px !important;
  white-space: nowrap;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__inner .choices__list--multiple .choices__item {
  position: relative;
  top: -1.5px;
  background-color: var(--wpforms-button-background-color);
  border: 1px solid var(--wpforms-button-background-color);
  border-radius: calc( max( var( --wpforms-field-border-radius ), 6px ) / 2);
  color: var(--wpforms-button-text-color-alt, var(--wpforms-button-text-color));
  margin: 0 6px 6px 0;
  line-height: 1;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__list--dropdown {
  background: var(--wpforms-field-menu-color) !important;
  color: var(--wpforms-field-text-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices .choices__list--dropdown .choices__item--selectable.is-highlighted {
  background-color: var(--wpforms-button-background-color);
  color: var(--wpforms-button-text-color-alt, var(--wpforms-button-text-color));
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices input.choices__input {
  display: inline-block;
  padding: 0 !important;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices[data-type*="select-one"] input.choices__input {
  background: none !important;
  margin: 5px !important;
  padding: 5px !important;
  width: calc( 100% - 10px) !important;
  border: 0 !important;
  box-shadow: none !important;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices ::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.5;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices ::-moz-placeholder {
  color: inherit;
  opacity: 0.5;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices :-ms-input-placeholder {
  color: inherit;
  opacity: 0.5;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices[data-type*="select-one"] .choices__button {
  opacity: 0.7;
  background-image: linear-gradient(45deg, transparent 44%, var(--wpforms-field-border-color-spare) 44%, var(--wpforms-field-border-color-spare) 56%, transparent 56%), linear-gradient(135deg, transparent 44%, var(--wpforms-field-border-color-spare) 44%, var(--wpforms-field-border-color-spare) 56%, transparent 56%);
  background-position: 50% 50%, 50% 50%;
  background-size: 8px 8px, 8px 8px;
  background-repeat: no-repeat;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices[data-type*="select-one"] .choices__button:hover {
  opacity: 1;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices[data-type*="select-one"] .choices__button:focus {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices[data-type*="select-one"]:after, div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices[data-type*="select-multiple"]:after {
  width: 0;
  height: 0;
  right: 12px;
  background: none;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid var(--wpforms-field-border-color-spare);
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices[data-type*="select-one"].is-open:after, div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices[data-type*="select-multiple"].is-open:after {
  border-top: 5px solid transparent;
  border-bottom: 5px solid var(--wpforms-field-border-color-spare);
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices.is-focused .choices__inner,
div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices.is-open .choices__inner,
div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices.is-open .choices__list--dropdown {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices.is-open:before {
  content: '';
  position: absolute;
  height: 3px;
  background: var(--wpforms-field-background-color);
  width: calc( 100% - 2px);
  left: 1px;
  right: 1px;
  z-index: 100000000000;
  opacity: 1;
  border-radius: 0;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices.is-open:not(.is-flipped):before {
  top: unset;
  bottom: 1px;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices.is-open:not(.is-flipped) .choices__inner {
  border-radius: var(--wpforms-field-border-radius) var(--wpforms-field-border-radius) 0 0;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices.is-open:not(.is-flipped) .choices__list--dropdown {
  border-radius: 0 0 var(--wpforms-field-border-radius) var(--wpforms-field-border-radius);
  margin-top: 0;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices.is-open.is-flipped:before {
  top: 1px;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices.is-open.is-flipped .choices__inner {
  border-radius: 0 0 var(--wpforms-field-border-radius) var(--wpforms-field-border-radius);
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern .choices.is-open.is-flipped .choices__list--dropdown {
  border-radius: var(--wpforms-field-border-radius) var(--wpforms-field-border-radius) 0 0;
  margin-bottom: 0;
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern.wpforms-has-error .choices .choices__inner {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern.wpforms-has-error .choices:hover .choices__inner {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  box-shadow: 0 0 2px 0 var(--wpforms-label-error-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern.wpforms-has-error .choices.is-focused .choices__inner,
div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern.wpforms-has-error .choices.is-open .choices__inner,
div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-field-select-style-modern.wpforms-has-error .choices.is-open .choices__list--dropdown {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  box-shadow: 0 0 0 1px var(--wpforms-label-error-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range] {
  appearance: none;
  height: calc( var( --wpforms-field-size-input-height ) / 4);
  padding: 0;
  margin-top: calc( var( --wpforms-field-size-input-spacing ) + var( --wpforms-field-size-input-height ) / 4);
  margin-bottom: calc( var( --wpforms-field-size-input-height ) / 4);
  border-radius: var(--wpforms-field-border-radius);
  border-width: var(--wpforms-field-border-size);
  border-style: var(--wpforms-field-border-style);
  border-color: var(--wpforms-field-border-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range]:first-child {
  margin-top: calc( var( --wpforms-field-size-input-height ) * 0.25);
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range]:focus {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range]:focus:invalid {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
  color: var(--wpforms-field-text-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range]::-webkit-slider-runnable-track {
  height: calc( var( --wpforms-field-size-input-height ) / 4);
  box-shadow: none;
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range]::-webkit-slider-thumb {
  appearance: none;
  width: calc( var( --wpforms-field-size-input-height ) * 0.6);
  height: calc( var( --wpforms-field-size-input-height ) * 0.6);
  margin-top: calc( -1 * var( --wpforms-field-size-input-height ) * 0.18);
  background-color: var(--wpforms-button-background-color);
  background-clip: padding-box;
  cursor: pointer;
  border-radius: 100%;
  border-width: var(--wpforms-button-border-size);
  border-style: var(--wpforms-button-border-style);
  border-color: var(--wpforms-button-border-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range]::-moz-range-thumb {
  appearance: none;
  width: calc( var( --wpforms-field-size-input-height ) * 0.6);
  height: calc( var( --wpforms-field-size-input-height ) * 0.6);
  margin-top: calc( -1 * var( --wpforms-field-size-input-height ) * 0.18);
  background-color: var(--wpforms-button-background-color);
  background-clip: padding-box;
  cursor: pointer;
  border-radius: 100%;
  border-width: var(--wpforms-button-border-size);
  border-style: var(--wpforms-button-border-style);
  border-color: var(--wpforms-button-border-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range]::-ms-thumb {
  appearance: none;
  width: calc( var( --wpforms-field-size-input-height ) * 0.6);
  height: calc( var( --wpforms-field-size-input-height ) * 0.6);
  margin-top: calc( -1 * var( --wpforms-field-size-input-height ) * 0.18);
  background-color: var(--wpforms-button-background-color);
  background-clip: padding-box;
  cursor: pointer;
  border-radius: 100%;
  border-width: var(--wpforms-button-border-size);
  border-style: var(--wpforms-button-border-style);
  border-color: var(--wpforms-button-border-color);
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider .wpforms-field-number-slider-hint {
  font-size: var(--wpforms-label-size-sublabel-font-size);
  line-height: var(--wpforms-label-size-sublabel-line-height);
  color: var(--wpforms-label-sublabel-color);
  padding: var(--wpforms-field-size-sublabel-spacing) 0 0 0;
  margin: 0;
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider .wpforms-field-number-slider-hint b, div.wpforms-container-full .wpforms-form .wpforms-field-number-slider .wpforms-field-number-slider-hint strong {
  color: var(--wpforms-label-sublabel-color);
}

div.wpforms-container-full .wpforms-form textarea {
  line-height: 1.3;
  min-height: var(--wpforms-field-size-input-height);
  padding: var(--wpforms-field-size-padding-h);
  resize: vertical;
}

div.wpforms-container-full .wpforms-form textarea.wpforms-field-small {
  height: calc( var( --wpforms-field-size-input-height ) * 2.26);
}

div.wpforms-container-full .wpforms-form textarea.wpforms-field-medium {
  height: calc( var( --wpforms-field-size-input-height ) * 2.8);
}

div.wpforms-container-full .wpforms-form textarea.wpforms-field-large {
  height: calc( var( --wpforms-field-size-input-height ) * 5.1);
}

div.wpforms-container-full .wpforms-form textarea:focus {
  color: var(--wpforms-field-text-color);
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview {
  border-radius: 4px;
  border: 1px solid #e2e2e2;
  border-collapse: separate;
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr td {
  border-top: 1px solid #e2e2e2;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  font-weight: 400;
  border: none;
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  padding: 9px 0;
  line-height: 20px;
  background: none;
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-wrap: balance;
  padding-left: 10px;
}

.rtl .wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label, .rtl
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  padding-right: 10px;
  padding-left: 0;
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-right: 10px;
}

.rtl .wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price, .rtl
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  padding-left: 10px;
  padding-right: 0;
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-left: 10px;
}

.rtl .wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td, .rtl
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  padding-right: 10px;
  padding-left: 0;
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td {
  font-weight: 700;
}

.wpforms-container .wpforms-form .wpforms-order-summary-container,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container,
.wpforms-confirmation-container-full .wpforms-order-summary-container {
  font-size: var(--wpforms-field-size-font-size);
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview {
  border-style: var(--wpforms-field-border-style);
  border-width: var(--wpforms-field-border-size);
  border-radius: var(--wpforms-field-border-radius);
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.wpforms-confirmation-container-full .wpforms-order-summary-container table.wpforms-order-summary-preview tr td {
  border-top-style: var(--wpforms-field-border-style);
  border-top-width: var(--wpforms-field-border-size);
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview {
  border-color: var(--wpforms-field-border-color);
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr th,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  color: var(--wpforms-label-color);
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr td,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr td {
  color: var(--wpforms-label-sublabel-color);
  border-top-color: var(--wpforms-field-border-color);
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: var(--wpforms-label-error-color);
}

.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.wpforms-container .wpforms-form .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-total td,
.wpforms-confirmation-container-order-summary .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-preview-subtotal td {
  color: var(--wpforms-label-color);
}

.wpforms-confirmation-container-order-summary .wpforms-order-summary-container,
.wpforms-confirmation-container-full .wpforms-order-summary-container {
  max-width: 100%;
  margin-bottom: 24px;
}

@container wpforms-field-row-responsive (max-width: 200px) {
  div.wpforms-container-full .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block:not(:last-child) {
    margin-bottom: var(--wpforms-field-size-input-spacing);
  }
}

@container wpforms-field-row-responsive-300px (max-width: 300px) {
  div.wpforms-container-full .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block:not(:last-child) {
    margin-bottom: var(--wpforms-field-size-input-spacing);
  }
}

@media only screen and (max-width: 768px) {
  div.wpforms-container-full:not(:empty) {
    padding: calc( min( var( --wpforms-container-padding ), 40px ));
  }
}

@media only screen and (max-width: 600px) {
  div.wpforms-container-full:not(:empty) {
    padding: calc( min( var( --wpforms-container-padding ), 20px ));
  }
  div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range] {
    margin: 20px 0 20px;
  }
  div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range]::-webkit-slider-thumb {
    width: 30px;
    height: 30px;
  }
  div.wpforms-container-full .wpforms-form .wpforms-page-indicator.connector .wpforms-page-indicator-page-title {
    display: none;
  }
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
