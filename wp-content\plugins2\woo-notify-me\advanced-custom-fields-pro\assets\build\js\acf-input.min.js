!function(){var t={7787:function(){!function(t,e){acf.newCompatibility=function(t,e){return(e=e||{}).__proto__=t.__proto__,t.__proto__=e,t.compatibility=e,e},acf.getCompatibility=function(t){return t.compatibility||null};var i=acf.newCompatibility(acf,{l10n:{},o:{},fields:{},update:acf.set,add_action:acf.addAction,remove_action:acf.removeAction,do_action:acf.doAction,add_filter:acf.addFilter,remove_filter:acf.removeFilter,apply_filters:acf.applyFilters,parse_args:acf.parseArgs,disable_el:acf.disable,disable_form:acf.disable,enable_el:acf.enable,enable_form:acf.enable,update_user_setting:acf.updateUserSetting,prepare_for_ajax:acf.prepareForAjax,is_ajax_success:acf.isAjaxSuccess,remove_el:acf.remove,remove_tr:acf.remove,str_replace:acf.strReplace,render_select:acf.renderSelect,get_uniqid:acf.uniqid,serialize_form:acf.serialize,esc_html:acf.strEscape,str_sanitize:acf.strSanitize});i._e=function(t,e){t=t||"";var i=(e=e||"")?t+"."+e:t,a={"image.select":"Select Image","image.edit":"Edit Image","image.update":"Update Image"};if(a[i])return acf.__(a[i]);var n=this.l10n[t]||"";return e&&(n=n[e]||""),n},i.get_selector=function(e){var i=".acf-field";if(!e)return i;if(t.isPlainObject(e)){if(t.isEmptyObject(e))return i;for(var a in e){e=e[a];break}}return i+="-"+e,i=acf.strReplace("_","-",i),acf.strReplace("field-field-","field-",i)},i.get_fields=function(t,e,i){var a={is:t||"",parent:e||!1,suppressFilters:i||!1};return a.is&&(a.is=this.get_selector(a.is)),acf.findFields(a)},i.get_field=function(t,e){var i=this.get_fields.apply(this,arguments);return!!i.length&&i.first()},i.get_closest_field=function(t,e){return t.closest(this.get_selector(e))},i.get_field_wrap=function(t){return t.closest(this.get_selector())},i.get_field_key=function(t){return t.data("key")},i.get_field_type=function(t){return t.data("type")},i.get_data=function(t,e){return acf.parseArgs(t.data(),e)},i.maybe_get=function(t,e,i){void 0===i&&(i=null),keys=String(e).split(".");for(var a=0;a<keys.length;a++){if(!t.hasOwnProperty(keys[a]))return i;t=t[keys[a]]}return t};var a=function(t){return t instanceof acf.Field?t.$el:t},n=function(e){return function(){if(arguments.length)var i=function(t){return acf.arrayArgs(t).map(a)}(arguments);else i=[t(document)];return e.apply(this,i)}};i.add_action=function(t,e,a,s){var o=t.split(" "),r=o.length;if(r>1){for(var c=0;c<r;c++)t=o[c],i.add_action.apply(this,arguments);return this}return e=n(e),acf.addAction.apply(this,arguments)},i.add_filter=function(t,e,i,a){return e=n(e),acf.addFilter.apply(this,arguments)},i.model={actions:{},filters:{},events:{},extend:function(e){var i=t.extend({},this,e);return t.each(i.actions,(function(t,e){i._add_action(t,e)})),t.each(i.filters,(function(t,e){i._add_filter(t,e)})),t.each(i.events,(function(t,e){i._add_event(t,e)})),i},_add_action:function(t,e){var i=t.split(" "),a=(t=i[0]||"",i[1]||10);acf.add_action(t,this[e],a,this)},_add_filter:function(t,e){var i=t.split(" "),a=(t=i[0]||"",i[1]||10);acf.add_filter(t,this[e],a,this)},_add_event:function(e,i){var a=this,n=e.indexOf(" "),s=n>0?e.substr(0,n):e,o=n>0?e.substr(n+1):"",r=function(e){e.$el=t(this),acf.field_group&&(e.$field=e.$el.closest(".acf-field-object")),"function"==typeof a.event&&(e=a.event(e)),a[i].apply(a,arguments)};o?t(document).on(s,o,r):t(document).on(s,r)},get:function(t,e){return e=e||null,void 0!==this[t]&&(e=this[t]),e},set:function(t,e){return this[t]=e,"function"==typeof this["_set_"+t]&&this["_set_"+t].apply(this),this}},i.field=acf.model.extend({type:"",o:{},$field:null,_add_action:function(t,e){var i=this;t=t+"_field/type="+i.type,acf.add_action(t,(function(t){i.set("$field",t),i[e].apply(i,arguments)}))},_add_filter:function(t,e){var i=this;t=t+"_field/type="+i.type,acf.add_filter(t,(function(t){i.set("$field",t),i[e].apply(i,arguments)}))},_add_event:function(e,i){var a=this,n=e.substr(0,e.indexOf(" ")),s=e.substr(e.indexOf(" ")+1),o=acf.get_selector(a.type);t(document).on(n,o+" "+s,(function(e){var n=t(this),s=acf.get_closest_field(n,a.type);s.length&&(s.is(a.$field)||a.set("$field",s),e.$el=n,e.$field=s,a[i].apply(a,[e]))}))},_set_$field:function(){"function"==typeof this.focus&&this.focus()},doFocus:function(t){return this.set("$field",t)}}),acf.newCompatibility(acf.validation,{remove_error:function(t){acf.getField(t).removeError()},add_warning:function(t,e){acf.getField(t).showNotice({text:e,type:"warning",timeout:1e3})},fetch:acf.validateForm,enableSubmit:acf.enableSubmit,disableSubmit:acf.disableSubmit,showSpinner:acf.showSpinner,hideSpinner:acf.hideSpinner,unlockForm:acf.unlockForm,lockForm:acf.lockForm}),i.tooltip={tooltip:function(t,e){return acf.newTooltip({text:t,target:e}).$el},temp:function(t,e){acf.newTooltip({text:t,target:e,timeout:250})},confirm:function(t,e,i,a,n){acf.newTooltip({confirm:!0,text:i,target:t,confirm:function(){e(!0)},cancel:function(){e(!1)}})},confirm_remove:function(t,e){acf.newTooltip({confirmRemove:!0,target:t,confirm:function(){e(!0)},cancel:function(){e(!1)}})}},i.media=new acf.Model({activeFrame:!1,actions:{new_media_popup:"onNewMediaPopup"},frame:function(){return this.activeFrame},onNewMediaPopup:function(t){this.activeFrame=t.frame},popup:function(t){return t.mime_types&&(t.allowedTypes=t.mime_types),t.id&&(t.attachment=t.id),acf.newMediaPopup(t).frame}}),i.select2={init:function(t,e,i){return e.allow_null&&(e.allowNull=e.allow_null),e.ajax_action&&(e.ajaxAction=e.ajax_action),i&&(e.field=acf.getField(i)),acf.newSelect2(t,e)},destroy:function(t){return acf.getInstance(t).destroy()}},i.postbox={render:function(t){return t.edit_url&&(t.editLink=t.edit_url),t.edit_title&&(t.editTitle=t.edit_title),acf.newPostbox(t)}},acf.newCompatibility(acf.screen,{update:function(){return this.set.apply(this,arguments)},fetch:acf.screen.check}),i.ajax=acf.screen}(jQuery)},682:function(){!function(t,e){var __=acf.__,i=function(t){return t?""+t:""},a=function(t,e){return i(t).toLowerCase()===i(e).toLowerCase()},n=acf.Condition.extend({type:"hasValue",operator:"!=empty",label:__("Has any value"),fieldTypes:["text","textarea","number","range","email","url","password","image","file","wysiwyg","oembed","select","checkbox","radio","button_group","link","post_object","page_link","relationship","taxonomy","user","google_map","date_picker","date_time_picker","time_picker","color_picker"],match:function(t,e){let i=e.val();return i instanceof Array&&(i=i.length),!!i},choices:function(t){return'<input type="text" disabled="" />'}});acf.registerConditionType(n);var s=n.extend({type:"hasNoValue",operator:"==empty",label:__("Has no value"),match:function(t,e){return!n.prototype.match.apply(this,arguments)}});acf.registerConditionType(s);var o=acf.Condition.extend({type:"equalTo",operator:"==",label:__("Value is equal to"),fieldTypes:["text","textarea","number","range","email","url","password"],match:function(t,e){return acf.isNumeric(t.value)?(i=t.value,n=e.val(),parseFloat(i)===parseFloat(n)):a(t.value,e.val());var i,n},choices:function(t){return'<input type="text" />'}});acf.registerConditionType(o);var r=o.extend({type:"notEqualTo",operator:"!=",label:__("Value is not equal to"),match:function(t,e){return!o.prototype.match.apply(this,arguments)}});acf.registerConditionType(r);var c=acf.Condition.extend({type:"patternMatch",operator:"==pattern",label:__("Value matches pattern"),fieldTypes:["text","textarea","email","url","password","wysiwyg"],match:function(t,e){return a=e.val(),n=t.value,s=new RegExp(i(n),"gi"),i(a).match(s);var a,n,s},choices:function(t){return'<input type="text" placeholder="[a-z0-9]" />'}});acf.registerConditionType(c);var l=acf.Condition.extend({type:"contains",operator:"==contains",label:__("Value contains"),fieldTypes:["text","textarea","number","email","url","password","wysiwyg","oembed","select"],match:function(t,e){return a=e.val(),n=t.value,i(a).indexOf(i(n))>-1;var a,n},choices:function(t){return'<input type="text" />'}});acf.registerConditionType(l);var d=o.extend({type:"trueFalseEqualTo",choiceType:"select",fieldTypes:["true_false"],choices:function(t){return[{id:1,text:__("Checked")}]}});acf.registerConditionType(d);var u=r.extend({type:"trueFalseNotEqualTo",choiceType:"select",fieldTypes:["true_false"],choices:function(t){return[{id:1,text:__("Checked")}]}});acf.registerConditionType(u);var f=acf.Condition.extend({type:"selectEqualTo",operator:"==",label:__("Value is equal to"),fieldTypes:["select","checkbox","radio","button_group"],match:function(t,e){var n,s=e.val();return s instanceof Array?(n=t.value,s.map((function(t){return i(t)})).indexOf(n)>-1):a(t.value,s)},choices:function(t){var e=[],i=t.$setting("choices textarea").val().split("\n");return t.$input("allow_null").prop("checked")&&e.push({id:"",text:__("Null")}),i.map((function(t){(t=t.split(":"))[1]=t[1]||t[0],e.push({id:t[0].trim(),text:t[1].trim()})})),e}});acf.registerConditionType(f);var p=f.extend({type:"selectNotEqualTo",operator:"!=",label:__("Value is not equal to"),match:function(t,e){return!f.prototype.match.apply(this,arguments)}});acf.registerConditionType(p);var h=acf.Condition.extend({type:"greaterThan",operator:">",label:__("Value is greater than"),fieldTypes:["number","range"],match:function(t,e){var i,a,n=e.val();return n instanceof Array&&(n=n.length),i=n,a=t.value,parseFloat(i)>parseFloat(a)},choices:function(t){return'<input type="number" />'}});acf.registerConditionType(h);var g=h.extend({type:"lessThan",operator:"<",label:__("Value is less than"),match:function(t,e){var i,a,n=e.val();return n instanceof Array&&(n=n.length),null==n||!1===n||(i=n,a=t.value,parseFloat(i)<parseFloat(a))},choices:function(t){return'<input type="number" />'}});acf.registerConditionType(g);var m=h.extend({type:"selectionGreaterThan",label:__("Selection is greater than"),fieldTypes:["checkbox","select","post_object","page_link","relationship","taxonomy","user"]});acf.registerConditionType(m);var v=g.extend({type:"selectionLessThan",label:__("Selection is less than"),fieldTypes:["checkbox","select","post_object","page_link","relationship","taxonomy","user"]});acf.registerConditionType(v)}(jQuery)},2849:function(){!function(t,e){var i=[];acf.Condition=acf.Model.extend({type:"",operator:"==",label:"",choiceType:"input",fieldTypes:[],data:{conditions:!1,field:!1,rule:{}},events:{change:"change",keyup:"change",enableField:"change",disableField:"change"},setup:function(e){t.extend(this.data,e)},getEventTarget:function(t,e){return t||this.get("field").$el},change:function(t,e){this.get("conditions").change(t)},match:function(t,e){return!1},calculate:function(){return this.match(this.get("rule"),this.get("field"))},choices:function(t){return'<input type="text" />'}}),acf.newCondition=function(t,e){var i=e.get("field"),a=i.getField(t.field);if(!i||!a)return!1;var n={rule:t,target:i,conditions:e,field:a},s=a.get("type"),o=t.operator;return new(acf.getConditionTypes({fieldType:s,operator:o})[0]||acf.Condition)(n)};var a=function(t){return acf.strPascalCase(t||"")+"Condition"};acf.registerConditionType=function(t){var e=t.prototype.type,n=a(e);acf.models[n]=t,i.push(e)},acf.getConditionType=function(t){var e=a(t);return acf.models[e]||!1},acf.registerConditionForFieldType=function(t,e){var i=acf.getConditionType(t);i&&i.prototype.fieldTypes.push(e)},acf.getConditionTypes=function(t){t=acf.parseArgs(t,{fieldType:"",operator:""});var e=[];return i.map((function(i){var a=acf.getConditionType(i),n=a.prototype.fieldTypes,s=a.prototype.operator;t.fieldType&&-1===n.indexOf(t.fieldType)||t.operator&&s!==t.operator||e.push(a)})),e}}(jQuery)},3155:function(){!function(t,e){var i="conditional_logic",a=(new acf.Model({id:"conditionsManager",priority:20,actions:{new_field:"onNewField"},onNewField:function(t){t.has("conditions")&&t.getConditions().render()}}),function(e,i){var a=acf.getFields({key:i,sibling:e.$el,suppressFilters:!0});return a.length||(a=acf.getFields({key:i,parent:e.$el.parent(),suppressFilters:!0})),!a.length&&t(".acf-field-settings").length&&(a=acf.getFields({key:i,parent:e.$el.parents(".acf-field-settings:first"),suppressFilters:!0})),!a.length&&t("#acf-basic-settings").length&&(a=acf.getFields({key:i,parent:t("#acf-basic-settings"),suppressFilters:!0})),!!a.length&&a[0]});acf.Field.prototype.getField=function(t){var e=a(this,t);if(e)return e;for(var i=this.parents(),n=0;n<i.length;n++)if(e=a(i[n],t))return e;return!1},acf.Field.prototype.getConditions=function(){return this.conditions||(this.conditions=new n(this)),this.conditions};var n=acf.Model.extend({id:"Conditions",data:{field:!1,timeStamp:!1,groups:[]},setup:function(t){this.data.field=t;var e=t.get("conditions");e instanceof Array?e[0]instanceof Array?e.map((function(t,e){this.addRules(t,e)}),this):this.addRules(e):this.addRule(e)},change:function(t){if(this.get("timeStamp")===t.timeStamp)return!1;this.set("timeStamp",t.timeStamp,!0),this.render()},render:function(){return this.calculate()?this.show():this.hide()},show:function(){return this.get("field").showEnable(this.cid,i)},hide:function(){return this.get("field").hideDisable(this.cid,i)},calculate:function(){var t=!1;return this.getGroups().map((function(e){t||e.filter((function(t){return t.calculate()})).length==e.length&&(t=!0)})),t},hasGroups:function(){return null!=this.data.groups},getGroups:function(){return this.data.groups},addGroup:function(){var t=[];return this.data.groups.push(t),t},hasGroup:function(t){return null!=this.data.groups[t]},getGroup:function(t){return this.data.groups[t]},removeGroup:function(t){return this.data.groups[t].delete,this},addRules:function(t,e){t.map((function(t){this.addRule(t,e)}),this)},addRule:function(t,e){var i;e=e||0,i=this.hasGroup(e)?this.getGroup(e):this.addGroup();var a=acf.newCondition(t,this);if(!a)return!1;i.push(a)},hasRule:function(){},getRule:function(t,e){return t=t||0,e=e||0,this.data.groups[e][t]},removeRule:function(){}})}(jQuery)},2213:function(){!function(t,e){var i=0,a=acf.Field.extend({type:"accordion",wait:"",$control:function(){return this.$(".acf-fields:first")},initialize:function(){if(!this.$el.hasClass("acf-accordion")&&!this.$el.is("td")){if(this.get("endpoint"))return this.remove();var e=this.$el,a=this.$labelWrap(),s=this.$inputWrap(),o=this.$control(),r=s.children(".description");if(r.length&&a.append(r),this.$el.is("tr")){var c=this.$el.closest("table"),l=t('<div class="acf-accordion-title"/>'),d=t('<div class="acf-accordion-content"/>'),u=t('<table class="'+c.attr("class")+'"/>'),f=t("<tbody/>");l.append(a.html()),u.append(f),d.append(u),s.append(l),s.append(d),a.remove(),o.remove(),s.attr("colspan",2),a=l,s=d,o=f}e.addClass("acf-accordion"),a.addClass("acf-accordion-title"),s.addClass("acf-accordion-content"),i++,this.get("multi_expand")&&e.attr("multi-expand",1);var p=acf.getPreference("this.accordions")||[];void 0!==p[i-1]&&this.set("open",p[i-1]),this.get("open")&&(e.addClass("-open"),s.css("display","block")),a.prepend(n.iconHtml({open:this.get("open")}));var h=e.parent();o.addClass(h.hasClass("-left")?"-left":""),o.addClass(h.hasClass("-clear")?"-clear":""),o.append(e.nextUntil(".acf-field-accordion",".acf-field")),o.removeAttr("data-open data-multi_expand data-endpoint")}}});acf.registerFieldType(a);var n=new acf.Model({actions:{unload:"onUnload"},events:{"click .acf-accordion-title":"onClick","invalidField .acf-accordion":"onInvalidField"},isOpen:function(t){return t.hasClass("-open")},toggle:function(t){this.isOpen(t)?this.close(t):this.open(t)},iconHtml:function(t){return acf.isGutenberg()?t.open?'<svg width="24px" height="24px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" class="acf-accordion-icon components-panel__arrow" aria-hidden="true" focusable="false"><path d="M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"></path></svg>':'<svg width="24px" height="24px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" class=" acf-accordion-icon components-panel__arrow" aria-hidden="true" focusable="false"><path d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"></path></svg>':t.open?'<i class="acf-accordion-icon dashicons dashicons-arrow-down"></i>':'<i class="acf-accordion-icon dashicons dashicons-arrow-right"></i>'},open:function(e){var i=acf.isGutenberg()?0:300;e.find(".acf-accordion-content:first").slideDown(i).css("display","block"),e.find(".acf-accordion-icon:first").replaceWith(this.iconHtml({open:!0})),e.addClass("-open"),acf.doAction("show",e),e.attr("multi-expand")||e.siblings(".acf-accordion.-open").each((function(){n.close(t(this))}))},close:function(t){var e=acf.isGutenberg()?0:300;t.find(".acf-accordion-content:first").slideUp(e),t.find(".acf-accordion-icon:first").replaceWith(this.iconHtml({open:!1})),t.removeClass("-open"),acf.doAction("hide",t)},onClick:function(t,e){t.preventDefault(),this.toggle(e.parent())},onInvalidField:function(t,e){this.busy||(this.busy=!0,this.setTimeout((function(){this.busy=!1}),1e3),this.open(e))},onUnload:function(e){var i=[];t(".acf-accordion").each((function(){var e=t(this).hasClass("-open")?1:0;i.push(e)})),i.length&&acf.setPreference("this.accordions",i)}})}(jQuery)},1357:function(){var t;jQuery,t=acf.Field.extend({type:"button_group",events:{'click input[type="radio"]':"onClick"},$control:function(){return this.$(".acf-button-group")},$input:function(){return this.$("input:checked")},setValue:function(t){this.$('input[value="'+t+'"]').prop("checked",!0).trigger("change")},onClick:function(t,e){var i=e.parent("label"),a=i.hasClass("selected");this.$(".selected").removeClass("selected"),i.addClass("selected"),this.get("allow_null")&&a&&(i.removeClass("selected"),e.prop("checked",!1).trigger("change"))}}),acf.registerFieldType(t)},8171:function(){var t,e;t=jQuery,e=acf.Field.extend({type:"checkbox",events:{"change input":"onChange","click .acf-add-checkbox":"onClickAdd","click .acf-checkbox-toggle":"onClickToggle","click .acf-checkbox-custom":"onClickCustom"},$control:function(){return this.$(".acf-checkbox-list")},$toggle:function(){return this.$(".acf-checkbox-toggle")},$input:function(){return this.$('input[type="hidden"]')},$inputs:function(){return this.$('input[type="checkbox"]').not(".acf-checkbox-toggle")},getValue:function(){var e=[];return this.$(":checked").each((function(){e.push(t(this).val())})),!!e.length&&e},onChange:function(t,e){var i=e.prop("checked"),a=e.parent("label"),n=this.$toggle();i?a.addClass("selected"):a.removeClass("selected"),n.length&&(0==this.$inputs().not(":checked").length?n.prop("checked",!0):n.prop("checked",!1))},onClickAdd:function(t,e){var i='<li><input class="acf-checkbox-custom" type="checkbox" checked="checked" /><input type="text" name="'+this.getInputName()+'[]" /></li>';e.parent("li").before(i),e.parent("li").parent().find('input[type="text"]').last().focus()},onClickToggle:function(t,e){var i=e.prop("checked"),a=this.$('input[type="checkbox"]'),n=this.$("label");a.prop("checked",i),i?n.addClass("selected"):n.removeClass("selected")},onClickCustom:function(t,e){var i=e.prop("checked"),a=e.next('input[type="text"]');i?a.prop("disabled",!1):(a.prop("disabled",!0),""==a.val()&&e.parent("li").remove())}}),acf.registerFieldType(e)},9459:function(){var t;jQuery,t=acf.Field.extend({type:"color_picker",wait:"load",events:{duplicateField:"onDuplicate"},$control:function(){return this.$(".acf-color-picker")},$input:function(){return this.$('input[type="hidden"]')},$inputText:function(){return this.$('input[type="text"]')},setValue:function(t){acf.val(this.$input(),t),this.$inputText().iris("color",t)},initialize:function(){var t=this.$input(),e=this.$inputText(),i=function(i){setTimeout((function(){acf.val(t,e.val())}),1)},a={defaultColor:!1,palettes:!0,hide:!0,change:i,clear:i};a=acf.applyFilters("color_picker_args",a,this),e.wpColorPicker(a)},onDuplicate:function(t,e,i){$colorPicker=i.find(".wp-picker-container"),$inputText=i.find('input[type="text"]'),$colorPicker.replaceWith($inputText)}}),acf.registerFieldType(t)},7597:function(){var t,e;t=jQuery,e=acf.Field.extend({type:"date_picker",events:{'blur input[type="text"]':"onBlur",duplicateField:"onDuplicate"},$control:function(){return this.$(".acf-date-picker")},$input:function(){return this.$('input[type="hidden"]')},$inputText:function(){return this.$('input[type="text"]')},initialize:function(){if(this.has("save_format"))return this.initializeCompatibility();var t=this.$input(),e=this.$inputText(),i={dateFormat:this.get("date_format"),altField:t,altFormat:"yymmdd",changeYear:!0,yearRange:"-100:+100",changeMonth:!0,showButtonPanel:!0,firstDay:this.get("first_day")};i=acf.applyFilters("date_picker_args",i,this),acf.newDatePicker(e,i),acf.doAction("date_picker_init",e,i,this)},initializeCompatibility:function(){var t=this.$input(),e=this.$inputText();e.val(t.val());var i={dateFormat:this.get("date_format"),altField:t,altFormat:this.get("save_format"),changeYear:!0,yearRange:"-100:+100",changeMonth:!0,showButtonPanel:!0,firstDay:this.get("first_day")},a=(i=acf.applyFilters("date_picker_args",i,this)).dateFormat;i.dateFormat=this.get("save_format"),acf.newDatePicker(e,i),e.datepicker("option","dateFormat",a),acf.doAction("date_picker_init",e,i,this)},onBlur:function(){this.$inputText().val()||acf.val(this.$input(),"")},onDuplicate:function(t,e,i){i.find('input[type="text"]').removeClass("hasDatepicker").removeAttr("id")}}),acf.registerFieldType(e),new acf.Model({priority:5,wait:"ready",initialize:function(){var e=acf.get("locale"),i=acf.get("rtl"),a=acf.get("datePickerL10n");return!!a&&void 0!==t.datepicker&&(a.isRTL=i,t.datepicker.regional[e]=a,void t.datepicker.setDefaults(a))}}),acf.newDatePicker=function(e,i){if(void 0===t.datepicker)return!1;i=i||{},e.datepicker(i),t("body > #ui-datepicker-div").exists()&&t("body > #ui-datepicker-div").wrap('<div class="acf-ui-datepicker" />')}},684:function(){var t,e;t=jQuery,e=acf.models.DatePickerField.extend({type:"date_time_picker",$control:function(){return this.$(".acf-date-time-picker")},initialize:function(){var t=this.$input(),e=this.$inputText(),i={dateFormat:this.get("date_format"),timeFormat:this.get("time_format"),altField:t,altFieldTimeOnly:!1,altFormat:"yy-mm-dd",altTimeFormat:"HH:mm:ss",changeYear:!0,yearRange:"-100:+100",changeMonth:!0,showButtonPanel:!0,firstDay:this.get("first_day"),controlType:"select",oneLine:!0};i=acf.applyFilters("date_time_picker_args",i,this),acf.newDateTimePicker(e,i),acf.doAction("date_time_picker_init",e,i,this)}}),acf.registerFieldType(e),new acf.Model({priority:5,wait:"ready",initialize:function(){var e=acf.get("locale"),i=acf.get("rtl"),a=acf.get("dateTimePickerL10n");return!!a&&void 0!==t.timepicker&&(a.isRTL=i,t.timepicker.regional[e]=a,void t.timepicker.setDefaults(a))}}),acf.newDateTimePicker=function(e,i){if(void 0===t.timepicker)return!1;i=i||{},e.datetimepicker(i),t("body > #ui-datepicker-div").exists()&&t("body > #ui-datepicker-div").wrap('<div class="acf-ui-datepicker" />')}},5647:function(){var t,e;t=jQuery,e=acf.models.ImageField.extend({type:"file",$control:function(){return this.$(".acf-file-uploader")},$input:function(){return this.$('input[type="hidden"]:first')},validateAttachment:function(t){return void 0!==(t=t||{}).id&&(t=t.attributes),acf.parseArgs(t,{url:"",alt:"",title:"",filename:"",filesizeHumanReadable:"",icon:"/wp-includes/images/media/default.png"})},render:function(t){t=this.validateAttachment(t),this.$("img").attr({src:t.icon,alt:t.alt,title:t.title}),this.$('[data-name="title"]').text(t.title),this.$('[data-name="filename"]').text(t.filename).attr("href",t.url),this.$('[data-name="filesize"]').text(t.filesizeHumanReadable);var e=t.id||"";acf.val(this.$input(),e),e?this.$control().addClass("has-value"):this.$control().removeClass("has-value")},selectAttachment:function(){var e=this.parent(),i=e&&"repeater"===e.get("type");acf.newMediaPopup({mode:"select",title:acf.__("Select File"),field:this.get("key"),multiple:i,library:this.get("library"),allowedTypes:this.get("mime_types"),select:t.proxy((function(t,i){i>0?this.append(t,e):this.render(t)}),this)})},editAttachment:function(){var e=this.val();if(!e)return!1;acf.newMediaPopup({mode:"edit",title:acf.__("Edit File"),button:acf.__("Update File"),attachment:e,field:this.get("key"),select:t.proxy((function(t,e){this.render(t)}),this)})}}),acf.registerFieldType(e)},8489:function(){!function(t,e){var i=acf.Field.extend({type:"google_map",map:!1,wait:"load",events:{'click a[data-name="clear"]':"onClickClear",'click a[data-name="locate"]':"onClickLocate",'click a[data-name="search"]':"onClickSearch","keydown .search":"onKeydownSearch","keyup .search":"onKeyupSearch","focus .search":"onFocusSearch","blur .search":"onBlurSearch",showField:"onShow"},$control:function(){return this.$(".acf-google-map")},$search:function(){return this.$(".search")},$canvas:function(){return this.$(".canvas")},setState:function(t){this.$control().removeClass("-value -loading -searching"),"default"===t&&(t=this.val()?"value":""),t&&this.$control().addClass("-"+t)},getValue:function(){var t=this.$input().val();return!!t&&JSON.parse(t)},setValue:function(t,e){var i="";t&&(i=JSON.stringify(t)),acf.val(this.$input(),i),e||(this.renderVal(t),acf.doAction("google_map_change",t,this.map,this))},renderVal:function(t){t?(this.setState("value"),this.$search().val(t.address),this.setPosition(t.lat,t.lng)):(this.setState(""),this.$search().val(""),this.map.marker.setVisible(!1))},newLatLng:function(t,e){return new google.maps.LatLng(parseFloat(t),parseFloat(e))},setPosition:function(t,e){this.map.marker.setPosition({lat:parseFloat(t),lng:parseFloat(e)}),this.map.marker.setVisible(!0),this.center()},center:function(){var t=this.map.marker.getPosition();if(t)var e=t.lat(),i=t.lng();else e=this.get("lat"),i=this.get("lng");this.map.setCenter({lat:parseFloat(e),lng:parseFloat(i)})},initialize:function(){!function(e){if(n)return e();if(acf.isset(window,"google","maps","Geocoder"))return n=new google.maps.Geocoder,e();if(acf.addAction("google_map_api_loaded",e),!a){var i=acf.get("google_map_api");i&&(a=!0,t.ajax({url:i,dataType:"script",cache:!0,success:function(){n=new google.maps.Geocoder,acf.doAction("google_map_api_loaded")}}))}}(this.initializeMap.bind(this))},initializeMap:function(){var t=this.getValue(),e=acf.parseArgs(t,{zoom:this.get("zoom"),lat:this.get("lat"),lng:this.get("lng")}),i={scrollwheel:!1,zoom:parseInt(e.zoom),center:{lat:parseFloat(e.lat),lng:parseFloat(e.lng)},mapTypeId:google.maps.MapTypeId.ROADMAP,marker:{draggable:!0,raiseOnDrag:!0},autocomplete:{}};i=acf.applyFilters("google_map_args",i,this);var a=new google.maps.Map(this.$canvas()[0],i),n=acf.parseArgs(i.marker,{draggable:!0,raiseOnDrag:!0,map:a});n=acf.applyFilters("google_map_marker_args",n,this);var s=new google.maps.Marker(n),o=!1;if(acf.isset(google,"maps","places","Autocomplete")){var r=i.autocomplete||{};r=acf.applyFilters("google_map_autocomplete_args",r,this),(o=new google.maps.places.Autocomplete(this.$search()[0],r)).bindTo("bounds",a)}this.addMapEvents(this,a,s,o),a.acf=this,a.marker=s,a.autocomplete=o,this.map=a,t&&this.setPosition(t.lat,t.lng),acf.doAction("google_map_init",a,s,this)},addMapEvents:function(t,e,i,a){google.maps.event.addListener(e,"click",(function(e){var i=e.latLng.lat(),a=e.latLng.lng();t.searchPosition(i,a)})),google.maps.event.addListener(i,"dragend",(function(){var e=this.getPosition().lat(),i=this.getPosition().lng();t.searchPosition(e,i)})),a&&google.maps.event.addListener(a,"place_changed",(function(){var e=this.getPlace();t.searchPlace(e)})),google.maps.event.addListener(e,"zoom_changed",(function(){var i=t.val();i&&(i.zoom=e.getZoom(),t.setValue(i,!0))}))},searchPosition:function(t,e){this.setState("loading");var i={lat:t,lng:e};n.geocode({location:i},function(i,a){if(this.setState(""),"OK"!==a)this.showNotice({text:acf.__("Location not found: %s").replace("%s",a),type:"warning"});else{var n=this.parseResult(i[0]);n.lat=t,n.lng=e,this.val(n)}}.bind(this))},searchPlace:function(t){if(t)if(t.geometry){t.formatted_address=this.$search().val();var e=this.parseResult(t);this.val(e)}else t.name&&this.searchAddress(t.name)},searchAddress:function(t){if(t){var e=t.split(",");if(2==e.length){var i=parseFloat(e[0]),a=parseFloat(e[1]);if(i&&a)return this.searchPosition(i,a)}this.setState("loading"),n.geocode({address:t},function(e,i){if(this.setState(""),"OK"!==i)this.showNotice({text:acf.__("Location not found: %s").replace("%s",i),type:"warning"});else{var a=this.parseResult(e[0]);a.address=t,this.val(a)}}.bind(this))}},searchLocation:function(){if(!navigator.geolocation)return alert(acf.__("Sorry, this browser does not support geolocation"));this.setState("loading"),navigator.geolocation.getCurrentPosition(function(t){this.setState("");var e=t.coords.latitude,i=t.coords.longitude;this.searchPosition(e,i)}.bind(this),function(t){this.setState("")}.bind(this))},parseResult:function(t){var e={address:t.formatted_address,lat:t.geometry.location.lat(),lng:t.geometry.location.lng()};e.zoom=this.map.getZoom(),t.place_id&&(e.place_id=t.place_id),t.name&&(e.name=t.name);var i={street_number:["street_number"],street_name:["street_address","route"],city:["locality","postal_town"],state:["administrative_area_level_1","administrative_area_level_2","administrative_area_level_3","administrative_area_level_4","administrative_area_level_5"],post_code:["postal_code"],country:["country"]};for(var a in i)for(var n=i[a],s=0;s<t.address_components.length;s++){var o=t.address_components[s],r=o.types[0];-1!==n.indexOf(r)&&(e[a]=o.long_name,o.long_name!==o.short_name&&(e[a+"_short"]=o.short_name))}return acf.applyFilters("google_map_result",e,t,this.map,this)},onClickClear:function(){this.val(!1)},onClickLocate:function(){this.searchLocation()},onClickSearch:function(){this.searchAddress(this.$search().val())},onFocusSearch:function(t,e){this.setState("searching")},onBlurSearch:function(t,e){var i=this.val(),a=i?i.address:"";e.val()===a&&this.setState("default")},onKeyupSearch:function(t,e){e.val()||this.val(!1)},onKeydownSearch:function(t,e){13==t.which&&(t.preventDefault(),e.blur())},onShow:function(){this.map&&this.setTimeout(this.center)}});acf.registerFieldType(i);var a=!1,n=!1}(jQuery)},6691:function(){var t,e;t=jQuery,e=acf.Field.extend({type:"image",$control:function(){return this.$(".acf-image-uploader")},$input:function(){return this.$('input[type="hidden"]:first')},events:{'click a[data-name="add"]':"onClickAdd",'click a[data-name="edit"]':"onClickEdit",'click a[data-name="remove"]':"onClickRemove",'change input[type="file"]':"onChange"},initialize:function(){"basic"===this.get("uploader")&&this.$el.closest("form").attr("enctype","multipart/form-data")},validateAttachment:function(t){t&&t.attributes&&(t=t.attributes),t=acf.parseArgs(t,{id:0,url:"",alt:"",title:"",caption:"",description:"",width:0,height:0});var e=acf.isget(t,"sizes",this.get("preview_size"));return e&&(t.url=e.url,t.width=e.width,t.height=e.height),t},render:function(t){t=this.validateAttachment(t),this.$("img").attr({src:t.url,alt:t.alt}),t.id?(this.val(t.id),this.$control().addClass("has-value")):(this.val(""),this.$control().removeClass("has-value"))},append:function(t,e){var i=function(t,e){for(var i=acf.getFields({key:t.get("key"),parent:e.$el}),a=0;a<i.length;a++)if(!i[a].val())return i[a];return!1},a=i(this,e);a||(e.$(".acf-button:last").trigger("click"),a=i(this,e)),a&&a.render(t)},selectAttachment:function(){var e=this.parent(),i=e&&"repeater"===e.get("type");acf.newMediaPopup({mode:"select",type:"image",title:acf.__("Select Image"),field:this.get("key"),multiple:i,library:this.get("library"),allowedTypes:this.get("mime_types"),select:t.proxy((function(t,i){i>0?this.append(t,e):this.render(t)}),this)})},editAttachment:function(){var e=this.val();e&&acf.newMediaPopup({mode:"edit",title:acf.__("Edit Image"),button:acf.__("Update Image"),attachment:e,field:this.get("key"),select:t.proxy((function(t,e){this.render(t)}),this)})},removeAttachment:function(){this.render(!1)},onClickAdd:function(t,e){this.selectAttachment()},onClickEdit:function(t,e){this.editAttachment()},onClickRemove:function(t,e){this.removeAttachment()},onChange:function(e,i){var a=this.$input();i.val()||a.val(""),acf.getFileInputData(i,(function(e){a.val(t.param(e))}))}}),acf.registerFieldType(e)},4658:function(){var t,e;t=jQuery,e=acf.Field.extend({type:"link",events:{'click a[data-name="add"]':"onClickEdit",'click a[data-name="edit"]':"onClickEdit",'click a[data-name="remove"]':"onClickRemove","change .link-node":"onChange"},$control:function(){return this.$(".acf-link")},$node:function(){return this.$(".link-node")},getValue:function(){var t=this.$node();return!!t.attr("href")&&{title:t.html(),url:t.attr("href"),target:t.attr("target")}},setValue:function(t){t=acf.parseArgs(t,{title:"",url:"",target:""});var e=this.$control(),i=this.$node();e.removeClass("-value -external"),t.url&&e.addClass("-value"),"_blank"===t.target&&e.addClass("-external"),this.$(".link-title").html(t.title),this.$(".link-url").attr("href",t.url).html(t.url),i.html(t.title),i.attr("href",t.url),i.attr("target",t.target),this.$(".input-title").val(t.title),this.$(".input-target").val(t.target),this.$(".input-url").val(t.url).trigger("change")},onClickEdit:function(t,e){acf.wpLink.open(this.$node())},onClickRemove:function(t,e){this.setValue(!1)},onChange:function(t,e){var i=this.getValue();this.setValue(i)}}),acf.registerFieldType(e),acf.wpLink=new acf.Model({getNodeValue:function(){var t=this.get("node");return{title:acf.decode(t.html()),url:t.attr("href"),target:t.attr("target")}},setNodeValue:function(t){var e=this.get("node");e.text(t.title),e.attr("href",t.url),e.attr("target",t.target),e.trigger("change")},getInputValue:function(){return{title:t("#wp-link-text").val(),url:t("#wp-link-url").val(),target:t("#wp-link-target").prop("checked")?"_blank":""}},setInputValue:function(e){t("#wp-link-text").val(e.title),t("#wp-link-url").val(e.url),t("#wp-link-target").prop("checked","_blank"===e.target)},open:function(e){this.on("wplink-open","onOpen"),this.on("wplink-close","onClose"),this.set("node",e);var i=t('<textarea id="acf-link-textarea" style="display:none;"></textarea>');t("body").append(i);var a=this.getNodeValue();wpLink.open("acf-link-textarea",a.url,a.title,null)},onOpen:function(){t("#wp-link-wrap").addClass("has-text-field");var e=this.getNodeValue();this.setInputValue(e),e.url&&wpLinkL10n&&t("#wp-link-submit").val(wpLinkL10n.update)},close:function(){wpLink.close()},onClose:function(){if(!this.has("node"))return!1;var e=t("#wp-link-submit");if(e.is(":hover")||e.is(":focus")){var i=this.getInputValue();this.setNodeValue(i)}this.off("wplink-open"),this.off("wplink-close"),t("#acf-link-textarea").remove(),this.set("node",null)}})},719:function(){var t,e;t=jQuery,e=acf.Field.extend({type:"oembed",events:{'click [data-name="clear-button"]':"onClickClear","keypress .input-search":"onKeypressSearch","keyup .input-search":"onKeyupSearch","change .input-search":"onChangeSearch"},$control:function(){return this.$(".acf-oembed")},$input:function(){return this.$(".input-value")},$search:function(){return this.$(".input-search")},getValue:function(){return this.$input().val()},getSearchVal:function(){return this.$search().val()},setValue:function(t){t?this.$control().addClass("has-value"):this.$control().removeClass("has-value"),acf.val(this.$input(),t)},showLoading:function(t){acf.showLoading(this.$(".canvas"))},hideLoading:function(){acf.hideLoading(this.$(".canvas"))},maybeSearch:function(){var e=this.val(),i=this.getSearchVal();if(!i)return this.clear();if("http"!=i.substr(0,4)&&(i="http://"+i),i!==e){var a=this.get("timeout");a&&clearTimeout(a);var n=t.proxy(this.search,this,i);this.set("timeout",setTimeout(n,300))}},search:function(e){var i={action:"acf/fields/oembed/search",s:e,field_key:this.get("key")};(a=this.get("xhr"))&&a.abort(),this.showLoading();var a=t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(i),type:"post",dataType:"json",context:this,success:function(t){t&&t.html||(t={url:!1,html:""}),this.val(t.url),this.$(".canvas-media").html(t.html)},complete:function(){this.hideLoading()}});this.set("xhr",a)},clear:function(){this.val(""),this.$search().val(""),this.$(".canvas-media").html("")},onClickClear:function(t,e){this.clear()},onKeypressSearch:function(t,e){13==t.which&&(t.preventDefault(),this.maybeSearch())},onKeyupSearch:function(t,e){e.val()&&this.maybeSearch()},onChangeSearch:function(t,e){this.maybeSearch()}}),acf.registerFieldType(e)},1281:function(){var t;jQuery,t=acf.models.SelectField.extend({type:"page_link"}),acf.registerFieldType(t)},1987:function(){var t;jQuery,t=acf.models.SelectField.extend({type:"post_object"}),acf.registerFieldType(t)},2557:function(){var t;jQuery,t=acf.Field.extend({type:"radio",events:{'click input[type="radio"]':"onClick"},$control:function(){return this.$(".acf-radio-list")},$input:function(){return this.$("input:checked")},$inputText:function(){return this.$('input[type="text"]')},getValue:function(){var t=this.$input().val();return"other"===t&&this.get("other_choice")&&(t=this.$inputText().val()),t},onClick:function(t,e){var i=e.parent("label"),a=i.hasClass("selected"),n=e.val();this.$(".selected").removeClass("selected"),i.addClass("selected"),this.get("allow_null")&&a&&(i.removeClass("selected"),e.prop("checked",!1).trigger("change"),n=!1),this.get("other_choice")&&("other"===n?this.$inputText().prop("disabled",!1):this.$inputText().prop("disabled",!0))}}),acf.registerFieldType(t)},2489:function(){var t;jQuery,t=acf.Field.extend({type:"range",events:{'input input[type="range"]':"onChange","change input":"onChange"},$input:function(){return this.$('input[type="range"]')},$inputAlt:function(){return this.$('input[type="number"]')},setValue:function(t){this.busy=!0,acf.val(this.$input(),t),acf.val(this.$inputAlt(),this.$input().val(),!0),this.busy=!1},onChange:function(t,e){this.busy||this.setValue(e.val())}}),acf.registerFieldType(t)},714:function(){var t,e;t=jQuery,e=acf.Field.extend({type:"relationship",events:{"keypress [data-filter]":"onKeypressFilter","change [data-filter]":"onChangeFilter","keyup [data-filter]":"onChangeFilter","click .choices-list .acf-rel-item":"onClickAdd","keypress .choices-list .acf-rel-item":"onKeypressFilter","keypress .values-list .acf-rel-item":"onKeypressFilter",'click [data-name="remove_item"]':"onClickRemove"},$control:function(){return this.$(".acf-relationship")},$list:function(t){return this.$("."+t+"-list")},$listItems:function(t){return this.$list(t).find(".acf-rel-item")},$listItem:function(t,e){return this.$list(t).find('.acf-rel-item[data-id="'+e+'"]')},getValue:function(){var e=[];return this.$listItems("values").each((function(){e.push(t(this).data("id"))})),!!e.length&&e},newChoice:function(t){return["<li>",'<span tabindex="0" data-id="'+t.id+'" class="acf-rel-item">'+t.text+"</span>","</li>"].join("")},newValue:function(t){return["<li>",'<input type="hidden" name="'+this.getInputName()+'[]" value="'+t.id+'" />','<span tabindex="0" data-id="'+t.id+'" class="acf-rel-item acf-rel-item-remove">'+t.text,'<a href="#" class="acf-icon -minus small dark" data-name="remove_item"></a>',"</span>","</li>"].join("")},initialize:function(){var t=this.proxy(acf.once((function(){this.$list("values").sortable({items:"li",forceHelperSize:!0,forcePlaceholderSize:!0,scroll:!0,update:this.proxy((function(){this.$input().trigger("change")}))}),this.$list("choices").scrollTop(0).on("scroll",this.proxy(this.onScrollChoices)),this.fetch()})));this.$el.one("mouseover",t),this.$el.one("focus","input",t),acf.onceInView(this.$el,t)},onScrollChoices:function(t){if(!this.get("loading")&&this.get("more")){var e=this.$list("choices"),i=Math.ceil(e.scrollTop()),a=Math.ceil(e[0].scrollHeight),n=Math.ceil(e.innerHeight()),s=this.get("paged")||1;i+n>=a&&(this.set("paged",s+1),this.fetch())}},onKeypressFilter:function(t,e){e.hasClass("acf-rel-item-add")&&13==t.which&&this.onClickAdd(t,e),e.hasClass("acf-rel-item-remove")&&13==t.which&&this.onClickRemove(t,e),13==t.which&&t.preventDefault()},onChangeFilter:function(t,e){var i=e.val(),a=e.data("filter");this.get(a)!==i&&(this.set(a,i),this.set("paged",1),e.is("select")?this.fetch():this.maybeFetch())},onClickAdd:function(t,e){var i=this.val(),a=parseInt(this.get("max"));if(e.hasClass("disabled"))return!1;if(a>0&&i&&i.length>=a)return this.showNotice({text:acf.__("Maximum values reached ( {max} values )").replace("{max}",a),type:"warning"}),!1;e.addClass("disabled");var n=this.newValue({id:e.data("id"),text:e.html()});this.$list("values").append(n),this.$input().trigger("change")},onClickRemove:function(t,e){let i;t.preventDefault(),i=e.hasClass("acf-rel-item-remove")?e:e.parent();const a=i.parent(),n=i.data("id");a.remove(),this.$listItem("choices",n).removeClass("disabled"),this.$input().trigger("change")},maybeFetch:function(){var t=this.get("timeout");t&&clearTimeout(t),t=this.setTimeout(this.fetch,300),this.set("timeout",t)},getAjaxData:function(){var t=this.$control().data();for(var e in t)t[e]=this.get(e);return t.action="acf/fields/relationship/query",t.field_key=this.get("key"),acf.applyFilters("relationship_ajax_data",t,this)},fetch:function(){(n=this.get("xhr"))&&n.abort();var e=this.getAjaxData(),i=this.$list("choices");1==e.paged&&i.html("");var a=t('<li><i class="acf-loading"></i> '+acf.__("Loading")+"</li>");i.append(a),this.set("loading",!0);var n=t.ajax({url:acf.get("ajaxurl"),dataType:"json",type:"post",data:acf.prepareForAjax(e),context:this,success:function(e){if(!e||!e.results||!e.results.length)return this.set("more",!1),void(1==this.get("paged")&&this.$list("choices").append("<li>"+acf.__("No matches found")+"</li>"));this.set("more",e.more);var a=this.walkChoices(e.results),n=t(a),s=this.val();s&&s.length&&s.map((function(t){n.find('.acf-rel-item[data-id="'+t+'"]').addClass("disabled")})),i.append(n);var o=!1,r=!1;i.find(".acf-rel-label").each((function(){var e=t(this),i=e.siblings("ul");if(o&&o.text()==e.text())return r.append(i.children()),void t(this).parent().remove();o=e,r=i}))},complete:function(){this.set("loading",!1),a.remove()}});this.set("xhr",n)},walkChoices:function(e){var i=function(e){var a="";return t.isArray(e)?e.map((function(t){a+=i(t)})):t.isPlainObject(e)&&(void 0!==e.children?(a+='<li><span class="acf-rel-label">'+acf.escHtml(e.text)+'</span><ul class="acf-bl">',a+=i(e.children),a+="</ul></li>"):a+='<li><span tabindex="0" class="acf-rel-item acf-rel-item-add" data-id="'+acf.escAttr(e.id)+'">'+acf.escHtml(e.text)+"</span></li>"),a};return i(e)}}),acf.registerFieldType(e)},6965:function(){var t;jQuery,t=acf.Field.extend({type:"select",select2:!1,wait:"load",events:{removeField:"onRemove",duplicateField:"onDuplicate"},$input:function(){return this.$("select")},initialize:function(){var t=this.$input();if(this.inherit(t),this.get("ui")){var e=this.get("ajax_action");e||(e="acf/fields/"+this.get("type")+"/query"),this.select2=acf.newSelect2(t,{field:this,ajax:this.get("ajax"),multiple:this.get("multiple"),placeholder:this.get("placeholder"),allowNull:this.get("allow_null"),ajaxAction:e})}},onRemove:function(){this.select2&&this.select2.destroy()},onDuplicate:function(t,e,i){this.select2&&(i.find(".select2-container").remove(),i.find("select").removeClass("select2-hidden-accessible"))}}),acf.registerFieldType(t)},177:function(){!function(t,e){var i="tab",a=acf.Field.extend({type:"tab",wait:"",tabs:!1,tab:!1,events:{duplicateField:"onDuplicate"},findFields:function(){let t;switch(this.get("key")){case"acf_field_settings_tabs":t=".acf-field-settings-main";break;case"acf_field_group_settings_tabs":t=".field-group-settings-tab";break;case"acf_browse_fields_tabs":t=".acf-field-types-tab";break;case"acf_post_type_tabs":t=".acf-post-type-advanced-settings";break;case"acf_taxonomy_tabs":t=".acf-taxonomy-advanced-settings";break;case"acf_ui_options_page_tabs":t=".acf-ui-options-page-advanced-settings";break;default:t=".acf-field"}return this.$el.nextUntil(".acf-field-tab",t)},getFields:function(){return acf.getFields(this.findFields())},findTabs:function(){return this.$el.prevAll(".acf-tab-wrap:first")},findTab:function(){return this.$(".acf-tab-button")},initialize:function(){if(this.$el.is("td"))return this.events={},!1;var t=this.findTabs(),e=this.findTab(),i=acf.parseArgs(e.data(),{endpoint:!1,placement:"",before:this.$el});!t.length||i.endpoint?this.tabs=new s(i):this.tabs=t.data("acf"),this.tab=this.tabs.addTab(e,this)},isActive:function(){return this.tab.isActive()},showFields:function(){this.getFields().map((function(t){t.show(this.cid,i),t.hiddenByTab=!1}),this)},hideFields:function(){this.getFields().map((function(t){t.hide(this.cid,i),t.hiddenByTab=this.tab}),this)},show:function(t){var e=acf.Field.prototype.show.apply(this,arguments);return e&&(this.tab.show(),this.tabs.refresh()),e},hide:function(t){var e=acf.Field.prototype.hide.apply(this,arguments);return e&&(this.tab.hide(),this.isActive()&&this.tabs.reset()),e},enable:function(t){this.getFields().map((function(t){t.enable(i)}))},disable:function(t){this.getFields().map((function(t){t.disable(i)}))},onDuplicate:function(t,e,i){this.isActive()&&i.prevAll(".acf-tab-wrap:first").remove()}});acf.registerFieldType(a);var n=0,s=acf.Model.extend({tabs:[],active:!1,actions:{refresh:"onRefresh",close_field_object:"onCloseFieldObject"},data:{before:!1,placement:"top",index:0,initialized:!1},setup:function(e){t.extend(this.data,e),this.tabs=[],this.active=!1;var i=this.get("placement"),a=this.get("before"),s=a.parent();if("left"==i&&s.hasClass("acf-fields")&&s.addClass("-sidebar"),a.is("tr"))this.$el=t('<tr class="acf-tab-wrap"><td colspan="2"><ul class="acf-hl acf-tab-group"></ul></td></tr>');else{let e="acf-hl acf-tab-group";"acf_field_settings_tabs"===this.get("key")&&(e="acf-field-settings-tab-bar"),this.$el=t('<div class="acf-tab-wrap -'+i+'"><ul class="'+e+'"></ul></div>')}a.before(this.$el),this.set("index",n,!0),n++},initializeTabs:function(){if("acf_field_settings_tabs"!==this.get("key")||!t("#acf-field-group-fields").hasClass("hide-tabs")){var e=this.getVisible().shift(),i=(acf.getPreference("this.tabs")||[])[this.get("index")];this.tabs[i]&&this.tabs[i].isVisible()&&(e=this.tabs[i]),e?this.selectTab(e):this.closeTabs(),this.set("initialized",!0)}},getVisible:function(){return this.tabs.filter((function(t){return t.isVisible()}))},getActive:function(){return this.active},setActive:function(t){return this.active=t},hasActive:function(){return!1!==this.active},isActive:function(t){var e=this.getActive();return e&&e.cid===t.cid},closeActive:function(){this.hasActive()&&this.closeTab(this.getActive())},openTab:function(t){this.closeActive(),t.open(),this.setActive(t)},closeTab:function(t){t.close(),this.setActive(!1)},closeTabs:function(){this.tabs.map(this.closeTab,this)},selectTab:function(t){this.tabs.map((function(e){t.cid!==e.cid&&this.closeTab(e)}),this),this.openTab(t)},addTab:function(e,i){var a=t("<li>"+e.outerHTML()+"</li>"),n=e.attr("class").replace("acf-tab-button","");a.addClass(n),this.$("ul").append(a);var s=new o({$el:a,field:i,group:this});return this.tabs.push(s),s},reset:function(){return this.closeActive(),this.refresh()},refresh:function(){if(this.hasActive())return!1;var t=this.getVisible().shift();return t&&this.openTab(t),t},onRefresh:function(){if("left"===this.get("placement")){var t=this.$el.parent(),e=this.$el.children("ul"),i=t.is("td")?"height":"min-height",a=e.position().top+e.outerHeight(!0)-1;t.css(i,a)}},onCloseFieldObject:function(t){const e=this.getVisible().find((e=>{const i=e.$el.closest("div[data-id]").data("id");if(t.data.id===i)return e}));e&&setTimeout((()=>{this.openTab(e)}),300)}}),o=acf.Model.extend({group:!1,field:!1,events:{"click a":"onClick"},index:function(){return this.$el.index()},isVisible:function(){return acf.isVisible(this.$el)},isActive:function(){return this.$el.hasClass("active")},open:function(){this.$el.addClass("active"),this.field.showFields()},close:function(){this.$el.removeClass("active"),this.field.hideFields()},onClick:function(t,e){t.preventDefault(),this.toggle()},toggle:function(){this.isActive()||this.group.openTab(this)}});new acf.Model({priority:50,actions:{prepare:"render",append:"render",unload:"onUnload",show:"render",invalid_field:"onInvalidField"},findTabs:function(){return t(".acf-tab-wrap")},getTabs:function(){return acf.getInstances(this.findTabs())},render:function(t){this.getTabs().map((function(t){t.get("initialized")||t.initializeTabs()}))},onInvalidField:function(t){this.busy||t.hiddenByTab&&(t.hiddenByTab.toggle(),this.busy=!0,this.setTimeout((function(){this.busy=!1}),100))},onUnload:function(){var t=[];this.getTabs().map((function(e){if(e.$el.children(".acf-field-settings-tab-bar").length||e.$el.parents("#acf-advanced-settings.postbox").length)return!0;var i=e.hasActive()?e.getActive().index():0;t.push(i)})),t.length&&acf.setPreference("this.tabs",t)}})}(jQuery)},2573:function(){var t,e;t=jQuery,e=acf.Field.extend({type:"taxonomy",data:{ftype:"select"},select2:!1,wait:"load",events:{'click a[data-name="add"]':"onClickAdd",'click input[type="radio"]':"onClickRadio",removeField:"onRemove"},$control:function(){return this.$(".acf-taxonomy-field")},$input:function(){return this.getRelatedPrototype().$input.apply(this,arguments)},getRelatedType:function(){var t=this.get("ftype");return"multi_select"==t&&(t="select"),t},getRelatedPrototype:function(){return acf.getFieldType(this.getRelatedType()).prototype},getValue:function(){return this.getRelatedPrototype().getValue.apply(this,arguments)},setValue:function(){return this.getRelatedPrototype().setValue.apply(this,arguments)},initialize:function(){this.getRelatedPrototype().initialize.apply(this,arguments)},onRemove:function(){var t=this.getRelatedPrototype();t.onRemove&&t.onRemove.apply(this,arguments)},onClickAdd:function(e,i){var a=this,n=!1,s=!1,o=!1,r=!1,c=!1,l=!1,d=function(t){n.loading(!1),n.content(t),s=n.$("form"),o=n.$('input[name="term_name"]'),r=n.$('select[name="term_parent"]'),c=n.$(".acf-submit-button"),o.trigger("focus"),n.on("submit","form",u)},u=function(e,i){if(e.preventDefault(),e.stopImmediatePropagation(),""===o.val())return o.trigger("focus"),!1;acf.startButtonLoading(c);var n={action:"acf/fields/taxonomy/add_term",field_key:a.get("key"),term_name:o.val(),term_parent:r.length?r.val():0};t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(n),type:"post",dataType:"json",success:f})},f=function(t){acf.stopButtonLoading(c),l&&l.remove(),acf.isAjaxSuccess(t)?(o.val(""),p(t.data),l=acf.newNotice({type:"success",text:acf.getAjaxMessage(t),target:s,timeout:2e3,dismiss:!1})):l=acf.newNotice({type:"error",text:acf.getAjaxError(t),target:s,timeout:2e3,dismiss:!1}),o.trigger("focus")},p=function(e){var i=t('<option value="'+e.term_id+'">'+e.term_label+"</option>");e.term_parent?r.children('option[value="'+e.term_parent+'"]').after(i):r.append(i),acf.getFields({type:"taxonomy"}).map((function(t){t.get("taxonomy")==a.get("taxonomy")&&t.appendTerm(e)})),a.selectTerm(e.term_id)};!function(){n=acf.newPopup({title:i.attr("title"),loading:!0,width:"300px"});var e={action:"acf/fields/taxonomy/add_term",field_key:a.get("key")};t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(e),type:"post",dataType:"html",success:d})}()},appendTerm:function(t){"select"==this.getRelatedType()?this.appendTermSelect(t):this.appendTermCheckbox(t)},appendTermSelect:function(t){this.select2.addOption({id:t.term_id,text:t.term_label})},appendTermCheckbox:function(e){var i=this.$("[name]:first").attr("name"),a=this.$("ul:first");"checkbox"==this.getRelatedType()&&(i+="[]");var n=t(['<li data-id="'+e.term_id+'">',"<label>",'<input type="'+this.get("ftype")+'" value="'+e.term_id+'" name="'+i+'" /> ',"<span>"+e.term_name+"</span>","</label>","</li>"].join(""));if(e.term_parent){var s=a.find('li[data-id="'+e.term_parent+'"]');(a=s.children("ul")).exists()||(a=t('<ul class="children acf-bl"></ul>'),s.append(a))}a.append(n)},selectTerm:function(t){"select"==this.getRelatedType()?this.select2.selectOption(t):this.$('input[value="'+t+'"]').prop("checked",!0).trigger("change")},onClickRadio:function(t,e){var i=e.parent("label"),a=i.hasClass("selected");this.$(".selected").removeClass("selected"),i.addClass("selected"),this.get("allow_null")&&a&&(i.removeClass("selected"),e.prop("checked",!1).trigger("change"))}}),acf.registerFieldType(e)},9047:function(){var t,e;t=jQuery,e=acf.models.DatePickerField.extend({type:"time_picker",$control:function(){return this.$(".acf-time-picker")},initialize:function(){var t=this.$input(),e=this.$inputText(),i={timeFormat:this.get("time_format"),altField:t,altFieldTimeOnly:!1,altTimeFormat:"HH:mm:ss",showButtonPanel:!0,controlType:"select",oneLine:!0,closeText:acf.get("dateTimePickerL10n").selectText,timeOnly:!0,onClose:function(t,e,i){var a=e.dpDiv.find(".ui-datepicker-close");!t&&a.is(":hover")&&i._updateDateTime()}};i=acf.applyFilters("time_picker_args",i,this),acf.newTimePicker(e,i),acf.doAction("time_picker_init",e,i,this)}}),acf.registerFieldType(e),acf.newTimePicker=function(e,i){if(void 0===t.timepicker)return!1;i=i||{},e.timepicker(i),t("body > #ui-datepicker-div").exists()&&t("body > #ui-datepicker-div").wrap('<div class="acf-ui-datepicker" />')}},1788:function(){var t;jQuery,t=acf.Field.extend({type:"true_false",events:{"change .acf-switch-input":"onChange","focus .acf-switch-input":"onFocus","blur .acf-switch-input":"onBlur","keypress .acf-switch-input":"onKeypress"},$input:function(){return this.$('input[type="checkbox"]')},$switch:function(){return this.$(".acf-switch")},getValue:function(){return this.$input().prop("checked")?1:0},initialize:function(){this.render()},render:function(){var t=this.$switch();if(t.length){var e=t.children(".acf-switch-on"),i=t.children(".acf-switch-off"),a=Math.max(e.width(),i.width());a&&(e.css("min-width",a),i.css("min-width",a))}},switchOn:function(){this.$input().prop("checked",!0),this.$switch().addClass("-on")},switchOff:function(){this.$input().prop("checked",!1),this.$switch().removeClass("-on")},onChange:function(t,e){e.prop("checked")?this.switchOn():this.switchOff()},onFocus:function(t,e){this.$switch().addClass("-focus")},onBlur:function(t,e){this.$switch().removeClass("-focus")},onKeypress:function(t,e){return 37===t.keyCode?this.switchOff():39===t.keyCode?this.switchOn():void 0}}),acf.registerFieldType(t)},4429:function(){var t;jQuery,t=acf.Field.extend({type:"url",events:{'keyup input[type="url"]':"onkeyup"},$control:function(){return this.$(".acf-input-wrap")},$input:function(){return this.$('input[type="url"]')},initialize:function(){this.render()},isValid:function(){var t=this.val();return!!t&&(-1!==t.indexOf("://")||0===t.indexOf("//"))},render:function(){this.isValid()?this.$control().addClass("-valid"):this.$control().removeClass("-valid")},onkeyup:function(t,e){this.render()}}),acf.registerFieldType(t)},7790:function(){var t;jQuery,t=acf.models.SelectField.extend({type:"user"}),acf.registerFieldType(t),acf.addFilter("select2_ajax_data",(function(t,e,i,a,n){if(!a)return t;const s=a.get("queryNonce");return s&&s.length&&(t.user_query_nonce=s),t}))},4850:function(){var t;jQuery,t=acf.Field.extend({type:"wysiwyg",wait:"load",events:{"mousedown .acf-editor-wrap.delay":"onMousedown",unmountField:"disableEditor",remountField:"enableEditor",removeField:"disableEditor"},$control:function(){return this.$(".acf-editor-wrap")},$input:function(){return this.$("textarea")},getMode:function(){return this.$control().hasClass("tmce-active")?"visual":"text"},initialize:function(){this.$control().hasClass("delay")||this.initializeEditor()},initializeEditor:function(){var t=this.$control(),e=this.$input(),i={tinymce:!0,quicktags:!0,toolbar:this.get("toolbar"),mode:this.getMode(),field:this},a=e.attr("id"),n=acf.uniqueId("acf-editor-"),s=e.data(),o=e.val();acf.rename({target:t,search:a,replace:n,destructive:!0}),this.set("id",n,!0),this.$input().data(s).val(o),acf.tinymce.initialize(n,i)},onMousedown:function(t){t.preventDefault();var e=this.$control();e.removeClass("delay"),e.find(".acf-editor-toolbar").remove(),this.initializeEditor()},enableEditor:function(){"visual"==this.getMode()&&acf.tinymce.enable(this.get("id"))},disableEditor:function(){acf.tinymce.destroy(this.get("id"))}}),acf.registerFieldType(t)},6291:function(){!function(t,e){var i=[];acf.Field=acf.Model.extend({type:"",eventScope:".acf-field",wait:"ready",setup:function(t){this.$el=t,this.inherit(t),this.inherit(this.$control())},val:function(t){return t!==e?this.setValue(t):this.prop("disabled")?null:this.getValue()},getValue:function(){return this.$input().val()},setValue:function(t){return acf.val(this.$input(),t)},__:function(t){return acf._e(this.type,t)},$control:function(){return!1},$input:function(){return this.$("[name]:first")},$inputWrap:function(){return this.$(".acf-input:first")},$labelWrap:function(){return this.$(".acf-label:first")},getInputName:function(){return this.$input().attr("name")||""},parent:function(){var t=this.parents();return!!t.length&&t[0]},parents:function(){var t=this.$el.parents(".acf-field");return acf.getFields(t)},show:function(t,e){var i=acf.show(this.$el,t);return i&&(this.prop("hidden",!1),acf.doAction("show_field",this,e)),i},hide:function(t,e){var i=acf.hide(this.$el,t);return i&&(this.prop("hidden",!0),acf.doAction("hide_field",this,e)),i},enable:function(t,e){var i=acf.enable(this.$el,t);return i&&(this.prop("disabled",!1),acf.doAction("enable_field",this,e)),i},disable:function(t,e){var i=acf.disable(this.$el,t);return i&&(this.prop("disabled",!0),acf.doAction("disable_field",this,e)),i},showEnable:function(t,e){return this.enable.apply(this,arguments),this.show.apply(this,arguments)},hideDisable:function(t,e){return this.disable.apply(this,arguments),this.hide.apply(this,arguments)},showNotice:function(t){"object"!=typeof t&&(t={text:t}),this.notice&&this.notice.remove(),t.target=this.$inputWrap(),this.notice=acf.newNotice(t)},removeNotice:function(t){this.notice&&(this.notice.away(t||0),this.notice=!1)},showError:function(i){this.$el.addClass("acf-error"),i!==e&&this.showNotice({text:i,type:"error",dismiss:!1}),acf.doAction("invalid_field",this),this.$el.one("focus change","input, select, textarea",t.proxy(this.removeError,this))},removeError:function(){this.$el.removeClass("acf-error"),this.removeNotice(250),acf.doAction("valid_field",this)},trigger:function(t,e,i){return"invalidField"==t&&(i=!0),acf.Model.prototype.trigger.apply(this,[t,e,i])}}),acf.newField=function(t){var e=t.data("type"),i=a(e),n=new(acf.models[i]||acf.Field)(t);return acf.doAction("new_field",n),n};var a=function(t){return acf.strPascalCase(t||"")+"Field"};acf.registerFieldType=function(t){var e=t.prototype.type,n=a(e);acf.models[n]=t,i.push(e)},acf.getFieldType=function(t){var e=a(t);return acf.models[e]||!1},acf.getFieldTypes=function(t){t=acf.parseArgs(t,{category:""});var e=[];return i.map((function(i){var a=acf.getFieldType(i),n=a.prototype;t.category&&n.category!==t.category||e.push(a)})),e}}(jQuery)},1580:function(){!function(t,e){acf.findFields=function(e){var i=".acf-field",a=!1;return(e=acf.parseArgs(e,{key:"",name:"",type:"",is:"",parent:!1,sibling:!1,limit:!1,visible:!1,suppressFilters:!1,excludeSubFields:!1})).suppressFilters||(e=acf.applyFilters("find_fields_args",e)),e.key&&(i+='[data-key="'+e.key+'"]'),e.type&&(i+='[data-type="'+e.type+'"]'),e.name&&(i+='[data-name="'+e.name+'"]'),e.is&&(i+=e.is),e.visible&&(i+=":visible"),e.suppressFilters||(i=acf.applyFilters("find_fields_selector",i,e)),e.parent?(a=e.parent.find(i),e.excludeSubFields&&(a=a.not(e.parent.find(".acf-is-subfields .acf-field")))):a=e.sibling?e.sibling.siblings(i):t(i),e.suppressFilters||(a=a.not(".acf-clone .acf-field"),a=acf.applyFilters("find_fields",a)),e.limit&&(a=a.slice(0,e.limit)),a},acf.findField=function(t,e){return acf.findFields({key:t,limit:1,parent:e,suppressFilters:!0})},acf.getField=function(t){t instanceof jQuery||(t=acf.findField(t));var e=t.data("acf");return e||(e=acf.newField(t)),e},acf.getFields=function(e){e instanceof jQuery||(e=acf.findFields(e));var i=[];return e.each((function(){var e=acf.getField(t(this));i.push(e)})),i},acf.findClosestField=function(t){return t.closest(".acf-field")},acf.getClosestField=function(t){var e=acf.findClosestField(t);return this.getField(e)};var i=function(t){var e=t+"_field",i=t+"Field";acf.addAction(e,(function(n){var s=acf.arrayArgs(arguments),o=s.slice(1);["type","name","key"].map((function(t){var i="/"+t+"="+n.get(t);s=[e+i,n].concat(o),acf.doAction.apply(null,s)})),a.indexOf(t)>-1&&n.trigger(i,o)}))},a=["remove","unmount","remount","sortstart","sortstop","show","hide","unload","valid","invalid","enable","disable","duplicate"];["prepare","ready","load","append","remove","unmount","remount","sortstart","sortstop","show","hide","unload"].map((function(t){var e=t,a=t+"_fields",n=t+"_field";acf.addAction(e,(function(t){var e=acf.arrayArgs(arguments).slice(1),i=acf.getFields({parent:t});if(i.length){var n=[a,i].concat(e);acf.doAction.apply(null,n)}})),acf.addAction(a,(function(t){var e=acf.arrayArgs(arguments).slice(1);t.map((function(t,i){var a=[n,t].concat(e);acf.doAction.apply(null,a)}))})),i(t)})),["valid","invalid","enable","disable","new","duplicate"].map(i),new acf.Model({id:"fieldsEventManager",events:{'click .acf-field a[href="#"]':"onClick","change .acf-field":"onChange"},onClick:function(t){t.preventDefault()},onChange:function(){t("#_acf_changed").val(1)}}),new acf.Model({id:"duplicateFieldsManager",actions:{duplicate:"onDuplicate",duplicate_fields:"onDuplicateFields"},onDuplicate:function(t,e){var i=acf.getFields({parent:t});if(i.length){var a=acf.findFields({parent:e});acf.doAction("duplicate_fields",i,a)}},onDuplicateFields:function(e,i){e.map((function(e,a){acf.doAction("duplicate_field",e,t(i[a]))}))}})}(jQuery)},5938:function(){var t;t=jQuery,new acf.Model({priority:90,actions:{new_field:"refresh",show_field:"refresh",hide_field:"refresh",remove_field:"refresh",unmount_field:"refresh",remount_field:"refresh"},refresh:function(){acf.refresh()}}),new acf.Model({priority:1,actions:{sortstart:"onSortstart",sortstop:"onSortstop"},onSortstart:function(t){acf.doAction("unmount",t)},onSortstop:function(t){acf.doAction("remount",t)}}),new acf.Model({actions:{sortstart:"onSortstart"},onSortstart:function(e,i){e.is("tr")&&(i.html('<td style="padding:0;" colspan="'+i.children().length+'"></td>'),e.addClass("acf-sortable-tr-helper"),e.children().each((function(){t(this).width(t(this).width())})),i.height(e.height()+"px"),e.removeClass("acf-sortable-tr-helper"))}}),new acf.Model({actions:{after_duplicate:"onAfterDuplicate"},onAfterDuplicate:function(e,i){var a=[];e.find("select").each((function(e){a.push(t(this).val())})),i.find("select").each((function(e){t(this).val(a[e])}))}}),new acf.Model({id:"tableHelper",priority:20,actions:{refresh:"renderTables"},renderTables:function(e){var i=this;t(".acf-table:visible").each((function(){i.renderTable(t(this))}))},renderTable:function(e){var i=e.find("> thead > tr:visible > th[data-key]"),a=e.find("> tbody > tr:visible > td[data-key]");if(!i.length||!a.length)return!1;i.each((function(e){var i=t(this),n=i.data("key"),s=a.filter('[data-key="'+n+'"]'),o=s.filter(".acf-hidden");s.removeClass("acf-empty"),s.length===o.length?acf.hide(i):(acf.show(i),o.addClass("acf-empty"))})),i.css("width","auto"),i=i.not(".acf-hidden");var n=100;i.length,i.filter("[data-width]").each((function(){var e=t(this).data("width");t(this).css("width",e+"%"),n-=e}));var s=i.not("[data-width]");if(s.length){var o=n/s.length;s.css("width",o+"%"),n=0}n>0&&i.last().css("width","auto"),a.filter(".-collapsed-target").each((function(){var e=t(this);e.parent().hasClass("-collapsed")?e.attr("colspan",i.length):e.removeAttr("colspan")}))}}),new acf.Model({id:"fieldsHelper",priority:30,actions:{refresh:"renderGroups"},renderGroups:function(){var e=this;t(".acf-fields:visible").each((function(){e.renderGroup(t(this))}))},renderGroup:function(e){var i=0,a=0,n=t(),s=e.children(".acf-field[data-width]:visible");return!!s.length&&(e.hasClass("-left")?(s.removeAttr("data-width"),s.css("width","auto"),!1):(s.removeClass("-r0 -c0").css({"min-height":0}),s.each((function(e){var s=t(this),o=s.position(),r=Math.ceil(o.top),c=Math.ceil(o.left);n.length&&r>i&&(n.css({"min-height":a+"px"}),o=s.position(),r=Math.ceil(o.top),c=Math.ceil(o.left),i=0,a=0,n=t()),acf.get("rtl")&&(c=Math.ceil(s.parent().width()-(o.left+s.outerWidth()))),0==r?s.addClass("-r0"):0==c&&s.addClass("-c0");var l=Math.ceil(s.outerHeight())+1;a=Math.max(a,l),i=Math.max(i,r),n=n.add(s)})),void(n.length&&n.css({"min-height":a+"px"}))))}}),new acf.Model({id:"bodyClassShiftHelper",events:{keydown:"onKeyDown",keyup:"onKeyUp"},isShiftKey:function(t){return 16===t.keyCode},onKeyDown:function(e){this.isShiftKey(e)&&t("body").addClass("acf-keydown-shift")},onKeyUp:function(e){this.isShiftKey(e)&&t("body").removeClass("acf-keydown-shift")}})},3812:function(){!function(t,e){acf.newMediaPopup=function(t){var e=null;return t=acf.parseArgs(t,{mode:"select",title:"",button:"",type:"",field:!1,allowedTypes:"",library:"all",multiple:!1,attachment:0,autoOpen:!0,open:function(){},select:function(){},close:function(){}}),e="edit"==t.mode?new acf.models.EditMediaPopup(t):new acf.models.SelectMediaPopup(t),t.autoOpen&&setTimeout((function(){e.open()}),1),acf.doAction("new_media_popup",e),e};var i=function(){var t=acf.get("post_id");return acf.isNumeric(t)?t:0};acf.getMimeTypes=function(){return this.get("mimeTypes")},acf.getMimeType=function(t){var e=acf.getMimeTypes();if(void 0!==e[t])return e[t];for(var i in e)if(-1!==i.indexOf(t))return e[i];return!1};var a=acf.Model.extend({id:"MediaPopup",data:{},defaults:{},frame:!1,setup:function(e){t.extend(this.data,e)},initialize:function(){var t=this.getFrameOptions();this.addFrameStates(t);var e=wp.media(t);e.acf=this,this.addFrameEvents(e,t),this.frame=e},open:function(){this.frame.open()},close:function(){this.frame.close()},remove:function(){this.frame.detach(),this.frame.remove()},getFrameOptions:function(){var t={title:this.get("title"),multiple:this.get("multiple"),library:{},states:[]};return this.get("type")&&(t.library.type=this.get("type")),"uploadedTo"===this.get("library")&&(t.library.uploadedTo=i()),this.get("attachment")&&(t.library.post__in=[this.get("attachment")]),this.get("button")&&(t.button={text:this.get("button")}),t},addFrameStates:function(t){var e=wp.media.query(t.library);this.get("field")&&acf.isset(e,"mirroring","args")&&(e.mirroring.args._acfuploader=this.get("field")),t.states.push(new wp.media.controller.Library({library:e,multiple:this.get("multiple"),title:this.get("title"),priority:20,filterable:"all",editable:!0,allowLocalEdits:!0})),acf.isset(wp,"media","controller","EditImage")&&t.states.push(new wp.media.controller.EditImage)},addFrameEvents:function(t,e){t.on("open",(function(){this.$el.closest(".media-modal").addClass("acf-media-modal -"+this.acf.get("mode"))}),t),t.on("content:render:edit-image",(function(){var t=this.state().get("image"),e=new wp.media.view.EditImage({model:t,controller:this}).render();this.content.set(e),e.loadEditor()}),t),t.on("select",(function(){var e=t.state().get("selection");e&&e.each((function(e,i){t.acf.get("select").apply(t.acf,[e,i])}))})),t.on("close",(function(){setTimeout((function(){t.acf.get("close").apply(t.acf),t.acf.remove()}),1)}))}});acf.models.SelectMediaPopup=a.extend({id:"SelectMediaPopup",setup:function(t){t.button||(t.button=acf._x("Select","verb")),a.prototype.setup.apply(this,arguments)},addFrameEvents:function(t,e){acf.isset(_wpPluploadSettings,"defaults","multipart_params")&&(_wpPluploadSettings.defaults.multipart_params._acfuploader=this.get("field"),t.on("open",(function(){delete _wpPluploadSettings.defaults.multipart_params._acfuploader}))),t.on("content:activate:browse",(function(){var e=!1;try{e=t.content.get().toolbar}catch(t){return void console.log(t)}t.acf.customizeFilters.apply(t.acf,[e])})),a.prototype.addFrameEvents.apply(this,arguments)},customizeFilters:function(e){var i=e.get("filters");if("image"==this.get("type")&&(i.filters.all.text=acf.__("All images"),delete i.filters.audio,delete i.filters.video,delete i.filters.image,t.each(i.filters,(function(t,e){e.props.type=e.props.type||"image"}))),this.get("allowedTypes")&&this.get("allowedTypes").split(" ").join("").split(".").join("").split(",").map((function(t){var e=acf.getMimeType(t);if(e){var a={text:e,props:{status:null,type:e,uploadedTo:null,orderby:"date",order:"DESC"},priority:20};i.filters[e]=a}})),"uploadedTo"===this.get("library")){var a=this.frame.options.library.uploadedTo;delete i.filters.unattached,delete i.filters.uploaded,t.each(i.filters,(function(t,e){e.text+=" ("+acf.__("Uploaded to this post")+")",e.props.uploadedTo=a}))}var n=this.get("field");t.each(i.filters,(function(t,e){e.props._acfuploader=n})),e.get("search").model.attributes._acfuploader=n,i.renderFilters&&i.renderFilters()}}),acf.models.EditMediaPopup=a.extend({id:"SelectMediaPopup",setup:function(t){t.button||(t.button=acf._x("Update","verb")),a.prototype.setup.apply(this,arguments)},addFrameEvents:function(t,e){t.on("open",(function(){this.$el.closest(".media-modal").addClass("acf-expanded"),"browse"!=this.content.mode()&&this.content.mode("browse");var e=this.state().get("selection"),i=wp.media.attachment(t.acf.get("attachment"));e.add(i)}),t),a.prototype.addFrameEvents.apply(this,arguments)}}),new acf.Model({id:"customizePrototypes",wait:"ready",initialize:function(){if(acf.isset(window,"wp","media","view")){var t=i();t&&acf.isset(wp,"media","view","settings","post")&&(wp.media.view.settings.post.id=t),this.customizeAttachmentsButton(),this.customizeAttachmentsRouter(),this.customizeAttachmentFilters(),this.customizeAttachmentCompat(),this.customizeAttachmentLibrary()}},customizeAttachmentsButton:function(){if(acf.isset(wp,"media","view","Button")){var t=wp.media.view.Button;wp.media.view.Button=t.extend({initialize:function(){var t=_.defaults(this.options,this.defaults);this.model=new Backbone.Model(t),this.listenTo(this.model,"change",this.render)}})}},customizeAttachmentsRouter:function(){if(acf.isset(wp,"media","view","Router")){var e=wp.media.view.Router;wp.media.view.Router=e.extend({addExpand:function(){var e=t(['<a href="#" class="acf-expand-details">','<span class="is-closed"><i class="acf-icon -left -small"></i>'+acf.__("Expand Details")+"</span>",'<span class="is-open"><i class="acf-icon -right -small"></i>'+acf.__("Collapse Details")+"</span>","</a>"].join(""));e.on("click",(function(e){e.preventDefault();var i=t(this).closest(".media-modal");i.hasClass("acf-expanded")?i.removeClass("acf-expanded"):i.addClass("acf-expanded")})),this.$el.append(e)},initialize:function(){return e.prototype.initialize.apply(this,arguments),this.addExpand(),this}})}},customizeAttachmentFilters:function(){acf.isset(wp,"media","view","AttachmentFilters","All")&&(wp.media.view.AttachmentFilters.All.prototype.renderFilters=function(){this.$el.html(_.chain(this.filters).map((function(e,i){return{el:t("<option></option>").val(i).html(e.text)[0],priority:e.priority||50}}),this).sortBy("priority").pluck("el").value())})},customizeAttachmentCompat:function(){if(acf.isset(wp,"media","view","AttachmentCompat")){var e=wp.media.view.AttachmentCompat,i=!1;wp.media.view.AttachmentCompat=e.extend({render:function(){return this.rendered?this:(e.prototype.render.apply(this,arguments),this.$("#acf-form-data").length?(clearTimeout(i),i=setTimeout(t.proxy((function(){this.rendered=!0,acf.doAction("append",this.$el)}),this),50),this):this)},save:function(t){var e;t&&t.preventDefault(),e=acf.serializeForAjax(this.$el),this.controller.trigger("attachment:compat:waiting",["waiting"]),this.model.saveCompat(e).always(_.bind(this.postSave,this))}})}},customizeAttachmentLibrary:function(){if(acf.isset(wp,"media","view","Attachment","Library")){var t=wp.media.view.Attachment.Library;wp.media.view.Attachment.Library=t.extend({render:function(){var e=acf.isget(this,"controller","acf"),i=acf.isget(this,"model","attributes");if(e&&i){i.acf_errors&&this.$el.addClass("acf-disabled");var a=e.get("selected");a&&a.indexOf(i.id)>-1&&this.$el.addClass("acf-selected")}return t.prototype.render.apply(this,arguments)},toggleSelection:function(e){this.collection;var i=this.options.selection,a=this.model,n=(i.single(),this.controller),s=acf.isget(this,"model","attributes","acf_errors"),o=n.$el.find(".media-frame-content .media-sidebar");if(o.children(".acf-selection-error").remove(),o.children().removeClass("acf-hidden"),n&&s){var r=acf.isget(this,"model","attributes","filename");return o.children().addClass("acf-hidden"),o.prepend(['<div class="acf-selection-error">','<span class="selection-error-label">'+acf.__("Restricted")+"</span>",'<span class="selection-error-filename">'+r+"</span>",'<span class="selection-error-message">'+s+"</span>","</div>"].join("")),i.reset(),void i.single(a)}return t.prototype.toggleSelection.apply(this,arguments)}})}}})}(jQuery)},1128:function(){var t;t=jQuery,new acf.Model({wait:"prepare",priority:1,initialize:function(){(acf.get("postboxes")||[]).map(acf.newPostbox)}}),acf.getPostbox=function(e){return"string"==typeof arguments[0]&&(e=t("#"+arguments[0])),acf.getInstance(e)},acf.getPostboxes=function(){return acf.getInstances(t(".acf-postbox"))},acf.newPostbox=function(t){return new acf.models.Postbox(t)},acf.models.Postbox=acf.Model.extend({data:{id:"",key:"",style:"default",label:"top",edit:""},setup:function(e){e.editLink&&(e.edit=e.editLink),t.extend(this.data,e),this.$el=this.$postbox()},$postbox:function(){return t("#"+this.get("id"))},$hide:function(){return t("#"+this.get("id")+"-hide")},$hideLabel:function(){return this.$hide().parent()},$hndle:function(){return this.$("> .hndle")},$handleActions:function(){return this.$("> .postbox-header .handle-actions")},$inside:function(){return this.$("> .inside")},isVisible:function(){return this.$el.hasClass("acf-hidden")},isHiddenByScreenOptions:function(){return this.$el.hasClass("hide-if-js")||"none"==this.$el.css("display")},initialize:function(){if(this.$el.addClass("acf-postbox"),"block"!==acf.get("editor")){var t=this.get("style");"default"!==t&&this.$el.addClass(t)}this.$inside().addClass("acf-fields").addClass("-"+this.get("label"));var e=this.get("edit");if(e){var i='<a href="'+e+'" class="dashicons dashicons-admin-generic acf-hndle-cog acf-js-tooltip" title="'+acf.__("Edit field group")+'"></a>',a=this.$handleActions();a.length?a.prepend(i):this.$hndle().append(i)}this.show()},show:function(){this.$el.hasClass("hide-if-js")?this.$hide().prop("checked",!1):(this.$hideLabel().show(),this.$hide().prop("checked",!0),this.$el.show().removeClass("acf-hidden"),acf.doAction("show_postbox",this))},enable:function(){acf.enable(this.$el,"postbox")},showEnable:function(){this.enable(),this.show()},hide:function(){this.$hideLabel().hide(),this.$el.hide().addClass("acf-hidden"),acf.doAction("hide_postbox",this)},disable:function(){acf.disable(this.$el,"postbox")},hideDisable:function(){this.disable(),this.hide()},html:function(t){this.$inside().html(t),acf.doAction("append",this.$el)}})},7240:function(){var t;t=jQuery,acf.screen=new acf.Model({active:!0,xhr:!1,timeout:!1,wait:"load",events:{"change #page_template":"onChange","change #parent_id":"onChange","change #post-formats-select":"onChange","change .categorychecklist":"onChange","change .tagsdiv":"onChange",'change .acf-taxonomy-field[data-save="1"]':"onChange","change #product-type":"onChange"},isPost:function(){return"post"===acf.get("screen")},isUser:function(){return"user"===acf.get("screen")},isTaxonomy:function(){return"taxonomy"===acf.get("screen")},isAttachment:function(){return"attachment"===acf.get("screen")},isNavMenu:function(){return"nav_menu"===acf.get("screen")},isWidget:function(){return"widget"===acf.get("screen")},isComment:function(){return"comment"===acf.get("screen")},getPageTemplate:function(){var e=t("#page_template");return e.length?e.val():null},getPageParent:function(e,i){return(i=t("#parent_id")).length?i.val():null},getPageType:function(t,e){return this.getPageParent()?"child":"parent"},getPostType:function(){return t("#post_type").val()},getPostFormat:function(e,i){if((i=t("#post-formats-select input:checked")).length){var a=i.val();return"0"==a?"standard":a}return null},getPostCoreTerms:function(){var e={},i=acf.serialize(t(".categorydiv, .tagsdiv"));for(var a in i.tax_input&&(e=i.tax_input),i.post_category&&(e.category=i.post_category),e)acf.isArray(e[a])||(e[a]=e[a].split(/,[\s]?/));return e},getPostTerms:function(){var t=this.getPostCoreTerms();for(var e in acf.getFields({type:"taxonomy"}).map((function(e){if(e.get("save")){var i=e.val(),a=e.get("taxonomy");i&&(t[a]=t[a]||[],i=acf.isArray(i)?i:[i],t[a]=t[a].concat(i))}})),null!==(productType=this.getProductType())&&(t.product_type=[productType]),t)t[e]=acf.uniqueArray(t[e]);return t},getProductType:function(){var e=t("#product-type");return e.length?e.val():null},check:function(){if("post"===acf.get("screen")){this.xhr&&this.xhr.abort();var e=acf.parseArgs(this.data,{action:"acf/ajax/check_screen",screen:acf.get("screen"),exists:[]});this.isPost()&&(e.post_id=acf.get("post_id")),null!==(postType=this.getPostType())&&(e.post_type=postType),null!==(pageTemplate=this.getPageTemplate())&&(e.page_template=pageTemplate),null!==(pageParent=this.getPageParent())&&(e.page_parent=pageParent),null!==(pageType=this.getPageType())&&(e.page_type=pageType),null!==(postFormat=this.getPostFormat())&&(e.post_format=postFormat),null!==(postTerms=this.getPostTerms())&&(e.post_terms=postTerms),acf.getPostboxes().map((function(t){e.exists.push(t.get("key"))})),e=acf.applyFilters("check_screen_args",e),this.xhr=t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(e),type:"post",dataType:"json",context:this,success:function(t){"post"==acf.get("screen")?this.renderPostScreen(t):"user"==acf.get("screen")&&this.renderUserScreen(t),acf.doAction("check_screen_complete",t,e)}})}},onChange:function(t,e){this.setTimeout(this.check,1)},renderPostScreen:function(e){var i=function(e,i){var a=t._data(e[0]).events;for(var n in a)for(var s=0;s<a[n].length;s++)i.on(n,a[n][s].handler)},a=function(e,i){var a=i.indexOf(e);if(-1==a)return!1;for(var n=a-1;n>=0;n--)if(t("#"+i[n]).length)return t("#"+i[n]).after(t("#"+e));for(n=a+1;n<i.length;n++)if(t("#"+i[n]).length)return t("#"+i[n]).before(t("#"+e));return!1};e.visible=[],e.hidden=[],e.results=e.results.map((function(n,s){var o=acf.getPostbox(n.id);if(acf.isGutenberg()&&"acf_after_title"==n.position&&(n.position="normal"),!o){if(parseFloat(acf.get("wp_version"))>=5.5)var r=['<div class="postbox-header">','<h2 class="hndle ui-sortable-handle">',"<span>"+acf.escHtml(n.title)+"</span>","</h2>",'<div class="handle-actions hide-if-no-js">','<button type="button" class="handlediv" aria-expanded="true">','<span class="screen-reader-text">Toggle panel: '+acf.escHtml(n.title)+"</span>",'<span class="toggle-indicator" aria-hidden="true"></span>',"</button>","</div>","</div>"].join("");else r=['<button type="button" class="handlediv" aria-expanded="true">','<span class="screen-reader-text">Toggle panel: '+acf.escHtml(n.title)+"</span>",'<span class="toggle-indicator" aria-hidden="true"></span>',"</button>",'<h2 class="hndle ui-sortable-handle">',"<span>"+acf.escHtml(n.title)+"</span>","</h2>"].join("");n.classes||(n.classes="");var c=t(['<div id="'+n.id+'" class="postbox '+n.classes+'">',r,'<div class="inside">',n.html,"</div>","</div>"].join(""));if(t("#adv-settings").length){var l=t("#adv-settings .metabox-prefs"),d=t(['<label for="'+n.id+'-hide">','<input class="hide-postbox-tog" name="'+n.id+'-hide" type="checkbox" id="'+n.id+'-hide" value="'+n.id+'" checked="checked">'," "+n.title,"</label>"].join(""));i(l.find("input").first(),d.find("input")),l.append(d)}t(".postbox").length&&(i(t(".postbox .handlediv").first(),c.children(".handlediv")),i(t(".postbox .hndle").first(),c.children(".hndle"))),"side"===n.position?t("#"+n.position+"-sortables").append(c):t("#"+n.position+"-sortables").prepend(c);var u=[];if(e.results.map((function(e){n.position===e.position&&t("#"+n.position+"-sortables #"+e.id).length&&u.push(e.id)})),a(n.id,u),e.sorted)for(var f in e.sorted){let t=e.sorted[f];if("string"==typeof t&&(t=t.split(","),a(n.id,t)))break}o=acf.newPostbox(n),acf.doAction("append",c),acf.doAction("append_postbox",o)}return o.showEnable(),e.visible.push(n.id),n})),acf.getPostboxes().map((function(t){-1===e.visible.indexOf(t.get("id"))&&(t.hideDisable(),e.hidden.push(t.get("id")))})),t("#acf-style").html(e.style),acf.doAction("refresh_post_screen",e)},renderUserScreen:function(t){}}),new acf.Model({postEdits:{},wait:"prepare",initialize:function(){acf.isGutenberg()&&(wp.data.subscribe(acf.debounce(this.onChange).bind(this)),acf.screen.getPageTemplate=this.getPageTemplate,acf.screen.getPageParent=this.getPageParent,acf.screen.getPostType=this.getPostType,acf.screen.getPostFormat=this.getPostFormat,acf.screen.getPostCoreTerms=this.getPostCoreTerms,acf.unload.disable(),parseFloat(acf.get("wp_version"))>=5.3&&this.addAction("refresh_post_screen",this.onRefreshPostScreen),wp.domReady(acf.refresh))},onChange:function(){var t=["template","parent","format"];(wp.data.select("core").getTaxonomies()||[]).map((function(e){t.push(e.rest_base)}));var e=wp.data.select("core/editor").getPostEdits(),i={};t.map((function(t){void 0!==e[t]&&(i[t]=e[t])})),JSON.stringify(i)!==JSON.stringify(this.postEdits)&&(this.postEdits=i,acf.screen.check())},getPageTemplate:function(){return wp.data.select("core/editor").getEditedPostAttribute("template")},getPageParent:function(t,e){return wp.data.select("core/editor").getEditedPostAttribute("parent")},getPostType:function(){return wp.data.select("core/editor").getEditedPostAttribute("type")},getPostFormat:function(t,e){return wp.data.select("core/editor").getEditedPostAttribute("format")},getPostCoreTerms:function(){var t={};return(wp.data.select("core").getTaxonomies()||[]).map((function(e){var i=wp.data.select("core/editor").getEditedPostAttribute(e.rest_base);i&&(t[e.slug]=i)})),t},onRefreshPostScreen:function(t){var e=wp.data.select("core/edit-post"),i=wp.data.dispatch("core/edit-post"),a={};e.getActiveMetaBoxLocations().map((function(t){a[t]=e.getMetaBoxesPerLocation(t)}));var n=[];for(var s in a)a[s].map((function(t){n.push(t.id)}));for(var s in t.results.filter((function(t){return-1===n.indexOf(t.id)})).map((function(t,e){var i=t.position;a[i]=a[i]||[],a[i].push({id:t.id,title:t.title})})),a)a[s]=a[s].filter((function(e){return-1===t.hidden.indexOf(e.id)}));i.setAvailableMetaBoxesPerLocation(a)}})},5796:function(){!function(t,e){function i(){return acf.isset(window,"jQuery","fn","select2","amd")?4:!!acf.isset(window,"Select2")&&3}acf.newSelect2=function(t,e){if(e=acf.parseArgs(e,{allowNull:!1,placeholder:"",multiple:!1,field:!1,ajax:!1,ajaxAction:"",ajaxData:function(t){return t},ajaxResults:function(t){return t},templateSelection:!1,templateResult:!1,dropdownCssClass:"",suppressFilters:!1}),4==i())var a=new n(t,e);else a=new s(t,e);return acf.doAction("new_select2",a),a};var a=acf.Model.extend({setup:function(e,i){t.extend(this.data,i),this.$el=e},initialize:function(){},selectOption:function(t){var e=this.getOption(t);e.prop("selected")||e.prop("selected",!0).trigger("change")},unselectOption:function(t){var e=this.getOption(t);e.prop("selected")&&e.prop("selected",!1).trigger("change")},getOption:function(t){return this.$('option[value="'+t+'"]')},addOption:function(e){e=acf.parseArgs(e,{id:"",text:"",selected:!1});var i=this.getOption(e.id);return i.length||((i=t("<option></option>")).html(e.text),i.attr("value",e.id),i.prop("selected",e.selected),this.$el.append(i)),i},getValue:function(){var e=[],i=this.$el.find("option:selected");return i.exists()?((i=i.sort((function(t,e){return+t.getAttribute("data-i")-+e.getAttribute("data-i")}))).each((function(){var i=t(this);e.push({$el:i,id:i.attr("value"),text:i.text()})})),e):e},mergeOptions:function(){},getChoices:function(){var e=function(i){var a=[];return i.children().each((function(){var i=t(this);i.is("optgroup")?a.push({text:i.attr("label"),children:e(i)}):a.push({id:i.attr("value"),text:i.text()})})),a};return e(this.$el)},getAjaxData:function(t){var e={action:this.get("ajaxAction"),s:t.term||"",paged:t.page||1},i=this.get("field");i&&(e.field_key=i.get("key"));var a=this.get("ajaxData");return a&&(e=a.apply(this,[e,t])),e=acf.applyFilters("select2_ajax_data",e,this.data,this.$el,i||!1,this),acf.prepareForAjax(e)},getAjaxResults:function(t,e){t=acf.parseArgs(t,{results:!1,more:!1});var i=this.get("ajaxResults");return i&&(t=i.apply(this,[t,e])),acf.applyFilters("select2_ajax_results",t,e,this)},processAjaxResults:function(e,i){return(e=this.getAjaxResults(e,i)).more&&(e.pagination={more:!0}),setTimeout(t.proxy(this.mergeOptions,this),1),e},destroy:function(){this.$el.data("select2")&&this.$el.select2("destroy"),this.$el.siblings(".select2-container").remove()}}),n=a.extend({initialize:function(){var i=this.$el,a={width:"100%",allowClear:this.get("allowNull"),placeholder:this.get("placeholder"),multiple:this.get("multiple"),templateSelection:this.get("templateSelection"),templateResult:this.get("templateResult"),dropdownCssClass:this.get("dropdownCssClass"),suppressFilters:this.get("suppressFilters"),data:[],escapeMarkup:function(t){return"string"!=typeof t?t:acf.escHtml(t)}};a.templateSelection||delete a.templateSelection,a.templateResult||delete a.templateResult,a.dropdownCssClass||delete a.dropdownCssClass,acf.isset(window,"jQuery","fn","selectWoo")?(delete a.templateSelection,delete a.templateResult):a.templateSelection||(a.templateSelection=function(e){var i=t('<span class="acf-selection"></span>');return i.html(acf.escHtml(e.text)),i.data("element",e.element),i}),a.multiple&&this.getValue().map((function(t){t.$el.detach().appendTo(i)}));var n=i.attr("data-ajax");if(n!==e&&(i.removeData("ajax"),i.removeAttr("data-ajax")),this.get("ajax")&&(a.ajax={url:acf.get("ajaxurl"),delay:250,dataType:"json",type:"post",cache:!1,data:t.proxy(this.getAjaxData,this),processResults:t.proxy(this.processAjaxResults,this)}),!a.suppressFilters){var s=this.get("field");a=acf.applyFilters("select2_args",a,i,this.data,s||!1,this)}i.select2(a);var o=i.next(".select2-container");if(a.multiple){var r=o.find("ul");r.sortable({stop:function(e){r.find(".select2-selection__choice").each((function(){if(t(this).data("data"))var e=t(t(this).data("data").element);else e=t(t(this).find("span.acf-selection").data("element"));e.detach().appendTo(i)})),i.trigger("change")}}),i.on("select2:select",this.proxy((function(t){this.getOption(t.params.data.id).detach().appendTo(this.$el)})))}i.on("select2:open",(()=>{t(".select2-container--open .select2-search__field").get(-1).focus()})),o.addClass("-acf"),n!==e&&i.attr("data-ajax",n),a.suppressFilters||acf.doAction("select2_init",i,a,this.data,s||!1,this)},mergeOptions:function(){var e=!1,i=!1;t('.select2-results__option[role="group"]').each((function(){var a=t(this).children("ul"),n=t(this).children("strong");if(i&&i.text()===n.text())return e.append(a.children()),void t(this).remove();e=a,i=n}))}}),s=a.extend({initialize:function(){var e=this.$el,i=this.getValue(),a=this.get("multiple"),n={width:"100%",allowClear:this.get("allowNull"),placeholder:this.get("placeholder"),separator:"||",multiple:this.get("multiple"),data:this.getChoices(),escapeMarkup:function(t){return acf.escHtml(t)},dropdownCss:{"z-index":"999999999"},initSelection:function(t,e){e(a?i:i.shift())}},s=e.siblings("input");s.length||(s=t('<input type="hidden" />'),e.before(s)),inputValue=i.map((function(t){return t.id})).join("||"),s.val(inputValue),n.multiple&&i.map((function(t){t.$el.detach().appendTo(e)})),n.allowClear&&(n.data=n.data.filter((function(t){return""!==t.id}))),e.removeData("ajax"),e.removeAttr("data-ajax"),this.get("ajax")&&(n.ajax={url:acf.get("ajaxurl"),quietMillis:250,dataType:"json",type:"post",cache:!1,data:t.proxy(this.getAjaxData,this),results:t.proxy(this.processAjaxResults,this)});var o=this.get("field");n=acf.applyFilters("select2_args",n,e,this.data,o||!1,this),s.select2(n);var r=s.select2("container"),c=t.proxy(this.getOption,this);if(n.multiple){var l=r.find("ul");l.sortable({stop:function(){l.find(".select2-search-choice").each((function(){var i=t(this).data("select2Data");c(i.id).detach().appendTo(e)})),e.trigger("change")}})}s.on("select2-selecting",(function(i){var a=i.choice,n=c(a.id);n.length||(n=t('<option value="'+a.id+'">'+a.text+"</option>")),n.detach().appendTo(e)})),r.addClass("-acf"),acf.doAction("select2_init",e,n,this.data,o||!1,this),s.on("change",(function(){var t=s.val();t.indexOf("||")&&(t=t.split("||")),e.val(t).trigger("change")})),e.hide()},mergeOptions:function(){var e=!1;t("#select2-drop .select2-result-with-children").each((function(){var i=t(this).children("ul"),a=t(this).children(".select2-result-label");if(e&&e.text()===a.text())return e.append(i.children()),void t(this).remove();e=a}))},getAjaxData:function(t,e){var i={term:t,page:e},n=this.get("field");return i=acf.applyFilters("select2_ajax_data",i,this.data,this.$el,n||!1,this),a.prototype.getAjaxData.apply(this,[i])}});new acf.Model({priority:5,wait:"prepare",actions:{duplicate:"onDuplicate"},initialize:function(){var t=acf.get("locale"),e=(acf.get("rtl"),acf.get("select2L10n")),a=i();return!!e&&0!==t.indexOf("en")&&void(4==a?this.addTranslations4():3==a&&this.addTranslations3())},addTranslations4:function(){var t=acf.get("select2L10n"),e=acf.get("locale");e=e.replace("_","-");var i={errorLoading:function(){return t.load_fail},inputTooLong:function(e){var i=e.input.length-e.maximum;return i>1?t.input_too_long_n.replace("%d",i):t.input_too_long_1},inputTooShort:function(e){var i=e.minimum-e.input.length;return i>1?t.input_too_short_n.replace("%d",i):t.input_too_short_1},loadingMore:function(){return t.load_more},maximumSelected:function(e){var i=e.maximum;return i>1?t.selection_too_long_n.replace("%d",i):t.selection_too_long_1},noResults:function(){return t.matches_0},searching:function(){return t.searching}};jQuery.fn.select2.amd.define("select2/i18n/"+e,[],(function(){return i}))},addTranslations3:function(){var e=acf.get("select2L10n"),i=acf.get("locale");i=i.replace("_","-");var a={formatMatches:function(t){return t>1?e.matches_n.replace("%d",t):e.matches_1},formatNoMatches:function(){return e.matches_0},formatAjaxError:function(){return e.load_fail},formatInputTooShort:function(t,i){var a=i-t.length;return a>1?e.input_too_short_n.replace("%d",a):e.input_too_short_1},formatInputTooLong:function(t,i){var a=t.length-i;return a>1?e.input_too_long_n.replace("%d",a):e.input_too_long_1},formatSelectionTooBig:function(t){return t>1?e.selection_too_long_n.replace("%d",t):e.selection_too_long_1},formatLoadMore:function(){return e.load_more},formatSearching:function(){return e.searching}};t.fn.select2.locales=t.fn.select2.locales||{},t.fn.select2.locales[i]=a,t.extend(t.fn.select2.defaults,a)},onDuplicate:function(t,e){e.find(".select2-container").remove()}})}(jQuery)},8061:function(){var t;t=jQuery,acf.tinymce={defaults:function(){return"undefined"!=typeof tinyMCEPreInit&&{tinymce:tinyMCEPreInit.mceInit.acf_content,quicktags:tinyMCEPreInit.qtInit.acf_content}},initialize:function(t,e){(e=acf.parseArgs(e,{tinymce:!0,quicktags:!0,toolbar:"full",mode:"visual",field:!1})).tinymce&&this.initializeTinymce(t,e),e.quicktags&&this.initializeQuicktags(t,e)},initializeTinymce:function(e,i){var a=t("#"+e),n=this.defaults(),s=acf.get("toolbars"),o=i.field||!1;if(o.$el,"undefined"==typeof tinymce)return!1;if(!n)return!1;if(tinymce.get(e))return this.enable(e);var r=t.extend({},n.tinymce,i.tinymce);r.id=e,r.selector="#"+e;var c=i.toolbar;if(c&&s&&s[c])for(var l=1;l<=4;l++)r["toolbar"+l]=s[c][l]||"";if(r.setup=function(t){t.on("change",(function(e){t.save(),a.trigger("change")})),t.on("mouseup",(function(t){var e=new MouseEvent("mouseup");window.dispatchEvent(e)}))},r.wp_autoresize_on=!1,r.tadv_noautop||(r.wpautop=!0),r=acf.applyFilters("wysiwyg_tinymce_settings",r,e,o),tinyMCEPreInit.mceInit[e]=r,"visual"==i.mode){tinymce.init(r);var d=tinymce.get(e);if(!d)return!1;d.acf=i.field,acf.doAction("wysiwyg_tinymce_init",d,d.id,r,o)}},initializeQuicktags:function(e,i){var a=this.defaults();if("undefined"==typeof quicktags)return!1;if(!a)return!1;var n=t.extend({},a.quicktags,i.quicktags);n.id=e;var s=i.field||!1;s.$el,n=acf.applyFilters("wysiwyg_quicktags_settings",n,n.id,s),tinyMCEPreInit.qtInit[e]=n;var o=quicktags(n);if(!o)return!1;this.buildQuicktags(o),acf.doAction("wysiwyg_quicktags_init",o,o.id,n,s)},buildQuicktags:function(t){var e,i,a,n,s,o,r,c;for(o in t.canvas,e=t.name,i=t.settings,n="",a={},r="",c=t.id,i.buttons&&(r=","+i.buttons+","),edButtons)edButtons[o]&&(s=edButtons[o].id,r&&-1!==",strong,em,link,block,del,ins,img,ul,ol,li,code,more,close,".indexOf(","+s+",")&&-1===r.indexOf(","+s+",")||edButtons[o].instance&&edButtons[o].instance!==c||(a[s]=edButtons[o],edButtons[o].html&&(n+=edButtons[o].html(e+"_"))));r&&-1!==r.indexOf(",dfw,")&&(a.dfw=new QTags.DFWButton,n+=a.dfw.html(e+"_")),"rtl"===document.getElementsByTagName("html")[0].dir&&(a.textdirection=new QTags.TextDirectionButton,n+=a.textdirection.html(e+"_")),t.toolbar.innerHTML=n,t.theButtons=a,"undefined"!=typeof jQuery&&jQuery(document).triggerHandler("quicktags-init",[t])},disable:function(t){this.destroyTinymce(t)},remove:function(t){this.destroyTinymce(t)},destroy:function(t){this.destroyTinymce(t)},destroyTinymce:function(t){if("undefined"==typeof tinymce)return!1;var e=tinymce.get(t);return!!e&&(e.save(),e.destroy(),!0)},enable:function(t){this.enableTinymce(t)},enableTinymce:function(e){return"undefined"!=typeof switchEditors&&void 0!==tinyMCEPreInit.mceInit[e]&&(t("#"+e).show(),switchEditors.go(e,"tmce"),!0)}},new acf.Model({priority:5,actions:{prepare:"onPrepare",ready:"onReady"},onPrepare:function(){var e=t("#acf-hidden-wp-editor");e.exists()&&e.appendTo("body")},onReady:function(){acf.isset(window,"wp","oldEditor")&&(wp.editor.autop=wp.oldEditor.autop,wp.editor.removep=wp.oldEditor.removep),acf.isset(window,"tinymce","on")&&tinymce.on("AddEditor",(function(t){var e=t.editor;"acf"===e.id.substr(0,3)&&(e=tinymce.editors.content||e,tinymce.activeEditor=e,wpActiveEditor=e.id)}))}})},1417:function(){var t;t=jQuery,acf.unload=new acf.Model({wait:"load",active:!0,changed:!1,actions:{validation_failure:"startListening",validation_success:"stopListening"},events:{"change form .acf-field":"startListening","submit form":"stopListening"},enable:function(){this.active=!0},disable:function(){this.active=!1},reset:function(){this.stopListening()},startListening:function(){!this.changed&&this.active&&(this.changed=!0,t(window).on("beforeunload",this.onUnload))},stopListening:function(){this.changed=!1,t(window).off("beforeunload",this.onUnload)},onUnload:function(){return acf.__("The changes you made will be lost if you navigate away from this page")}})},6148:function(){!function(t,e){var i=acf.Model.extend({id:"Validator",data:{errors:[],notice:null,status:""},events:{"changed:status":"onChangeStatus"},addErrors:function(t){t.map(this.addError,this)},addError:function(t){this.data.errors.push(t)},hasErrors:function(){return this.data.errors.length},clearErrors:function(){return this.data.errors=[]},getErrors:function(){return this.data.errors},getFieldErrors:function(){var t=[],e=[];return this.getErrors().map((function(i){if(i.input){var a=e.indexOf(i.input);a>-1?t[a]=i:(t.push(i),e.push(i.input))}})),t},getGlobalErrors:function(){return this.getErrors().filter((function(t){return!t.input}))},showErrors:function(){if(this.hasErrors()){var e=this.getFieldErrors(),i=this.getGlobalErrors(),a=0,n=!1;e.map((function(t){var e=this.$('[name="'+t.input+'"]').first();if(e.length||(e=this.$('[name^="'+t.input+'"]').first()),e.length){a++;var i=acf.getClosestField(e);o(i.$el),i.showError(t.message),n||(n=i.$el)}}),this);var s=acf.__("Validation failed");if(i.map((function(t){s+=". "+t.message})),1==a?s+=". "+acf.__("1 field requires attention"):a>1&&(s+=". "+acf.__("%d fields require attention").replace("%d",a)),this.has("notice"))this.get("notice").update({type:"error",text:s});else{var r=acf.newNotice({type:"error",text:s,target:this.$el});this.set("notice",r)}this.$el.parents(".acf-popup-box").length||(n||(n=this.get("notice").$el),setTimeout((function(){t("html, body").animate({scrollTop:n.offset().top-t(window).height()/2},500)}),10))}},onChangeStatus:function(t,e,i,a){this.$el.removeClass("is-"+a).addClass("is-"+i)},validate:function(e){if(e=acf.parseArgs(e,{event:!1,reset:!1,loading:function(){},complete:function(){},failure:function(){},success:function(t){t.submit()}}),"valid"==this.get("status"))return!0;if("validating"==this.get("status"))return!1;if(!this.$(".acf-field").length)return!0;if(e.event){var i=t.Event(null,e.event);e.success=function(){acf.enableSubmit(t(i.target)).trigger(i)}}acf.doAction("validation_begin",this.$el),acf.lockForm(this.$el),e.loading(this.$el,this),this.set("status","validating");var a=acf.serialize(this.$el);return a.action="acf/validate_save_post",t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(a),type:"post",dataType:"json",context:this,success:function(t){if(acf.isAjaxSuccess(t)){var e=acf.applyFilters("validation_complete",t.data,this.$el,this);e.valid||this.addErrors(e.errors)}},complete:function(){acf.unlockForm(this.$el),this.hasErrors()?(this.set("status","invalid"),acf.doAction("validation_failure",this.$el,this),this.showErrors(),e.failure(this.$el,this)):(this.set("status","valid"),this.has("notice")&&this.get("notice").update({type:"success",text:acf.__("Validation successful"),timeout:1e3}),acf.doAction("validation_success",this.$el,this),acf.doAction("submit",this.$el),e.success(this.$el,this),acf.lockForm(this.$el),e.reset&&this.reset()),e.complete(this.$el,this),this.clearErrors()}}),!1},setup:function(t){this.$el=t},reset:function(){this.set("errors",[]),this.set("notice",null),this.set("status",""),acf.unlockForm(this.$el)}}),a=function(t){var e=t.data("acf");return e||(e=new i(t)),e};acf.validateForm=function(t){return a(t.form).validate(t)},acf.enableSubmit=function(t){return t.removeClass("disabled").removeAttr("disabled")},acf.disableSubmit=function(t){return t.addClass("disabled").attr("disabled",!0)},acf.showSpinner=function(t){return t.addClass("is-active"),t.css("display","inline-block"),t},acf.hideSpinner=function(t){return t.removeClass("is-active"),t.css("display","none"),t},acf.lockForm=function(t){var e=n(t),i=e.find('.button, [type="submit"]').not(".acf-nav, .acf-repeater-add-row"),a=e.find(".spinner, .acf-spinner");return acf.hideSpinner(a),acf.disableSubmit(i),acf.showSpinner(a.last()),t},acf.unlockForm=function(t){var e=n(t),i=e.find('.button, [type="submit"]').not(".acf-nav, .acf-repeater-add-row"),a=e.find(".spinner, .acf-spinner");return acf.enableSubmit(i),acf.hideSpinner(a),t};var n=function(e){var i;return(i=e.find("#submitdiv")).length||(i=e.find("#submitpost")).length||(i=e.find("p.submit").last()).length||(i=e.find(".acf-form-submit")).length||(i=t("#acf-create-options-page-form .acf-actions")).length||(i=t(".acf-headerbar-actions")).length?i:e},s=acf.debounce((function(t){t.submit()})),o=function(t){var e=t.parents(".acf-postbox");if(e.length){var i=acf.getPostbox(e);i&&i.isHiddenByScreenOptions()&&(i.$el.removeClass("hide-if-js"),i.$el.css("display",""))}};acf.validation=new acf.Model({id:"validation",active:!0,wait:"prepare",actions:{ready:"addInputEvents",append:"addInputEvents"},events:{'click input[type="submit"]':"onClickSubmit",'click button[type="submit"]':"onClickSubmit","click #save-post":"onClickSave","submit form#post":"onSubmitPost","submit form":"onSubmit"},initialize:function(){acf.get("validation")||(this.active=!1,this.actions={},this.events={})},enable:function(){this.active=!0},disable:function(){this.active=!1},reset:function(t){a(t).reset()},addInputEvents:function(e){if("safari"!==acf.get("browser")){var i=t(".acf-field [name]",e);i.length&&this.on(i,"invalid","onInvalid")}},onInvalid:function(t,e){t.preventDefault();var i=e.closest("form");i.length&&(a(i).addError({input:e.attr("name"),message:acf.strEscape(t.target.validationMessage)}),s(i))},onClickSubmit:function(e,i){t(".acf-field input").each((function(){this.checkValidity()||o(t(this))})),this.set("originalEvent",e)},onClickSave:function(t,e){this.set("ignore",!0)},onClickSubmitGutenberg:function(e,i){acf.validateForm({form:t("#editor"),event:e,reset:!0,failure:function(t,e){var i=e.get("notice").$el;i.appendTo(".components-notice-list"),i.find(".acf-notice-dismiss").removeClass("small")}})||(e.preventDefault(),e.stopImmediatePropagation())},onSubmitPost:function(e,i){"dopreview"===t("input#wp-preview").val()&&(this.set("ignore",!0),acf.unlockForm(i))},onSubmit:function(t,e){if(!this.active||this.get("ignore")||t.isDefaultPrevented())return this.allowSubmit();acf.validateForm({form:e,event:this.get("originalEvent")})||t.preventDefault()},allowSubmit:function(){return this.set("ignore",!1),this.set("originalEvent",!1),!0}}),new acf.Model({wait:"prepare",initialize:function(){acf.isGutenberg()&&this.customizeEditor()},customizeEditor:function(){var e=wp.data.dispatch("core/editor"),i=wp.data.select("core/editor"),a=wp.data.dispatch("core/notices"),n=e.savePost,s=!1,o="";wp.data.subscribe((function(){var t=i.getEditedPostAttribute("status");s="publish"===t||"future"===t,o="publish"!==t?t:o})),e.savePost=function(i){i=i||{};var r=this,c=arguments;return new Promise((function(n,r){return i.isAutosave||i.isPreview?n("Validation ignored (autosave)."):s?void(acf.validateForm({form:t("#editor"),reset:!0,complete:function(t,i){e.unlockPostSaving("acf")},failure:function(t,i){var n=i.get("notice");a.createErrorNotice(n.get("text"),{id:"acf-validation",isDismissible:!0}),n.remove(),o&&e.editPost({status:o}),r("Validation failed.")},success:function(){a.removeNotice("acf-validation"),n("Validation success.")}})?n("Validation bypassed."):e.lockPostSaving("acf")):n("Validation ignored (draft).")})).then((function(){return n.apply(r,c)}),(t=>{}))}}})}(jQuery)}},e={};function i(a){var n=e[a];if(void 0!==n)return n.exports;var s=e[a]={exports:{}};return t[a](s,s.exports,i),s.exports}i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,{a:e}),e},i.d=function(t,e){for(var a in e)i.o(e,a)&&!i.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:e[a]})},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},function(){"use strict";i(6291),i(1580),i(2213),i(1357),i(8171),i(9459),i(7597),i(684),i(8489),i(6691),i(5647),i(4658),i(719),i(2557),i(2489),i(714),i(6965),i(177),i(1987),i(1281),i(7790),i(2573),i(9047),i(1788),i(4429),i(4850),i(2849),i(3155),i(682),i(1417),i(1128),i(3812),i(7240),i(5796),i(8061),i(6148),i(5938),i(7787)}()}();