<?php

$mapping = array('Yay<PERSON>TP\\Aws3\\Aws\\Waf\\WafClient' => __DIR__ . '/Aws/Waf/WafClient.php', 'YaySMTP\\Aws3\\Aws\\Waf\\Exception\\WafException' => __DIR__ . '/Aws/Waf/Exception/WafException.php', 'YaySMTP\\Aws3\\Aws\\Rds\\Exception\\RdsException' => __DIR__ . '/Aws/Rds/Exception/RdsException.php', 'YaySMTP\\Aws3\\Aws\\Rds\\RdsClient' => __DIR__ . '/Aws/Rds/RdsClient.php', 'YaySMTP\\Aws3\\Aws\\Rds\\AuthTokenGenerator' => __DIR__ . '/Aws/Rds/AuthTokenGenerator.php', 'YaySMTP\\Aws3\\Aws\\WorkDocs\\Exception\\WorkDocsException' => __DIR__ . '/Aws/WorkDocs/Exception/WorkDocsException.php', 'YaySMTP\\Aws3\\Aws\\WorkDocs\\WorkDocsClient' => __DIR__ . '/Aws/WorkDocs/WorkDocsClient.php', 'YaySMTP\\Aws3\\Aws\\PhpHash' => __DIR__ . '/Aws/PhpHash.php', 'YaySMTP\\Aws3\\Aws\\ElasticTranscoder\\ElasticTranscoderClient' => __DIR__ . '/Aws/ElasticTranscoder/ElasticTranscoderClient.php', 'YaySMTP\\Aws3\\Aws\\ElasticTranscoder\\Exception\\ElasticTranscoderException' => __DIR__ . '/Aws/ElasticTranscoder/Exception/ElasticTranscoderException.php', 'YaySMTP\\Aws3\\Aws\\Kinesis\\Exception\\KinesisException' => __DIR__ . '/Aws/Kinesis/Exception/KinesisException.php', 'YaySMTP\\Aws3\\Aws\\Kinesis\\KinesisClient' => __DIR__ . '/Aws/Kinesis/KinesisClient.php', 'YaySMTP\\Aws3\\Aws\\LexRuntimeService\\Exception\\LexRuntimeServiceException' => __DIR__ . '/Aws/LexRuntimeService/Exception/LexRuntimeServiceException.php', 'YaySMTP\\Aws3\\Aws\\LexRuntimeService\\LexRuntimeServiceClient' => __DIR__ . '/Aws/LexRuntimeService/LexRuntimeServiceClient.php', 'YaySMTP\\Aws3\\Aws\\Waiter' => __DIR__ . '/Aws/Waiter.php', 'YaySMTP\\Aws3\\Aws\\WorkMail\\Exception\\WorkMailException' => __DIR__ . '/Aws/WorkMail/Exception/WorkMailException.php', 'YaySMTP\\Aws3\\Aws\\WorkMail\\WorkMailClient' => __DIR__ . '/Aws/WorkMail/WorkMailClient.php', 'YaySMTP\\Aws3\\Aws\\LexModelBuildingService\\LexModelBuildingServiceClient' => __DIR__ . '/Aws/LexModelBuildingService/LexModelBuildingServiceClient.php', 'YaySMTP\\Aws3\\Aws\\LexModelBuildingService\\Exception\\LexModelBuildingServiceException' => __DIR__ . '/Aws/LexModelBuildingService/Exception/LexModelBuildingServiceException.php', 'YaySMTP\\Aws3\\Aws\\Handler\\GuzzleV6\\GuzzleHandler' => __DIR__ . '/Aws/Handler/GuzzleV6/GuzzleHandler.php', 'YaySMTP\\Aws3\\Aws\\Handler\\GuzzleV5\\GuzzleHandler' => __DIR__ . '/Aws/Handler/GuzzleV5/GuzzleHandler.php', 'YaySMTP\\Aws3\\Aws\\Handler\\GuzzleV5\\GuzzleStream' => __DIR__ . '/Aws/Handler/GuzzleV5/GuzzleStream.php', 'YaySMTP\\Aws3\\Aws\\Handler\\GuzzleV5\\PsrStream' => __DIR__ . '/Aws/Handler/GuzzleV5/PsrStream.php', 'YaySMTP\\Aws3\\Aws\\Health\\Exception\\HealthException' => __DIR__ . '/Aws/Health/Exception/HealthException.php', 'YaySMTP\\Aws3\\Aws\\Health\\HealthClient' => __DIR__ . '/Aws/Health/HealthClient.php', 'YaySMTP\\Aws3\\Aws\\CloudFront\\CloudFrontClient' => __DIR__ . '/Aws/CloudFront/CloudFrontClient.php', 'YaySMTP\\Aws3\\Aws\\CloudFront\\Exception\\CloudFrontException' => __DIR__ . '/Aws/CloudFront/Exception/CloudFrontException.php', 'YaySMTP\\Aws3\\Aws\\CloudFront\\UrlSigner' => __DIR__ . '/Aws/CloudFront/UrlSigner.php', 'YaySMTP\\Aws3\\Aws\\CloudFront\\Signer' => __DIR__ . '/Aws/CloudFront/Signer.php', 'YaySMTP\\Aws3\\Aws\\CloudFront\\CookieSigner' => __DIR__ . '/Aws/CloudFront/CookieSigner.php', 'YaySMTP\\Aws3\\Aws\\Iot\\IotClient' => __DIR__ . '/Aws/Iot/IotClient.php', 'YaySMTP\\Aws3\\Aws\\Iot\\Exception\\IotException' => __DIR__ . '/Aws/Iot/Exception/IotException.php', 'YaySMTP\\Aws3\\Aws\\MTurk\\Exception\\MTurkException' => __DIR__ . '/Aws/MTurk/Exception/MTurkException.php', 'YaySMTP\\Aws3\\Aws\\MTurk\\MTurkClient' => __DIR__ . '/Aws/MTurk/MTurkClient.php', 'YaySMTP\\Aws3\\Aws\\Connect\\ConnectClient' => __DIR__ . '/Aws/Connect/ConnectClient.php', 'YaySMTP\\Aws3\\Aws\\Connect\\Exception\\ConnectException' => __DIR__ . '/Aws/Connect/Exception/ConnectException.php', 'YaySMTP\\Aws3\\Aws\\DynamoDbStreams\\DynamoDbStreamsClient' => __DIR__ . '/Aws/DynamoDbStreams/DynamoDbStreamsClient.php', 'YaySMTP\\Aws3\\Aws\\DynamoDbStreams\\Exception\\DynamoDbStreamsException' => __DIR__ . '/Aws/DynamoDbStreams/Exception/DynamoDbStreamsException.php', 'YaySMTP\\Aws3\\Aws\\TranscribeService\\Exception\\TranscribeServiceException' => __DIR__ . '/Aws/TranscribeService/Exception/TranscribeServiceException.php', 'YaySMTP\\Aws3\\Aws\\TranscribeService\\TranscribeServiceClient' => __DIR__ . '/Aws/TranscribeService/TranscribeServiceClient.php', 'YaySMTP\\Aws3\\Aws\\CloudWatchLogs\\Exception\\CloudWatchLogsException' => __DIR__ . '/Aws/CloudWatchLogs/Exception/CloudWatchLogsException.php', 'YaySMTP\\Aws3\\Aws\\CloudWatchLogs\\CloudWatchLogsClient' => __DIR__ . '/Aws/CloudWatchLogs/CloudWatchLogsClient.php', 'YaySMTP\\Aws3\\Aws\\MarketplaceCommerceAnalytics\\Exception\\MarketplaceCommerceAnalyticsException' => __DIR__ . '/Aws/MarketplaceCommerceAnalytics/Exception/MarketplaceCommerceAnalyticsException.php', 'YaySMTP\\Aws3\\Aws\\MarketplaceCommerceAnalytics\\MarketplaceCommerceAnalyticsClient' => __DIR__ . '/Aws/MarketplaceCommerceAnalytics/MarketplaceCommerceAnalyticsClient.php', 'YaySMTP\\Aws3\\Aws\\ElasticLoadBalancing\\Exception\\ElasticLoadBalancingException' => __DIR__ . '/Aws/ElasticLoadBalancing/Exception/ElasticLoadBalancingException.php', 'YaySMTP\\Aws3\\Aws\\ElasticLoadBalancing\\ElasticLoadBalancingClient' => __DIR__ . '/Aws/ElasticLoadBalancing/ElasticLoadBalancingClient.php', 'YaySMTP\\Aws3\\Aws\\AlexaForBusiness\\Exception\\AlexaForBusinessException' => __DIR__ . '/Aws/AlexaForBusiness/Exception/AlexaForBusinessException.php', 'YaySMTP\\Aws3\\Aws\\AlexaForBusiness\\AlexaForBusinessClient' => __DIR__ . '/Aws/AlexaForBusiness/AlexaForBusinessClient.php', 'YaySMTP\\Aws3\\Aws\\MigrationHub\\Exception\\MigrationHubException' => __DIR__ . '/Aws/MigrationHub/Exception/MigrationHubException.php', 'YaySMTP\\Aws3\\Aws\\MigrationHub\\MigrationHubClient' => __DIR__ . '/Aws/MigrationHub/MigrationHubClient.php', 'YaySMTP\\Aws3\\Aws\\RetryMiddleware' => __DIR__ . '/Aws/RetryMiddleware.php', 'YaySMTP\\Aws3\\Aws\\Middleware' => __DIR__ . '/Aws/Middleware.php', 'YaySMTP\\Aws3\\Aws\\GuardDuty\\Exception\\GuardDutyException' => __DIR__ . '/Aws/GuardDuty/Exception/GuardDutyException.php', 'YaySMTP\\Aws3\\Aws\\GuardDuty\\GuardDutyClient' => __DIR__ . '/Aws/GuardDuty/GuardDutyClient.php', 'YaySMTP\\Aws3\\Aws\\WafRegional\\WafRegionalClient' => __DIR__ . '/Aws/WafRegional/WafRegionalClient.php', 'YaySMTP\\Aws3\\Aws\\WafRegional\\Exception\\WafRegionalException' => __DIR__ . '/Aws/WafRegional/Exception/WafRegionalException.php', 'YaySMTP\\Aws3\\Aws\\ConfigService\\ConfigServiceClient' => __DIR__ . '/Aws/ConfigService/ConfigServiceClient.php', 'YaySMTP\\Aws3\\Aws\\ConfigService\\Exception\\ConfigServiceException' => __DIR__ . '/Aws/ConfigService/Exception/ConfigServiceException.php', 'YaySMTP\\Aws3\\Aws\\data\\gamelift\\2015-10-01\\paginators-1.json' => __DIR__ . '/Aws/data/gamelift/2015-10-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\gamelift\\2015-10-01\\api-2.json' => __DIR__ . '/Aws/data/gamelift/2015-10-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\support\\2013-04-15\\paginators-1.json' => __DIR__ . '/Aws/data/support/2013-04-15/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\support\\2013-04-15\\api-2.json' => __DIR__ . '/Aws/data/support/2013-04-15/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\serverlessrepo\\2017-09-08\\paginators-1.json' => __DIR__ . '/Aws/data/serverlessrepo/2017-09-08/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\serverlessrepo\\2017-09-08\\api-2.json' => __DIR__ . '/Aws/data/serverlessrepo/2017-09-08/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\config\\2014-11-12\\paginators-1.json' => __DIR__ . '/Aws/data/config/2014-11-12/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\config\\2014-11-12\\api-2.json' => __DIR__ . '/Aws/data/config/2014-11-12/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\config\\2014-11-12\\smoke.json' => __DIR__ . '/Aws/data/config/2014-11-12/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dynamodb\\2012-08-10\\paginators-1.json' => __DIR__ . '/Aws/data/dynamodb/2012-08-10/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dynamodb\\2012-08-10\\api-2.json' => __DIR__ . '/Aws/data/dynamodb/2012-08-10/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dynamodb\\2012-08-10\\waiters-1.json' => __DIR__ . '/Aws/data/dynamodb/2012-08-10/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dynamodb\\2012-08-10\\waiters-2.json' => __DIR__ . '/Aws/data/dynamodb/2012-08-10/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dynamodb\\2011-12-05\\paginators-1.json' => __DIR__ . '/Aws/data/dynamodb/2011-12-05/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dynamodb\\2011-12-05\\api-2.json' => __DIR__ . '/Aws/data/dynamodb/2011-12-05/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dynamodb\\2011-12-05\\waiters-1.json' => __DIR__ . '/Aws/data/dynamodb/2011-12-05/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dynamodb\\2011-12-05\\waiters-2.json' => __DIR__ . '/Aws/data/dynamodb/2011-12-05/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\swf\\2012-01-25\\paginators-1.json' => __DIR__ . '/Aws/data/swf/2012-01-25/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\swf\\2012-01-25\\api-2.json' => __DIR__ . '/Aws/data/swf/2012-01-25/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ds\\2015-04-16\\paginators-1.json' => __DIR__ . '/Aws/data/ds/2015-04-16/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ds\\2015-04-16\\api-2.json' => __DIR__ . '/Aws/data/ds/2015-04-16/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ds\\2015-04-16\\smoke.json' => __DIR__ . '/Aws/data/ds/2015-04-16/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\machinelearning\\2014-12-12\\paginators-1.json' => __DIR__ . '/Aws/data/machinelearning/2014-12-12/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\machinelearning\\2014-12-12\\api-2.json' => __DIR__ . '/Aws/data/machinelearning/2014-12-12/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\machinelearning\\2014-12-12\\waiters-2.json' => __DIR__ . '/Aws/data/machinelearning/2014-12-12/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\glue\\2017-03-31\\paginators-1.json' => __DIR__ . '/Aws/data/glue/2017-03-31/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\glue\\2017-03-31\\api-2.json' => __DIR__ . '/Aws/data/glue/2017-03-31/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\secretsmanager\\2017-10-17\\paginators-1.json' => __DIR__ . '/Aws/data/secretsmanager/2017-10-17/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\secretsmanager\\2017-10-17\\api-2.json' => __DIR__ . '/Aws/data/secretsmanager/2017-10-17/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cognito-sync\\2014-06-30\\api-2.json' => __DIR__ . '/Aws/data/cognito-sync/2014-06-30/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticmapreduce\\2009-03-31\\paginators-1.json' => __DIR__ . '/Aws/data/elasticmapreduce/2009-03-31/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticmapreduce\\2009-03-31\\api-2.json' => __DIR__ . '/Aws/data/elasticmapreduce/2009-03-31/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticmapreduce\\2009-03-31\\waiters-2.json' => __DIR__ . '/Aws/data/elasticmapreduce/2009-03-31/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\connect\\2017-08-08\\paginators-1.json' => __DIR__ . '/Aws/data/connect/2017-08-08/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\connect\\2017-08-08\\api-2.json' => __DIR__ . '/Aws/data/connect/2017-08-08/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\translate\\2017-07-01\\paginators-1.json' => __DIR__ . '/Aws/data/translate/2017-07-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\translate\\2017-07-01\\api-2.json' => __DIR__ . '/Aws/data/translate/2017-07-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cognito-identity\\2014-06-30\\paginators-1.json' => __DIR__ . '/Aws/data/cognito-identity/2014-06-30/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cognito-identity\\2014-06-30\\api-2.json' => __DIR__ . '/Aws/data/cognito-identity/2014-06-30/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\workmail\\2017-10-01\\paginators-1.json' => __DIR__ . '/Aws/data/workmail/2017-10-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\workmail\\2017-10-01\\api-2.json' => __DIR__ . '/Aws/data/workmail/2017-10-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\autoscaling\\2011-01-01\\paginators-1.json' => __DIR__ . '/Aws/data/autoscaling/2011-01-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\autoscaling\\2011-01-01\\api-2.json' => __DIR__ . '/Aws/data/autoscaling/2011-01-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\autoscaling\\2011-01-01\\waiters-2.json' => __DIR__ . '/Aws/data/autoscaling/2011-01-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\s3\\2006-03-01\\paginators-1.json' => __DIR__ . '/Aws/data/s3/2006-03-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\s3\\2006-03-01\\api-2.json' => __DIR__ . '/Aws/data/s3/2006-03-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\s3\\2006-03-01\\waiters-1.json' => __DIR__ . '/Aws/data/s3/2006-03-01/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\s3\\2006-03-01\\waiters-2.json' => __DIR__ . '/Aws/data/s3/2006-03-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\s3\\2006-03-01\\smoke.json' => __DIR__ . '/Aws/data/s3/2006-03-01/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\resourcegroupstaggingapi\\2017-01-26\\paginators-1.json' => __DIR__ . '/Aws/data/resourcegroupstaggingapi/2017-01-26/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\resourcegroupstaggingapi\\2017-01-26\\api-2.json' => __DIR__ . '/Aws/data/resourcegroupstaggingapi/2017-01-26/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kinesis-video-archived-media\\2017-09-30\\paginators-1.json' => __DIR__ . '/Aws/data/kinesis-video-archived-media/2017-09-30/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kinesis-video-archived-media\\2017-09-30\\api-2.json' => __DIR__ . '/Aws/data/kinesis-video-archived-media/2017-09-30/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kinesisanalytics\\2015-08-14\\paginators-1.json' => __DIR__ . '/Aws/data/kinesisanalytics/2015-08-14/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kinesisanalytics\\2015-08-14\\api-2.json' => __DIR__ . '/Aws/data/kinesisanalytics/2015-08-14/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\health\\2016-08-04\\paginators-1.json' => __DIR__ . '/Aws/data/health/2016-08-04/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\health\\2016-08-04\\api-2.json' => __DIR__ . '/Aws/data/health/2016-08-04/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kinesis\\2013-12-02\\paginators-1.json' => __DIR__ . '/Aws/data/kinesis/2013-12-02/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kinesis\\2013-12-02\\api-2.json' => __DIR__ . '/Aws/data/kinesis/2013-12-02/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kinesis\\2013-12-02\\waiters-2.json' => __DIR__ . '/Aws/data/kinesis/2013-12-02/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kinesis\\2013-12-02\\smoke.json' => __DIR__ . '/Aws/data/kinesis/2013-12-02/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\medialive\\2017-10-14\\paginators-1.json' => __DIR__ . '/Aws/data/medialive/2017-10-14/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\medialive\\2017-10-14\\api-2.json' => __DIR__ . '/Aws/data/medialive/2017-10-14/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\lambda\\2015-03-31\\paginators-1.json' => __DIR__ . '/Aws/data/lambda/2015-03-31/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\lambda\\2015-03-31\\api-2.json' => __DIR__ . '/Aws/data/lambda/2015-03-31/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\lambda\\2015-03-31\\smoke.json' => __DIR__ . '/Aws/data/lambda/2015-03-31/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\states\\2016-11-23\\paginators-1.json' => __DIR__ . '/Aws/data/states/2016-11-23/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\states\\2016-11-23\\api-2.json' => __DIR__ . '/Aws/data/states/2016-11-23/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ce\\2017-10-25\\paginators-1.json' => __DIR__ . '/Aws/data/ce/2017-10-25/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ce\\2017-10-25\\api-2.json' => __DIR__ . '/Aws/data/ce/2017-10-25/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\xray\\2016-04-12\\paginators-1.json' => __DIR__ . '/Aws/data/xray/2016-04-12/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\xray\\2016-04-12\\api-2.json' => __DIR__ . '/Aws/data/xray/2016-04-12/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudformation\\2010-05-15\\paginators-1.json' => __DIR__ . '/Aws/data/cloudformation/2010-05-15/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudformation\\2010-05-15\\api-2.json' => __DIR__ . '/Aws/data/cloudformation/2010-05-15/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudformation\\2010-05-15\\waiters-2.json' => __DIR__ . '/Aws/data/cloudformation/2010-05-15/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticloadbalancingv2\\2015-12-01\\paginators-1.json' => __DIR__ . '/Aws/data/elasticloadbalancingv2/2015-12-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticloadbalancingv2\\2015-12-01\\api-2.json' => __DIR__ . '/Aws/data/elasticloadbalancingv2/2015-12-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticloadbalancingv2\\2015-12-01\\waiters-2.json' => __DIR__ . '/Aws/data/elasticloadbalancingv2/2015-12-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticloadbalancingv2\\2015-12-01\\smoke.json' => __DIR__ . '/Aws/data/elasticloadbalancingv2/2015-12-01/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codestar\\2017-04-19\\paginators-1.json' => __DIR__ . '/Aws/data/codestar/2017-04-19/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codestar\\2017-04-19\\api-2.json' => __DIR__ . '/Aws/data/codestar/2017-04-19/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\greengrass\\2017-06-07\\api-2.json' => __DIR__ . '/Aws/data/greengrass/2017-06-07/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\alexaforbusiness\\2017-11-09\\paginators-1.json' => __DIR__ . '/Aws/data/alexaforbusiness/2017-11-09/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\alexaforbusiness\\2017-11-09\\api-2.json' => __DIR__ . '/Aws/data/alexaforbusiness/2017-11-09/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\snowball\\2016-06-30\\paginators-1.json' => __DIR__ . '/Aws/data/snowball/2016-06-30/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\snowball\\2016-06-30\\api-2.json' => __DIR__ . '/Aws/data/snowball/2016-06-30/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sqs\\2012-11-05\\paginators-1.json' => __DIR__ . '/Aws/data/sqs/2012-11-05/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sqs\\2012-11-05\\api-2.json' => __DIR__ . '/Aws/data/sqs/2012-11-05/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sqs\\2012-11-05\\waiters-2.json' => __DIR__ . '/Aws/data/sqs/2012-11-05/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\es\\2015-01-01\\paginators-1.json' => __DIR__ . '/Aws/data/es/2015-01-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\es\\2015-01-01\\api-2.json' => __DIR__ . '/Aws/data/es/2015-01-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\es\\2015-01-01\\smoke.json' => __DIR__ . '/Aws/data/es/2015-01-01/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\application-autoscaling\\2016-02-06\\paginators-1.json' => __DIR__ . '/Aws/data/application-autoscaling/2016-02-06/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\application-autoscaling\\2016-02-06\\api-2.json' => __DIR__ . '/Aws/data/application-autoscaling/2016-02-06/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\application-autoscaling\\2016-02-06\\smoke.json' => __DIR__ . '/Aws/data/application-autoscaling/2016-02-06/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\polly\\2016-06-10\\paginators-1.json' => __DIR__ . '/Aws/data/polly/2016-06-10/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\polly\\2016-06-10\\api-2.json' => __DIR__ . '/Aws/data/polly/2016-06-10/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\discovery\\2015-11-01\\paginators-1.json' => __DIR__ . '/Aws/data/discovery/2015-11-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\discovery\\2015-11-01\\api-2.json' => __DIR__ . '/Aws/data/discovery/2015-11-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudhsm\\2014-05-30\\paginators-1.json' => __DIR__ . '/Aws/data/cloudhsm/2014-05-30/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudhsm\\2014-05-30\\api-2.json' => __DIR__ . '/Aws/data/cloudhsm/2014-05-30/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\data.iot\\2015-05-28\\api-2.json' => __DIR__ . '/Aws/data/data.iot/2015-05-28/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2016-11-15\\paginators-1.json' => __DIR__ . '/Aws/data/ec2/2016-11-15/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2016-11-15\\api-2.json' => __DIR__ . '/Aws/data/ec2/2016-11-15/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2016-11-15\\waiters-1.json' => __DIR__ . '/Aws/data/ec2/2016-11-15/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2016-11-15\\waiters-2.json' => __DIR__ . '/Aws/data/ec2/2016-11-15/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2016-11-15\\smoke.json' => __DIR__ . '/Aws/data/ec2/2016-11-15/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2016-09-15\\paginators-1.json' => __DIR__ . '/Aws/data/ec2/2016-09-15/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2016-09-15\\api-2.json' => __DIR__ . '/Aws/data/ec2/2016-09-15/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2016-09-15\\waiters-1.json' => __DIR__ . '/Aws/data/ec2/2016-09-15/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2016-09-15\\waiters-2.json' => __DIR__ . '/Aws/data/ec2/2016-09-15/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2016-04-01\\paginators-1.json' => __DIR__ . '/Aws/data/ec2/2016-04-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2016-04-01\\api-2.json' => __DIR__ . '/Aws/data/ec2/2016-04-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2016-04-01\\waiters-2.json' => __DIR__ . '/Aws/data/ec2/2016-04-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2015-10-01\\paginators-1.json' => __DIR__ . '/Aws/data/ec2/2015-10-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2015-10-01\\api-2.json' => __DIR__ . '/Aws/data/ec2/2015-10-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2015-10-01\\waiters-1.json' => __DIR__ . '/Aws/data/ec2/2015-10-01/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ec2\\2015-10-01\\waiters-2.json' => __DIR__ . '/Aws/data/ec2/2015-10-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticloadbalancing\\2012-06-01\\paginators-1.json' => __DIR__ . '/Aws/data/elasticloadbalancing/2012-06-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticloadbalancing\\2012-06-01\\api-2.json' => __DIR__ . '/Aws/data/elasticloadbalancing/2012-06-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticloadbalancing\\2012-06-01\\waiters-2.json' => __DIR__ . '/Aws/data/elasticloadbalancing/2012-06-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\acm\\2015-12-08\\paginators-1.json' => __DIR__ . '/Aws/data/acm/2015-12-08/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\acm\\2015-12-08\\api-2.json' => __DIR__ . '/Aws/data/acm/2015-12-08/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\acm\\2015-12-08\\smoke.json' => __DIR__ . '/Aws/data/acm/2015-12-08/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codepipeline\\2015-07-09\\paginators-1.json' => __DIR__ . '/Aws/data/codepipeline/2015-07-09/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codepipeline\\2015-07-09\\api-2.json' => __DIR__ . '/Aws/data/codepipeline/2015-07-09/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\entitlement.marketplace\\2017-01-11\\paginators-1.json' => __DIR__ . '/Aws/data/entitlement.marketplace/2017-01-11/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\entitlement.marketplace\\2017-01-11\\api-2.json' => __DIR__ . '/Aws/data/entitlement.marketplace/2017-01-11/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\clouddirectory\\2016-05-10\\paginators-1.json' => __DIR__ . '/Aws/data/clouddirectory/2016-05-10/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\clouddirectory\\2016-05-10\\api-2.json' => __DIR__ . '/Aws/data/clouddirectory/2016-05-10/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\appstream\\2016-12-01\\paginators-1.json' => __DIR__ . '/Aws/data/appstream/2016-12-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\appstream\\2016-12-01\\api-2.json' => __DIR__ . '/Aws/data/appstream/2016-12-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\appstream\\2016-12-01\\waiters-2.json' => __DIR__ . '/Aws/data/appstream/2016-12-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\shield\\2016-06-02\\paginators-1.json' => __DIR__ . '/Aws/data/shield/2016-06-02/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\shield\\2016-06-02\\api-2.json' => __DIR__ . '/Aws/data/shield/2016-06-02/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mq\\2017-11-27\\api-2.json' => __DIR__ . '/Aws/data/mq/2017-11-27/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\waf-regional\\2016-11-28\\paginators-1.json' => __DIR__ . '/Aws/data/waf-regional/2016-11-28/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\waf-regional\\2016-11-28\\api-2.json' => __DIR__ . '/Aws/data/waf-regional/2016-11-28/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\waf-regional\\2016-11-28\\smoke.json' => __DIR__ . '/Aws/data/waf-regional/2016-11-28/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-09-07\\paginators-1.json' => __DIR__ . '/Aws/data/cloudfront/2016-09-07/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-09-07\\api-2.json' => __DIR__ . '/Aws/data/cloudfront/2016-09-07/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-09-07\\waiters-1.json' => __DIR__ . '/Aws/data/cloudfront/2016-09-07/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-09-07\\waiters-2.json' => __DIR__ . '/Aws/data/cloudfront/2016-09-07/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-08-20\\paginators-1.json' => __DIR__ . '/Aws/data/cloudfront/2016-08-20/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-08-20\\api-2.json' => __DIR__ . '/Aws/data/cloudfront/2016-08-20/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-08-20\\waiters-2.json' => __DIR__ . '/Aws/data/cloudfront/2016-08-20/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-09-29\\paginators-1.json' => __DIR__ . '/Aws/data/cloudfront/2016-09-29/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-09-29\\api-2.json' => __DIR__ . '/Aws/data/cloudfront/2016-09-29/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-09-29\\waiters-1.json' => __DIR__ . '/Aws/data/cloudfront/2016-09-29/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-09-29\\waiters-2.json' => __DIR__ . '/Aws/data/cloudfront/2016-09-29/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-11-25\\paginators-1.json' => __DIR__ . '/Aws/data/cloudfront/2016-11-25/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-11-25\\api-2.json' => __DIR__ . '/Aws/data/cloudfront/2016-11-25/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-11-25\\waiters-1.json' => __DIR__ . '/Aws/data/cloudfront/2016-11-25/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-11-25\\waiters-2.json' => __DIR__ . '/Aws/data/cloudfront/2016-11-25/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-01-28\\paginators-1.json' => __DIR__ . '/Aws/data/cloudfront/2016-01-28/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-01-28\\api-2.json' => __DIR__ . '/Aws/data/cloudfront/2016-01-28/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-01-28\\waiters-2.json' => __DIR__ . '/Aws/data/cloudfront/2016-01-28/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-08-01\\paginators-1.json' => __DIR__ . '/Aws/data/cloudfront/2016-08-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-08-01\\api-2.json' => __DIR__ . '/Aws/data/cloudfront/2016-08-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2016-08-01\\waiters-2.json' => __DIR__ . '/Aws/data/cloudfront/2016-08-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2017-10-30\\paginators-1.json' => __DIR__ . '/Aws/data/cloudfront/2017-10-30/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2017-10-30\\api-2.json' => __DIR__ . '/Aws/data/cloudfront/2017-10-30/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2017-10-30\\waiters-1.json' => __DIR__ . '/Aws/data/cloudfront/2017-10-30/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2017-10-30\\waiters-2.json' => __DIR__ . '/Aws/data/cloudfront/2017-10-30/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2017-10-30\\smoke.json' => __DIR__ . '/Aws/data/cloudfront/2017-10-30/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2017-03-25\\paginators-1.json' => __DIR__ . '/Aws/data/cloudfront/2017-03-25/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2017-03-25\\api-2.json' => __DIR__ . '/Aws/data/cloudfront/2017-03-25/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2017-03-25\\waiters-1.json' => __DIR__ . '/Aws/data/cloudfront/2017-03-25/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2017-03-25\\waiters-2.json' => __DIR__ . '/Aws/data/cloudfront/2017-03-25/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2015-07-27\\paginators-1.json' => __DIR__ . '/Aws/data/cloudfront/2015-07-27/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2015-07-27\\api-2.json' => __DIR__ . '/Aws/data/cloudfront/2015-07-27/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudfront\\2015-07-27\\waiters-2.json' => __DIR__ . '/Aws/data/cloudfront/2015-07-27/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\rekognition\\2016-06-27\\paginators-1.json' => __DIR__ . '/Aws/data/rekognition/2016-06-27/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\rekognition\\2016-06-27\\api-2.json' => __DIR__ . '/Aws/data/rekognition/2016-06-27/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sms\\2016-10-24\\paginators-1.json' => __DIR__ . '/Aws/data/sms/2016-10-24/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sms\\2016-10-24\\api-2.json' => __DIR__ . '/Aws/data/sms/2016-10-24/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\resource-groups\\2017-11-27\\paginators-1.json' => __DIR__ . '/Aws/data/resource-groups/2017-11-27/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\resource-groups\\2017-11-27\\api-2.json' => __DIR__ . '/Aws/data/resource-groups/2017-11-27/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloud9\\2017-09-23\\paginators-1.json' => __DIR__ . '/Aws/data/cloud9/2017-09-23/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloud9\\2017-09-23\\api-2.json' => __DIR__ . '/Aws/data/cloud9/2017-09-23/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\endpoints.json' => __DIR__ . '/Aws/data/endpoints.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sts\\2011-06-15\\paginators-1.json' => __DIR__ . '/Aws/data/sts/2011-06-15/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sts\\2011-06-15\\api-2.json' => __DIR__ . '/Aws/data/sts/2011-06-15/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sts\\2011-06-15\\smoke.json' => __DIR__ . '/Aws/data/sts/2011-06-15/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\fms\\2018-01-01\\paginators-1.json' => __DIR__ . '/Aws/data/fms/2018-01-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\fms\\2018-01-01\\api-2.json' => __DIR__ . '/Aws/data/fms/2018-01-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\rds\\2014-10-31\\paginators-1.json' => __DIR__ . '/Aws/data/rds/2014-10-31/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\rds\\2014-10-31\\api-2.json' => __DIR__ . '/Aws/data/rds/2014-10-31/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\rds\\2014-10-31\\waiters-1.json' => __DIR__ . '/Aws/data/rds/2014-10-31/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\rds\\2014-10-31\\waiters-2.json' => __DIR__ . '/Aws/data/rds/2014-10-31/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\rds\\2014-10-31\\smoke.json' => __DIR__ . '/Aws/data/rds/2014-10-31/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\rds\\2014-09-01\\paginators-1.json' => __DIR__ . '/Aws/data/rds/2014-09-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\rds\\2014-09-01\\api-2.json' => __DIR__ . '/Aws/data/rds/2014-09-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\rds\\2014-09-01\\smoke.json' => __DIR__ . '/Aws/data/rds/2014-09-01/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\lex-models\\2017-04-19\\paginators-1.json' => __DIR__ . '/Aws/data/lex-models/2017-04-19/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\lex-models\\2017-04-19\\api-2.json' => __DIR__ . '/Aws/data/lex-models/2017-04-19/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\iot-jobs-data\\2017-09-29\\paginators-1.json' => __DIR__ . '/Aws/data/iot-jobs-data/2017-09-29/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\iot-jobs-data\\2017-09-29\\api-2.json' => __DIR__ . '/Aws/data/iot-jobs-data/2017-09-29/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\servicediscovery\\2017-03-14\\paginators-1.json' => __DIR__ . '/Aws/data/servicediscovery/2017-03-14/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\servicediscovery\\2017-03-14\\api-2.json' => __DIR__ . '/Aws/data/servicediscovery/2017-03-14/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ssm\\2014-11-06\\paginators-1.json' => __DIR__ . '/Aws/data/ssm/2014-11-06/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ssm\\2014-11-06\\api-2.json' => __DIR__ . '/Aws/data/ssm/2014-11-06/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ssm\\2014-11-06\\smoke.json' => __DIR__ . '/Aws/data/ssm/2014-11-06/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mediastore-data\\2017-09-01\\paginators-1.json' => __DIR__ . '/Aws/data/mediastore-data/2017-09-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mediastore-data\\2017-09-01\\api-2.json' => __DIR__ . '/Aws/data/mediastore-data/2017-09-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ecr\\2015-09-21\\paginators-1.json' => __DIR__ . '/Aws/data/ecr/2015-09-21/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ecr\\2015-09-21\\api-2.json' => __DIR__ . '/Aws/data/ecr/2015-09-21/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kinesisvideo\\2017-09-30\\paginators-1.json' => __DIR__ . '/Aws/data/kinesisvideo/2017-09-30/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kinesisvideo\\2017-09-30\\api-2.json' => __DIR__ . '/Aws/data/kinesisvideo/2017-09-30/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\opsworkscm\\2016-11-01\\paginators-1.json' => __DIR__ . '/Aws/data/opsworkscm/2016-11-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\opsworkscm\\2016-11-01\\api-2.json' => __DIR__ . '/Aws/data/opsworkscm/2016-11-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\opsworkscm\\2016-11-01\\waiters-2.json' => __DIR__ . '/Aws/data/opsworkscm/2016-11-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\budgets\\2016-10-20\\paginators-1.json' => __DIR__ . '/Aws/data/budgets/2016-10-20/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\budgets\\2016-10-20\\api-2.json' => __DIR__ . '/Aws/data/budgets/2016-10-20/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\apigateway\\2015-07-09\\paginators-1.json' => __DIR__ . '/Aws/data/apigateway/2015-07-09/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\apigateway\\2015-07-09\\api-2.json' => __DIR__ . '/Aws/data/apigateway/2015-07-09/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\apigateway\\2015-07-09\\smoke.json' => __DIR__ . '/Aws/data/apigateway/2015-07-09/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudsearch\\2013-01-01\\paginators-1.json' => __DIR__ . '/Aws/data/cloudsearch/2013-01-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudsearch\\2013-01-01\\api-2.json' => __DIR__ . '/Aws/data/cloudsearch/2013-01-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\opsworks\\2013-02-18\\paginators-1.json' => __DIR__ . '/Aws/data/opsworks/2013-02-18/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\opsworks\\2013-02-18\\api-2.json' => __DIR__ . '/Aws/data/opsworks/2013-02-18/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\opsworks\\2013-02-18\\waiters-2.json' => __DIR__ . '/Aws/data/opsworks/2013-02-18/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\opsworks\\2013-02-18\\smoke.json' => __DIR__ . '/Aws/data/opsworks/2013-02-18/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\email\\2010-12-01\\paginators-1.json' => __DIR__ . '/Aws/data/email/2010-12-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\email\\2010-12-01\\api-2.json' => __DIR__ . '/Aws/data/email/2010-12-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\email\\2010-12-01\\waiters-1.json' => __DIR__ . '/Aws/data/email/2010-12-01/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\email\\2010-12-01\\waiters-2.json' => __DIR__ . '/Aws/data/email/2010-12-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\servicecatalog\\2015-12-10\\paginators-1.json' => __DIR__ . '/Aws/data/servicecatalog/2015-12-10/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\servicecatalog\\2015-12-10\\api-2.json' => __DIR__ . '/Aws/data/servicecatalog/2015-12-10/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mediastore\\2017-09-01\\paginators-1.json' => __DIR__ . '/Aws/data/mediastore/2017-09-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mediastore\\2017-09-01\\api-2.json' => __DIR__ . '/Aws/data/mediastore/2017-09-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\athena\\2017-05-18\\paginators-1.json' => __DIR__ . '/Aws/data/athena/2017-05-18/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\athena\\2017-05-18\\api-2.json' => __DIR__ . '/Aws/data/athena/2017-05-18/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\waf\\2015-08-24\\paginators-1.json' => __DIR__ . '/Aws/data/waf/2015-08-24/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\waf\\2015-08-24\\api-2.json' => __DIR__ . '/Aws/data/waf/2015-08-24/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\waf\\2015-08-24\\smoke.json' => __DIR__ . '/Aws/data/waf/2015-08-24/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mediapackage\\2017-10-12\\paginators-1.json' => __DIR__ . '/Aws/data/mediapackage/2017-10-12/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mediapackage\\2017-10-12\\api-2.json' => __DIR__ . '/Aws/data/mediapackage/2017-10-12/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\logs\\2014-03-28\\paginators-1.json' => __DIR__ . '/Aws/data/logs/2014-03-28/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\logs\\2014-03-28\\api-2.json' => __DIR__ . '/Aws/data/logs/2014-03-28/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\iam\\2010-05-08\\paginators-1.json' => __DIR__ . '/Aws/data/iam/2010-05-08/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\iam\\2010-05-08\\api-2.json' => __DIR__ . '/Aws/data/iam/2010-05-08/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\iam\\2010-05-08\\waiters-2.json' => __DIR__ . '/Aws/data/iam/2010-05-08/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\iam\\2010-05-08\\smoke.json' => __DIR__ . '/Aws/data/iam/2010-05-08/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\pinpoint\\2016-12-01\\api-2.json' => __DIR__ . '/Aws/data/pinpoint/2016-12-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\runtime.lex\\2016-11-28\\paginators-1.json' => __DIR__ . '/Aws/data/runtime.lex/2016-11-28/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\runtime.lex\\2016-11-28\\api-2.json' => __DIR__ . '/Aws/data/runtime.lex/2016-11-28/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudhsmv2\\2017-04-28\\paginators-1.json' => __DIR__ . '/Aws/data/cloudhsmv2/2017-04-28/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudhsmv2\\2017-04-28\\api-2.json' => __DIR__ . '/Aws/data/cloudhsmv2/2017-04-28/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudhsmv2\\2017-04-28\\smoke.json' => __DIR__ . '/Aws/data/cloudhsmv2/2017-04-28/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sagemaker\\2017-07-24\\paginators-1.json' => __DIR__ . '/Aws/data/sagemaker/2017-07-24/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sagemaker\\2017-07-24\\api-2.json' => __DIR__ . '/Aws/data/sagemaker/2017-07-24/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sagemaker\\2017-07-24\\waiters-2.json' => __DIR__ . '/Aws/data/sagemaker/2017-07-24/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mediaconvert\\2017-08-29\\api-2.json' => __DIR__ . '/Aws/data/mediaconvert/2017-08-29/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\events\\2015-10-07\\paginators-1.json' => __DIR__ . '/Aws/data/events/2015-10-07/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\events\\2015-10-07\\api-2.json' => __DIR__ . '/Aws/data/events/2015-10-07/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\events\\2015-10-07\\smoke.json' => __DIR__ . '/Aws/data/events/2015-10-07/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ecs\\2014-11-13\\paginators-1.json' => __DIR__ . '/Aws/data/ecs/2014-11-13/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ecs\\2014-11-13\\api-2.json' => __DIR__ . '/Aws/data/ecs/2014-11-13/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\ecs\\2014-11-13\\waiters-2.json' => __DIR__ . '/Aws/data/ecs/2014-11-13/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\appsync\\2017-07-25\\paginators-1.json' => __DIR__ . '/Aws/data/appsync/2017-07-25/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\appsync\\2017-07-25\\api-2.json' => __DIR__ . '/Aws/data/appsync/2017-07-25/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\importexport\\2010-06-01\\paginators-1.json' => __DIR__ . '/Aws/data/importexport/2010-06-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\importexport\\2010-06-01\\api-2.json' => __DIR__ . '/Aws/data/importexport/2010-06-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kms\\2014-11-01\\paginators-1.json' => __DIR__ . '/Aws/data/kms/2014-11-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kms\\2014-11-01\\api-2.json' => __DIR__ . '/Aws/data/kms/2014-11-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kms\\2014-11-01\\smoke.json' => __DIR__ . '/Aws/data/kms/2014-11-01/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\transcribe\\2017-10-26\\paginators-1.json' => __DIR__ . '/Aws/data/transcribe/2017-10-26/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\transcribe\\2017-10-26\\api-2.json' => __DIR__ . '/Aws/data/transcribe/2017-10-26/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\runtime.sagemaker\\2017-05-13\\paginators-1.json' => __DIR__ . '/Aws/data/runtime.sagemaker/2017-05-13/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\runtime.sagemaker\\2017-05-13\\api-2.json' => __DIR__ . '/Aws/data/runtime.sagemaker/2017-05-13/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mgh\\2017-05-31\\paginators-1.json' => __DIR__ . '/Aws/data/mgh/2017-05-31/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mgh\\2017-05-31\\api-2.json' => __DIR__ . '/Aws/data/mgh/2017-05-31/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\iot\\2015-05-28\\paginators-1.json' => __DIR__ . '/Aws/data/iot/2015-05-28/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\iot\\2015-05-28\\api-2.json' => __DIR__ . '/Aws/data/iot/2015-05-28/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudtrail\\2013-11-01\\paginators-1.json' => __DIR__ . '/Aws/data/cloudtrail/2013-11-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudtrail\\2013-11-01\\api-2.json' => __DIR__ . '/Aws/data/cloudtrail/2013-11-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticbeanstalk\\2010-12-01\\paginators-1.json' => __DIR__ . '/Aws/data/elasticbeanstalk/2010-12-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticbeanstalk\\2010-12-01\\api-2.json' => __DIR__ . '/Aws/data/elasticbeanstalk/2010-12-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticbeanstalk\\2010-12-01\\smoke.json' => __DIR__ . '/Aws/data/elasticbeanstalk/2010-12-01/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\streams.dynamodb\\2012-08-10\\paginators-1.json' => __DIR__ . '/Aws/data/streams.dynamodb/2012-08-10/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\streams.dynamodb\\2012-08-10\\api-2.json' => __DIR__ . '/Aws/data/streams.dynamodb/2012-08-10/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\comprehend\\2017-11-27\\paginators-1.json' => __DIR__ . '/Aws/data/comprehend/2017-11-27/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\comprehend\\2017-11-27\\api-2.json' => __DIR__ . '/Aws/data/comprehend/2017-11-27/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codedeploy\\2014-10-06\\paginators-1.json' => __DIR__ . '/Aws/data/codedeploy/2014-10-06/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codedeploy\\2014-10-06\\api-2.json' => __DIR__ . '/Aws/data/codedeploy/2014-10-06/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codedeploy\\2014-10-06\\waiters-1.json' => __DIR__ . '/Aws/data/codedeploy/2014-10-06/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codedeploy\\2014-10-06\\waiters-2.json' => __DIR__ . '/Aws/data/codedeploy/2014-10-06/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codedeploy\\2014-10-06\\smoke.json' => __DIR__ . '/Aws/data/codedeploy/2014-10-06/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\guardduty\\2017-11-28\\paginators-1.json' => __DIR__ . '/Aws/data/guardduty/2017-11-28/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\guardduty\\2017-11-28\\api-2.json' => __DIR__ . '/Aws/data/guardduty/2017-11-28/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\workspaces\\2015-04-08\\paginators-1.json' => __DIR__ . '/Aws/data/workspaces/2015-04-08/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\workspaces\\2015-04-08\\api-2.json' => __DIR__ . '/Aws/data/workspaces/2015-04-08/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\workspaces\\2015-04-08\\smoke.json' => __DIR__ . '/Aws/data/workspaces/2015-04-08/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codebuild\\2016-10-06\\paginators-1.json' => __DIR__ . '/Aws/data/codebuild/2016-10-06/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codebuild\\2016-10-06\\api-2.json' => __DIR__ . '/Aws/data/codebuild/2016-10-06/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codecommit\\2015-04-13\\paginators-1.json' => __DIR__ . '/Aws/data/codecommit/2015-04-13/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codecommit\\2015-04-13\\api-2.json' => __DIR__ . '/Aws/data/codecommit/2015-04-13/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\codecommit\\2015-04-13\\smoke.json' => __DIR__ . '/Aws/data/codecommit/2015-04-13/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\iotanalytics\\2017-11-27\\paginators-1.json' => __DIR__ . '/Aws/data/iotanalytics/2017-11-27/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\iotanalytics\\2017-11-27\\api-2.json' => __DIR__ . '/Aws/data/iotanalytics/2017-11-27/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\autoscaling-plans\\2018-01-06\\paginators-1.json' => __DIR__ . '/Aws/data/autoscaling-plans/2018-01-06/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\autoscaling-plans\\2018-01-06\\api-2.json' => __DIR__ . '/Aws/data/autoscaling-plans/2018-01-06/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\storagegateway\\2013-06-30\\paginators-1.json' => __DIR__ . '/Aws/data/storagegateway/2013-06-30/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\storagegateway\\2013-06-30\\api-2.json' => __DIR__ . '/Aws/data/storagegateway/2013-06-30/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\lightsail\\2016-11-28\\paginators-1.json' => __DIR__ . '/Aws/data/lightsail/2016-11-28/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\lightsail\\2016-11-28\\api-2.json' => __DIR__ . '/Aws/data/lightsail/2016-11-28/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\glacier\\2012-06-01\\paginators-1.json' => __DIR__ . '/Aws/data/glacier/2012-06-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\glacier\\2012-06-01\\api-2.json' => __DIR__ . '/Aws/data/glacier/2012-06-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\glacier\\2012-06-01\\waiters-1.json' => __DIR__ . '/Aws/data/glacier/2012-06-01/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\glacier\\2012-06-01\\waiters-2.json' => __DIR__ . '/Aws/data/glacier/2012-06-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\marketplacecommerceanalytics\\2015-07-01\\paginators-1.json' => __DIR__ . '/Aws/data/marketplacecommerceanalytics/2015-07-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\marketplacecommerceanalytics\\2015-07-01\\api-2.json' => __DIR__ . '/Aws/data/marketplacecommerceanalytics/2015-07-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dms\\2016-01-01\\paginators-1.json' => __DIR__ . '/Aws/data/dms/2016-01-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dms\\2016-01-01\\api-2.json' => __DIR__ . '/Aws/data/dms/2016-01-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dms\\2016-01-01\\smoke.json' => __DIR__ . '/Aws/data/dms/2016-01-01/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\manifest.json' => __DIR__ . '/Aws/data/manifest.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cognito-idp\\2016-04-18\\paginators-1.json' => __DIR__ . '/Aws/data/cognito-idp/2016-04-18/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cognito-idp\\2016-04-18\\api-2.json' => __DIR__ . '/Aws/data/cognito-idp/2016-04-18/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\pricing\\2017-10-15\\paginators-1.json' => __DIR__ . '/Aws/data/pricing/2017-10-15/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\pricing\\2017-10-15\\api-2.json' => __DIR__ . '/Aws/data/pricing/2017-10-15/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sns\\2010-03-31\\paginators-1.json' => __DIR__ . '/Aws/data/sns/2010-03-31/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\sns\\2010-03-31\\api-2.json' => __DIR__ . '/Aws/data/sns/2010-03-31/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\route53\\2013-04-01\\paginators-1.json' => __DIR__ . '/Aws/data/route53/2013-04-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\route53\\2013-04-01\\api-2.json' => __DIR__ . '/Aws/data/route53/2013-04-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\route53\\2013-04-01\\waiters-2.json' => __DIR__ . '/Aws/data/route53/2013-04-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\route53\\2013-04-01\\smoke.json' => __DIR__ . '/Aws/data/route53/2013-04-01/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\datapipeline\\2012-10-29\\paginators-1.json' => __DIR__ . '/Aws/data/datapipeline/2012-10-29/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\datapipeline\\2012-10-29\\api-2.json' => __DIR__ . '/Aws/data/datapipeline/2012-10-29/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticfilesystem\\2015-02-01\\paginators-1.json' => __DIR__ . '/Aws/data/elasticfilesystem/2015-02-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticfilesystem\\2015-02-01\\api-2.json' => __DIR__ . '/Aws/data/elasticfilesystem/2015-02-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mturk-requester\\2017-01-17\\paginators-1.json' => __DIR__ . '/Aws/data/mturk-requester/2017-01-17/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mturk-requester\\2017-01-17\\api-2.json' => __DIR__ . '/Aws/data/mturk-requester/2017-01-17/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mturk-requester\\2017-01-17\\smoke.json' => __DIR__ . '/Aws/data/mturk-requester/2017-01-17/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mobile\\2017-07-01\\paginators-1.json' => __DIR__ . '/Aws/data/mobile/2017-07-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\mobile\\2017-07-01\\api-2.json' => __DIR__ . '/Aws/data/mobile/2017-07-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elastictranscoder\\2012-09-25\\paginators-1.json' => __DIR__ . '/Aws/data/elastictranscoder/2012-09-25/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elastictranscoder\\2012-09-25\\api-2.json' => __DIR__ . '/Aws/data/elastictranscoder/2012-09-25/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elastictranscoder\\2012-09-25\\waiters-1.json' => __DIR__ . '/Aws/data/elastictranscoder/2012-09-25/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elastictranscoder\\2012-09-25\\waiters-2.json' => __DIR__ . '/Aws/data/elastictranscoder/2012-09-25/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\organizations\\2016-11-28\\paginators-1.json' => __DIR__ . '/Aws/data/organizations/2016-11-28/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\organizations\\2016-11-28\\api-2.json' => __DIR__ . '/Aws/data/organizations/2016-11-28/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dax\\2017-04-19\\paginators-1.json' => __DIR__ . '/Aws/data/dax/2017-04-19/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\dax\\2017-04-19\\api-2.json' => __DIR__ . '/Aws/data/dax/2017-04-19/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\acm-pca\\2017-08-22\\paginators-1.json' => __DIR__ . '/Aws/data/acm-pca/2017-08-22/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\acm-pca\\2017-08-22\\api-2.json' => __DIR__ . '/Aws/data/acm-pca/2017-08-22/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\batch\\2016-08-10\\paginators-1.json' => __DIR__ . '/Aws/data/batch/2016-08-10/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\batch\\2016-08-10\\api-2.json' => __DIR__ . '/Aws/data/batch/2016-08-10/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\batch\\2016-08-10\\smoke.json' => __DIR__ . '/Aws/data/batch/2016-08-10/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\monitoring\\2010-08-01\\paginators-1.json' => __DIR__ . '/Aws/data/monitoring/2010-08-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\monitoring\\2010-08-01\\api-2.json' => __DIR__ . '/Aws/data/monitoring/2010-08-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\monitoring\\2010-08-01\\waiters-2.json' => __DIR__ . '/Aws/data/monitoring/2010-08-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\firehose\\2015-08-04\\paginators-1.json' => __DIR__ . '/Aws/data/firehose/2015-08-04/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\firehose\\2015-08-04\\api-2.json' => __DIR__ . '/Aws/data/firehose/2015-08-04/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\firehose\\2015-08-04\\smoke.json' => __DIR__ . '/Aws/data/firehose/2015-08-04/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\devicefarm\\2015-06-23\\paginators-1.json' => __DIR__ . '/Aws/data/devicefarm/2015-06-23/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\devicefarm\\2015-06-23\\api-2.json' => __DIR__ . '/Aws/data/devicefarm/2015-06-23/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\devicefarm\\2015-06-23\\smoke.json' => __DIR__ . '/Aws/data/devicefarm/2015-06-23/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\redshift\\2012-12-01\\paginators-1.json' => __DIR__ . '/Aws/data/redshift/2012-12-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\redshift\\2012-12-01\\api-2.json' => __DIR__ . '/Aws/data/redshift/2012-12-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\redshift\\2012-12-01\\waiters-1.json' => __DIR__ . '/Aws/data/redshift/2012-12-01/waiters-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\redshift\\2012-12-01\\waiters-2.json' => __DIR__ . '/Aws/data/redshift/2012-12-01/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\redshift\\2012-12-01\\smoke.json' => __DIR__ . '/Aws/data/redshift/2012-12-01/smoke.json.php', 'YaySMTP\\Aws3\\Aws\\data\\metering.marketplace\\2016-01-14\\api-2.json' => __DIR__ . '/Aws/data/metering.marketplace/2016-01-14/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticache\\2015-02-02\\paginators-1.json' => __DIR__ . '/Aws/data/elasticache/2015-02-02/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticache\\2015-02-02\\api-2.json' => __DIR__ . '/Aws/data/elasticache/2015-02-02/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\elasticache\\2015-02-02\\waiters-2.json' => __DIR__ . '/Aws/data/elasticache/2015-02-02/waiters-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\inspector\\2016-02-16\\paginators-1.json' => __DIR__ . '/Aws/data/inspector/2016-02-16/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\inspector\\2016-02-16\\api-2.json' => __DIR__ . '/Aws/data/inspector/2016-02-16/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\workdocs\\2016-05-01\\paginators-1.json' => __DIR__ . '/Aws/data/workdocs/2016-05-01/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\workdocs\\2016-05-01\\api-2.json' => __DIR__ . '/Aws/data/workdocs/2016-05-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cur\\2017-01-06\\paginators-1.json' => __DIR__ . '/Aws/data/cur/2017-01-06/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cur\\2017-01-06\\api-2.json' => __DIR__ . '/Aws/data/cur/2017-01-06/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\directconnect\\2012-10-25\\paginators-1.json' => __DIR__ . '/Aws/data/directconnect/2012-10-25/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\directconnect\\2012-10-25\\api-2.json' => __DIR__ . '/Aws/data/directconnect/2012-10-25/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kinesis-video-media\\2017-09-30\\paginators-1.json' => __DIR__ . '/Aws/data/kinesis-video-media/2017-09-30/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\kinesis-video-media\\2017-09-30\\api-2.json' => __DIR__ . '/Aws/data/kinesis-video-media/2017-09-30/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\cloudsearchdomain\\2013-01-01\\api-2.json' => __DIR__ . '/Aws/data/cloudsearchdomain/2013-01-01/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\data\\route53domains\\2014-05-15\\paginators-1.json' => __DIR__ . '/Aws/data/route53domains/2014-05-15/paginators-1.json.php', 'YaySMTP\\Aws3\\Aws\\data\\route53domains\\2014-05-15\\api-2.json' => __DIR__ . '/Aws/data/route53domains/2014-05-15/api-2.json.php', 'YaySMTP\\Aws3\\Aws\\Emr\\EmrClient' => __DIR__ . '/Aws/Emr/EmrClient.php', 'YaySMTP\\Aws3\\Aws\\Emr\\Exception\\EmrException' => __DIR__ . '/Aws/Emr/Exception/EmrException.php', 'YaySMTP\\Aws3\\Aws\\KinesisVideo\\KinesisVideoClient' => __DIR__ . '/Aws/KinesisVideo/KinesisVideoClient.php', 'YaySMTP\\Aws3\\Aws\\KinesisVideo\\Exception\\KinesisVideoException' => __DIR__ . '/Aws/KinesisVideo/Exception/KinesisVideoException.php', 'YaySMTP\\Aws3\\Aws\\GameLift\\GameLiftClient' => __DIR__ . '/Aws/GameLift/GameLiftClient.php', 'YaySMTP\\Aws3\\Aws\\GameLift\\Exception\\GameLiftException' => __DIR__ . '/Aws/GameLift/Exception/GameLiftException.php', 'YaySMTP\\Aws3\\Aws\\CodeCommit\\Exception\\CodeCommitException' => __DIR__ . '/Aws/CodeCommit/Exception/CodeCommitException.php', 'YaySMTP\\Aws3\\Aws\\CodeCommit\\CodeCommitClient' => __DIR__ . '/Aws/CodeCommit/CodeCommitClient.php', 'YaySMTP\\Aws3\\Aws\\Credentials\\EcsCredentialProvider' => __DIR__ . '/Aws/Credentials/EcsCredentialProvider.php', 'YaySMTP\\Aws3\\Aws\\Credentials\\Credentials' => __DIR__ . '/Aws/Credentials/Credentials.php', 'YaySMTP\\Aws3\\Aws\\Credentials\\AssumeRoleCredentialProvider' => __DIR__ . '/Aws/Credentials/AssumeRoleCredentialProvider.php', 'YaySMTP\\Aws3\\Aws\\Credentials\\InstanceProfileProvider' => __DIR__ . '/Aws/Credentials/InstanceProfileProvider.php', 'YaySMTP\\Aws3\\Aws\\Credentials\\CredentialsInterface' => __DIR__ . '/Aws/Credentials/CredentialsInterface.php', 'YaySMTP\\Aws3\\Aws\\Credentials\\CredentialProvider' => __DIR__ . '/Aws/Credentials/CredentialProvider.php', 'YaySMTP\\Aws3\\Aws\\DAX\\Exception\\DAXException' => __DIR__ . '/Aws/DAX/Exception/DAXException.php', 'YaySMTP\\Aws3\\Aws\\DAX\\DAXClient' => __DIR__ . '/Aws/DAX/DAXClient.php', 'YaySMTP\\Aws3\\Aws\\PresignUrlMiddleware' => __DIR__ . '/Aws/PresignUrlMiddleware.php', 'YaySMTP\\Aws3\\Aws\\MQ\\Exception\\MQException' => __DIR__ . '/Aws/MQ/Exception/MQException.php', 'YaySMTP\\Aws3\\Aws\\MQ\\MQClient' => __DIR__ . '/Aws/MQ/MQClient.php', 'YaySMTP\\Aws3\\Aws\\Pinpoint\\Exception\\PinpointException' => __DIR__ . '/Aws/Pinpoint/Exception/PinpointException.php', 'YaySMTP\\Aws3\\Aws\\Pinpoint\\PinpointClient' => __DIR__ . '/Aws/Pinpoint/PinpointClient.php', 'YaySMTP\\Aws3\\Aws\\Rekognition\\RekognitionClient' => __DIR__ . '/Aws/Rekognition/RekognitionClient.php', 'YaySMTP\\Aws3\\Aws\\Rekognition\\Exception\\RekognitionException' => __DIR__ . '/Aws/Rekognition/Exception/RekognitionException.php', 'YaySMTP\\Aws3\\Aws\\ElasticBeanstalk\\Exception\\ElasticBeanstalkException' => __DIR__ . '/Aws/ElasticBeanstalk/Exception/ElasticBeanstalkException.php', 'YaySMTP\\Aws3\\Aws\\ElasticBeanstalk\\ElasticBeanstalkClient' => __DIR__ . '/Aws/ElasticBeanstalk/ElasticBeanstalkClient.php', 'YaySMTP\\Aws3\\Aws\\Athena\\Exception\\AthenaException' => __DIR__ . '/Aws/Athena/Exception/AthenaException.php', 'YaySMTP\\Aws3\\Aws\\Athena\\AthenaClient' => __DIR__ . '/Aws/Athena/AthenaClient.php', 'YaySMTP\\Aws3\\Aws\\ServiceDiscovery\\Exception\\ServiceDiscoveryException' => __DIR__ . '/Aws/ServiceDiscovery/Exception/ServiceDiscoveryException.php', 'YaySMTP\\Aws3\\Aws\\ServiceDiscovery\\ServiceDiscoveryClient' => __DIR__ . '/Aws/ServiceDiscovery/ServiceDiscoveryClient.php', 'YaySMTP\\Aws3\\Aws\\KinesisVideoMedia\\KinesisVideoMediaClient' => __DIR__ . '/Aws/KinesisVideoMedia/KinesisVideoMediaClient.php', 'YaySMTP\\Aws3\\Aws\\KinesisVideoMedia\\Exception\\KinesisVideoMediaException' => __DIR__ . '/Aws/KinesisVideoMedia/Exception/KinesisVideoMediaException.php', 'YaySMTP\\Aws3\\Aws\\TraceMiddleware' => __DIR__ . '/Aws/TraceMiddleware.php', 'YaySMTP\\Aws3\\Aws\\S3\\MultipartUploadingTrait' => __DIR__ . '/Aws/S3/MultipartUploadingTrait.php', 'YaySMTP\\Aws3\\Aws\\S3\\PutObjectUrlMiddleware' => __DIR__ . '/Aws/S3/PutObjectUrlMiddleware.php', 'YaySMTP\\Aws3\\Aws\\S3\\ObjectCopier' => __DIR__ . '/Aws/S3/ObjectCopier.php', 'YaySMTP\\Aws3\\Aws\\S3\\MultipartCopy' => __DIR__ . '/Aws/S3/MultipartCopy.php', 'YaySMTP\\Aws3\\Aws\\S3\\StreamWrapper' => __DIR__ . '/Aws/S3/StreamWrapper.php', 'YaySMTP\\Aws3\\Aws\\S3\\ApplyChecksumMiddleware' => __DIR__ . '/Aws/S3/ApplyChecksumMiddleware.php', 'YaySMTP\\Aws3\\Aws\\S3\\AmbiguousSuccessParser' => __DIR__ . '/Aws/S3/AmbiguousSuccessParser.php', 'YaySMTP\\Aws3\\Aws\\S3\\Exception\\S3Exception' => __DIR__ . '/Aws/S3/Exception/S3Exception.php', 'YaySMTP\\Aws3\\Aws\\S3\\Exception\\S3MultipartUploadException' => __DIR__ . '/Aws/S3/Exception/S3MultipartUploadException.php', 'YaySMTP\\Aws3\\Aws\\S3\\Exception\\PermanentRedirectException' => __DIR__ . '/Aws/S3/Exception/PermanentRedirectException.php', 'YaySMTP\\Aws3\\Aws\\S3\\Exception\\DeleteMultipleObjectsException' => __DIR__ . '/Aws/S3/Exception/DeleteMultipleObjectsException.php', 'YaySMTP\\Aws3\\Aws\\S3\\S3ClientInterface' => __DIR__ . '/Aws/S3/S3ClientInterface.php', 'YaySMTP\\Aws3\\Aws\\S3\\S3MultiRegionClient' => __DIR__ . '/Aws/S3/S3MultiRegionClient.php', 'YaySMTP\\Aws3\\Aws\\S3\\BatchDelete' => __DIR__ . '/Aws/S3/BatchDelete.php', 'YaySMTP\\Aws3\\Aws\\S3\\RetryableMalformedResponseParser' => __DIR__ . '/Aws/S3/RetryableMalformedResponseParser.php', 'YaySMTP\\Aws3\\Aws\\S3\\Crypto\\S3EncryptionMultipartUploader' => __DIR__ . '/Aws/S3/Crypto/S3EncryptionMultipartUploader.php', 'YaySMTP\\Aws3\\Aws\\S3\\Crypto\\HeadersMetadataStrategy' => __DIR__ . '/Aws/S3/Crypto/HeadersMetadataStrategy.php', 'YaySMTP\\Aws3\\Aws\\S3\\Crypto\\InstructionFileMetadataStrategy' => __DIR__ . '/Aws/S3/Crypto/InstructionFileMetadataStrategy.php', 'YaySMTP\\Aws3\\Aws\\S3\\Crypto\\CryptoParamsTrait' => __DIR__ . '/Aws/S3/Crypto/CryptoParamsTrait.php', 'YaySMTP\\Aws3\\Aws\\S3\\Crypto\\S3EncryptionClient' => __DIR__ . '/Aws/S3/Crypto/S3EncryptionClient.php', 'YaySMTP\\Aws3\\Aws\\S3\\S3ClientTrait' => __DIR__ . '/Aws/S3/S3ClientTrait.php', 'YaySMTP\\Aws3\\Aws\\S3\\BucketEndpointMiddleware' => __DIR__ . '/Aws/S3/BucketEndpointMiddleware.php', 'YaySMTP\\Aws3\\Aws\\S3\\PermanentRedirectMiddleware' => __DIR__ . '/Aws/S3/PermanentRedirectMiddleware.php', 'YaySMTP\\Aws3\\Aws\\S3\\MultipartUploader' => __DIR__ . '/Aws/S3/MultipartUploader.php', 'YaySMTP\\Aws3\\Aws\\S3\\S3UriParser' => __DIR__ . '/Aws/S3/S3UriParser.php', 'YaySMTP\\Aws3\\Aws\\S3\\SSECMiddleware' => __DIR__ . '/Aws/S3/SSECMiddleware.php', 'YaySMTP\\Aws3\\Aws\\S3\\GetBucketLocationParser' => __DIR__ . '/Aws/S3/GetBucketLocationParser.php', 'YaySMTP\\Aws3\\Aws\\S3\\PostObject' => __DIR__ . '/Aws/S3/PostObject.php', 'YaySMTP\\Aws3\\Aws\\S3\\Transfer' => __DIR__ . '/Aws/S3/Transfer.php', 'YaySMTP\\Aws3\\Aws\\S3\\S3Client' => __DIR__ . '/Aws/S3/S3Client.php', 'YaySMTP\\Aws3\\Aws\\S3\\ObjectUploader' => __DIR__ . '/Aws/S3/ObjectUploader.php', 'YaySMTP\\Aws3\\Aws\\S3\\PostObjectV4' => __DIR__ . '/Aws/S3/PostObjectV4.php', 'YaySMTP\\Aws3\\Aws\\S3\\S3EndpointMiddleware' => __DIR__ . '/Aws/S3/S3EndpointMiddleware.php', 'YaySMTP\\Aws3\\Aws\\Exception\\UnresolvedSignatureException' => __DIR__ . '/Aws/Exception/UnresolvedSignatureException.php', 'YaySMTP\\Aws3\\Aws\\Exception\\CredentialsException' => __DIR__ . '/Aws/Exception/CredentialsException.php', 'YaySMTP\\Aws3\\Aws\\Exception\\CouldNotCreateChecksumException' => __DIR__ . '/Aws/Exception/CouldNotCreateChecksumException.php', 'YaySMTP\\Aws3\\Aws\\Exception\\UnresolvedApiException' => __DIR__ . '/Aws/Exception/UnresolvedApiException.php', 'YaySMTP\\Aws3\\Aws\\Exception\\MultipartUploadException' => __DIR__ . '/Aws/Exception/MultipartUploadException.php', 'YaySMTP\\Aws3\\Aws\\Exception\\UnresolvedEndpointException' => __DIR__ . '/Aws/Exception/UnresolvedEndpointException.php', 'YaySMTP\\Aws3\\Aws\\Exception\\AwsException' => __DIR__ . '/Aws/Exception/AwsException.php', 'YaySMTP\\Aws3\\Aws\\Iam\\Exception\\IamException' => __DIR__ . '/Aws/Iam/Exception/IamException.php', 'YaySMTP\\Aws3\\Aws\\Iam\\IamClient' => __DIR__ . '/Aws/Iam/IamClient.php', 'YaySMTP\\Aws3\\Aws\\ApplicationAutoScaling\\Exception\\ApplicationAutoScalingException' => __DIR__ . '/Aws/ApplicationAutoScaling/Exception/ApplicationAutoScalingException.php', 'YaySMTP\\Aws3\\Aws\\ApplicationAutoScaling\\ApplicationAutoScalingClient' => __DIR__ . '/Aws/ApplicationAutoScaling/ApplicationAutoScalingClient.php', 'YaySMTP\\Aws3\\Aws\\Budgets\\Exception\\BudgetsException' => __DIR__ . '/Aws/Budgets/Exception/BudgetsException.php', 'YaySMTP\\Aws3\\Aws\\Budgets\\BudgetsClient' => __DIR__ . '/Aws/Budgets/BudgetsClient.php', 'YaySMTP\\Aws3\\Aws\\ClientResolver' => __DIR__ . '/Aws/ClientResolver.php', 'YaySMTP\\Aws3\\Aws\\Cloud9\\Cloud9Client' => __DIR__ . '/Aws/Cloud9/Cloud9Client.php', 'YaySMTP\\Aws3\\Aws\\Cloud9\\Exception\\Cloud9Exception' => __DIR__ . '/Aws/Cloud9/Exception/Cloud9Exception.php', 'YaySMTP\\Aws3\\Aws\\HashingStream' => __DIR__ . '/Aws/HashingStream.php', 'YaySMTP\\Aws3\\Aws\\Ecs\\Exception\\EcsException' => __DIR__ . '/Aws/Ecs/Exception/EcsException.php', 'YaySMTP\\Aws3\\Aws\\Ecs\\EcsClient' => __DIR__ . '/Aws/Ecs/EcsClient.php', 'YaySMTP\\Aws3\\Aws\\MarketplaceMetering\\MarketplaceMeteringClient' => __DIR__ . '/Aws/MarketplaceMetering/MarketplaceMeteringClient.php', 'YaySMTP\\Aws3\\Aws\\MarketplaceMetering\\Exception\\MarketplaceMeteringException' => __DIR__ . '/Aws/MarketplaceMetering/Exception/MarketplaceMeteringException.php', 'YaySMTP\\Aws3\\Aws\\CognitoIdentity\\Exception\\CognitoIdentityException' => __DIR__ . '/Aws/CognitoIdentity/Exception/CognitoIdentityException.php', 'YaySMTP\\Aws3\\Aws\\CognitoIdentity\\CognitoIdentityClient' => __DIR__ . '/Aws/CognitoIdentity/CognitoIdentityClient.php', 'YaySMTP\\Aws3\\Aws\\CognitoIdentity\\CognitoIdentityProvider' => __DIR__ . '/Aws/CognitoIdentity/CognitoIdentityProvider.php', 'YaySMTP\\Aws3\\Aws\\ApplicationDiscoveryService\\Exception\\ApplicationDiscoveryServiceException' => __DIR__ . '/Aws/ApplicationDiscoveryService/Exception/ApplicationDiscoveryServiceException.php', 'YaySMTP\\Aws3\\Aws\\ApplicationDiscoveryService\\ApplicationDiscoveryServiceClient' => __DIR__ . '/Aws/ApplicationDiscoveryService/ApplicationDiscoveryServiceClient.php', 'YaySMTP\\Aws3\\Aws\\Firehose\\Exception\\FirehoseException' => __DIR__ . '/Aws/Firehose/Exception/FirehoseException.php', 'YaySMTP\\Aws3\\Aws\\Firehose\\FirehoseClient' => __DIR__ . '/Aws/Firehose/FirehoseClient.php', 'YaySMTP\\Aws3\\Aws\\Route53\\Exception\\Route53Exception' => __DIR__ . '/Aws/Route53/Exception/Route53Exception.php', 'YaySMTP\\Aws3\\Aws\\Route53\\Route53Client' => __DIR__ . '/Aws/Route53/Route53Client.php', 'YaySMTP\\Aws3\\Aws\\CloudTrail\\Exception\\CloudTrailException' => __DIR__ . '/Aws/CloudTrail/Exception/CloudTrailException.php', 'YaySMTP\\Aws3\\Aws\\CloudTrail\\LogFileIterator' => __DIR__ . '/Aws/CloudTrail/LogFileIterator.php', 'YaySMTP\\Aws3\\Aws\\CloudTrail\\LogFileReader' => __DIR__ . '/Aws/CloudTrail/LogFileReader.php', 'YaySMTP\\Aws3\\Aws\\CloudTrail\\CloudTrailClient' => __DIR__ . '/Aws/CloudTrail/CloudTrailClient.php', 'YaySMTP\\Aws3\\Aws\\CloudTrail\\LogRecordIterator' => __DIR__ . '/Aws/CloudTrail/LogRecordIterator.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\AesEncryptingStream' => __DIR__ . '/Aws/Crypto/AesEncryptingStream.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\EncryptionTrait' => __DIR__ . '/Aws/Crypto/EncryptionTrait.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\MetadataStrategyInterface' => __DIR__ . '/Aws/Crypto/MetadataStrategyInterface.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\AesGcmEncryptingStream' => __DIR__ . '/Aws/Crypto/AesGcmEncryptingStream.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\Cipher\\CipherBuilderTrait' => __DIR__ . '/Aws/Crypto/Cipher/CipherBuilderTrait.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\Cipher\\Cbc' => __DIR__ . '/Aws/Crypto/Cipher/Cbc.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\Cipher\\CipherMethod' => __DIR__ . '/Aws/Crypto/Cipher/CipherMethod.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\AesStreamInterface' => __DIR__ . '/Aws/Crypto/AesStreamInterface.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\AbstractCryptoClient' => __DIR__ . '/Aws/Crypto/AbstractCryptoClient.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\AesDecryptingStream' => __DIR__ . '/Aws/Crypto/AesDecryptingStream.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\AesGcmDecryptingStream' => __DIR__ . '/Aws/Crypto/AesGcmDecryptingStream.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\KmsMaterialsProvider' => __DIR__ . '/Aws/Crypto/KmsMaterialsProvider.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\MetadataEnvelope' => __DIR__ . '/Aws/Crypto/MetadataEnvelope.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\MaterialsProvider' => __DIR__ . '/Aws/Crypto/MaterialsProvider.php', 'YaySMTP\\Aws3\\Aws\\Crypto\\DecryptionTrait' => __DIR__ . '/Aws/Crypto/DecryptionTrait.php', 'YaySMTP\\Aws3\\Aws\\DirectoryService\\Exception\\DirectoryServiceException' => __DIR__ . '/Aws/DirectoryService/Exception/DirectoryServiceException.php', 'YaySMTP\\Aws3\\Aws\\DirectoryService\\DirectoryServiceClient' => __DIR__ . '/Aws/DirectoryService/DirectoryServiceClient.php', 'YaySMTP\\Aws3\\Aws\\ACMPCA\\ACMPCAClient' => __DIR__ . '/Aws/ACMPCA/ACMPCAClient.php', 'YaySMTP\\Aws3\\Aws\\ACMPCA\\Exception\\ACMPCAException' => __DIR__ . '/Aws/ACMPCA/Exception/ACMPCAException.php', 'YaySMTP\\Aws3\\Aws\\MachineLearning\\MachineLearningClient' => __DIR__ . '/Aws/MachineLearning/MachineLearningClient.php', 'YaySMTP\\Aws3\\Aws\\MachineLearning\\Exception\\MachineLearningException' => __DIR__ . '/Aws/MachineLearning/Exception/MachineLearningException.php', 'YaySMTP\\Aws3\\Aws\\Route53Domains\\Exception\\Route53DomainsException' => __DIR__ . '/Aws/Route53Domains/Exception/Route53DomainsException.php', 'YaySMTP\\Aws3\\Aws\\Route53Domains\\Route53DomainsClient' => __DIR__ . '/Aws/Route53Domains/Route53DomainsClient.php', 'YaySMTP\\Aws3\\Aws\\LruArrayCache' => __DIR__ . '/Aws/LruArrayCache.php', 'YaySMTP\\Aws3\\Aws\\Kms\\Exception\\KmsException' => __DIR__ . '/Aws/Kms/Exception/KmsException.php', 'YaySMTP\\Aws3\\Aws\\Kms\\KmsClient' => __DIR__ . '/Aws/Kms/KmsClient.php', 'YaySMTP\\Aws3\\Aws\\CommandPool' => __DIR__ . '/Aws/CommandPool.php', 'YaySMTP\\Aws3\\Aws\\CloudWatchEvents\\Exception\\CloudWatchEventsException' => __DIR__ . '/Aws/CloudWatchEvents/Exception/CloudWatchEventsException.php', 'YaySMTP\\Aws3\\Aws\\CloudWatchEvents\\CloudWatchEventsClient' => __DIR__ . '/Aws/CloudWatchEvents/CloudWatchEventsClient.php', 'YaySMTP\\Aws3\\Aws\\AutoScaling\\Exception\\AutoScalingException' => __DIR__ . '/Aws/AutoScaling/Exception/AutoScalingException.php', 'YaySMTP\\Aws3\\Aws\\AutoScaling\\AutoScalingClient' => __DIR__ . '/Aws/AutoScaling/AutoScalingClient.php', 'YaySMTP\\Aws3\\Aws\\Mobile\\MobileClient' => __DIR__ . '/Aws/Mobile/MobileClient.php', 'YaySMTP\\Aws3\\Aws\\Mobile\\Exception\\MobileException' => __DIR__ . '/Aws/Mobile/Exception/MobileException.php', 'YaySMTP\\Aws3\\Aws\\CloudSearchDomain\\Exception\\CloudSearchDomainException' => __DIR__ . '/Aws/CloudSearchDomain/Exception/CloudSearchDomainException.php', 'YaySMTP\\Aws3\\Aws\\CloudSearchDomain\\CloudSearchDomainClient' => __DIR__ . '/Aws/CloudSearchDomain/CloudSearchDomainClient.php', 'YaySMTP\\Aws3\\Aws\\Api\\Serializer\\RestJsonSerializer' => __DIR__ . '/Aws/Api/Serializer/RestJsonSerializer.php', 'YaySMTP\\Aws3\\Aws\\Api\\Serializer\\QuerySerializer' => __DIR__ . '/Aws/Api/Serializer/QuerySerializer.php', 'YaySMTP\\Aws3\\Aws\\Api\\Serializer\\Ec2ParamBuilder' => __DIR__ . '/Aws/Api/Serializer/Ec2ParamBuilder.php', 'YaySMTP\\Aws3\\Aws\\Api\\Serializer\\RestSerializer' => __DIR__ . '/Aws/Api/Serializer/RestSerializer.php', 'YaySMTP\\Aws3\\Aws\\Api\\Serializer\\JsonBody' => __DIR__ . '/Aws/Api/Serializer/JsonBody.php', 'YaySMTP\\Aws3\\Aws\\Api\\Serializer\\XmlBody' => __DIR__ . '/Aws/Api/Serializer/XmlBody.php', 'YaySMTP\\Aws3\\Aws\\Api\\Serializer\\JsonRpcSerializer' => __DIR__ . '/Aws/Api/Serializer/JsonRpcSerializer.php', 'YaySMTP\\Aws3\\Aws\\Api\\Serializer\\RestXmlSerializer' => __DIR__ . '/Aws/Api/Serializer/RestXmlSerializer.php', 'YaySMTP\\Aws3\\Aws\\Api\\Serializer\\QueryParamBuilder' => __DIR__ . '/Aws/Api/Serializer/QueryParamBuilder.php', 'YaySMTP\\Aws3\\Aws\\Api\\Shape' => __DIR__ . '/Aws/Api/Shape.php', 'YaySMTP\\Aws3\\Aws\\Api\\Operation' => __DIR__ . '/Aws/Api/Operation.php', 'YaySMTP\\Aws3\\Aws\\Api\\StructureShape' => __DIR__ . '/Aws/Api/StructureShape.php', 'YaySMTP\\Aws3\\Aws\\Api\\DateTimeResult' => __DIR__ . '/Aws/Api/DateTimeResult.php', 'YaySMTP\\Aws3\\Aws\\Api\\DocModel' => __DIR__ . '/Aws/Api/DocModel.php', 'YaySMTP\\Aws3\\Aws\\Api\\AbstractModel' => __DIR__ . '/Aws/Api/AbstractModel.php', 'YaySMTP\\Aws3\\Aws\\Api\\MapShape' => __DIR__ . '/Aws/Api/MapShape.php', 'YaySMTP\\Aws3\\Aws\\Api\\Parser\\RestXmlParser' => __DIR__ . '/Aws/Api/Parser/RestXmlParser.php', 'YaySMTP\\Aws3\\Aws\\Api\\Parser\\AbstractParser' => __DIR__ . '/Aws/Api/Parser/AbstractParser.php', 'YaySMTP\\Aws3\\Aws\\Api\\Parser\\Exception\\ParserException' => __DIR__ . '/Aws/Api/Parser/Exception/ParserException.php', 'YaySMTP\\Aws3\\Aws\\Api\\Parser\\Crc32ValidatingParser' => __DIR__ . '/Aws/Api/Parser/Crc32ValidatingParser.php', 'YaySMTP\\Aws3\\Aws\\Api\\Parser\\XmlParser' => __DIR__ . '/Aws/Api/Parser/XmlParser.php', 'YaySMTP\\Aws3\\Aws\\Api\\Parser\\QueryParser' => __DIR__ . '/Aws/Api/Parser/QueryParser.php', 'YaySMTP\\Aws3\\Aws\\Api\\Parser\\RestJsonParser' => __DIR__ . '/Aws/Api/Parser/RestJsonParser.php', 'YaySMTP\\Aws3\\Aws\\Api\\Parser\\JsonParser' => __DIR__ . '/Aws/Api/Parser/JsonParser.php', 'YaySMTP\\Aws3\\Aws\\Api\\Parser\\AbstractRestParser' => __DIR__ . '/Aws/Api/Parser/AbstractRestParser.php', 'YaySMTP\\Aws3\\Aws\\Api\\Parser\\PayloadParserTrait' => __DIR__ . '/Aws/Api/Parser/PayloadParserTrait.php', 'YaySMTP\\Aws3\\Aws\\Api\\Parser\\JsonRpcParser' => __DIR__ . '/Aws/Api/Parser/JsonRpcParser.php', 'YaySMTP\\Aws3\\Aws\\Api\\Service' => __DIR__ . '/Aws/Api/Service.php', 'YaySMTP\\Aws3\\Aws\\Api\\ErrorParser\\JsonParserTrait' => __DIR__ . '/Aws/Api/ErrorParser/JsonParserTrait.php', 'YaySMTP\\Aws3\\Aws\\Api\\ErrorParser\\JsonRpcErrorParser' => __DIR__ . '/Aws/Api/ErrorParser/JsonRpcErrorParser.php', 'YaySMTP\\Aws3\\Aws\\Api\\ErrorParser\\RestJsonErrorParser' => __DIR__ . '/Aws/Api/ErrorParser/RestJsonErrorParser.php', 'YaySMTP\\Aws3\\Aws\\Api\\ErrorParser\\XmlErrorParser' => __DIR__ . '/Aws/Api/ErrorParser/XmlErrorParser.php', 'YaySMTP\\Aws3\\Aws\\Api\\Validator' => __DIR__ . '/Aws/Api/Validator.php', 'YaySMTP\\Aws3\\Aws\\Api\\ApiProvider' => __DIR__ . '/Aws/Api/ApiProvider.php', 'YaySMTP\\Aws3\\Aws\\Api\\ListShape' => __DIR__ . '/Aws/Api/ListShape.php', 'YaySMTP\\Aws3\\Aws\\Api\\TimestampShape' => __DIR__ . '/Aws/Api/TimestampShape.php', 'YaySMTP\\Aws3\\Aws\\Api\\ShapeMap' => __DIR__ . '/Aws/Api/ShapeMap.php', 'YaySMTP\\Aws3\\Aws\\Redshift\\Exception\\RedshiftException' => __DIR__ . '/Aws/Redshift/Exception/RedshiftException.php', 'YaySMTP\\Aws3\\Aws\\Redshift\\RedshiftClient' => __DIR__ . '/Aws/Redshift/RedshiftClient.php', 'YaySMTP\\Aws3\\Aws\\SageMakerRuntime\\Exception\\SageMakerRuntimeException' => __DIR__ . '/Aws/SageMakerRuntime/Exception/SageMakerRuntimeException.php', 'YaySMTP\\Aws3\\Aws\\SageMakerRuntime\\SageMakerRuntimeClient' => __DIR__ . '/Aws/SageMakerRuntime/SageMakerRuntimeClient.php', 'YaySMTP\\Aws3\\Aws\\IoTAnalytics\\IoTAnalyticsClient' => __DIR__ . '/Aws/IoTAnalytics/IoTAnalyticsClient.php', 'YaySMTP\\Aws3\\Aws\\IoTAnalytics\\Exception\\IoTAnalyticsException' => __DIR__ . '/Aws/IoTAnalytics/Exception/IoTAnalyticsException.php', 'YaySMTP\\Aws3\\Aws\\Sts\\Exception\\StsException' => __DIR__ . '/Aws/Sts/Exception/StsException.php', 'YaySMTP\\Aws3\\Aws\\Sts\\StsClient' => __DIR__ . '/Aws/Sts/StsClient.php', 'YaySMTP\\Aws3\\Aws\\Ecr\\Exception\\EcrException' => __DIR__ . '/Aws/Ecr/Exception/EcrException.php', 'YaySMTP\\Aws3\\Aws\\Ecr\\EcrClient' => __DIR__ . '/Aws/Ecr/EcrClient.php', 'YaySMTP\\Aws3\\Aws\\CodeStar\\Exception\\CodeStarException' => __DIR__ . '/Aws/CodeStar/Exception/CodeStarException.php', 'YaySMTP\\Aws3\\Aws\\CodeStar\\CodeStarClient' => __DIR__ . '/Aws/CodeStar/CodeStarClient.php', 'YaySMTP\\Aws3\\Aws\\Sfn\\Exception\\SfnException' => __DIR__ . '/Aws/Sfn/Exception/SfnException.php', 'YaySMTP\\Aws3\\Aws\\Sfn\\SfnClient' => __DIR__ . '/Aws/Sfn/SfnClient.php', 'YaySMTP\\Aws3\\Aws\\AwsClient' => __DIR__ . '/Aws/AwsClient.php', 'YaySMTP\\Aws3\\Aws\\CloudHSMV2\\Exception\\CloudHSMV2Exception' => __DIR__ . '/Aws/CloudHSMV2/Exception/CloudHSMV2Exception.php', 'YaySMTP\\Aws3\\Aws\\CloudHSMV2\\CloudHSMV2Client' => __DIR__ . '/Aws/CloudHSMV2/CloudHSMV2Client.php', 'YaySMTP\\Aws3\\Aws\\ResultPaginator' => __DIR__ . '/Aws/ResultPaginator.php', 'YaySMTP\\Aws3\\Aws\\HashInterface' => __DIR__ . '/Aws/HashInterface.php', 'YaySMTP\\Aws3\\Aws\\CodePipeline\\Exception\\CodePipelineException' => __DIR__ . '/Aws/CodePipeline/Exception/CodePipelineException.php', 'YaySMTP\\Aws3\\Aws\\CodePipeline\\CodePipelineClient' => __DIR__ . '/Aws/CodePipeline/CodePipelineClient.php', 'YaySMTP\\Aws3\\Aws\\Acm\\AcmClient' => __DIR__ . '/Aws/Acm/AcmClient.php', 'YaySMTP\\Aws3\\Aws\\Acm\\Exception\\AcmException' => __DIR__ . '/Aws/Acm/Exception/AcmException.php', 'YaySMTP\\Aws3\\Aws\\Glue\\GlueClient' => __DIR__ . '/Aws/Glue/GlueClient.php', 'YaySMTP\\Aws3\\Aws\\Glue\\Exception\\GlueException' => __DIR__ . '/Aws/Glue/Exception/GlueException.php', 'YaySMTP\\Aws3\\Aws\\CommandInterface' => __DIR__ . '/Aws/CommandInterface.php', 'YaySMTP\\Aws3\\Aws\\CloudHsm\\Exception\\CloudHsmException' => __DIR__ . '/Aws/CloudHsm/Exception/CloudHsmException.php', 'YaySMTP\\Aws3\\Aws\\CloudHsm\\CloudHsmClient' => __DIR__ . '/Aws/CloudHsm/CloudHsmClient.php', 'YaySMTP\\Aws3\\Aws\\Shield\\Exception\\ShieldException' => __DIR__ . '/Aws/Shield/Exception/ShieldException.php', 'YaySMTP\\Aws3\\Aws\\Shield\\ShieldClient' => __DIR__ . '/Aws/Shield/ShieldClient.php', 'YaySMTP\\Aws3\\Aws\\AppSync\\Exception\\AppSyncException' => __DIR__ . '/Aws/AppSync/Exception/AppSyncException.php', 'YaySMTP\\Aws3\\Aws\\AppSync\\AppSyncClient' => __DIR__ . '/Aws/AppSync/AppSyncClient.php', 'YaySMTP\\Aws3\\Aws\\CloudDirectory\\Exception\\CloudDirectoryException' => __DIR__ . '/Aws/CloudDirectory/Exception/CloudDirectoryException.php', 'YaySMTP\\Aws3\\Aws\\CloudDirectory\\CloudDirectoryClient' => __DIR__ . '/Aws/CloudDirectory/CloudDirectoryClient.php', 'YaySMTP\\Aws3\\Aws\\AwsClientTrait' => __DIR__ . '/Aws/AwsClientTrait.php', 'YaySMTP\\Aws3\\Aws\\Greengrass\\GreengrassClient' => __DIR__ . '/Aws/Greengrass/GreengrassClient.php', 'YaySMTP\\Aws3\\Aws\\Greengrass\\Exception\\GreengrassException' => __DIR__ . '/Aws/Greengrass/Exception/GreengrassException.php', 'YaySMTP\\Aws3\\Aws\\Signature\\SignatureV4' => __DIR__ . '/Aws/Signature/SignatureV4.php', 'YaySMTP\\Aws3\\Aws\\Signature\\AnonymousSignature' => __DIR__ . '/Aws/Signature/AnonymousSignature.php', 'YaySMTP\\Aws3\\Aws\\Signature\\SignatureProvider' => __DIR__ . '/Aws/Signature/SignatureProvider.php', 'YaySMTP\\Aws3\\Aws\\Signature\\S3SignatureV4' => __DIR__ . '/Aws/Signature/S3SignatureV4.php', 'YaySMTP\\Aws3\\Aws\\Signature\\SignatureTrait' => __DIR__ . '/Aws/Signature/SignatureTrait.php', 'YaySMTP\\Aws3\\Aws\\Signature\\SignatureInterface' => __DIR__ . '/Aws/Signature/SignatureInterface.php', 'YaySMTP\\Aws3\\Aws\\Command' => __DIR__ . '/Aws/Command.php', 'YaySMTP\\Aws3\\Aws\\CostExplorer\\Exception\\CostExplorerException' => __DIR__ . '/Aws/CostExplorer/Exception/CostExplorerException.php', 'YaySMTP\\Aws3\\Aws\\CostExplorer\\CostExplorerClient' => __DIR__ . '/Aws/CostExplorer/CostExplorerClient.php', 'YaySMTP\\Aws3\\Aws\\MediaStoreData\\Exception\\MediaStoreDataException' => __DIR__ . '/Aws/MediaStoreData/Exception/MediaStoreDataException.php', 'YaySMTP\\Aws3\\Aws\\MediaStoreData\\MediaStoreDataClient' => __DIR__ . '/Aws/MediaStoreData/MediaStoreDataClient.php', 'YaySMTP\\Aws3\\Aws\\Sqs\\Exception\\SqsException' => __DIR__ . '/Aws/Sqs/Exception/SqsException.php', 'YaySMTP\\Aws3\\Aws\\Sqs\\SqsClient' => __DIR__ . '/Aws/Sqs/SqsClient.php', 'YaySMTP\\Aws3\\Aws\\IotDataPlane\\Exception\\IotDataPlaneException' => __DIR__ . '/Aws/IotDataPlane/Exception/IotDataPlaneException.php', 'YaySMTP\\Aws3\\Aws\\IotDataPlane\\IotDataPlaneClient' => __DIR__ . '/Aws/IotDataPlane/IotDataPlaneClient.php', 'YaySMTP\\Aws3\\Aws\\SageMaker\\SageMakerClient' => __DIR__ . '/Aws/SageMaker/SageMakerClient.php', 'YaySMTP\\Aws3\\Aws\\SageMaker\\Exception\\SageMakerException' => __DIR__ . '/Aws/SageMaker/Exception/SageMakerException.php', 'YaySMTP\\Aws3\\Aws\\CodeBuild\\Exception\\CodeBuildException' => __DIR__ . '/Aws/CodeBuild/Exception/CodeBuildException.php', 'YaySMTP\\Aws3\\Aws\\CodeBuild\\CodeBuildClient' => __DIR__ . '/Aws/CodeBuild/CodeBuildClient.php', 'YaySMTP\\Aws3\\Aws\\AutoScalingPlans\\Exception\\AutoScalingPlansException' => __DIR__ . '/Aws/AutoScalingPlans/Exception/AutoScalingPlansException.php', 'YaySMTP\\Aws3\\Aws\\AutoScalingPlans\\AutoScalingPlansClient' => __DIR__ . '/Aws/AutoScalingPlans/AutoScalingPlansClient.php', 'YaySMTP\\Aws3\\Aws\\WorkSpaces\\WorkSpacesClient' => __DIR__ . '/Aws/WorkSpaces/WorkSpacesClient.php', 'YaySMTP\\Aws3\\Aws\\WorkSpaces\\Exception\\WorkSpacesException' => __DIR__ . '/Aws/WorkSpaces/Exception/WorkSpacesException.php', 'YaySMTP\\Aws3\\Aws\\History' => __DIR__ . '/Aws/History.php', 'YaySMTP\\Aws3\\Aws\\MediaConvert\\Exception\\MediaConvertException' => __DIR__ . '/Aws/MediaConvert/Exception/MediaConvertException.php', 'YaySMTP\\Aws3\\Aws\\MediaConvert\\MediaConvertClient' => __DIR__ . '/Aws/MediaConvert/MediaConvertClient.php', 'YaySMTP\\Aws3\\Aws\\DeviceFarm\\Exception\\DeviceFarmException' => __DIR__ . '/Aws/DeviceFarm/Exception/DeviceFarmException.php', 'YaySMTP\\Aws3\\Aws\\DeviceFarm\\DeviceFarmClient' => __DIR__ . '/Aws/DeviceFarm/DeviceFarmClient.php', 'YaySMTP\\Aws3\\Aws\\OpsWorks\\Exception\\OpsWorksException' => __DIR__ . '/Aws/OpsWorks/Exception/OpsWorksException.php', 'YaySMTP\\Aws3\\Aws\\OpsWorks\\OpsWorksClient' => __DIR__ . '/Aws/OpsWorks/OpsWorksClient.php', 'YaySMTP\\Aws3\\Aws\\HandlerList' => __DIR__ . '/Aws/HandlerList.php', 'YaySMTP\\Aws3\\Aws\\CognitoSync\\CognitoSyncClient' => __DIR__ . '/Aws/CognitoSync/CognitoSyncClient.php', 'YaySMTP\\Aws3\\Aws\\CognitoSync\\Exception\\CognitoSyncException' => __DIR__ . '/Aws/CognitoSync/Exception/CognitoSyncException.php', 'YaySMTP\\Aws3\\Aws\\ElasticLoadBalancingV2\\Exception\\ElasticLoadBalancingV2Exception' => __DIR__ . '/Aws/ElasticLoadBalancingV2/Exception/ElasticLoadBalancingV2Exception.php', 'YaySMTP\\Aws3\\Aws\\ElasticLoadBalancingV2\\ElasticLoadBalancingV2Client' => __DIR__ . '/Aws/ElasticLoadBalancingV2/ElasticLoadBalancingV2Client.php', 'YaySMTP\\Aws3\\Aws\\IdempotencyTokenMiddleware' => __DIR__ . '/Aws/IdempotencyTokenMiddleware.php', 'YaySMTP\\Aws3\\Aws\\FMS\\Exception\\FMSException' => __DIR__ . '/Aws/FMS/Exception/FMSException.php', 'YaySMTP\\Aws3\\Aws\\FMS\\FMSClient' => __DIR__ . '/Aws/FMS/FMSClient.php', 'YaySMTP\\Aws3\\Aws\\CacheInterface' => __DIR__ . '/Aws/CacheInterface.php', 'YaySMTP\\Aws3\\Aws\\ResultInterface' => __DIR__ . '/Aws/ResultInterface.php', 'YaySMTP\\Aws3\\Aws\\CostandUsageReportService\\CostandUsageReportServiceClient' => __DIR__ . '/Aws/CostandUsageReportService/CostandUsageReportServiceClient.php', 'YaySMTP\\Aws3\\Aws\\CostandUsageReportService\\Exception\\CostandUsageReportServiceException' => __DIR__ . '/Aws/CostandUsageReportService/Exception/CostandUsageReportServiceException.php', 'YaySMTP\\Aws3\\Aws\\MediaPackage\\Exception\\MediaPackageException' => __DIR__ . '/Aws/MediaPackage/Exception/MediaPackageException.php', 'YaySMTP\\Aws3\\Aws\\MediaPackage\\MediaPackageClient' => __DIR__ . '/Aws/MediaPackage/MediaPackageClient.php', 'YaySMTP\\Aws3\\Aws\\Ec2\\Exception\\Ec2Exception' => __DIR__ . '/Aws/Ec2/Exception/Ec2Exception.php', 'YaySMTP\\Aws3\\Aws\\Ec2\\Ec2Client' => __DIR__ . '/Aws/Ec2/Ec2Client.php', 'YaySMTP\\Aws3\\Aws\\DynamoDb\\DynamoDbClient' => __DIR__ . '/Aws/DynamoDb/DynamoDbClient.php', 'YaySMTP\\Aws3\\Aws\\DynamoDb\\NumberValue' => __DIR__ . '/Aws/DynamoDb/NumberValue.php', 'YaySMTP\\Aws3\\Aws\\DynamoDb\\Exception\\DynamoDbException' => __DIR__ . '/Aws/DynamoDb/Exception/DynamoDbException.php', 'YaySMTP\\Aws3\\Aws\\DynamoDb\\StandardSessionConnection' => __DIR__ . '/Aws/DynamoDb/StandardSessionConnection.php', 'YaySMTP\\Aws3\\Aws\\DynamoDb\\WriteRequestBatch' => __DIR__ . '/Aws/DynamoDb/WriteRequestBatch.php', 'YaySMTP\\Aws3\\Aws\\DynamoDb\\Marshaler' => __DIR__ . '/Aws/DynamoDb/Marshaler.php', 'YaySMTP\\Aws3\\Aws\\DynamoDb\\BinaryValue' => __DIR__ . '/Aws/DynamoDb/BinaryValue.php', 'YaySMTP\\Aws3\\Aws\\DynamoDb\\SetValue' => __DIR__ . '/Aws/DynamoDb/SetValue.php', 'YaySMTP\\Aws3\\Aws\\DynamoDb\\SessionHandler' => __DIR__ . '/Aws/DynamoDb/SessionHandler.php', 'YaySMTP\\Aws3\\Aws\\DynamoDb\\SessionConnectionInterface' => __DIR__ . '/Aws/DynamoDb/SessionConnectionInterface.php', 'YaySMTP\\Aws3\\Aws\\DynamoDb\\LockingSessionConnection' => __DIR__ . '/Aws/DynamoDb/LockingSessionConnection.php', 'YaySMTP\\Aws3\\Aws\\MockHandler' => __DIR__ . '/Aws/MockHandler.php', 'YaySMTP\\Aws3\\Aws\\ResourceGroups\\Exception\\ResourceGroupsException' => __DIR__ . '/Aws/ResourceGroups/Exception/ResourceGroupsException.php', 'YaySMTP\\Aws3\\Aws\\ResourceGroups\\ResourceGroupsClient' => __DIR__ . '/Aws/ResourceGroups/ResourceGroupsClient.php', 'YaySMTP\\Aws3\\Aws\\OpsWorksCM\\OpsWorksCMClient' => __DIR__ . '/Aws/OpsWorksCM/OpsWorksCMClient.php', 'YaySMTP\\Aws3\\Aws\\OpsWorksCM\\Exception\\OpsWorksCMException' => __DIR__ . '/Aws/OpsWorksCM/Exception/OpsWorksCMException.php', 'YaySMTP\\Aws3\\Aws\\Swf\\Exception\\SwfException' => __DIR__ . '/Aws/Swf/Exception/SwfException.php', 'YaySMTP\\Aws3\\Aws\\Swf\\SwfClient' => __DIR__ . '/Aws/Swf/SwfClient.php', 'YaySMTP\\Aws3\\Aws\\Sdk' => __DIR__ . '/Aws/Sdk.php', 'YaySMTP\\Aws3\\Aws\\CognitoIdentityProvider\\Exception\\CognitoIdentityProviderException' => __DIR__ . '/Aws/CognitoIdentityProvider/Exception/CognitoIdentityProviderException.php', 'YaySMTP\\Aws3\\Aws\\CognitoIdentityProvider\\CognitoIdentityProviderClient' => __DIR__ . '/Aws/CognitoIdentityProvider/CognitoIdentityProviderClient.php', 'YaySMTP\\Aws3\\Aws\\PsrCacheAdapter' => __DIR__ . '/Aws/PsrCacheAdapter.php', 'YaySMTP\\Aws3\\Aws\\DataPipeline\\DataPipelineClient' => __DIR__ . '/Aws/DataPipeline/DataPipelineClient.php', 'YaySMTP\\Aws3\\Aws\\DataPipeline\\Exception\\DataPipelineException' => __DIR__ . '/Aws/DataPipeline/Exception/DataPipelineException.php', 'YaySMTP\\Aws3\\Aws\\Translate\\Exception\\TranslateException' => __DIR__ . '/Aws/Translate/Exception/TranslateException.php', 'YaySMTP\\Aws3\\Aws\\Translate\\TranslateClient' => __DIR__ . '/Aws/Translate/TranslateClient.php', 'YaySMTP\\Aws3\\Aws\\Batch\\Exception\\BatchException' => __DIR__ . '/Aws/Batch/Exception/BatchException.php', 'YaySMTP\\Aws3\\Aws\\Batch\\BatchClient' => __DIR__ . '/Aws/Batch/BatchClient.php', 'YaySMTP\\Aws3\\Aws\\Organizations\\Exception\\OrganizationsException' => __DIR__ . '/Aws/Organizations/Exception/OrganizationsException.php', 'YaySMTP\\Aws3\\Aws\\Organizations\\OrganizationsClient' => __DIR__ . '/Aws/Organizations/OrganizationsClient.php', 'YaySMTP\\Aws3\\Aws\\Support\\Exception\\SupportException' => __DIR__ . '/Aws/Support/Exception/SupportException.php', 'YaySMTP\\Aws3\\Aws\\Support\\SupportClient' => __DIR__ . '/Aws/Support/SupportClient.php', 'YaySMTP\\Aws3\\Aws\\Pricing\\Exception\\PricingException' => __DIR__ . '/Aws/Pricing/Exception/PricingException.php', 'YaySMTP\\Aws3\\Aws\\Pricing\\PricingClient' => __DIR__ . '/Aws/Pricing/PricingClient.php', 'YaySMTP\\Aws3\\Aws\\ResourceGroupsTaggingAPI\\ResourceGroupsTaggingAPIClient' => __DIR__ . '/Aws/ResourceGroupsTaggingAPI/ResourceGroupsTaggingAPIClient.php', 'YaySMTP\\Aws3\\Aws\\ResourceGroupsTaggingAPI\\Exception\\ResourceGroupsTaggingAPIException' => __DIR__ . '/Aws/ResourceGroupsTaggingAPI/Exception/ResourceGroupsTaggingAPIException.php', 'YaySMTP\\Aws3\\Aws\\CloudWatch\\CloudWatchClient' => __DIR__ . '/Aws/CloudWatch/CloudWatchClient.php', 'YaySMTP\\Aws3\\Aws\\CloudWatch\\Exception\\CloudWatchException' => __DIR__ . '/Aws/CloudWatch/Exception/CloudWatchException.php', 'YaySMTP\\Aws3\\Aws\\AwsClientInterface' => __DIR__ . '/Aws/AwsClientInterface.php', 'YaySMTP\\Aws3\\Aws\\DirectConnect\\DirectConnectClient' => __DIR__ . '/Aws/DirectConnect/DirectConnectClient.php', 'YaySMTP\\Aws3\\Aws\\DirectConnect\\Exception\\DirectConnectException' => __DIR__ . '/Aws/DirectConnect/Exception/DirectConnectException.php', 'YaySMTP\\Aws3\\Aws\\MultiRegionClient' => __DIR__ . '/Aws/MultiRegionClient.php', 'YaySMTP\\Aws3\\Aws\\Ssm\\Exception\\SsmException' => __DIR__ . '/Aws/Ssm/Exception/SsmException.php', 'YaySMTP\\Aws3\\Aws\\Ssm\\SsmClient' => __DIR__ . '/Aws/Ssm/SsmClient.php', 'YaySMTP\\Aws3\\Aws\\WrappedHttpHandler' => __DIR__ . '/Aws/WrappedHttpHandler.php', 'YaySMTP\\Aws3\\Aws\\ImportExport\\ImportExportClient' => __DIR__ . '/Aws/ImportExport/ImportExportClient.php', 'YaySMTP\\Aws3\\Aws\\ImportExport\\Exception\\ImportExportException' => __DIR__ . '/Aws/ImportExport/Exception/ImportExportException.php', 'YaySMTP\\Aws3\\Aws\\functions' => __DIR__ . '/Aws/functions.php', 'YaySMTP\\Aws3\\Aws\\Polly\\Exception\\PollyException' => __DIR__ . '/Aws/Polly/Exception/PollyException.php', 'YaySMTP\\Aws3\\Aws\\Polly\\PollyClient' => __DIR__ . '/Aws/Polly/PollyClient.php', 'YaySMTP\\Aws3\\Aws\\ElastiCache\\Exception\\ElastiCacheException' => __DIR__ . '/Aws/ElastiCache/Exception/ElastiCacheException.php', 'YaySMTP\\Aws3\\Aws\\ElastiCache\\ElastiCacheClient' => __DIR__ . '/Aws/ElastiCache/ElastiCacheClient.php', 'YaySMTP\\Aws3\\Aws\\Appstream\\Exception\\AppstreamException' => __DIR__ . '/Aws/Appstream/Exception/AppstreamException.php', 'YaySMTP\\Aws3\\Aws\\Appstream\\AppstreamClient' => __DIR__ . '/Aws/Appstream/AppstreamClient.php', 'YaySMTP\\Aws3\\Aws\\Glacier\\Exception\\GlacierException' => __DIR__ . '/Aws/Glacier/Exception/GlacierException.php', 'YaySMTP\\Aws3\\Aws\\Glacier\\MultipartUploader' => __DIR__ . '/Aws/Glacier/MultipartUploader.php', 'YaySMTP\\Aws3\\Aws\\Glacier\\GlacierClient' => __DIR__ . '/Aws/Glacier/GlacierClient.php', 'YaySMTP\\Aws3\\Aws\\Glacier\\TreeHash' => __DIR__ . '/Aws/Glacier/TreeHash.php', 'YaySMTP\\Aws3\\Aws\\KinesisVideoArchivedMedia\\Exception\\KinesisVideoArchivedMediaException' => __DIR__ . '/Aws/KinesisVideoArchivedMedia/Exception/KinesisVideoArchivedMediaException.php', 'YaySMTP\\Aws3\\Aws\\KinesisVideoArchivedMedia\\KinesisVideoArchivedMediaClient' => __DIR__ . '/Aws/KinesisVideoArchivedMedia/KinesisVideoArchivedMediaClient.php', 'YaySMTP\\Aws3\\Aws\\SecretsManager\\SecretsManagerClient' => __DIR__ . '/Aws/SecretsManager/SecretsManagerClient.php', 'YaySMTP\\Aws3\\Aws\\SecretsManager\\Exception\\SecretsManagerException' => __DIR__ . '/Aws/SecretsManager/Exception/SecretsManagerException.php', 'YaySMTP\\Aws3\\Aws\\Sns\\Exception\\SnsException' => __DIR__ . '/Aws/Sns/Exception/SnsException.php', 'YaySMTP\\Aws3\\Aws\\Sns\\Exception\\InvalidSnsMessageException' => __DIR__ . '/Aws/Sns/Exception/InvalidSnsMessageException.php', 'YaySMTP\\Aws3\\Aws\\Sns\\SnsClient' => __DIR__ . '/Aws/Sns/SnsClient.php', 'YaySMTP\\Aws3\\Aws\\Sns\\MessageValidator' => __DIR__ . '/Aws/Sns/MessageValidator.php', 'YaySMTP\\Aws3\\Aws\\Sns\\Message' => __DIR__ . '/Aws/Sns/Message.php', 'YaySMTP\\Aws3\\Aws\\Endpoint\\PatternEndpointProvider' => __DIR__ . '/Aws/Endpoint/PatternEndpointProvider.php', 'YaySMTP\\Aws3\\Aws\\Endpoint\\PartitionEndpointProvider' => __DIR__ . '/Aws/Endpoint/PartitionEndpointProvider.php', 'YaySMTP\\Aws3\\Aws\\Endpoint\\PartitionInterface' => __DIR__ . '/Aws/Endpoint/PartitionInterface.php', 'YaySMTP\\Aws3\\Aws\\Endpoint\\EndpointProvider' => __DIR__ . '/Aws/Endpoint/EndpointProvider.php', 'YaySMTP\\Aws3\\Aws\\Endpoint\\Partition' => __DIR__ . '/Aws/Endpoint/Partition.php', 'YaySMTP\\Aws3\\Aws\\Ses\\Exception\\SesException' => __DIR__ . '/Aws/Ses/Exception/SesException.php', 'YaySMTP\\Aws3\\Aws\\Ses\\SesClient' => __DIR__ . '/Aws/Ses/SesClient.php', 'YaySMTP\\Aws3\\Aws\\Efs\\Exception\\EfsException' => __DIR__ . '/Aws/Efs/Exception/EfsException.php', 'YaySMTP\\Aws3\\Aws\\Efs\\EfsClient' => __DIR__ . '/Aws/Efs/EfsClient.php', 'YaySMTP\\Aws3\\Aws\\Comprehend\\Exception\\ComprehendException' => __DIR__ . '/Aws/Comprehend/Exception/ComprehendException.php', 'YaySMTP\\Aws3\\Aws\\Comprehend\\ComprehendClient' => __DIR__ . '/Aws/Comprehend/ComprehendClient.php', 'YaySMTP\\Aws3\\Aws\\ServerlessApplicationRepository\\ServerlessApplicationRepositoryClient' => __DIR__ . '/Aws/ServerlessApplicationRepository/ServerlessApplicationRepositoryClient.php', 'YaySMTP\\Aws3\\Aws\\ServerlessApplicationRepository\\Exception\\ServerlessApplicationRepositoryException' => __DIR__ . '/Aws/ServerlessApplicationRepository/Exception/ServerlessApplicationRepositoryException.php', 'YaySMTP\\Aws3\\Aws\\Result' => __DIR__ . '/Aws/Result.php', 'YaySMTP\\Aws3\\Aws\\Sms\\Exception\\SmsException' => __DIR__ . '/Aws/Sms/Exception/SmsException.php', 'YaySMTP\\Aws3\\Aws\\Sms\\SmsClient' => __DIR__ . '/Aws/Sms/SmsClient.php', 'YaySMTP\\Aws3\\Aws\\ElasticsearchService\\Exception\\ElasticsearchServiceException' => __DIR__ . '/Aws/ElasticsearchService/Exception/ElasticsearchServiceException.php', 'YaySMTP\\Aws3\\Aws\\ElasticsearchService\\ElasticsearchServiceClient' => __DIR__ . '/Aws/ElasticsearchService/ElasticsearchServiceClient.php', 'YaySMTP\\Aws3\\Aws\\ApiGateway\\Exception\\ApiGatewayException' => __DIR__ . '/Aws/ApiGateway/Exception/ApiGatewayException.php', 'YaySMTP\\Aws3\\Aws\\ApiGateway\\ApiGatewayClient' => __DIR__ . '/Aws/ApiGateway/ApiGatewayClient.php', 'YaySMTP\\Aws3\\Aws\\CloudFormation\\Exception\\CloudFormationException' => __DIR__ . '/Aws/CloudFormation/Exception/CloudFormationException.php', 'YaySMTP\\Aws3\\Aws\\CloudFormation\\CloudFormationClient' => __DIR__ . '/Aws/CloudFormation/CloudFormationClient.php', 'YaySMTP\\Aws3\\Aws\\KinesisAnalytics\\Exception\\KinesisAnalyticsException' => __DIR__ . '/Aws/KinesisAnalytics/Exception/KinesisAnalyticsException.php', 'YaySMTP\\Aws3\\Aws\\KinesisAnalytics\\KinesisAnalyticsClient' => __DIR__ . '/Aws/KinesisAnalytics/KinesisAnalyticsClient.php', 'YaySMTP\\Aws3\\Aws\\CloudSearch\\Exception\\CloudSearchException' => __DIR__ . '/Aws/CloudSearch/Exception/CloudSearchException.php', 'YaySMTP\\Aws3\\Aws\\CloudSearch\\CloudSearchClient' => __DIR__ . '/Aws/CloudSearch/CloudSearchClient.php', 'YaySMTP\\Aws3\\Aws\\MediaLive\\Exception\\MediaLiveException' => __DIR__ . '/Aws/MediaLive/Exception/MediaLiveException.php', 'YaySMTP\\Aws3\\Aws\\MediaLive\\MediaLiveClient' => __DIR__ . '/Aws/MediaLive/MediaLiveClient.php', 'YaySMTP\\Aws3\\Aws\\ServiceCatalog\\Exception\\ServiceCatalogException' => __DIR__ . '/Aws/ServiceCatalog/Exception/ServiceCatalogException.php', 'YaySMTP\\Aws3\\Aws\\ServiceCatalog\\ServiceCatalogClient' => __DIR__ . '/Aws/ServiceCatalog/ServiceCatalogClient.php', 'YaySMTP\\Aws3\\Aws\\CodeDeploy\\Exception\\CodeDeployException' => __DIR__ . '/Aws/CodeDeploy/Exception/CodeDeployException.php', 'YaySMTP\\Aws3\\Aws\\CodeDeploy\\CodeDeployClient' => __DIR__ . '/Aws/CodeDeploy/CodeDeployClient.php', 'YaySMTP\\Aws3\\Aws\\HasDataTrait' => __DIR__ . '/Aws/HasDataTrait.php', 'YaySMTP\\Aws3\\Aws\\Multipart\\UploadState' => __DIR__ . '/Aws/Multipart/UploadState.php', 'YaySMTP\\Aws3\\Aws\\Multipart\\AbstractUploadManager' => __DIR__ . '/Aws/Multipart/AbstractUploadManager.php', 'YaySMTP\\Aws3\\Aws\\Multipart\\AbstractUploader' => __DIR__ . '/Aws/Multipart/AbstractUploader.php', 'YaySMTP\\Aws3\\Aws\\DatabaseMigrationService\\Exception\\DatabaseMigrationServiceException' => __DIR__ . '/Aws/DatabaseMigrationService/Exception/DatabaseMigrationServiceException.php', 'YaySMTP\\Aws3\\Aws\\DatabaseMigrationService\\DatabaseMigrationServiceClient' => __DIR__ . '/Aws/DatabaseMigrationService/DatabaseMigrationServiceClient.php', 'YaySMTP\\Aws3\\Aws\\Inspector\\Exception\\InspectorException' => __DIR__ . '/Aws/Inspector/Exception/InspectorException.php', 'YaySMTP\\Aws3\\Aws\\Inspector\\InspectorClient' => __DIR__ . '/Aws/Inspector/InspectorClient.php', 'YaySMTP\\Aws3\\Aws\\DoctrineCacheAdapter' => __DIR__ . '/Aws/DoctrineCacheAdapter.php', 'YaySMTP\\Aws3\\Aws\\MarketplaceEntitlementService\\Exception\\MarketplaceEntitlementServiceException' => __DIR__ . '/Aws/MarketplaceEntitlementService/Exception/MarketplaceEntitlementServiceException.php', 'YaySMTP\\Aws3\\Aws\\MarketplaceEntitlementService\\MarketplaceEntitlementServiceClient' => __DIR__ . '/Aws/MarketplaceEntitlementService/MarketplaceEntitlementServiceClient.php', 'YaySMTP\\Aws3\\Aws\\XRay\\Exception\\XRayException' => __DIR__ . '/Aws/XRay/Exception/XRayException.php', 'YaySMTP\\Aws3\\Aws\\XRay\\XRayClient' => __DIR__ . '/Aws/XRay/XRayClient.php', 'YaySMTP\\Aws3\\Aws\\Lightsail\\Exception\\LightsailException' => __DIR__ . '/Aws/Lightsail/Exception/LightsailException.php', 'YaySMTP\\Aws3\\Aws\\Lightsail\\LightsailClient' => __DIR__ . '/Aws/Lightsail/LightsailClient.php', 'YaySMTP\\Aws3\\Aws\\IoTJobsDataPlane\\IoTJobsDataPlaneClient' => __DIR__ . '/Aws/IoTJobsDataPlane/IoTJobsDataPlaneClient.php', 'YaySMTP\\Aws3\\Aws\\IoTJobsDataPlane\\Exception\\IoTJobsDataPlaneException' => __DIR__ . '/Aws/IoTJobsDataPlane/Exception/IoTJobsDataPlaneException.php', 'YaySMTP\\Aws3\\Aws\\MediaStore\\MediaStoreClient' => __DIR__ . '/Aws/MediaStore/MediaStoreClient.php', 'YaySMTP\\Aws3\\Aws\\MediaStore\\Exception\\MediaStoreException' => __DIR__ . '/Aws/MediaStore/Exception/MediaStoreException.php', 'YaySMTP\\Aws3\\Aws\\Lambda\\LambdaClient' => __DIR__ . '/Aws/Lambda/LambdaClient.php', 'YaySMTP\\Aws3\\Aws\\Lambda\\Exception\\LambdaException' => __DIR__ . '/Aws/Lambda/Exception/LambdaException.php', 'YaySMTP\\Aws3\\Aws\\SnowBall\\Exception\\SnowBallException' => __DIR__ . '/Aws/SnowBall/Exception/SnowBallException.php', 'YaySMTP\\Aws3\\Aws\\SnowBall\\SnowBallClient' => __DIR__ . '/Aws/SnowBall/SnowBallClient.php', 'YaySMTP\\Aws3\\Aws\\JsonCompiler' => __DIR__ . '/Aws/JsonCompiler.php', 'YaySMTP\\Aws3\\Aws\\StorageGateway\\Exception\\StorageGatewayException' => __DIR__ . '/Aws/StorageGateway/Exception/StorageGatewayException.php', 'YaySMTP\\Aws3\\Aws\\StorageGateway\\StorageGatewayClient' => __DIR__ . '/Aws/StorageGateway/StorageGatewayClient.php', 'YaySMTP\\Aws3\\GuzzleHttp\\UriTemplate' => __DIR__ . '/GuzzleHttp/UriTemplate.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\TaskQueueInterface' => __DIR__ . '/GuzzleHttp/Promise/TaskQueueInterface.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\PromisorInterface' => __DIR__ . '/GuzzleHttp/Promise/PromisorInterface.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\TaskQueue' => __DIR__ . '/GuzzleHttp/Promise/TaskQueue.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\EachPromise' => __DIR__ . '/GuzzleHttp/Promise/EachPromise.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\PromiseInterface' => __DIR__ . '/GuzzleHttp/Promise/PromiseInterface.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\AggregateException' => __DIR__ . '/GuzzleHttp/Promise/AggregateException.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\FulfilledPromise' => __DIR__ . '/GuzzleHttp/Promise/FulfilledPromise.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\RejectionException' => __DIR__ . '/GuzzleHttp/Promise/RejectionException.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\RejectedPromise' => __DIR__ . '/GuzzleHttp/Promise/RejectedPromise.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\CancellationException' => __DIR__ . '/GuzzleHttp/Promise/CancellationException.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\functions' => __DIR__ . '/GuzzleHttp/Promise/functions.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\functions_include' => __DIR__ . '/GuzzleHttp/Promise/functions_include.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\Promise' => __DIR__ . '/GuzzleHttp/Promise/Promise.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Promise\\Coroutine' => __DIR__ . '/GuzzleHttp/Promise/Coroutine.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Pool' => __DIR__ . '/GuzzleHttp/Pool.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Handler\\CurlFactory' => __DIR__ . '/GuzzleHttp/Handler/CurlFactory.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Handler\\CurlHandler' => __DIR__ . '/GuzzleHttp/Handler/CurlHandler.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Handler\\CurlFactoryInterface' => __DIR__ . '/GuzzleHttp/Handler/CurlFactoryInterface.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Handler\\StreamHandler' => __DIR__ . '/GuzzleHttp/Handler/StreamHandler.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Handler\\MockHandler' => __DIR__ . '/GuzzleHttp/Handler/MockHandler.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Handler\\CurlMultiHandler' => __DIR__ . '/GuzzleHttp/Handler/CurlMultiHandler.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Handler\\Proxy' => __DIR__ . '/GuzzleHttp/Handler/Proxy.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Handler\\EasyHandle' => __DIR__ . '/GuzzleHttp/Handler/EasyHandle.php', 'YaySMTP\\Aws3\\GuzzleHttp\\RequestOptions' => __DIR__ . '/GuzzleHttp/RequestOptions.php', 'YaySMTP\\Aws3\\GuzzleHttp\\RetryMiddleware' => __DIR__ . '/GuzzleHttp/RetryMiddleware.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Middleware' => __DIR__ . '/GuzzleHttp/Middleware.php', 'YaySMTP\\Aws3\\GuzzleHttp\\TransferStats' => __DIR__ . '/GuzzleHttp/TransferStats.php', 'YaySMTP\\Aws3\\GuzzleHttp\\HandlerStack' => __DIR__ . '/GuzzleHttp/HandlerStack.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Exception\\ConnectException' => __DIR__ . '/GuzzleHttp/Exception/ConnectException.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Exception\\BadResponseException' => __DIR__ . '/GuzzleHttp/Exception/BadResponseException.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Exception\\GuzzleException' => __DIR__ . '/GuzzleHttp/Exception/GuzzleException.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Exception\\TooManyRedirectsException' => __DIR__ . '/GuzzleHttp/Exception/TooManyRedirectsException.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Exception\\RequestException' => __DIR__ . '/GuzzleHttp/Exception/RequestException.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Exception\\ServerException' => __DIR__ . '/GuzzleHttp/Exception/ServerException.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Exception\\SeekException' => __DIR__ . '/GuzzleHttp/Exception/SeekException.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Exception\\ClientException' => __DIR__ . '/GuzzleHttp/Exception/ClientException.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Exception\\TransferException' => __DIR__ . '/GuzzleHttp/Exception/TransferException.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\BufferStream' => __DIR__ . '/GuzzleHttp/Psr7/BufferStream.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\UriResolver' => __DIR__ . '/GuzzleHttp/Psr7/UriResolver.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\InflateStream' => __DIR__ . '/GuzzleHttp/Psr7/InflateStream.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\CachingStream' => __DIR__ . '/GuzzleHttp/Psr7/CachingStream.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\MultipartStream' => __DIR__ . '/GuzzleHttp/Psr7/MultipartStream.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\StreamWrapper' => __DIR__ . '/GuzzleHttp/Psr7/StreamWrapper.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\Request' => __DIR__ . '/GuzzleHttp/Psr7/Request.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\Response' => __DIR__ . '/GuzzleHttp/Psr7/Response.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\Stream' => __DIR__ . '/GuzzleHttp/Psr7/Stream.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\NoSeekStream' => __DIR__ . '/GuzzleHttp/Psr7/NoSeekStream.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\MessageTrait' => __DIR__ . '/GuzzleHttp/Psr7/MessageTrait.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\LazyOpenStream' => __DIR__ . '/GuzzleHttp/Psr7/LazyOpenStream.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\LimitStream' => __DIR__ . '/GuzzleHttp/Psr7/LimitStream.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\DroppingStream' => __DIR__ . '/GuzzleHttp/Psr7/DroppingStream.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\ServerRequest' => __DIR__ . '/GuzzleHttp/Psr7/ServerRequest.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\FnStream' => __DIR__ . '/GuzzleHttp/Psr7/FnStream.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\UploadedFile' => __DIR__ . '/GuzzleHttp/Psr7/UploadedFile.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\PumpStream' => __DIR__ . '/GuzzleHttp/Psr7/PumpStream.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\functions' => __DIR__ . '/GuzzleHttp/Psr7/functions.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\UriNormalizer' => __DIR__ . '/GuzzleHttp/Psr7/UriNormalizer.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\AppendStream' => __DIR__ . '/GuzzleHttp/Psr7/AppendStream.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\functions_include' => __DIR__ . '/GuzzleHttp/Psr7/functions_include.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\Uri' => __DIR__ . '/GuzzleHttp/Psr7/Uri.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Psr7\\StreamDecoratorTrait' => __DIR__ . '/GuzzleHttp/Psr7/StreamDecoratorTrait.php', 'YaySMTP\\Aws3\\GuzzleHttp\\PrepareBodyMiddleware' => __DIR__ . '/GuzzleHttp/PrepareBodyMiddleware.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Cookie\\CookieJarInterface' => __DIR__ . '/GuzzleHttp/Cookie/CookieJarInterface.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Cookie\\SessionCookieJar' => __DIR__ . '/GuzzleHttp/Cookie/SessionCookieJar.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Cookie\\SetCookie' => __DIR__ . '/GuzzleHttp/Cookie/SetCookie.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Cookie\\CookieJar' => __DIR__ . '/GuzzleHttp/Cookie/CookieJar.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Cookie\\FileCookieJar' => __DIR__ . '/GuzzleHttp/Cookie/FileCookieJar.php', 'YaySMTP\\Aws3\\GuzzleHttp\\ClientInterface' => __DIR__ . '/GuzzleHttp/ClientInterface.php', 'YaySMTP\\Aws3\\GuzzleHttp\\Client' => __DIR__ . '/GuzzleHttp/Client.php', 'YaySMTP\\Aws3\\GuzzleHttp\\functions' => __DIR__ . '/GuzzleHttp/functions.php', 'YaySMTP\\Aws3\\GuzzleHttp\\RedirectMiddleware' => __DIR__ . '/GuzzleHttp/RedirectMiddleware.php', 'YaySMTP\\Aws3\\GuzzleHttp\\functions_include' => __DIR__ . '/GuzzleHttp/functions_include.php', 'YaySMTP\\Aws3\\GuzzleHttp\\MessageFormatter' => __DIR__ . '/GuzzleHttp/MessageFormatter.php', 'YaySMTP\\Aws3\\JmesPath\\DebugRuntime' => __DIR__ . '/JmesPath/DebugRuntime.php', 'YaySMTP\\Aws3\\JmesPath\\Env' => __DIR__ . '/JmesPath/Env.php', 'YaySMTP\\Aws3\\JmesPath\\TreeCompiler' => __DIR__ . '/JmesPath/TreeCompiler.php', 'YaySMTP\\Aws3\\JmesPath\\Lexer' => __DIR__ . '/JmesPath/Lexer.php', 'YaySMTP\\Aws3\\JmesPath\\Parser' => __DIR__ . '/JmesPath/Parser.php', 'YaySMTP\\Aws3\\JmesPath\\AstRuntime' => __DIR__ . '/JmesPath/AstRuntime.php', 'YaySMTP\\Aws3\\JmesPath\\FnDispatcher' => __DIR__ . '/JmesPath/FnDispatcher.php', 'YaySMTP\\Aws3\\JmesPath\\TreeInterpreter' => __DIR__ . '/JmesPath/TreeInterpreter.php', 'YaySMTP\\Aws3\\JmesPath\\Utils' => __DIR__ . '/JmesPath/Utils.php', 'YaySMTP\\Aws3\\JmesPath\\SyntaxErrorException' => __DIR__ . '/JmesPath/SyntaxErrorException.php', 'YaySMTP\\Aws3\\JmesPath\\CompilerRuntime' => __DIR__ . '/JmesPath/CompilerRuntime.php', 'YaySMTP\\Aws3\\JmesPath\\JmesPath' => __DIR__ . '/JmesPath/JmesPath.php', 'YaySMTP\\Aws3\\Psr\\Http\\Message\\MessageInterface' => __DIR__ . '/Psr/Http/Message/MessageInterface.php', 'YaySMTP\\Aws3\\Psr\\Http\\Message\\ServerRequestInterface' => __DIR__ . '/Psr/Http/Message/ServerRequestInterface.php', 'YaySMTP\\Aws3\\Psr\\Http\\Message\\ResponseInterface' => __DIR__ . '/Psr/Http/Message/ResponseInterface.php', 'YaySMTP\\Aws3\\Psr\\Http\\Message\\StreamInterface' => __DIR__ . '/Psr/Http/Message/StreamInterface.php', 'YaySMTP\\Aws3\\Psr\\Http\\Message\\UriInterface' => __DIR__ . '/Psr/Http/Message/UriInterface.php', 'YaySMTP\\Aws3\\Psr\\Http\\Message\\RequestInterface' => __DIR__ . '/Psr/Http/Message/RequestInterface.php', 'YaySMTP\\Aws3\\Psr\\Http\\Message\\UploadedFileInterface' => __DIR__ . '/Psr/Http/Message/UploadedFileInterface.php');
\spl_autoload_register(function ($class) use($mapping) {
    if (isset($mapping[$class])) {
        require $mapping[$class];
    }
}, \true);
require __DIR__ . '/Aws/functions.php';
require __DIR__ . '/GuzzleHttp/functions.php';
require __DIR__ . '/GuzzleHttp/Psr7/functions.php';
require __DIR__ . '/GuzzleHttp/Promise/functions.php';
require __DIR__ . '/JmesPath/JmesPath.php';
