/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */html{line-height:1.15;-webkit-text-size-adjust:100%}body{margin:0}main{display:block}h1{font-size:2em;margin:0.67em 0}hr{box-sizing:content-box;height:0;overflow:visible}pre{font-family:monospace, monospace;font-size:1em}a{background-color:transparent}abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b,strong{font-weight:bolder}code,kbd,samp{font-family:monospace, monospace;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-0.25em}sup{top:-0.5em}img{border-style:none}button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;line-height:1.15;margin:0}button,input{overflow:visible}button,select{text-transform:none}button,[type="button"],[type="reset"],[type="submit"]{-webkit-appearance:button}button::-moz-focus-inner,[type="button"]::-moz-focus-inner,[type="reset"]::-moz-focus-inner,[type="submit"]::-moz-focus-inner{border-style:none;padding:0}button:-moz-focusring,[type="button"]:-moz-focusring,[type="reset"]:-moz-focusring,[type="submit"]:-moz-focusring{outline:1px dotted ButtonText}fieldset{padding:0.35em 0.75em 0.625em}legend{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress{vertical-align:baseline}textarea{overflow:auto}[type="checkbox"],[type="radio"]{box-sizing:border-box;padding:0}[type="number"]::-webkit-inner-spin-button,[type="number"]::-webkit-outer-spin-button{height:auto}[type="search"]{-webkit-appearance:textfield;outline-offset:-2px}[type="search"]::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details{display:block}summary{display:list-item}template{display:none}[hidden]{display:none}html,input[type="search"]{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}*,*:before,*:after{box-sizing:inherit}body{background-color:#f1f1f1;color:#333;font-family:'Helvetica Neue', Helvetica, Arial, sans-serif;font-size:14px;font-weight:400;line-height:1.625}.site{text-align:center;font-size:12px}.site a{text-decoration:underline}.site a:hover{color:#006799}h1,h2,h3,h4,h5,h6{color:#333;font-weight:700;line-height:1.2;margin:0;margin-bottom:16px;padding:0}a{color:#0073aa}a:hover,a:active,a:focus{color:#006799}#print{border-top:1px solid #eee;background-color:#fff;box-shadow:0 1px 2px #ccc;margin:30px auto 20px auto;overflow:auto;padding:30px;max-width:780px}#print .page-title{display:flex;justify-content:space-between;align-items:center;margin:0 0 20px 0}#print h1{font-size:22px;font-weight:600;margin:0}#print h1:after{content:"";display:table;clear:both}#print h1 span{font-weight:400}#print iframe{border:0}#print .buttons{display:flex;align-items:center}#print .buttons .fa-cog{color:#007CBA;font-size:20px;line-height:23px;vertical-align:middle}#print .buttons .fa-cog.active{color:#BBBBBB}#print .buttons .fa-cog:hover{color:#006799}#print .buttons .button{font-weight:normal;text-align:center;font-size:14px;margin-left:10px;line-height:28px;cursor:pointer}#print .buttons .button-close{color:#0071a1;background:#f3f5f6;min-height:30px;padding:0 10px;margin-left:15px;border-radius:3px;font-size:13px;text-decoration:none;border:1px solid #016087}#print .buttons .button-close:hover{background:#f1f1f1;border-color:#016087;color:#016087}#print .buttons .button-print{background:#007cba;color:#fff;padding:0 10px;text-decoration:none;border-radius:3px;font-size:13px;min-height:30px;border:1px solid #007cba}#print .buttons .button-print:hover,#print .buttons .button-print:active{background:#0071a1;border-color:#0071a1;color:#fff}#print .actions{text-align:left;margin:0;font-size:11px;align-items:center;border-top:1px solid #EEEEEE;padding-top:20px;display:none}#print .actions.active{display:flex;flex-wrap:wrap;align-items:flex-start}#print .actions .switch-container{display:flex;align-items:center;margin-right:20px;margin-bottom:20px}#print .actions .switch-container a{font-family:Helvetica Neue, sans-serif;font-style:normal;font-weight:normal;font-size:14px;color:#444444;text-decoration:none;padding:0;line-height:1;display:flex;align-items:center}#print .actions .switch-container a:hover .switch{background:#777777}#print .actions .switch-container a:hover .switch.active{background:#006799}#print .actions .switch-container a .switch{cursor:pointer;height:18px;width:28px;background:#BBBBBB;display:block;border-radius:10px;position:relative;margin-right:7px}#print .actions .switch-container a .switch:after{content:'';position:absolute;top:2px;left:2px;width:14px;height:14px;background:#fff;border-radius:10px;transition:0.3s}#print .actions .switch-container a .switch.active{background:#007cba}#print .actions .switch-container a .switch.active:after{left:calc(100% - 2px);transform:translateX(-100%)}#print .fields{border:1px solid #eee}#print .fields.empty{display:none}#print .fields .wpforms-hidden{display:none}#print .fields .wpforms-pagebreak-divider{position:relative;height:30px;text-align:center;margin:10px}#print .fields .wpforms-pagebreak-divider .pagebreak-label{font-size:14px;font-weight:600;background-color:#fff;position:relative;padding:5px 10px;display:inline-block;z-index:2;margin:0}#print .fields .wpforms-pagebreak-divider .line{display:block;border-top:1px dashed #aaa;position:absolute;top:50%;left:0;width:100%}#print .field-name,#print .note-byline{font-weight:600;background:#ebf3fb;padding:8px 12px;margin:0}#print .field-value,#print .note-text{background:#fff;padding:8px 12px;margin:0}#print .field-value iframe,#print .note-text iframe{width:100%}#print .file-icon{padding-right:10px}#print .file-icon img{vertical-align:middle}#print .notes-head{margin:26px 0 16px 0;display:none}#print .notes{border:1px solid #eee;display:none}#print .notes p{margin:0 0 10px 0}#print .notes p:last-of-type{margin:0}#print.compact{font-size:12px;line-height:1.4;padding:15px;margin-bottom:10px}#print.compact h1{font-size:16px !important}#print.compact .field{border-top:1px solid #eee;overflow:hidden;clear:both;position:relative}#print.compact .field::after{content:"";clear:both;display:table}#print.compact .fields{border-top:0}#print.compact .field-name{width:30%;float:left;height:100%}#print.compact .field-value{width:70%;float:right}#print.compact .file-icon{display:none}#print.compact .notes-head{font-size:16px;margin:16px 0 10px 0}@media print{#print{border:none;box-shadow:none;padding:30px 0 15px;margin:0;width:100%;max-width:100%}#print h1{text-align:center}#print .buttons,#print .actions{display:none}#print .fields,#print .notes{border:1px solid #ccc}#print .field,#print .note{border-top:1px solid #ccc;border-color:#ccc !important}#print .field:first-of-type,#print .note:first-of-type{border:none}#print .field-name,#print .note-byline{padding:8px 12px 0 8px}#print .field-value,#print .note-text{padding-top:6px}#print.compact{padding:15px;font-size:11px}#print.compact .fields{border-top:none}#print.compact .field:first-of-type{border-top:1px solid #ccc}#print .no-print,#print .no-print *{display:none !important}}
