# Hisense Project Handover Document

## Project Overview

**Project Name:** Hisense Saudi Arabia Website  
**Theme Name:** Hisense  
**Version:** 1.55.8  
**Author:** WP Theme Team  
**Platform:** WordPress + WooCommerce  
**Text Domain:** hisense  

## Project Structure

### Theme Location
- **Main Theme Directory:** `c:\wamp64\www\nuox\hisense\themes\themes\hisense`
- **Theme Type:** Custom WordPress Theme (Scratch Build)
- **Framework:** Custom PHP/WordPress

### Key Directories

```
hisense/
├── assets/                    # Static assets (CSS, JS, Images, Fonts)
│   ├── css/                  # Stylesheets
│   ├── js/                   # JavaScript files
│   ├── images/               # Theme images
│   ├── fonts/                # Custom fonts (Helvetica Neue variants)
│   └── f/                    # Additional assets
├── inc/                      # Theme includes
│   ├── theme-setup.php       # Theme configuration
│   ├── scripts-style.php     # Asset enqueuing
│   ├── general-functions.php # Utility functions
│   ├── custom-widgets.php    # Custom widgets
│   ├── shortcodes.php        # Custom shortcodes
│   └── cpt.php              # Custom post types
├── template-parts/           # Reusable template components
├── woocommerce/             # WooCommerce template overrides
├── wp-content/              # WordPress content
│   └── plugins/             # Custom and third-party plugins
├── landing-page-assets/     # Landing page specific assets
└── wcfm/                    # WCFM marketplace customizations
```

## Core Theme Files

### Main Theme Files
- `style.css` - Main stylesheet with theme information
- `functions.php` - Theme functions and file loader
- `index.php` - Main template file
- `header.php` - Site header template
- `footer.php` - Site footer template
- `page.php` - Default page template

### Custom Page Templates
- `home-template.php` - Homepage template
- `about-template.php` - About page template
- `product-detail-template.php` - Product detail page
- `landing-template.php` - Landing page template
- `coming-soon-template.php` - Coming soon page
- `sitemap-template.php` - Sitemap page
- Various campaign templates (World Cup, Ramadan, etc.)

### WooCommerce Integration
- Complete WooCommerce template overrides in `/woocommerce/` directory
- Custom cart and checkout templates
- Email template customizations
- Product page customizations
- My Account page customizations

## Custom Plugins

### 1. Hisense Anti-Spam Protection
**Location:** `hisense-anti-spam/` and `wp-content/plugins/hisense-anti-spam/`
**Version:** 1.0.0
**Purpose:** Comprehensive anti-spam protection for WooCommerce registration

**Features:**
- Advanced bot detection
- Domain and IP blocking
- Spam cleanup tools
- Admin settings page
- Integration with WooCommerce registration forms

**Key Files:**
- `hisense-anti-spam.php` - Main plugin file
- `includes/class-spam-validator.php` - Validation logic
- `includes/class-spam-cleanup.php` - Cleanup functionality
- `admin/admin-page.php` - Admin interface

### 2. Multi Inventory Manager for WooCommerce
**Location:** `multi-inventory-manager/`
**Version:** 1.0.0
**Purpose:** Multiple inventory stocks based on location/distributor

**Features:**
- Location-based inventory management
- Stock tracking per location
- Order inventory selection
- Frontend stock display
- Manual inventory restoration
- Integration with order refunds

**Key Files:**
- `multi-inventory-manager.php` - Main plugin file
- `zagzoog-integration-example.php` - Integration example
- `INSTALLATION.md` - Installation guide
- `README.md` - Documentation

### 3. Zagzoog WooCommerce Integration
**Location:** `zagzoog-woocommerce-integration/`
**Purpose:** Integration with Zagzoog shipping/logistics service

**Features:**
- Shipping integration
- Order tracking
- API connectivity
- Debug logging

**Key Files:**
- `zagzoog-integration.php` - Main integration file
- `zagzoog-integration1.php` - Alternative version
- `zagzoog-integration2.php` - Additional version
- `TESTING-GUIDE.md` - Testing documentation

## Third-Party Plugins

### Essential Plugins
1. **WooCommerce** - E-commerce functionality
2. **WPML (Multilingual CMS)** - Multi-language support
3. **Advanced Custom Fields Pro** - Custom fields management
4. **Elementor** - Page builder
5. **WC Frontend Manager** - Frontend vendor management
6. **WC Multivendor Marketplace** - Marketplace functionality

### Payment Gateways
- **Checkout.com Unified Payments API** - Payment processing
- **Tamara Checkout** - Buy now, pay later solution

### Performance & Security
- **LiteSpeed Cache** - Caching solution
- **Wordfence** - Security plugin
- **Autoptimize** - Performance optimization
- **WebP Express** - Image optimization

### Email & Communication
- **Newsletter** - Email marketing
- **WP Mail SMTP** - Email delivery
- **9Mail WP Email Templates Designer** - Email customization

### Additional Functionality
- **Ti WooCommerce Wishlist** - Wishlist functionality
- **Quiz Master Next** - Quiz/survey functionality
- **Google Site Kit** - Analytics integration
- **Yoast SEO** - SEO optimization

## Database Considerations

### Custom Tables
- Multi-inventory location data
- Anti-spam blocked domains/IPs
- Custom user registration data
- Campaign/quiz responses

### Important Meta Fields
- Product inventory locations
- User verification status
- Custom product attributes
- Order inventory selections

## Configuration Files

### WordPress Configuration
- Standard WordPress configuration
- WooCommerce settings
- WPML language configuration
- Custom theme options

### Plugin Configurations
- Anti-spam settings and blocked lists
- Inventory location configurations
- Payment gateway settings
- Email template customizations

## Development Notes

### Code Standards
- WordPress coding standards followed
- Custom functions prefixed with 'hisense_'
- Proper sanitization and validation
- Translation-ready code

### Security Implementations
- Anti-spam protection on registration
- Input validation and sanitization
- Nonce verification for forms
- Capability checks for admin functions

### Performance Optimizations
- Asset minification and compression
- Image optimization (WebP)
- Caching implementation
- Database query optimization

## Maintenance Requirements

### Regular Updates
- WordPress core updates
- Plugin updates (especially security-related)
- Theme version updates
- WooCommerce compatibility checks

### Monitoring
- Anti-spam effectiveness
- Inventory accuracy
- Payment gateway functionality
- Email delivery rates

### Backup Strategy
- Regular database backups
- File system backups
- Plugin configuration exports
- Custom code version control

## Known Issues & Solutions

### Inventory Management
- When order status is set to refunded, inventory should be re-added to corresponding location
- Manual restoration tools available in Multi Inventory Manager

### Anti-Spam
- Regular cleanup of spam registrations required
- Monitor blocked domains list for false positives
- Check spam detection accuracy

## Contact Information

### Development Team
- **Theme Development:** WP Theme Team
- **Custom Plugins:** Hisense Team
- **Integration Support:** Available through plugin documentation

### Support Resources
- Plugin documentation in respective README files
- Testing guides for integrations
- Installation guides for custom plugins

## Technical Specifications

### Server Requirements
- **PHP Version:** 7.4+ (recommended 8.0+)
- **WordPress Version:** 5.0+
- **WooCommerce Version:** 5.0+
- **MySQL Version:** 5.6+
- **Memory Limit:** 256MB minimum (512MB recommended)

### Theme Dependencies
- WooCommerce (required)
- Advanced Custom Fields Pro (recommended)
- WPML (for multilingual support)

### Custom Plugin Dependencies
- **Hisense Anti-Spam:** WooCommerce
- **Multi Inventory Manager:** WooCommerce
- **Zagzoog Integration:** WooCommerce, cURL support

## File Structure Details

### Assets Organization
```
assets/
├── css/
│   ├── style.css (main styles)
│   ├── rtl.css (RTL language support)
│   └── responsive.css (mobile styles)
├── js/
│   ├── main.js (core functionality)
│   ├── antispam.js (anti-spam client-side)
│   └── custom-scripts.js (additional features)
├── images/
│   ├── logos/ (brand assets)
│   ├── products/ (product images)
│   └── ui/ (interface elements)
└── fonts/
    └── HelveticaNeueLTW06/ (custom font family)
```

### Template Hierarchy
- Custom page templates for specific campaigns
- WooCommerce template overrides for e-commerce
- Responsive design implementation
- RTL language support

## Plugin Detailed Analysis

### Complete Plugin List (wp-content/plugins/)

#### Core E-commerce
1. **woocommerce** - Main e-commerce platform
2. **wc-frontend-manager** - Vendor frontend management
3. **wc-multivendor-marketplace** - Multi-vendor functionality
4. **wc-multivendor-membership** - Vendor membership system

#### Payment Solutions
1. **checkout-com-unified-payments-api** - Primary payment gateway
2. **tamara-checkout** - Buy now, pay later
3. **tamara-instore-gateway** - In-store payment solution

#### Multilingual & Translation
1. **sitepress-multilingual-cms** (WPML) - Core multilingual
2. **wpml-string-translation** - String translation
3. **acfml** - ACF multilingual support
4. **contact-form-7-multilingual** - CF7 translation

#### Performance & Optimization
1. **litespeed-cache** - Advanced caching
2. **autoptimize** - CSS/JS optimization
3. **webp-express** - Image format optimization
4. **lazy-loading-responsive-images** - Image lazy loading
5. **wp-optimize** - Database optimization

#### Security & Anti-Spam
1. **wordfence** - Security suite
2. **google-captcha** - CAPTCHA protection
3. **hisense-anti-spam** - Custom anti-spam (CUSTOM)
4. **emails-verification-for-woocommerce** - Email verification

#### Content Management
1. **advanced-custom-fields-pro** - Custom fields
2. **elementor** - Page builder
3. **classic-editor** - Classic WordPress editor
4. **page-or-post-clone** - Content duplication

#### Email & Communication
1. **newsletter** - Email marketing platform
2. **newsletter-extensions** - Newsletter add-ons
3. **newsletter-import** - Subscriber import
4. **wp-mail-smtp** - SMTP configuration
5. **yaysmtp** - Alternative SMTP
6. **9mail-wp-email-templates-designer** - Email design
7. **email-template-customizer-for-woo** - WooCommerce emails

#### Analytics & SEO
1. **google-site-kit** - Google services integration
2. **wordpress-seo** (Yoast) - SEO optimization

#### User Experience
1. **ti-woocommerce-wishlist** - Wishlist functionality
2. **nextend-facebook-connect** - Social login
3. **ajax-search-lite** - Enhanced search
4. **woo-variation-gallery** - Product image galleries
5. **woo-variation-swatches** - Product variation display

#### Import/Export & Data Management
1. **product-import-export-for-woo** - Product data management
2. **order-import-export-for-woocommerce** - Order data management
3. **better-search-replace** - Database search/replace
4. **duplicator** - Site migration/backup
5. **all-in-one-wp-migration** - Site migration
6. **updraftplus** - Backup solution

#### Additional Features
1. **quiz-master-next** - Quiz/survey functionality
2. **world-cup-predictor** - Sports prediction system
3. **kaya-qr-code-generator** - QR code generation
4. **contact-form-7** - Contact forms
5. **advanced-cf7-db** - CF7 database storage
6. **wpcf7-redirect** - CF7 redirections

#### Development & Maintenance
1. **wp-file-manager** - File management
2. **wp-phpmyadmin-extension** - Database management
3. **string-locator** - Code search
4. **site-cleanup** - Maintenance tools

## Custom Development Features

### Theme Customizations
- **Multi-language Support:** Full RTL and LTR support with WPML integration
- **Responsive Design:** Mobile-first approach with custom breakpoints
- **Custom Post Types:** Certificates, History, Newsroom
- **Advanced Custom Fields:** Extensive use for flexible content management
- **WooCommerce Integration:** Deep customization of e-commerce features
- **Custom Navigation:** Advanced menu walker with dropdown functionality

### Custom Post Types (CPT)
1. **Certificates** (`certificate`)
   - Purpose: Manage product certificates and documentation
   - Features: Featured images, custom fields, archive pages
   - Admin menu: "Certificates"

2. **History** (`history`)
   - Purpose: Company history timeline content
   - Features: Chronological content management
   - Admin menu: "History"

3. **Newsroom** (`newsroom`)
   - Purpose: Press releases and news content
   - Features: Public facing, menu position 10
   - Admin menu: "Newsroom"

### Custom Functions & Utilities
- **Debug Function:** `pa()` for array debugging
- **Custom Menu Walker:** `hisense_top_menu_Walker` for advanced navigation
- **Theme File Loader:** `hisense_loader()` and `hisense_inclusion()` functions
- **Responsive Navigation:** Mobile-friendly dropdown menus
- **Custom Widget Areas:** Specialized widget functionality

### Custom Shortcodes
- Product displays and catalogs
- Newsletter signup forms
- Campaign-specific elements
- Social media integration widgets
- Certificate download sections

### Security Implementations
- **Custom Anti-Spam System:** Multi-layer protection for registrations
- **Registration Validation:** Enhanced user verification
- **Input Sanitization:** Comprehensive data cleaning
- **CSRF Protection:** Form security tokens
- **Domain/IP Blocking:** Automated spam prevention

### Performance Features
- **Asset Optimization:** Minified CSS/JS delivery
- **Image Optimization:** WebP format support
- **Caching Integration:** LiteSpeed Cache compatibility
- **Lazy Loading:** Progressive image loading
- **Database Optimization:** Efficient query structures

### Campaign & Special Templates
1. **World Cup Templates**
   - `template-worldcup.php` - World Cup campaign page
   - `template-prediction.php` - Sports prediction functionality
   - Integration with World Cup Predictor plugin

2. **Ramadan Campaign**
   - `template-ramdan.php` - Ramadan-specific landing page
   - `template-thankyou-ramada.php` - Thank you page
   - Cultural customizations and Arabic support

3. **Youth Cup**
   - `page-youth-cup.php` - Youth sports campaign
   - Custom registration and participation forms

4. **Quiz System**
   - `template-quiz.php` - Interactive quiz functionality
   - Integration with Quiz Master Next plugin
   - Custom scoring and results

5. **Pre-order System**
   - `template-preorder-registration.php` - Product pre-orders
   - Inventory management integration
   - Email notifications

### E-commerce Customizations
- **Custom Cart:** Enhanced shopping cart with location-based inventory
- **Checkout Process:** Multi-step checkout with payment gateway integration
- **Order Management:** Advanced order tracking and status updates
- **Email Templates:** Branded email communications
- **PDF Invoices:** Custom invoice generation with company branding
- **Wishlist Integration:** Ti WooCommerce Wishlist customization
- **Product Variations:** Advanced variation display with swatches and galleries

### Integration Points
- **Zagzoog Shipping:** Logistics and delivery integration
- **Tamara Payments:** Buy now, pay later functionality
- **Checkout.com:** Primary payment processing
- **WPML:** Complete multilingual support (Arabic/English)
- **Google Services:** Analytics, Site Kit, and reCAPTCHA
- **Social Login:** Facebook and social media authentication

## Deployment Checklist

### Pre-Deployment
- [ ] Database backup completed
- [ ] File system backup completed
- [ ] Plugin compatibility verified
- [ ] Custom code tested
- [ ] Performance optimization applied

### Post-Deployment
- [ ] SSL certificate configured
- [ ] Caching enabled
- [ ] Security plugins activated
- [ ] Email delivery tested
- [ ] Payment gateways tested
- [ ] Multi-language functionality verified

### Ongoing Maintenance
- [ ] Weekly security scans
- [ ] Monthly plugin updates
- [ ] Quarterly performance reviews
- [ ] Semi-annual full backups

## Troubleshooting Guide

### Common Issues & Solutions

#### Anti-Spam Issues
- **Problem:** Legitimate users blocked
- **Solution:** Check blocked domains list in Hisense Anti-Spam settings
- **Location:** WordPress Admin > Hisense Anti-Spam > Settings

#### Inventory Management
- **Problem:** Stock not updating after refunds
- **Solution:** Use Multi Inventory Manager restoration tools
- **Location:** WordPress Admin > Multi Inventory > Manual Restore

#### Email Delivery
- **Problem:** Emails not sending
- **Solution:** Check SMTP settings in WP Mail SMTP or YaySMTP
- **Verify:** Test email functionality in plugin settings

#### Payment Gateway Issues
- **Problem:** Checkout.com or Tamara not working
- **Solution:** Verify API credentials and webhook URLs
- **Check:** Plugin logs for error messages

#### Performance Issues
- **Problem:** Slow page loading
- **Solution:** Clear LiteSpeed Cache, check Autoptimize settings
- **Verify:** WebP image optimization is working

#### WPML Translation Issues
- **Problem:** Content not translating
- **Solution:** Check WPML String Translation settings
- **Verify:** All strings are registered for translation

### Log File Locations
- **WordPress Debug:** `/wp-content/debug.log`
- **WooCommerce Logs:** WordPress Admin > WooCommerce > Status > Logs
- **Plugin Logs:** Various locations in `/wp-content/plugins/[plugin-name]/`
- **Server Logs:** Check hosting provider's error logs

### Emergency Contacts
- **Hosting Support:** Contact hosting provider for server issues
- **Payment Gateway Support:** Checkout.com and Tamara technical support
- **WPML Support:** For translation and multilingual issues
- **Plugin Developers:** Individual plugin support channels

## Important File Locations

### Configuration Files
- **WordPress Config:** `wp-config.php` (not in theme directory)
- **Theme Functions:** `functions.php`
- **Custom Styles:** `style.css`, `assets/css/`
- **Custom Scripts:** `assets/js/`

### Backup Locations
- **UpdraftPlus Backups:** Check plugin settings for storage location
- **All-in-One Migration:** `/wp-content/ai1wm-backups/`
- **Manual Backups:** As configured by hosting provider

### Custom Plugin Files
- **Anti-Spam:** `hisense-anti-spam/` and `wp-content/plugins/hisense-anti-spam/`
- **Inventory Manager:** `multi-inventory-manager/`
- **Zagzoog Integration:** `zagzoog-woocommerce-integration/`

---

**Document Version:** 1.0
**Last Updated:** July 29, 2025
**Prepared By:** Augment Agent
**Total Plugins:** 80+ (including custom developments)
