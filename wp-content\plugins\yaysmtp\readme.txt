=== YaySMTP - WP SMTP Plugin with Full Email Log & 15+ SMTP Services ===
Contributors: YayCommerce
Tags: wp mail, email log, wp mail smtp, smtp, gmail smtp
Requires at least: 5.5
Requires PHP: 5.4
Tested up to: 6.5.2
Stable tag: 2.6.0
License: GPL-2.0+
License URI: http://www.gnu.org/licenses/gpl-2.0.txt


== Description ==
**Send WordPress emails successfully with WP Mail SMTP via your favorite mailer**

[DOCUMENTATION](https://yaycommerce.gitbook.io/yaysmtp/)  

[youtube https://youtu.be/J6crljFKwVA]

🏆 [**Get YaySMTP Pro**](https://yaycommerce.com/yaysmtp-wordpress-mail-smtp/)

YaySMTP - WP SMTP Plugin helps you send emails from your WordPress website via your chosen SMTP server. It comes with **unlimited email log**, migration from a previous SMTP plugin, and **tracking opened & clicked emails**.

###⚡️ FEATURES

YaySMTP is built with powerful options to connect via API of popular sending services, including Gmail SMTP, Sendinblue SMTP, Zoho SMTP, SendGrid SMTP, and so on.

3 steps to set up YaySMTP that lets you start sending emails in minutes:

- Fill in the sender 'From' name and email address
- Choose an email SMTP service provider as your mailer
- Config settings with built-in fields

Whether you're selling via your WooCommerce website or not, you should make sure your email notifications make it to your audience inboxes. Communication should not end up in spam folders or get lost on the way. 

Setting up your WordPress site to use a WP SMTP mailer helps you send through without paying for regular technical maintenance.

###🚀 MORE BENEFITS

- **Seamless connection**: YaySMTP works with your hosting's email server and dedicated SMTP service providers.
- **Send test email**: Quickly send a test email in a single click. Or you can send test and [preview WooCommerce order emails](https://yaycommerce.com/how-to-preview-and-test-your-woocommerce-emails/) as well.
- **Full email log**: Keep all email logs with email content and metadata in basic or full information. You can filter, show/hide columns, and search by email subject, user email, etc.
- **Automatically delete email logs**: You can keep logging emails forever or have your email log retained within the latest 7 - 365 days.
- **Export/Import email log**: Easily save a backup of your sent-out emails as CSV in case you need to refer to it later.
- **One click to migrate**: Import your server's API key and settings from other WP SMTP plugins, including Easy WP SMTP, WP Mail SMTP Pro, SMTP Mailer, WP SMTP, Mail Bank, and more [upon request](https://yaycommerce.com/support/).
- **Import email logs**: Migrate email logs from other WP SMTP plugins to ensure a complete record on your new [email logging page](https://docs.yaycommerce.com/yaysmtp/settings-and-tools/view-email-log) of YaySMTP.
- **Fallback mailer**: Pick an alternative email sender or relay server with full options.
- **Disable email delivery**: In the development local mode or maintenance stage, you can record email logs as if they are sent. This is helpful for testing purposes without consuming your sending volumes.
- **Top-notch design**: Built with quality code and clean UI/UX (your clients will love it!).
- **Ecommerce optimized**: You can send WooCommerce transactional emails and also marketing campaigns.


###🎉 Supported Themes and Plugins

- Complete compatibility with all themes, page builders and major plugins.
- Perfect with [WooCommerce Email Customizer](https://yaycommerce.com/yaymail-woocommerce-email-customizer/) plugin.

###💪 Mailer
YaySMTP - WP SMTP Plugin allows you to freely integrate the following email SMTP services:

1. SendGrid 
2. Gmail SMTP server
3. Zoho
4. Brevo (formerly Sendinblue SMTP)
5. Mailgun
6. SMTP.com 
7. Amazon SES
8. Postmark
9. MailJet
10. MessageBird (formely Sparkpost)
11. Pepipost
12. SendPulse
13. Microsoft Outlook, Office 365, Microsoft Exchange Online
14. Mandrill SMTP by Mailchimp
15. Your hosting's email server
16. And more! [Contact us](https://yaycommerce.com/contact/) to suggest your favorite mailer

###🎯 Why use YaySMTP?
Customers who buy products from your website expect regular updates and follow-ups. That's why timely communication is crucial to your business.

Sending follow-up WordPress emails on time can bring customers back while building trust and brand value.

###📝 Documentation and Support
If you're having issues, do let us know, and we'll be [happy to help](https://yaycommerce.com/support/).

###♥️ Like this YaySMTP Plugin?
- Rate us 5 stars on [WordPress.org](https://wordpress.org/support/plugin/yaysmtp/reviews/#new-post).
- Check out our best-selling WordPress plugins: 
- [YayMail - WooCommerce Email Customizer](https://yaycommerce.com/yaymail-woocommerce-email-customizer/)
- [Email Customizer Addons](https://yaycommerce.com/yaymail-addons/)
- [YayCurrency - WooCommerce Multi-Currency Switcher](https://yaycommerce.com/yaycurrency-woocommerce-multi-currency-switcher/)
- [YayPricing - WooCommerce Dynamic Pricing And Discounts](https://yaycommerce.com/yaypricing-woocommerce-dynamic-pricing-and-discounts/)
- [YayExtra - WooCommerce Extra Product Options](https://yaycommerce.com/yayextra-woocommerce-extra-product-options/)
- [YaySwatches – Variation Swatches for WooCommerce](https://yaycommerce.com/yayswatches-variation-swatches-for-woocommerce/)

== Frequently Asked Questions ==

= Is this plugin compliant with GDPR? =
Absolutely! YaySMTP doesn't collect or store any personal information. So please rest assured.

= Is YaySMTP free? =
Yes, you can use it free of charge. YayCommerce SMTP plugin comes with all the features that you can find in the description above.

= What will I get if I become a premium user? =
You'll have our priority support for whatever issue you have with sending and receiving emails through WordPress. You can also ask us to develop new options or features. We might not say Yes to all requests, but your feedback definitely gets its place on our desk.

== Installation ==
1. Upload the entire plugin folder to the '/wp-content/plugins/' directory.
2. Activate the plugin through the **Plugins** menu in WordPress.

== Screenshots ==
1. YaySMTP Settings Overview
2. Send Test Email via Your SMTP Service
3. Email List and Email Log Settings
4. Additional Settings for Email Delivery Summary and Fallback Options

== Changelog ==

= Oct 3, 2024 - Version 2.6.0 =
- Added: Data Centers option for Zoho
- Fixed: Issue with Reply To of Mandrill

= Apr 23, 2024 - Version 2.5.9 =
- Added: Note for Gmail
- Updated: Change icon/text Sendinblue to Brevo
- Fixed: Attachment for Mailjet
- Fixed: Deprecated issue 
- Fixed: Logs feature of Mandrill

= Mar 13, 2024 - Version 2.5.7 =
- Added: Import email logs from previously used plugins (WP Mail SMTP and 4 other WP plugins)
- Improved: Email log retention logic when changing the retention period
- Improved: UI of importing SMTP settings 
- Fixed: Bug with attachments of Mailgun

= Dec 21, 2023 - Version 2.5.6 =
- Fixed: Deprecated: Return type of YaySMTPAmazonSES

= Nov 25, 2023 - Version 2.5.5 =
- Improved: Monthly/Weekly mail report UI  
- Fixed: Issue about open tracking image

= Nov 25, 2023 - Version 2.5.4.1 =
- Removed: License tab

= Oct 25, 2023 - Version 2.5.4 =
- Added: 6 new regions for Amazon SES: us-west-1 (US West (N. California)), eu-west-3 (EU (Paris)), eu-south-1 (EU (Milan)), eu-north-1 (EU (Stockholm)), ap-northeast-3 (Asia Pacific (Osaka)), me-south-1 (Middle East (Bahrain))

= Oct 20, 2023 - Version 2.5.3 =
- Changed: Content in test email 

= Oct 18, 2023 - Version 2.5.2 =
- Fixed: Notice of Deprecated Return Type

= Oct 10, 2023 =
- Fixed: Warning notice of Google library

= Sep 15, 2023 - Version 2.5 =
- Fixed: Mail Fallback

= Jun 29, 2023 - Version 2.4.9 =
- Added: Export CSV for Email Logs 
- Improved: Move feature “Import settings from other SMTP plugins” into “Additional Settings” tab
- Improved: Sendpulse processing
- Fixed: Delete notification popup
- Fixed: Detail log
- Fixed: Lost menu on network site (multiple site)

= Jun 8, 2023 - Version 2.4.8 =
- Updated: Guzzle HTTP library (Google API PHP client) 
- Fixed: Redirect link of Gmail and Zoho

= Jun 7, 2023 - Version 2.4.7 =
- Improved: Processing

= Jun 6, 2023 - Version 2.4.6 =
- Fixed: XSS for email log 
- Fixed: UI text

= May 31, 2023 - Version 2.4.5 =
- Added: Tracking Email Opened & Email Clicked links
- Improved: Logs processing
- Fixed: Some issues

= May 17, 2023 - Version 2.4.4 =
- Added: YayCommerce menu 

= Apr 26, 2023 - Version 2.4.3 =
- Added: Encryption Type None for Other SMTP
- Improved: Security for password of Other SMTP
- Improved: UI
- Improved: Log detail, log list 
- Updated: Mail fallback used after 1 attempt failed
- Fixed: Compatible with Contact Form 7

= Apr 20, 2023 - Version 2.4.2 =
- Fixed: Generate incorrect password
- Improved: Security

= Apr 12, 2023 - Version 2.4.1 =
- Improved: Text and UI

= Apr 11, 2023 - Version 2.4 =
- Added: Support Mandrill SMTP
- Added: Option Disable email delivery
- Improved: Logs, Fallback Carrier

= Feb 6, 2023 - Version 2.3 =
- Fixed: Bcc for Amazon SES

= Oct 31, 2022 - Version 2.2.9 =
- Fixed: Issue with Select2

= Oct 7, 2022 - Version 2.2.8 =
- Fixed: Email log sorting

= Sep 21, 2022 - Version 2.2.7 =
- Fixed: Conflict with the Select2 library 

= Aug 17, 2022 - Version 2.2.6 =
- Added: Option delete all data in database
- Fixed: Issue about delete logs

= Aug 11, 2022 - Version 2.2.5 =
- Fixed: PHPCS 
- Fixed: Some bugs 

= Aug 3, 2022 - Version 2.2.4 =
- Fixed: Error with PHP version > 8.0 for Amazon vendor library

= Jul 27, 2022 - Version 2.2.3 =
- Fixed: Error with Google Library 
- Fixed: Conflict with SureCart plugin 

= Jul 14. 2022 - Version 2.2.2 =
- Added: Support Multisite
- Improved: Logs page
- Fixed: XSS, Roles, and some issues

= Jul 1, 2022 - Version 2.2.1 =
- Fixed: Logs data disclosure
- Fixed: Restrict access to admin user role only

= May 24, 2022 - Version 2.2 =
- Added: Mail logs date filter
- Added: Delete all mail logs feature
- Added: Translation for German and Japanese
- Improved: UI
- Improved: Mail logs settings
- Improved: Responsive
- Fixed: Some bugs

= May 11, 2022 - Version 2.1.3 =
- Fixed: Outlook SMTP text/plain type

= May 2, 2022 - Version 2.1.2 =
- Fixed: Issue about text/plain type of Outlook SMTP
- Added: Japanese and German translation

= Feb 25, 2022 - Version 2.1.1 =
- Fixed: Error with apostrophe in email From field

= Nov 29, 2021 - Version 2.1 =
- Added: Weekly/Monthly Report, Fallback Carrier, Summary Chart
- New: Weekly/monthly summary email notifications
- New: Fallback additional setting using PHPMailer
- Improved: Email delivery report, performance chart, and top emails
- Improved: Email notification template

= Nov 1, 2021 - Version 2.0.1 =
- Fixed: Error Exception of Outlook SMTP

= Oct 23, 2021 - Version 2.0 =
- Added: Import settings from other SMTP plugins (Easy WP SMTP, WP Mail SMTP, SMTP Mailer, WP SMTP, Mail Bank, Post SMTP Mailer)
- Improved: Text 
- Fixed: Small bugs

= Sep 12, 2021 - Version 1.9 =
- Added: Chart Statics Widget on Dashboard
- Improved: Email Logs
- Fixed: guzzlehttp – Google client library
- Fixed: Conflict with Google Listings and Ads 
- Fixed: Some small bugs

= Aug 3, 2021 - Version 1.8 =
- Improved: Mailer dropdown
- Improved: Email logs

= Jul 27, 2021 - Version 1.7 =
- Added: Email Force Options
- Added: MailJet SMTP
- Added: SparkPost SMTP
- Added: Pepipost SMTP
- Added: SendPulse SMTP
- Added: Microsoft/Outlook/Office 365/Exchange SMTP
- Fixed: Some small bugs

= Jul 5, 2021 - Version 1.6 =
- Added: Email logs
- Added: Postmark mailer
- Improved: UI
- Fixed: Some bugs

= Jun 21, 2021 - Version 1.5 =
- Improved: Mailers

= May 16, 2021 - Version 1.4 =
- Improved: Mailers

= Apr 25, 2021 - Version 1.3.3 =
- Fixed: Some small bugs

= Mar 7, 2021 - Version 1.3.2 =
- Improved: Refactored code

= Feb 28, 2021 - Version 1.3.1 =
- Improved: Layout and optimize code
- Fixed: Auto refresh token Zoho

= Feb 5, 2021 - Version 1.3 =
- Added: Amazon SES SMTP
- Improved: UI

= Jan 7, 2021 - Version 1.2 =
- Added: SMTP.com SMTP
- Improved: UI
- Fixed: Close/open drawer event

= Dec 13, 2020 - Version 1.1 =
- Added: Mailgun SMTP
- Improved: YaySMTP icon
- Fixed: Zoho
- Fixed: UI/UX
- Fixed: Some small bugs

= Nov 29, 2020 - Version 1.0 =
Initial Release 