=== File Manager ===
Contributors: mndpsingh287
Tags: wp-file-manager, elfinder,file manager, ftp, wp-filemanager,file manager, wp-filemanager, Upload Files, WP File Manager, File Manage, Edit Files, Delete Files, FTP, filemanager, wpfilemanager, ftp, file transfer, update, create, delete, view, rename, editor, Cpanel, Control Panel, Admin, Shortcode, explorer, file explorer, filemanager
Requires at least: 4.0
Tested up to: 6.6.1
Requires PHP: 5.2.4
Stable tag: 8.0
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

file manager provides you ability to edit, delete, upload, download, copy and paste files and folders.

== Description ==

#### File Manager allows you to edit, delete, upload, download, zip, copy and paste files and folders directly from the Wordpress backend. Don’t bother with FTP to manage and move your files from location to location. The most powerful, flexible, and easiest Wordpress file management solution ever built!

https://www.youtube.com/watch?v=CiLkRDVlL2o
       
= Key Features in File Manager free Version Plugin =

Key Features in the Free File Manager plugin include:
* **Operations**: Various operations with files and folders on a remote server (copy, move, upload, create folder/file, rename, edit, delete, etc.)
* **Move/Copy**: Admin can Move/Copy files with Drag & Drop. Also includes multi file selection. 
* **Archives**: Admin can create, archive and extract files(zip, rar, tar, gzip).
* **File Size**: Admin/User can upload any size files.
* **File Type**: Control what files can be uploaded and what file can be downloaded.
* **Code Editor**: File Manager comes with a built in integrated development environment (IDE) - New Feature
* **Syntax Checker**: File Manager now can complete code reviews before saving files to ensure your site will not go down when updating code. Reviewing code for errors has never been so easy! - New Feature
* **Multiple Themes**: Multiple File Manager Themes Available – New Feature
* **Get Info**: All file details, properties, information is now available by simply right clicking a file and selecting Get Info - New Feature 
* **Share Files by Email**: With File Manager you can easily and quickly share files by Email. Simply right click a file and press share, that’s it! - New Feature 
* **Private Folder**: Available only for File Manager Pro Edition
* **Shortcode**: Available only for File Manager Pro Edition
* **Root Directory**: Quickly and easily edit your root path directory. With this feature you can access files inside and outside of Wordpress
* **PDF Support**: Preview PDF files easily 
* **Built-in Trash**: Delete files by moving them to trash
* **File View**: Icon and list view both available for easy navigation
* **Preview Support**: Easily preview common file types including media (video, audio, mp3, thumbnails, etc)
* **Search**: Search functionality is built directly into File Manager making it simple to find your files.
* **Shortcut Support**: Common shortcuts are available in File Manager
* **Automatic File Resize**: automatically resize files once uploaded.
* **Responsive UI**: File Manager works on tablet and mobile devices
* **Browsing History**: File and folders browsing history
* **Trash function**: Move to Trash Folder Feature
* **PDF Preview**: PDF Preview feature available
* **FTP/SFTP Support**: Alternative to FTP or Cpanel
* **File Preview**: preview for common MIMEs and file types
* **Directory Size**: Calculate directory size
* **Icon View**: List and Icons view available for files and files
* **Keyboard shortcuts**: Keyboard shortcuts available e.g. copy,paste,drag & drop
* **Drag and drop**: File Drag & Drop file upload function available
* **Functions Toolbar**: Rich context menu and file manager functions toolbar
* **Thumbnails**: Thumbnails for all types of image files
* **Upload to Media Library**: We have now included the ability to enable images, pdf's, and zip files to be uploaded to you folders and as well be available via the native Wordpress Media Library
* **Backup/Restore**: Backup and restore themes files, plugins files,uploads folder and db data on server.
* **Multi Languages Added**

= Key Features in File Manager Pro Editions =

* **File Type:** Control what files can be uploaded and what file can be downloaded.**
* **Operations:** Various operations with files and folders on a remote server (copy, move, upload, create folder/file, rename, edit, delete etc.)
* **Shortcode:** Available with custom attributes for frontend.
* **Private Folder:** Admin can give access of same and different folder to different users roles and different users.
* **Move/Copy:** Admin can Move/Copy files with Drag & Drop.
* **Archives:** Admin can create/extract files(zip, rar, tar, gzip).
* **File Size:** Admin/User can control file upload size.
* **Fullscreen View:** Admin can control code editor fullscreen view.
* **Editor**: There are a lots of themes available for code editor. Admin can control code editor themes.
* **Hide Files/Folder:** Here admin is able to hide files and folders for user roles and for users.
* **File Type:** Control what files can be uploaded and what file can be downloaded.
* **User Role:** admin is able to control file operations and hide and lock Files and Folders for user roles .
* **Users:** admin is able to control file operations and hide and lock Files and Folders for particular user .
* **High performance:** High performance server backend and light client UI.
* **File system:** Local file system storage drivers.
* **Edit file:** User can edit text files and images.
* **Frontend Access:** User can access frontend using shortcode.
* **Admin Email Notifications.** Admin will get a Notification whenever a file is updated. 
* **Admin Email Notifications.** Admin will get a Notification whenever a file is Downloaded. 
* **Admin Email Notifications.** Admin will get a Notification whenever a file is Edited. 
* **Google Drive Integration**   drag drop, copy paste all other operations between file manager and google drive.
* **Dropbox Integration**        drag drop, copy paste all other operations between file manager and dropbox.


> <strong>[Buy Pro Version](https://filemanagerpro.io/product/file-manager/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> with various features & support.
> <strong>[Contact us](https://filemanagerpro.io/contact/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> for Support Only Pro Version Users.
> <strong>[Documentation](https://filemanagerpro.io/documentation/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> Click Here.
> <strong>[Addons](https://filemanagerpro.io/addons/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> with various features & support.
**[Upgrade to Pro Version](https://filemanagerpro.io/product/file-manager/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)**

= Premium Addons =

<strong>[File Manager Digital Ocean](https://filemanagerpro.io/product/digital-ocean-add-on/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> provides you ability to edit, delete, upload, download, copy and paste files and folders in Digital Ocean.
<strong>[File Manager Google Drive](https://filemanagerpro.io/product/file-manager-google-drive/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> provides you ability to edit, delete, upload, download, copy and paste files and folders in Google Drive.
<strong>[File Manager OneDrive](https://filemanagerpro.io/product/file-manager-one-drive/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> provides you ability to edit, delete, upload, download, copy and paste files and folders in OneDrive from File Manager.
<strong>[File Manager Dropbox](https://filemanagerpro.io/product/file-manager-dropbox/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> provides you ability to edit, delete, upload, download, copy and paste files and folders in dropbox.
<strong>[File Manager Box](https://filemanagerpro.io/product/file-manager-box/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> provides you ability to edit, delete, upload, download, copy and paste files and folders in Box.
<strong>[File Manager AWS S3](https://filemanagerpro.io/product/file-manager-aws-s3/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> provides you ability to edit, delete, upload, download, copy and paste files and folders in AWS S3 bucket.
<strong>[File Manager Git](https://filemanagerpro.io/product/file-manager-git/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> Github integration for file manager.
<strong>[File Manager Slack](https://filemanagerpro.io/product/file-manager-slack/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> Slack incoming webhook integration to file manager.
<strong>[File Manager Google Cloud](https://filemanagerpro.io/product/file-manager-google-cloud/?utm_source=Wordpress.org&utm_medium=Website&utm_campaign=File%20Manager%20Pro)</strong> provides you ability to edit, delete, upload, download, copy and paste files and folders in Google Cloud bucket.


== Installation ==

1. Upload the `wp-file-manager` folder to the directory `/wp-content/plugins/`.
2. Activate the plugin using the 'Plugins' menu in WordPress.

== Frequently asked questions ==

= Can we make zip of any folder or file and download it ? =
Yes, You can archive any files and folders as zip then simple download it. Please view screenshots.

== Support == 
* If any problem occurs, please contact us at https://filemanagerpro.io/contact/

## How to use

1. First Activate Plugin.
2. Then Click on " WP File Manager " menu. Then do with files what you want to do.

== Screenshots ==

1. File Manager File View Screen.
2. Make a folder or file archive as zip.
3. Download archived zip file.
4. PRO:  In File Manager Pro version user is able to give accessibility to user roles by just one click and Control File upload size.
5. PRO:  In File manager pro version admin is able to control file operations for user roles and also hide any file and folder. Admin also lock any file to prevent from edit.
6. PRO:  In File manager pro version admin is able to control file operations for particular user and also hide any file and folder. Admin also lock any file to prevent from edit.
7. PRO:  Admin can control code editor fullscreen view & code editor themes.
8. PRO: Code editor fullscreen view with selected theme.
9. PRO: Private Folder Access
10. File Manager with Multiple Languages
11. File Manager with Multiple Themes - Light Theme
12. Dark Theme View
13. Grey Theme View
14. Window 10 Theme View
15. Edit Root Directory Path


== Changelog ==

= 8.0 (06th Aug, 2024) =
* Broken Image Fixes

= 7.2.10 (29th July, 2024) =
* Compatible with WordPress 6.6.1

= 7.2.9 (6th June, 2024) =
* Trash Folder & Security Fixes.

= 7.2.8 (31 May, 2024) =
* Security Fixes.

= 7.2.7 (07 May, 2024) =
* Fixed image preview issue.
* Checked compatibility with WordPress 6.5.2

= 7.2.6 (01 April, 2024) =
* Directory Traversal issue resolved.

= 7.2.5 (14 Mar, 2024) =
* Improved Language check.

= 7.2.4 (28 Feb, 2024) =
* Fixed Language issue.

= 7.2.3 (26 Feb, 2024) =
* Fixed Language issue.

= 7.2.2 (18 Jan, 2024) =
* Fixed Security issue.

= 7.2.1 (26th Oct, 2023) =
* Directory Traversal issue resolved.
* Checked compatibility with wordpress 6.3.2

= 7.2 (18th August, 2023) =
* Api Update
* Checked compatibility with wordpress 6.3 

= 7.1.9 (4th May, 2023) =
* Minor updations
* Checked compatibility with wordpress 6.2 

= 7.1.8 (8th Feb, 2023) =

* Fixed confliction with pro version
* Minor updations

= 7.1.7 (5th December, 2022) =

* Removed Google Fonts External links
* Issue Resolved for zip download folder
* Checked compatibility with wordpress 6.1.1

= 7.1.6 (28th June, 2022) =

* Checked compatibility with wordpress 5.8.2
* Updated APIs

= 7.1.5 (19th Apr, 2022) =

* Updated translations 
* Fixed zip extract issue
* Minor other bug fixes

= 7.1.4 (27th Jan, 2022) =

* Fixed compatibility issue with PHP > 8
* Fixed issue of fatal error on activating plugin
* Fixed compatibility issue of restore backup in multisite
* Fixed autohide toolbar issue for Ipad

= 7.1.3 (28th Dec, 2021) =

* Elfinder Library Updated
* Checked compatibility with wordpress 5.8.2
* Enhanced backup and restore process

= 7.1.2 (20th July, 2021) =

* Checked compatibility with wordpress 5.8
* Fixed minor bugs
* Checked compatibility with Query Monitor plugin
* Updated Translations

= 7.1.1 (30th March, 2021) =

* Checked compatibility with wordpress 5.7

= 7.1 (18th Feb, 2021) =

* Fixed Cross site scripting (XSS) issue

= 7.0 (8th Feb, 2021) =

* Confliction issue fixed with wordpress 5.6 version
* Fixed download backups links not works on some servers issue
* Fixed PHP warnings issue
* Add support to tiff extension images

= 6.9 (1st Sept, 2020) =

* Security issue fixed

= 6.8 (31st Aug, 2020) =

* Fixed design compatibility issues with wordpress 5.5 version

= 6.7 (20th Aug, 2020) =

* Fixed issue of deprecated function of jquery
* Updated messages text 

= 6.6 (18th Aug, 2020) =

* Updated Translations
* Added media title to the uploaded file when Files Upload to Media Library is enabled

= 6.5 (18th Jun, 2020) =

* Security Fix

= 6.4 (25th May, 2020) =

* $ confliction fixes

= 6.3 (22nd May, 2020) =

* Files extract issues fixes

= 6.2 (15th May, 2020) =

* jQuery confliction fixes

= 6.1 (14th May, 2020) =

* Compatibility issues

= 6.0 (14th May, 2020) =

* Google doc preview feature added and Library Updates - Major Update

= 5.9 (13th APR, 2020) =

* Fixed Errors Deprecated Unparenthesized

= 5.8 (31st MARCH, 2020) =

* Tested with Wordpress 5.4 version.

= 5.7 (23rd JAN, 2020) =

* Media library js fixes

= 5.6 (14th JAN, 2020) =

* Media library option fixes

= 5.5 (2nd DEC, 2019) =

* Unparenthesized issue fixes.

= 5.4 (16th AUGUST, 2019) =

* Minor fixes and added logs demo screenshots.

= 5.3 (20th AUGUST, 2019) =

* Rate us bar repetition removed.

= 5.2 (12th JULY, 2019) =

* Security fixes addressed by wordpress.

= 5.1 (11th JULY, 2019) =

* Security fixes.

= 5.0 (10th JULY, 2019) =

* Search outline issue fixed, Restore feature bugs fixes

= 4.9 (8th JULY, 2019) =

* Media library multiple file extensions allowed, backup feature admin authorized issue fixes

= 4.8 (13th MAY, 2019) =

* Minor fixes

= 4.7 (13th MAY, 2019) =

* Files and Database backup - restore feature added

= 4.6 (18th APR, 2019) =

* Elfinder Library Updated, Security Fixes

= 4.5 (17th APR, 2019) =

* PHP 7 issues fixes

= 4.4 (22nd FEB, 2019) =

* Extract issue fixed

= 4.3 (21st FEB, 2019) =

* Syntax checker feature removed for now

= 4.2 (21st FEB, 2019) =

* elFinder Library Updated

= 4.1 (21st JAN, 2019) =

* Syntax Error Feature Added

= 4.0 (10th JAN, 2019) =

* Http API fixes

= 3.9 (10th JAN, 2019) =

* CURL issue fixes

= 3.8 (20th DEC, 2018) =

* php 7.2 warnings fixes

= 3.7 (3rd DEC, 2018) =

* Header issue fixed

= 3.6 (3rd DEC, 2018) =

* Security Fixes

= 3.5 (3rd DEC, 2018) =

* Zip extract issue fixes

= 3.4 (30th Nov, 2018) =

* Minor Fixes

= 3.3 (30th Nov, 2018) =

* Library updated

= 3.2 (20th Oct, 2018) =

* CompaNovle with php 7.3 and wordpress 5.0

= 3.1 (17th Sep, 2018) =

* Security fixes and design fixes

= 3.0 (5th Sep, 2018) =

* Security issues fixed

= 2.9 (27th Aug ,2018) =

* Russian Translations added. Credit: @ivan192

* Code editor lines number added.

* Minimized window buttons collapsing issue fixed

= 2.8 (15th Jun ,2018) =

* minor Performence fix

= 2.7 (2th Jun ,2018) =

major Performence fix


= 2.6 (18th May ,2018) =

* '/' error Fix,major fix

= 2.5 (16th May ,2018) =

* Upload File issue Fix

= 2.4 (16th Apr ,2018) =

* On extract Invaild Backend issue

= 2.3 (16th Apr ,2018) =

* Extract Issue Resolved

= 2.2 (9th Apr ,2018) =

* PHP 7 Compatibility Issues Resolved

= 2.1 (26th March ,2018) =

* major design fixes

= 2.0 (1st March ,2018) =

* Edit Root Directory Path Feature - Major Update

= 1.9 (8th Jan ,2018) =

* fix Console en js missing error
* Now WP File Manager has various themes. - Major Update

= 1.8 (20th Sep ,2017) =

* fix some Bug in 1.7 - Minor Update
* Now WP File Manager is in various languages. - Major Update
* WP File Manager Translations Available. Compatible with any wordpress language. - Major Update

= 1.7 (18th Aug ,2017) =

* fix some Bug in 1.6 - Minor Update
* System Properties Menu - Added(New)

= 1.6 (20th Apr ,2017) =

* fix some Bug in 1.5 - Minor Update

= 1.5 (01th Mar ,2017) =

* fix some Bug in 1.4 - Major Update

= 1.4 (09th Jan ,2017) =

* fix some Bug in 1.3 - Major Update
* fix File edit auto slash add problem

= 1.3 (23th Nov ,2016) =

* fix some Bug in 1.2 - Minor Update
* fix max upload size problem

= 1.2 (17th Sep ,2016) =

* fix some Bug in 1.1 - Minor Update
* Compatible upto wordpress 4.6.1

= 1.1 (26th Aug ,2016) =

* fix some Bug in 1.0 - Minor Update
* Compatible upto wordpress 4.6


== Upgrade Notice ==
= Upgrade your old version to 3.2


== Other Notes ==

= Minimum requirements for File Manager =
*   WordPress 3.3+
*   PHP 5.x
*   MySQL 5.x

If any problem occurs, please contact us at https://filemanagerpro.io/contact/
