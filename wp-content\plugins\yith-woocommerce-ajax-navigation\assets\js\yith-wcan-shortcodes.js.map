{"version": 3, "sources": ["webpack://yith-woocommerce-ajax-navigation/./assets/js/shortcodes/config.js", "webpack://yith-woocommerce-ajax-navigation/./assets/js/shortcodes/modules/yith-wcan-filter.js", "webpack://yith-woocommerce-ajax-navigation/./assets/js/shortcodes/modules/yith-wcan-reset-button.js", "webpack://yith-woocommerce-ajax-navigation/./assets/js/shortcodes/modules/yith-wcan-dropdown.js", "webpack://yith-woocommerce-ajax-navigation/./assets/js/shortcodes/modules/yith-wcan-preset.js", "webpack://yith-woocommerce-ajax-navigation/./assets/js/shortcodes/index.js"], "names": ["$", "j<PERSON><PERSON><PERSON>", "YITH_WCAN_Filter", "location", "search", "head", "html", "pageTitle", "document", "title", "alternativeUrl", "searchAlternativeUrl", "doingAjax", "initialized", "yith_wcan_shortcodes", "ajax_filters", "pushUrlToHistory", "filters", "target", "preset", "targetUrl", "$target", "customFilters", "<PERSON><PERSON><PERSON><PERSON>", "block", "buildUrl", "window", "_doAjax", "done", "response", "_beforeFilter", "refreshFragments", "unblock", "_afterFilter", "trigger", "on", "closest", "submit", "Object", "keys", "length", "addClass", "removeClass", "queryParam", "query_param", "params", "url", "base_url", "origin", "pathname", "self", "haveFilters", "session_param", "replace", "RegExp", "originalSearch", "searchParams", "split", "reduce", "a", "v", "items", "isFilterParam", "i", "_cleanParam", "defaultUrl", "matches", "indexOf", "match", "change_browser_url", "navigator", "userAgent", "history", "pushState", "responseDom", "createElement", "$response", "innerHTML", "$preset", "$destination", "find", "replaceWith", "first", "content", "param", "process_sanitize", "skip_sanitize", "encodeURIComponent", "xhr", "abort", "headers", "$el", "background", "loader", "message", "overlayCSS", "opacity", "supportedParams", "customParams", "concat", "supported_taxonomies", "map", "YITH_WCAN_Reset_Button", "el", "$reset", "ev", "preventDefault", "each", "data", "deactivateAllFilters", "closeModal", "YITH_WCAN_Dropdown", "opts", "$originalSelect", "is", "defaultPerPage", "defaultOrder", "defaultAll", "defaults", "showSearch", "paginate", "perPage", "order", "getElements", "labels", "emptyLabel", "empty_option", "searchPlaceholder", "search_placeholder", "noItemsFound", "no_items", "showMore", "show_more", "options", "_hideSelect", "_initTemplate", "_initActions", "hide", "$mainSpan", "$labelSpan", "get<PERSON><PERSON><PERSON>", "$dropdownSpan", "$matchingItemsList", "append", "_initSearchTemplate", "_initShowMoreTemplate", "after", "$_main", "$_label", "$_dropdown", "$_items", "$dropdwonSpan", "$container", "$search", "name", "type", "placeholder", "prependTo", "$_search", "$showMore", "text", "loadNextPage", "bind", "$_showMore", "stopPropagation", "toggleDropdown", "_populateItems", "$li", "value", "isActive", "hasClass", "isValueSelected", "toggleClass", "_changeItemStatus", "siblings", "input", "parent", "prop", "change", "selfOriginated", "_selectItem", "_deselectItem", "updateLabel", "closeDropdown", "_afterDropdownOpen", "dropdowns", "filter", "select", "_closeOtherDropdowns", "val", "limit", "matchingElements", "$options", "getOptions", "promise", "Promise", "resolve", "t", "label", "regex", "show", "test", "push", "then", "retrievedElements", "_formatItems", "indexes", "hasMore", "sort", "b", "mod", "slice", "active", "option", "getOptionByValue", "$item", "attr", "$anchor", "template", "count", "href", "$checkbox", "$label", "prepend", "page", "parseInt", "getMatchingElements", "resultSet", "matchingItems", "_emptyItems", "_hideLoadMore", "currentPage", "unshift", "_generateItem", "_showLoadMore", "status", "$option", "hasSelectedValues", "getSelectedLabels", "join", "getSelectedOptions", "found", "getSelectedValues", "toString", "values", "YITH_WCAN_Preset", "_regiterStatus", "_initFilterButton", "_initResponsive", "_initFilters", "getFilters", "$filter", "_initFilter", "maybeShowClearAllFilters", "$filterButtons", "modal_on_mobile", "media", "matchMedia", "mobile_media_query", "isMobile", "_afterLayoutChange", "resize", "handleChange", "$currentFilter", "multiple", "$items", "not", "children", "activeFilters", "maybeFilter", "maybeToggleClearAllFilters", "maybe<PERSON><PERSON><PERSON><PERSON>lear<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "call", "$input", "_initTooltip", "_initPriceSlider", "_initDropdown", "_initCollapsable", "maybe<PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>", "_initCustomInput", "position", "th", "tooltip", "wrapperWidth", "outerWidth", "left", "width", "container", "css", "toFixed", "fadeIn", "fadeOut", "remove", "$dropdown", "selectWoo", "_initDropdownObject", "terms_per_page", "$minInput", "$maxInput", "min", "parseFloat", "max", "currentMin", "currentMax", "step", "handleSliderChange", "sliderTimeout", "clearTimeout", "setTimeout", "ionRangeSlider", "skin", "from", "to", "min_interval", "values_separator", "prettify", "formatPrice", "onChange", "onFinish", "add", "off", "_initTitleCollapsable", "_initHierarchyCollapsable", "$title", "_initToggle", "parents", "show_current_children", "$t", "$toggle", "appendTo", "slideToggle", "containerClass", "wrap", "originalFilters", "getFiltersProperties", "_addCloseModalButton", "_addApplyFiltersModalButton", "_switchToCollapsables", "removeAttr", "_removeCloseModalButton", "_removeApplyFiltersModalButton", "_switchBackCollapsables", "$closeButton", "close", "modalElements", "closeButton", "$filterButton", "show_results", "applyFiltersButton", "removeData", "$filters", "isFilterActive", "end", "currentFilters", "currentStr", "JSON", "stringify", "originalStr", "dirty", "$initiator", "maybeRegisterStatusChange", "instant_filters", "product_filter", "<PERSON><PERSON><PERSON><PERSON>", "newPreset", "scroll_top", "targetOffset", "offset", "top", "scroll_target", "scrollTarget", "animate", "scrollTop", "getActiveFilters", "filterType", "filteredActive", "Math", "abs", "properties", "filteredProperties", "$active", "activeTerms", "taxonomy", "isAttr", "relation", "get", "pop", "glue", "rating_filter", "price_ranges", "min_price", "max_price", "onsale_filter", "instock_filter", "featured_filter", "orderby", "filterProperties", "getFilterProperties", "mergeProperties", "hasProp", "includes", "maybeHideClearFilter", "isAnyFilterActive", "maybeHideClearAllFilters", "show_clear_filter", "clear_selection", "role", "deactivateFilter", "clear_all_selections", "$activeItems", "click", "formattedRange", "$priceSlider", "getFiltersByProperties", "toggles_open_on_modal", "_openAllCollapsables", "_closeAllCollapsables", "price", "accounting", "formatMoney", "symbol", "currency_format", "decimal", "thousand", "precision", "format", "set1", "set2", "hasOwnProperty", "newValue", "index", "arr", "queryTypeParam", "globalThis"], "mappings": ";;;;;AAAa;AAEb;;AAEA,IAAMA,CAAC,GAAGC,MAAV,C,CAAkB;;;;ACJL;AAEb;;;;;;;;;;;;AAEA;;IAEqBC,gB;AACpB;AAGA;AAGA;AAGA;AAGA;AACA,8BAAc;AAAA;;AAAA,iCAZR,IAYQ;;AAAA,uCATF,KASE;;AAAA,4CANGC,QAAQ,CAACC,MAMZ;;AAAA,yCAHA,KAGA;;AACb,QAAMC,IAAI,GAAGL,CAAC,CAAE,MAAF,CAAD,CAAYM,IAAZ,EAAb;AAAA,QACCC,SAAS,GAAGC,QAAQ,CAACC,KADtB;AAAA,QAECC,cAAc,GAAG,KAAKC,oBAAL,CAA2BN,IAA3B,CAFlB;AAIAK,kBAAc,IACb,CAAE,KAAKE,SADR,IAEC,CAAE,KAAKC,WAFR,IAGC,CAAEC,oBAAoB,CAACC,YAHxB,IAIC,KAAKC,gBAAL,CAAuBN,cAAvB,EAAuCH,SAAvC,CAJD;AAMA,SAAKM,WAAL,GAAmB,IAAnB;AACA,G,CAED;;;;;WACA,kBAAUI,OAAV,EAAmBC,MAAnB,EAA2BC,MAA3B,EAAoC;AAAA;;AACnC,UAAIC,SAAJ;AAAA,UACCC,OAAO,GAAGH,MAAM,GAAGlB,CAAC,CAAEkB,MAAF,CAAJ,GAAiBlB,CAAC,CAAE,MAAF,CADnC;AAAA,UAECsB,aAFD,CADmC,CAKnC;;AACAA,mBAAa,GAAGtB,CAAC,CAChBQ,QADgB,CAAD,CAEde,cAFc,CAEE,8BAFF,EAEkC,CAAEN,OAAF,CAFlC,CAAhB;;AAIA,UAAK,CAAC,CAAEK,aAAR,EAAwB;AACvBL,eAAO,GAAGK,aAAV;AACA,OAZkC,CAcnC;;;AACAD,aAAO,IAAI,KAAKG,KAAL,CAAYH,OAAZ,CAAX,CAfmC,CAiBnC;;AACAD,eAAS,GAAG,KAAKK,QAAL,CAAeR,OAAf,CAAZ,CAlBmC,CAoBnC;;AACA,UAAK,CAAEH,oBAAoB,CAACC,YAA5B,EAA2C;AAC1CW,cAAM,CAACvB,QAAP,GAAkBiB,SAAlB;AACA;AACA,OAxBkC,CA0BnC;;;AACA,WAAKR,SAAL,GAAiB,IAAjB;AAEA,aAAO,KAAKe,OAAL,CAAcP,SAAd,EAA0BQ,IAA1B,CAAgC,UAAEC,QAAF,EAAgB;AACtDT,iBAAS,GAAG,KAAI,CAACT,oBAAL,CAA2BkB,QAA3B,EAAqCT,SAArC,CAAZ;;AAEA,aAAI,CAACU,aAAL,CAAoBD,QAApB,EAA8BZ,OAA9B;;AAEA,aAAI,CAACc,gBAAL,CAAuBb,MAAvB,EAA+BC,MAA/B,EAAuCU,QAAvC;;AACA,aAAI,CAACb,gBAAL,CAAuBI,SAAvB,EAAkCS,QAAQ,CAACtB,SAA3C;;AAEAc,eAAO,IAAI,KAAI,CAACW,OAAL,CAAcX,OAAd,CAAX;;AAEA,aAAI,CAACY,YAAL,CAAmBJ,QAAnB,EAA6BZ,OAA7B;;AAEA,aAAI,CAACL,SAAL,GAAiB,KAAjB;AACA,OAbM,CAAP;AAcA,K,CAED;;;;WACA,uBAAeiB,QAAf,EAAyBZ,OAAzB,EAAmC;AAClCjB,OAAC,CAAEQ,QAAF,CAAD,CAAc0B,OAAd,CAAuB,wBAAvB,EAAiD,CAChDL,QADgD,EAEhDZ,OAFgD,CAAjD;AAIA,K,CAED;;;;WACA,sBAAcY,QAAd,EAAwBZ,OAAxB,EAAkC;AACjCjB,OAAC,CAAE,uBAAF,CAAD,CAA6BmC,EAA7B,CACC,QADD,EAEC,gBAFD,EAGC,YAAY;AACXnC,SAAC,CAAE,IAAF,CAAD,CAAUoC,OAAV,CAAmB,MAAnB,EAA4BC,MAA5B;AACA,OALF;;AAQA,UAAKpB,OAAO,IAAI,CAAC,CAAEqB,MAAM,CAACC,IAAP,CAAatB,OAAb,EAAuBuB,MAA1C,EAAmD;AAClDxC,SAAC,CAAE,MAAF,CAAD,CAAYyC,QAAZ,CAAsB,UAAtB;AACA,OAFD,MAEO;AACNzC,SAAC,CAAE,MAAF,CAAD,CAAY0C,WAAZ,CAAyB,UAAzB;AACA;;AAED1C,OAAC,CAAE0B,MAAF,CAAD,CAAYQ,OAAZ,CAAqB,QAArB;AAEAlC,OAAC,CAAEQ,QAAF,CAAD,CACE0B,OADF,CACW,yBADX,EACsC,CAAEL,QAAF,EAAYZ,OAAZ,CADtC,EAEEiB,OAFF,CAEW,4BAFX;AAGA,K,CAED;;;;WACA,kBAAUjB,OAAV,EAAoB;AAAA;;AACnB,UAAI0B,UAAU,GAAG7B,oBAAoB,CAAC8B,WAAtC;AAAA,UACCC,MAAM,GAAG,EADV;AAAA,UAEC1C,QAAQ,GAAGuB,MAAM,CAACvB,QAFnB;AAAA,UAGC2C,GAAG,GAAG,CAAC,CAAEhC,oBAAoB,CAACiC,QAAxB,GACHjC,oBAAoB,CAACiC,QADlB,GAEH,CAAA5C,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAE6C,MAAV,KAAmB7C,QAAnB,aAAmBA,QAAnB,uBAAmBA,QAAQ,CAAE8C,QAA7B,CALJ;AAAA,UAMC7C,MAAM,GAAG,EANV;AAAA,UAOC8C,IAAI,GAAG,IAPR;AASA,UAAMC,WAAW,GAChB,QAAOlC,OAAP,MAAmB,QAAnB,IAA+BqB,MAAM,CAACC,IAAP,CAAatB,OAAb,EAAuBuB,MADvD,CAVmB,CAanB;;AACA,UAAK,CAAC,CAAE1B,oBAAoB,CAACsC,aAA7B,EAA6C;AAC5CN,WAAG,GAAGA,GAAG,CAACO,OAAJ,CACL,IAAIC,MAAJ,CACC,MAAMxC,oBAAoB,CAACsC,aAA3B,GAA2C,SAD5C,CADK,EAIL,EAJK,CAAN;AAMA;;AAED,UAAKD,WAAL,EAAmB;AAClBN,cAAM,CAAEF,UAAF,CAAN,GAAuB,CAAvB;AACA;;AAED,UAAK,CAAC,CAAE,KAAKY,cAAb,EAA8B;AAC7B,YAAMC,YAAY,GAAG,KAAKD,cAAL,CACnBF,OADmB,CACV,GADU,EACL,EADK,EAEnBI,KAFmB,CAEZ,GAFY,EAGnBC,MAHmB,CAGX,UAAEC,CAAF,EAAKC,CAAL,EAAY;AACpB,cAAMC,KAAK,GAAGD,CAAC,CAACH,KAAF,CAAS,GAAT,CAAd;;AAEA,cAAKI,KAAK,CAACrB,MAAN,KAAiB,CAAtB,EAA0B;AACzB,gBAAK,MAAI,CAACsB,aAAL,CAAoBD,KAAK,CAAE,CAAF,CAAzB,CAAL,EAAwC;AACvC,qBAAOF,CAAP;AACA;;AAEDA,aAAC,CAAEE,KAAK,CAAE,CAAF,CAAP,CAAD,GAAkBA,KAAK,CAAE,CAAF,CAAvB;AACA;;AAED,iBAAOF,CAAP;AACA,SAfmB,EAejB,EAfiB,CAArB;AAiBAd,cAAM,GAAG7C,QAAA,CAAU6C,MAAV,EAAkBW,YAAlB,CAAT;AACA;;AAED,UAAKL,WAAL,EAAmB;AAClBN,cAAM,GAAG7C,QAAA,CAAU6C,MAAV,EAAkB5B,OAAlB,CAAT;AACA;;AAEDb,YAAM,GAAGkC,MAAM,CAACC,IAAP,CAAaM,MAAb,EACPa,MADO,CACC,UAAWC,CAAX,EAAcI,CAAd,EAAkB;AAC1B,YAAMH,CAAC,GAAGf,MAAM,CAAEkB,CAAF,CAAhB;;AAEA,YAAK,CAAEH,CAAF,IAAO,CAAEG,CAAd,EAAkB;AACjB,iBAAOJ,CAAP;AACA;;AAEDA,SAAC,IAAIT,IAAI,CAACc,WAAL,CAAkBD,CAAlB,IAAwB,GAAxB,GAA8Bb,IAAI,CAACc,WAAL,CAAkBJ,CAAlB,CAA9B,GAAsD,GAA3D;AAEA,eAAOD,CAAP;AACA,OAXO,EAWL,GAXK,EAYPN,OAZO,CAYE,KAZF,EAYS,EAZT,EAaPA,OAbO,CAaE,MAbF,EAaU,GAbV,EAcPA,OAdO,CAcE,MAdF,EAcU,GAdV,CAAT;;AAgBA,UAAKjD,MAAM,CAACoC,MAAP,GAAgB,CAArB,EAAyB;AACxBM,WAAG,IAAI1C,MAAP;AACA;;AAED,aAAO0C,GAAP;AACA,K,CAED;;;;WACA,8BAAsBjB,QAAtB,EAAkD;AAAA,UAAlBoC,UAAkB,uEAAL,EAAK;AACjD,UAAInB,GAAG,GAAGmB,UAAV;AAAA,UACCC,OADD;;AAGA,UAAK,CAAC,CAAD,KAAOrC,QAAQ,CAACsC,OAAT,CAAkB,uBAAlB,CAAZ,EAA0D;AACzD,eAAOrB,GAAP;AACA;;AAEDoB,aAAO,GAAGrC,QAAQ,CAACuC,KAAT,CACT,uDADS,CAAV;AAGAtB,SAAG,GAAGoB,OAAO,IAAI,KAAKA,OAAhB,GAA0BA,OAAO,CAAE,CAAF,CAAjC,GAAyCpB,GAA/C;AAEA,aAAOA,GAAP;AACA,K,CAED;;;;WACA,0BAAkBA,GAAlB,EAAuBrC,KAAvB,EAA+B;AAC9B,UACC,CAAEK,oBAAoB,CAACuD,kBAAvB,IACAC,SAAS,CAACC,SAAV,CAAoBH,KAApB,CAA2B,OAA3B,CAFD,EAGE;AACD;AACA;;AAED1C,YAAM,CAAC8C,OAAP,CAAeC,SAAf,CACC;AACClE,iBAAS,EAAEE;AADZ,OADD,EAIC,EAJD,EAKCqC,GALD;AAOA,K,CAED;;;;WACA,0BAAkB5B,MAAlB,EAA0BC,MAA1B,EAAkCU,QAAlC,EAA6C;AAC5C,UAAM6C,WAAW,GAAGlE,QAAQ,CAACmE,aAAT,CAAwB,MAAxB,CAApB;AAAA,UACCC,SAAS,GAAG5E,CAAC,CAAE0E,WAAF,CADd;AAGAA,iBAAW,CAACG,SAAZ,GAAwBhD,QAAxB;;AAEA,UAAKX,MAAL,EAAc;AACb,YAAI4D,OAAO,GAAG9E,CAAC,CAAEmB,MAAF,CAAf;AAAA,YACCE,OAAO,GAAGrB,CAAC,CAAEkB,MAAF,CADZ;AAAA,YAEC6D,YAFD;;AAIA,YAAKD,OAAO,CAACtC,MAAb,EAAsB;AACrBuC,sBAAY,GAAGH,SAAS,CAACI,IAAV,CAAgB7D,MAAhB,CAAf;;AAEA,cAAK4D,YAAY,CAACvC,MAAlB,EAA2B;AAC1BsC,mBAAO,CAACG,WAAR,CAAqBF,YAAY,CAACG,KAAb,EAArB;AACA;AACD;;AAED,YAAK7D,OAAO,CAACmB,MAAb,EAAsB;AACrBuC,sBAAY,GAAGH,SAAS,CAACI,IAAV,CAAgB9D,MAAhB,CAAf;;AAEA,cAAK6D,YAAY,CAACvC,MAAlB,EAA2B;AAC1BnB,mBAAO,CAAC4D,WAAR,CAAqBF,YAAY,CAACG,KAAb,EAArB;AACA;AACD;AACD,OApBD,MAoBO;AACN,YAAMC,OAAO,GAAGnF,CAAC,CAAEc,oBAAoB,CAACqE,OAAvB,CAAjB;;AAEA,YAAKA,OAAO,CAAC3C,MAAb,EAAsB;AACrB2C,iBAAO,CAACF,WAAR,CACCL,SAAS,CAACI,IAAV,CAAgBlE,oBAAoB,CAACqE,OAArC,CADD;AAGA,SAJD,MAIO;AACNnF,WAAC,CAAE,MAAF,CAAD,CAAYiF,WAAZ,CAAyBL,SAAS,CAACI,IAAV,CAAgB,MAAhB,CAAzB;AACA;AACD;;AAEDhF,OAAC,CAAEQ,QAAF,CAAD,CAAc0B,OAAd,CAAuB,2BAAvB;AACA,K,CAED;;;;WACA,qBAAakD,KAAb,EAAqB;AAAA;;AACpB,UACC,2BAAEtE,oBAAF,kDAAE,sBAAsBuE,gBAAxB,+BACAvE,oBADA,mDACA,uBAAsBwE,aAFvB,EAGE;AACD,eAAOF,KAAP;AACA;;AAED,aAAOG,kBAAkB,CAAEH,KAAF,CAAzB;AACA,K,CAED;;;;WACA,iBAAStC,GAAT,EAAcD,MAAd,EAAuB;AACtB,UAAK,KAAK2C,GAAV,EAAgB;AACf,aAAKA,GAAL,CAASC,KAAT;AACA;;AAED5C,YAAM,GAAG7C,QAAA,CACR;AACC8C,WAAG,EAAHA,GADD;AAEC4C,eAAO,EAAE;AACR,yBAAe;AADP;AAFV,OADQ,EAOR7C,MAPQ,CAAT;AAUA,WAAK2C,GAAL,GAAWxF,MAAA,CAAQ6C,MAAR,CAAX;AAEA,aAAO,KAAK2C,GAAZ;AACA,K,CAED;;;;WACA,eAAOG,GAAP,EAAa;AAAA;;AACZ,UAAK,OAAO3F,UAAP,KAAsB,WAA3B,EAAyC;AACxC;AACA;;AAED,UAAI4F,UAAU,GAAG,8BAAjB;;AAEA,oCAAK9E,oBAAL,mDAAK,uBAAsB+E,MAA3B,EAAoC;AACnCD,kBAAU,kBAAY9E,oBAAoB,CAAC+E,MAAjC,gBAA+CD,UAA/C,CAAV;AACA;;AAEDD,SAAG,CAACnE,KAAJ,CAAW;AACVsE,eAAO,EAAE,IADC;AAEVC,kBAAU,EAAE;AACXH,oBAAU,EAAVA,UADW;AAEXI,iBAAO,EAAE;AAFE;AAFF,OAAX;AAOA,K,CAED;;;;WACA,iBAASL,GAAT,EAAe;AACd,UAAK,OAAO3F,YAAP,KAAwB,WAA7B,EAA2C;AAC1C;AACA;;AAED2F,SAAG,CAAC3D,OAAJ;AACA,K,CAED;;;;WACA,uBAAeoD,KAAf,EAAuB;AACtB,UAAIa,eAAe,GAAG,CACpB,eADoB,EAEpB,WAFoB,EAGpB,WAHoB,EAIpB,cAJoB,EAKpB,eALoB,EAMpB,gBANoB,EAOpB,iBAPoB,EAQpB,SARoB,EASpB,cAToB,EAUpBnF,oBAAoB,CAAC8B,WAVD,CAAtB;AAAA,UAYCsD,YAZD,CADsB,CAetB;;AACAA,kBAAY,GAAGlG,CAAC,CACfQ,QADe,CAAD,CAEbe,cAFa,CAEG,wCAFH,EAE6C,CAC3D0E,eAD2D,CAF7C,CAAf;;AAMA,UAAK,CAAC,CAAEC,YAAR,EAAuB;AACtBD,uBAAe,GAAGC,YAAlB;AACA;;AAEDD,qBAAe,GAAGA,eAAe,CAACE,MAAhB,CACjBrF,oBAAoB,CAACsF,oBAArB,CAA0CC,GAA1C,CAA+C,UAAEtC,CAAF;AAAA,eAC9CA,CAAC,CAACV,OAAF,CAAW,KAAX,EAAkB,SAAlB,CAD8C;AAAA,OAA/C,CADiB,CAAlB;;AAMA,UAAK,CAAC,CAAD,KAAO4C,eAAe,CAAC9B,OAAhB,CAAyBiB,KAAzB,CAAZ,EAA+C;AAC9C,eAAO,IAAP;AACA;;AAED,UAAK,CAAC,CAAD,KAAOA,KAAK,CAACjB,OAAN,CAAe,SAAf,CAAZ,EAAyC;AACxC,eAAO,IAAP;AACA;;AAED,UAAK,CAAC,CAAD,KAAOiB,KAAK,CAACjB,OAAN,CAAe,aAAf,CAAZ,EAA6C;AAC5C,eAAO,IAAP;AACA;;AAED,aAAO,KAAP;AACA;;;;;;;;ACrXW;AAEb;;;;;;AAEA;;IAEqBmC,sB,GACpB;AAGA;AACA,gCAAaC,EAAb,EAAkB;AAAA;;AAAA,wDAHT,IAGS;;AACjB;AACA,OAAKC,MAAL,GAAcD,EAAd;AAEA,OAAKC,MAAL,CAAYrE,EAAZ,CAAgB,OAAhB,EAAyB,UAAWsE,EAAX,EAAgB;AACxCA,MAAE,CAACC,cAAH;AAEA1G,KAAC,CAAE,oBAAF,CAAD,CAA0B2G,IAA1B,CAAgC,YAAY;AAC3C,UAAMxF,MAAM,GAAGnB,CAAC,CAAE,IAAF,CAAD,CAAU4G,IAAV,CAAgB,QAAhB,CAAf;AAEAzF,YAAM,CAAC0F,oBAAP,CAA6B,IAA7B;AACA1F,YAAM,CAAC2F,UAAP;AACA,KALD;AAMA,GATD;AAWA,OAAKN,MAAL,CAAYI,IAAZ,CAAkB,OAAlB,EAA2B,IAA3B,EAAkCnE,QAAlC,CAA4C,UAA5C;AACA,C;;;;AC3BW;AAEb;;;;;;;;;;;;;;;;AAEA;;IAEqBsE,kB;AACpB;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AACA,8BAAaR,EAAb,EAAiBS,IAAjB,EAAwB;AAAA;;AAAA,+DA3BN,IA2BM;;AAAA,sDAxBf,IAwBe;;AAAA,uDArBd,IAqBc;;AAAA,0DAlBX,IAkBW;;AAAA,wDAfb,IAea;;AAAA,0DAZX,IAYW;;AAAA,uDATd,IASc;;AAAA,2DANV,CAMU;;AAAA,uDAHd,EAGc;;AACvB,SAAKC,eAAL,GAAuBV,EAAvB;;AAEA,QAAK,CAAE,KAAKU,eAAL,CAAqBC,EAArB,CAAyB,QAAzB,CAAP,EAA6C;AAC5C;AACA;;AAED,QAAMC,cAAc,GAAG,KAAKF,eAAL,CAAqBL,IAArB,CAA2B,UAA3B,CAAvB;AAAA,QACCQ,YAAY,GAAG,KAAKH,eAAL,CAAqBL,IAArB,CAA2B,OAA3B,CADhB;AAAA,QAECS,UAAU,GAAG,KAAKJ,eAAL,CAAqBL,IAArB,CAA2B,WAA3B,CAFd;AAAA,QAGCU,QAAQ,GAAG;AACVC,gBAAU,EAAE,KAAKN,eAAL,CAAqBL,IAArB,CAA2B,aAA3B,CADF;AAEVY,cAAQ,EAAE,KAAKP,eAAL,CAAqBL,IAArB,CAA2B,UAA3B,CAFA;AAGVa,aAAO,EAAEN,cAAc,GAAGA,cAAH,GAAoB,EAHjC;AAIVO,WAAK,EAAEN,YAAY,GAAGA,YAAH,GAAkB,KAJ3B;AAKVO,iBAAW,EAAE,IALH;AAMVC,YAAM,EAAE;AACPC,kBAAU,EAAER,UAAU,GACnBA,UADmB,GAEnBvG,oBAAoB,CAAC8G,MAArB,CAA4BE,YAHxB;AAIPC,yBAAiB,EAChBjH,oBAAoB,CAAC8G,MAArB,CAA4BI,kBALtB;AAMPC,oBAAY,EAAEnH,oBAAoB,CAAC8G,MAArB,CAA4BM,QANnC;AAOPC,gBAAQ,EAAErH,oBAAoB,CAAC8G,MAArB,CAA4BQ;AAP/B;AANE,KAHZ;AAoBA,SAAKC,OAAL,GAAerI,QAAA,CAAUsH,QAAV,EAAoBN,IAApB,CAAf;;AAEA,SAAKsB,WAAL;;AACA,SAAKC,aAAL;;AACA,SAAKC,YAAL;;AAEA,SAAKvB,eAAL,CAAqBL,IAArB,CAA2B,UAA3B,EAAuC,IAAvC,EAA8CnE,QAA9C,CAAwD,UAAxD;AACA,G,CAED;;;;;WACA,uBAAc;AACb,WAAKwE,eAAL,CAAqBwB,IAArB;AACA,K,CAED;;;;WACA,yBAAgB;AACf,UAAMC,SAAS,GAAG1I,CAAC,CAAE,QAAF,EAAY;AAC7B,iBAAO;AADsB,OAAZ,CAAnB;AAAA,UAGC2I,UAAU,GAAG3I,CAAC,CAAE,QAAF,EAAY;AACzB,iBAAO,gBADkB;AAEzBM,YAAI,EAAE,KAAKsI,QAAL;AAFmB,OAAZ,CAHf;AAAA,UAOCC,aAAa,GAAG7I,CAAC,CAAE,OAAF,EAAW;AAC3B,iBAAO;AADoB,OAAX,CAPlB;AAAA,UAUC8I,kBAAkB,GAAG9I,CAAC,CAAE,OAAF,EAAW;AAChC,iBAAO;AADyB,OAAX,CAVvB;AAcA6I,mBAAa,CAACE,MAAd,CAAsBD,kBAAtB;AACAJ,eAAS,CAACK,MAAV,CAAkBJ,UAAlB,EAA+BI,MAA/B,CAAuCF,aAAvC;;AAEA,UAAK,KAAKR,OAAL,CAAad,UAAlB,EAA+B;AAC9B,aAAKyB,mBAAL,CAA0BH,aAA1B;AACA;;AAED,UAAK,KAAKR,OAAL,CAAab,QAAlB,EAA6B;AAC5B,aAAKyB,qBAAL,CAA4BJ,aAA5B;AACA;;AAED,WAAK5B,eAAL,CAAqBiC,KAArB,CAA4BR,SAA5B;AACA,WAAKS,MAAL,GAAcT,SAAd;AACA,WAAKU,OAAL,GAAeT,UAAf;AACA,WAAKU,UAAL,GAAkBR,aAAlB;AACA,WAAKS,OAAL,GAAeR,kBAAf;AACA,K,CAED;;;;WACA,6BAAqBS,aAArB,EAAqC;AACpC,UAAMC,UAAU,GAAGxJ,CAAC,CAAE,QAAF,EAAY;AAC9B,iBAAO;AADuB,OAAZ,CAApB;AAAA,UAGCyJ,OAAO,GAAGzJ,CAAC,CAAE,UAAF,EAAc;AACxB0J,YAAI,EAAE,GADkB;AAExB,iBAAO,cAFiB;AAGxBC,YAAI,EAAE,QAHkB;AAIxBC,mBAAW,EAAE,KAAKvB,OAAL,CAAaT,MAAb,CAAoBG;AAJT,OAAd,CAHZ;AAUAyB,gBAAU,CAACT,MAAX,CAAmBU,OAAnB,EAA6BI,SAA7B,CAAwCN,aAAxC;AACA,WAAKO,QAAL,GAAgBL,OAAhB;AACA,K,CAED;;;;WACA,+BAAuBF,aAAvB,EAAuC;AACtC,UAAMQ,SAAS,GAAG/J,CAAC,CAAE,MAAF,EAAU;AAC5B,iBAAO,WADqB;AAE5BgK,YAAI,EAAE,KAAK3B,OAAL,CAAaT,MAAb,CAAoBO,QAApB,CAA6B9E,OAA7B,CACL,IADK,EAEL,KAAKgF,OAAL,CAAaZ,OAFR;AAFsB,OAAV,CAAnB;AAQAsC,eAAS,CAAC5H,EAAV,CAAc,OAAd,EAAuB,KAAK8H,YAAL,CAAkBC,IAAlB,CAAwB,IAAxB,CAAvB,EAAwDzB,IAAxD;AAEAc,mBAAa,CAACR,MAAd,CAAsBgB,SAAtB;AACA,WAAKI,UAAL,GAAkBJ,SAAlB;AACA,K,CAED;;;;WACA,wBAAe;AAAA;;AACd,UAAM7G,IAAI,GAAG,IAAb,CADc,CAGd;;AACA,2BAAKiG,MAAL,8DAAahH,EAAb,CAAiB,OAAjB,EAA0B,UAAEsE,EAAF,EAAU;AACnCA,UAAE,CAAC2D,eAAH;AACAlH,YAAI,CAACmH,cAAL;AACA,OAHD;AAIA,WAAKhB,UAAL,CAAgBlH,EAAhB,CAAoB,OAApB,EAA6B,UAAEsE,EAAF,EAAU;AACtCA,UAAE,CAAC2D,eAAH;AACA,OAFD,EARc,CAYd;;AACA,6BAAKN,QAAL,kEAAe3H,EAAf,CAAmB,cAAnB,EAAmC,YAAM;AACxCe,YAAI,CAACoH,cAAL;AACA,OAFD,EAbc,CAiBd;;AACA,WAAKhB,OAAL,CAAanH,EAAb,CAAiB,QAAjB,EAA2B,QAA3B,EAAqC,YAAY;AAChD,YAAIoI,GAAG,GAAGvK,CAAC,CAAE,IAAF,CAAD,CAAUoC,OAAV,CAAmB,IAAnB,CAAV;AAAA,YACCoI,KAAK,GAAGD,GAAG,CAAC3D,IAAJ,CAAU,OAAV,CADT;AAAA,YAEC6D,QAAQ,GAAG,KAFZ;;AAIA,YACCF,GAAG,CAACG,QAAJ,CAAc,UAAd,KACA,CAAExH,IAAI,CAACyH,eAAL,CAAsBH,KAAtB,CAFH,EAGE;AACD,iBAAO,KAAP;AACA;;AAEDD,WAAG,CAACK,WAAJ,CAAiB,QAAjB;AACAH,gBAAQ,GAAGF,GAAG,CAACG,QAAJ,CAAc,QAAd,CAAX;;AAEAxH,YAAI,CAAC2H,iBAAL,CAAwBL,KAAxB,EAA+BC,QAA/B;AACA,OAhBD;AAiBA,WAAKnB,OAAL,CAAanH,EAAb,CAAiB,OAAjB,EAA0B,qBAA1B,EAAiD,UAAWsE,EAAX,EAAgB;AAChE,YAAI8D,GAAG,GAAGvK,CAAC,CAAE,IAAF,CAAD,CAAUoC,OAAV,CAAmB,IAAnB,CAAV;AAAA,YACCoI,KAAK,GAAGD,GAAG,CAAC3D,IAAJ,CAAU,OAAV,CADT;AAAA,YAEC6D,QAAQ,GAAG,KAFZ;AAIAhE,UAAE,CAACC,cAAH;;AAEA,YACC6D,GAAG,CAACG,QAAJ,CAAc,UAAd,KACA,CAAExH,IAAI,CAACyH,eAAL,CAAsBH,KAAtB,CAFH,EAGE;AACD,iBAAO,KAAP;AACA;;AAEDD,WAAG,CAACK,WAAJ,CAAiB,QAAjB;AACAH,gBAAQ,GAAGF,GAAG,CAACG,QAAJ,CAAc,QAAd,CAAX;;AAEA,YAAKD,QAAL,EAAgB;AACfF,aAAG,CAACO,QAAJ,GAAepI,WAAf,CAA4B,QAA5B;AACA;;AAEDQ,YAAI,CAAC2H,iBAAL,CAAwBL,KAAxB,EAA+BC,QAA/B;AACA,OAtBD;AAuBA,WAAKnB,OAAL,CAAanH,EAAb,CAAiB,OAAjB,EAA0B,WAA1B,EAAuC,UAAWsE,EAAX,EAAgB;AACtD,YAAMsE,KAAK,GAAG/K,CAAC,CAAE,IAAF,CAAD,CAAUgL,MAAV,GAAmBhG,IAAnB,CAAyB,QAAzB,CAAd;AAEAyB,UAAE,CAACC,cAAH;;AAEA,YACCqE,KAAK,CAAC7D,EAAN,CAAU,gBAAV,KACA6D,KAAK,CAAC7D,EAAN,CAAU,mBAAV,CAFD,EAGE;AACD6D,eAAK,CAACE,IAAN,CAAY,SAAZ,EAAuB,CAAEF,KAAK,CAACE,IAAN,CAAY,SAAZ,CAAzB;AACA;;AAEDF,aAAK,CAACG,MAAN;AACA,OAbD,EA1Dc,CAyEd;;AACA,WAAKjE,eAAL,CAAqB9E,EAArB,CAAyB,QAAzB,EAAmC,UAAEsE,EAAF,EAAM0E,cAAN,EAA0B;AAC5D,YAAKA,cAAL,EAAsB;AACrB;AACA;;AAEDjI,YAAI,CAACoG,OAAL,CAAatE,IAAb,CAAmB,IAAnB,EAA0B2B,IAA1B,CAAgC,YAAY;AAC3C,cAAM6D,KAAK,GAAGxK,CAAC,CAAE,IAAF,CAAD,CAAU4G,IAAV,CAAgB,OAAhB,CAAd;;AAEA,cAAK1D,IAAI,CAACyH,eAAL,CAAsBH,KAAtB,CAAL,EAAqC;AACpCtH,gBAAI,CAACkI,WAAL,CAAkBZ,KAAlB;AACA,WAFD,MAEO;AACNtH,gBAAI,CAACmI,aAAL,CAAoBb,KAApB;AACA;AACD,SARD;AAUAtH,YAAI,CAACoI,WAAL;AACA,OAhBD,EA1Ec,CA4Fd;;AACAtL,OAAC,CAAEQ,QAAF,CAAD,CAAc2B,EAAd,CAAkB,OAAlB,EAA2B,KAAKoJ,aAAL,CAAmBrB,IAAnB,CAAyB,IAAzB,CAA3B;AACA,K,CAED;;;;WACA,wBAAe;AAAA;;AACd,4BAAKf,MAAL,gEAAa1G,QAAb,CAAuB,MAAvB,EAAgCC,WAAhC,CAA6C,QAA7C;;AACA,WAAK8I,kBAAL;AACA,K,CAED;;;;WACA,yBAAgB;AAAA;;AACf,4BAAKrC,MAAL,gEAAazG,WAAb,CAA0B,MAA1B,EAAmCD,QAAnC,CAA6C,QAA7C;AACA,K,CAED;;;;WACA,gCAAuB;AACtB,UAAMS,IAAI,GAAG,IAAb;AAAA,UACCuI,SAAS,GAAGzL,CAAC,CAAEQ,QAAF,CAAD,CACVwE,IADU,CACJ,iBADI,EAEV0G,MAFU,CAEF,UAAW3H,CAAX,EAAc4H,MAAd,EAAuB;AAC/B,YAAMhG,GAAG,GAAG3F,CAAC,CAAE2L,MAAF,CAAb;AAEA,eACC,CAAC,CAAEhG,GAAG,CAACiB,IAAJ,CAAU,UAAV,CAAH,IACA,CAAEjB,GAAG,CAACuB,EAAJ,CAAQhE,IAAI,CAAC+D,eAAb,CAFH;AAIA,OATU,CADb;AAYAwE,eAAS,CAAC9E,IAAV,CAAgB,YAAY;AAC3B3G,SAAC,CAAE,IAAF,CAAD,CAAU4G,IAAV,CAAgB,UAAhB,EAA6B2E,aAA7B;AACA,OAFD;AAGA,K,CAED;;;;WACA,0BAAiB;AAAA;;AAChB,4BAAKpC,MAAL,gEAAayB,WAAb,CAA0B,MAA1B,EAAmCA,WAAnC,CAAgD,QAAhD;;AAEA,2BAAK,KAAKzB,MAAV,0CAAK,cAAauB,QAAb,CAAuB,MAAvB,CAAL,EAAuC;AACtC,aAAKc,kBAAL;AACA;AACD,K,CAED;;;;WACA,8BAAqB;AAAA;;AACpB,WAAKI,oBAAL;;AAEA,6BAAK,KAAK9B,QAAV,4CAAK,gBAAetH,MAApB,EAA6B;AAC5B,aAAKsH,QAAL,CAAc+B,GAAd,CAAmB,EAAnB;AACA;;AAED,WAAKvB,cAAL;AACA,K,CAED;;;;WACA,6BAAqBlK,MAArB,EAA6B0L,KAA7B,EAAqC;AAAA;;AACpC,UAAIC,gBAAgB,GAAG,EAAvB;AAAA,UACCC,QAAQ,GAAG,KAAKC,UAAL,EADZ;AAAA,UAECC,OAFD;AAIAA,aAAO,GAAG,IAAIC,OAAJ,CAAa,UAAEC,OAAF,EAAe;AACrC;AACAJ,gBAAQ,CAACrF,IAAT,CAAe,YAAY;AAC1B,cAAM0F,CAAC,GAAGrM,CAAC,CAAE,IAAF,CAAX;AAAA,cACCwK,KAAK,GAAG6B,CAAC,CAACR,GAAF,EADT;AAAA,cAECS,KAAK,GAAGD,CAAC,CAAC/L,IAAF,EAFT;AAAA,cAGCiM,KAAK,GAAG,IAAIjJ,MAAJ,CAAY,OAAOlD,MAAP,GAAgB,IAA5B,EAAkC,GAAlC,CAHT;AAAA,cAICoM,IAAI,GACH,CAAEpM,MAAF,IAAYmM,KAAK,CAACE,IAAN,CAAYjC,KAAZ,CAAZ,IAAmC+B,KAAK,CAACE,IAAN,CAAYH,KAAZ,CALrC;;AAOA,cAAKE,IAAL,EAAY;AACXT,4BAAgB,CAACW,IAAjB,CAAuB;AACtBlC,mBAAK,EAALA,KADsB;AAEtB8B,mBAAK,EAALA;AAFsB,aAAvB;AAIA;AACD,SAdD,EAFqC,CAkBrC;;AACA,YAAK,KAAI,CAACjE,OAAL,CAAaV,WAAlB,EAAgC;AAC/B;AACA,eAAI,CAACU,OAAL,CACEV,WADF,CACevH,MADf,EAEEuM,IAFF,CAEQ,UAAEC,iBAAF,EAAyB;AAC/B,gBAAKA,iBAAL,EAAyB;AACxB;AACAA,+BAAiB,GAAGA,iBAAiB,CAAClJ,MAAlB,CACnB,UAAEC,CAAF,EAAKC,CAAL,EAAQG,CAAR,EAAe;AACdJ,iBAAC,CAAC+I,IAAF,CAAQ;AAAEJ,uBAAK,EAAEvI,CAAT;AAAYyG,uBAAK,EAAE5G;AAAnB,iBAAR;AACA,uBAAOD,CAAP;AACA,eAJkB,EAKnB,EALmB,CAApB,CAFwB,CAUxB;;AACAoI,8BAAgB,GAAG/L,QAAA,CAClB+L,gBADkB,EAElBa,iBAFkB,CAAnB;AAIA;;AAEDR,mBAAO,CAAE,KAAI,CAACS,YAAL,CAAmBd,gBAAnB,EAAqCD,KAArC,CAAF,CAAP;AACA,WArBF;AAsBA,SAxBD,MAwBO;AACNM,iBAAO,CAAE,KAAI,CAACS,YAAL,CAAmBd,gBAAnB,EAAqCD,KAArC,CAAF,CAAP;AACA;AACD,OA9CS,CAAV;AAgDA,aAAOI,OAAP;AACA,K,CAED;;;;WACA,sBAAcrI,KAAd,EAAqBiI,KAArB,EAA6B;AAAA;;AAC5B,UAAIgB,OAAO,GAAG,EAAd;AAAA,UACCC,OAAO,GAAG,KADX,CAD4B,CAI5B;;AACAlJ,WAAK,CACH6H,MADF,CACU,UAAE9H,CAAF,EAAS;AACjB,YAAK,CAAC,CAAD,KAAOkJ,OAAO,CAAC3I,OAAR,CAAiBP,CAAC,CAAC4G,KAAnB,CAAZ,EAAyC;AACxCsC,iBAAO,CAACJ,IAAR,CAAc9I,CAAC,CAAC4G,KAAhB;AACA,iBAAO,IAAP;AACA;;AAED,eAAO,KAAP;AACA,OARF,EASEwC,IATF,CASQ,UAAErJ,CAAF,EAAKsJ,CAAL,EAAY;AAClB,YAAMvF,KAAK,GAAG,MAAI,CAACW,OAAL,CAAaX,KAA3B;AAAA,YACCwF,GAAG,GAAGxF,KAAK,KAAK,KAAV,GAAkB,CAAlB,GAAsB,CAAC,CAD9B;;AAGA,YAAK/D,CAAC,CAAC6G,KAAF,GAAUyC,CAAC,CAACzC,KAAjB,EAAyB;AACxB,iBAAO,CAAC,CAAD,GAAK0C,GAAZ;AACA,SAFD,MAEO,IAAKvJ,CAAC,CAAC6G,KAAF,GAAUyC,CAAC,CAACzC,KAAjB,EAAyB;AAC/B,iBAAO0C,GAAP;AACA;;AAED,eAAO,CAAP;AACA,OApBF,EAL4B,CA2B5B;;AACA,UAAKpB,KAAL,EAAa;AACZiB,eAAO,GAAGjB,KAAK,GAAGxJ,MAAM,CAACC,IAAP,CAAasB,KAAb,EAAqBrB,MAAvC;AACAqB,aAAK,GAAGA,KAAK,CAACsJ,KAAN,CAAa,CAAb,EAAgBrB,KAAhB,CAAR;AACA;;AAED,aAAO;AACNjI,aAAK,EAALA,KADM;AAENkJ,eAAO,EAAPA;AAFM,OAAP;AAIA,K,CAED;;;;WACA,uBAAevC,KAAf,EAAsB8B,KAAtB,EAA8B;AAC7B,UAAIc,MAAM,GAAG,KAAKzC,eAAL,CAAsBH,KAAtB,CAAb;AAAA,UACC6C,MAAM,GAAG,KAAKC,gBAAL,CAAuB9C,KAAvB,CADV;AAAA,UAEC+C,KAAK,GAAGvN,CAAC,CAAE,OAAF,EAAW;AACnB,sBAAcwK,KADK;AAEnB,iBAAO6C,MAAM,CAAC7K,MAAP,GAAgB6K,MAAM,CAACG,IAAP,CAAa,OAAb,CAAhB,GAAyC;AAF7B,OAAX,CAFV;AAAA,UAMCC,OAND;;AAQA,UAAKJ,MAAM,CAAC7K,MAAZ,EAAqB;AACpB,YAAMkL,QAAQ,GAAGL,MAAM,CAACzG,IAAP,CAAa,UAAb,CAAjB;AAAA,YACC+G,KAAK,GAAGN,MAAM,CAACzG,IAAP,CAAa,OAAb,CADT;AAGA0F,aAAK,GAAGoB,QAAQ,GAAGA,QAAH,GAAcpB,KAA9B;;AAEA,YAAK,CAAC,CAAEqB,KAAR,EAAgB;AACfrB,eAAK,IAAIqB,KAAT;AACA;AACD;;AAEDF,aAAO,GAAGzN,CAAC,CAAE,MAAF,EAAU;AACpB4N,YAAI,EAAEP,MAAM,CAAC7K,MAAP,GAAgB6K,MAAM,CAACzG,IAAP,CAAa,YAAb,CAAhB,GAA8C,GADhC;AAEpBtG,YAAI,EAAEgM,KAFc;AAGpB,sBAAce,MAAM,CAAC7K,MAAP,GAAgB6K,MAAM,CAACzG,IAAP,CAAa,OAAb,CAAhB,GAAyC;AAHnC,OAAV,CAAX;;AAMA,UAAK,KAAKK,eAAL,CAAqBgE,IAArB,CAA2B,UAA3B,CAAL,EAA+C;AAC9C,YAAM4C,SAAS,GAAG7N,CAAC,CAAE,UAAF,EAAc;AAC/B2J,cAAI,EAAE,UADyB;AAE/Ba,eAAK,EAALA;AAF+B,SAAd,CAAnB;AAAA,YAICsD,MAAM,GAAG9N,CAAC,CAAE,SAAF,CAJX;AAMA6N,iBAAS,CAAC5C,IAAV,CAAgB,SAAhB,EAA2BmC,MAA3B;AACAU,cAAM,CAACC,OAAP,CAAgBF,SAAhB,EAA4B9E,MAA5B,CAAoC0E,OAApC;AACAF,aAAK,CAACxE,MAAN,CAAc+E,MAAd,EAAuBrL,QAAvB,CAAiC,UAAjC;AACA,OAVD,MAUO;AACN8K,aAAK,CAACxE,MAAN,CAAc0E,OAAd;AACA;;AAEDL,YAAM,GAAGG,KAAK,CAAC9K,QAAN,CAAgB,QAAhB,CAAH,GAAgC8K,KAAK,CAAC7K,WAAN,CAAmB,QAAnB,CAAtC;AAEA,aAAO6K,KAAP;AACA,K,CAED;;;;WACA,wBAAgBS,IAAhB,EAAuB;AAAA;AAAA;;AACtB,UAAI5N,MAAM,GAAG,wBAAK0J,QAAL,4DAAetH,MAAf,GAAwB,KAAKsH,QAAL,CAAc+B,GAAd,EAAxB,GAA8C,EAA3D;AAAA,UACCpE,OAAO,GAAG,KAAKY,OAAL,CAAab,QAAb,GAAwB,KAAKa,OAAL,CAAaZ,OAArC,GAA+C,CAD1D;AAAA,UAECqE,KAFD;AAIAkC,UAAI,GAAGA,IAAI,GAAGC,QAAQ,CAAED,IAAF,CAAX,GAAsB,CAAjC;AACAlC,WAAK,GAAGkC,IAAI,GAAGvG,OAAf;AAEA,WAAKyG,mBAAL,CAA0B9N,MAA1B,EAAkC0L,KAAlC,EAA0Ca,IAA1C,CAAgD,UAAEwB,SAAF,EAAiB;AAChE,YAAIC,aAAa,GAAGD,SAAS,CAACtK,KAA9B;AAAA,YACCA,KAAK,GAAG,EADT;AAAA,YAECkJ,OAAO,GAAG,KAFX,CADgE,CAKhE;;AACA,cAAI,CAACsB,WAAL;;AACA,cAAI,CAACC,aAAL;;AAEA,YAAK,CAAEF,aAAa,CAAC5L,MAArB,EAA8B;AAC7BqB,eAAK,CAAC6I,IAAN,CACC1M,CAAC,CAAE,OAAF,EAAW;AAAEgK,gBAAI,EAAE,MAAI,CAAC3B,OAAL,CAAaT,MAAb,CAAoBK;AAA5B,WAAX,CADF;AAIA,gBAAI,CAACsG,WAAL,GAAmB,CAAnB;AACA,SAND,MAMO;AAAA,qDACWH,aADX;AAAA;;AAAA;AACN,gEAAiC;AAAA,kBAArBxK,CAAqB;;AAChC,kBAAKA,CAAC,CAAC4G,KAAF,KAAY,EAAjB,EAAsB;AACrB3G,qBAAK,CAAC2K,OAAN,CAAe,MAAI,CAACC,aAAL,CAAoB7K,CAAC,CAAC4G,KAAtB,EAA6B5G,CAAC,CAAC0I,KAA/B,CAAf;AACA,eAFD,MAEO;AACNzI,qBAAK,CAAC6I,IAAN,CAAY,MAAI,CAAC+B,aAAL,CAAoB7K,CAAC,CAAC4G,KAAtB,EAA6B5G,CAAC,CAAC0I,KAA/B,CAAZ;AACA;AACD;AAPK;AAAA;AAAA;AAAA;AAAA;;AASN,gBAAI,CAACiC,WAAL,GAAmBP,IAAnB;AACAjB,iBAAO,GAAGoB,SAAS,CAACpB,OAApB;AACA;;AAED,cAAI,CAACzD,OAAL,CAAaP,MAAb,CAAqBlF,KAArB;;AAEA7D,SAAC,CAAEQ,QAAF,CAAD,CAAc0B,OAAd,CAAuB,4BAAvB;;AAEA,YAAK6K,OAAL,EAAe;AACd,gBAAI,CAAC2B,aAAL;AACA;AACD,OAnCD;AAoCA,K,CAED;;;;WACA,wBAAe;AACd,UAAMV,IAAI,GAAG,KAAKO,WAAL,GAAmB,CAAhC;;AAEA,WAAKjE,cAAL,CAAqB0D,IAArB;AACA,K,CAED;;;;WACA,qBAAaxD,KAAb,EAAqB;AACpB,aAAO,KAAKK,iBAAL,CAAwBL,KAAxB,EAA+B,IAA/B,CAAP;AACA,K,CAED;;;;WACA,uBAAeA,KAAf,EAAuB;AACtB,aAAO,KAAKK,iBAAL,CAAwBL,KAAxB,EAA+B,KAA/B,CAAP;AACA,K,CAED;;;;WACA,2BAAmBA,KAAnB,EAA0BmE,MAA1B,EAAmC;AAClC,UAAMC,OAAO,GAAG,KAAK3H,eAAL,CAAqBjC,IAArB,0BACGwF,KADH,SAAhB;;AAIA,UAAKoE,OAAO,CAACpM,MAAb,EAAsB;AACrBoM,eAAO,CAAC3D,IAAR,CAAc,UAAd,EAA0B0D,MAA1B;AAEA,aAAKpD,aAAL;AACA,aAAKD,WAAL;AAEA,aAAKrE,eAAL,CAAqB/E,OAArB,CAA8B,QAA9B,EAAwC,CAAE,IAAF,CAAxC;AAEA,eAAO,IAAP;AACA;;AACD,aAAO,KAAP;AACA,K,CAED;;;;WACA,uBAAc;AACb,WAAKoH,OAAL,CAAahJ,IAAb,CAAmB,EAAnB;AACA,K,CAED;;;;WACA,yBAAgB;AACf,WAAK6J,UAAL,CAAgBqC,IAAhB;AACA,K,CAED;;;;WACA,yBAAgB;AACf,WAAKrC,UAAL,CAAgB1B,IAAhB;AACA,K,CAED;;;;WACA,oBAAW;AACV,aAAO,KAAKoG,iBAAL,KACJ,KAAKC,iBAAL,GAAyBC,IAAzB,CAA+B,IAA/B,CADI,GAEJ,KAAK1G,OAAL,CAAaT,MAAb,CAAoBC,UAFvB;AAGA,K,CAED;;;;WACA,uBAAc;AAAA;;AACb,UAAMyE,KAAK,GAAG,KAAK1D,QAAL,EAAd;AAEA,4BAAKQ,OAAL,gEAAc9I,IAAd,CAAoBgM,KAApB;AACA,K,CAED;;;;WACA,sBAAa;AACZ,aAAO,KAAKrF,eAAL,CAAqBjC,IAArB,CAA2B,QAA3B,CAAP;AACA,K,CAED;;;;WACA,6BAAoB;AACnB,aAAO,KAAKgK,kBAAL,GAA0BxM,MAAjC;AACA,K,CAED;;;;WACA,yBAAiBgI,KAAjB,EAAyB;AACxB,UAAMyE,KAAK,GAAG,KAAKC,iBAAL,GAAyB/K,OAAzB,CAAkCqG,KAAK,CAAC2E,QAAN,EAAlC,CAAd;AAEA,aAAO,CAAC,CAAD,KAAOF,KAAd;AACA,K,CAED;;;;WACA,8BAAqB;AACpB,aAAO,KAAKhI,eAAL,CAAqBjC,IAArB,CAA2B,QAA3B,EAAsC0G,MAAtC,CAA8C,WAA9C,CAAP;AACA,K,CAED;;;;WACA,0BAAkBlB,KAAlB,EAA0B;AACzB,aAAO,KAAKvD,eAAL,CAAqBjC,IAArB,0BAA6CwF,KAA7C,SAAP;AACA,K,CAED;;;;WACA,6BAAoB;AACnB,UAAM5C,MAAM,GAAG,EAAf;AAEA,WAAKoH,kBAAL,GAA0BrI,IAA1B,CAAgC,YAAY;AAC3C,YAAIiI,OAAO,GAAG5O,CAAC,CAAE,IAAF,CAAf;AAAA,YACC0N,QAAQ,GAAGkB,OAAO,CAAChI,IAAR,CAAc,UAAd,CADZ;AAGA8G,gBAAQ,GAAGA,QAAQ,GAChBA,QADgB,GAEhBkB,OAAO,CAACtO,IAAR,GAAe+C,OAAf,CAAwB,YAAxB,EAAsC,EAAtC,CAFH;AAIAuE,cAAM,CAAC8E,IAAP,CAAagB,QAAb;AACA,OATD;AAWA,aAAO9F,MAAP;AACA,K,CAED;;;;WACA,6BAAoB;AACnB,UAAMwH,MAAM,GAAG,EAAf;AAEA,WAAKJ,kBAAL,GAA0BrI,IAA1B,CAAgC,YAAY;AAC3CyI,cAAM,CAAC1C,IAAP,CAAa1M,CAAC,CAAE,IAAF,CAAD,CAAU6L,GAAV,EAAb;AACA,OAFD;AAIA,aAAOuD,MAAP;AACA;;;WAED,mBAAU,CACT;AACA;;;;;;;;AC1lBW;AAEb;;;;;;;;;;;;AAEA;AACA;;IAEqBC,gB;AACpB;AAIA;AAIA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAIA;AACA,4BAAa9I,EAAb,EAAkB;AAAA;;AAAA,oDA9BT,KA8BS;;AAAA,qDA7BR,KA6BQ;;AAAA,oDA1BT,KA0BS;;AAAA,qDAzBR,KAyBQ;;AAAA,sDAtBP,KAsBO;;AAAA,4DAnBD,KAmBC;;AAAA,2DAhBF,EAgBE;;AAAA,2DAbF,KAaE;;AAAA,sDAVP,KAUO;;AAAA,2DAPF,KAOE;;AAAA,6DAJA,IAIA;;AAAA,mDAHV,KAGU;;AACjB;AACA,SAAKpF,MAAL,GAAc,MAAMoF,EAAE,CAACiH,IAAH,CAAS,IAAT,CAApB;AACA,SAAK1I,OAAL,GAAeyB,EAAf,CAHiB,CAKjB;;AACA,SAAKrF,MAAL,GAAc,KAAK4D,OAAL,CAAa8B,IAAb,CAAmB,QAAnB,CAAd;AACA,SAAKvF,OAAL,GAAe,KAAKH,MAAL,GAAclB,CAAC,CAAE,KAAKkB,MAAP,CAAf,GAAiC,KAAhD;;AAEA,SAAKoO,cAAL;;AACA,SAAKC,iBAAL;;AACA,SAAKC,eAAL;;AACA,SAAKC,YAAL;;AACA,SAAKjH,YAAL;;AAEA,SAAK1D,OAAL,CACE8B,IADF,CACQ,QADR,EACkB,IADlB,EAEEnE,QAFF,CAEY,UAFZ,EAGEP,OAHF,CAGW,8BAHX,EAG2C,CAAE,IAAF,CAH3C;AAIA,G,CAED;;;;;WACA,wBAAe;AACd,UAAMgB,IAAI,GAAG,IAAb;AAEA,WAAKwM,UAAL,GAAkB/I,IAAlB,CAAwB,YAAY;AACnC,YAAMgJ,OAAO,GAAG3P,CAAC,CAAE,IAAF,CAAjB;;AAEAkD,YAAI,CAAC0M,WAAL,CAAkBD,OAAlB;AACA,OAJD;AAMA,WAAKE,wBAAL;AACA,K,CAED;;;;WACA,6BAAoB;AAAA;;AACnB,WAAKC,cAAL,GAAsB,KAAKhL,OAAL,CAAaE,IAAb,CAAmB,gBAAnB,CAAtB;;AAEA,UAAK,CAAE,KAAK8K,cAAL,CAAoBtN,MAA3B,EAAoC;AACnC;AACA,OALkB,CAOnB;;;AACA,WAAKsN,cAAL,CACE3N,EADF,CACM,OADN,EACe,UAAEsE,EAAF,EAAU;AACvBA,UAAE,CAACC,cAAH;;AACA,aAAI,CAACgF,MAAL;AACA,OAJF,EAKEjD,IALF;AAMA,K,CAED;;;;WACA,wBAAe;AACd,WAAK3D,OAAL,CAAaE,IAAb,CAAmB,MAAnB,EAA4B7C,EAA5B,CAAgC,QAAhC,EAA0C,UAAEsE,EAAF,EAAU;AACnDA,UAAE,CAACC,cAAH;AACA,OAFD;AAGA,K,CAED;;;;WACA,2BAAkB;AAAA;;AACjB,UAAK,CAAE5F,oBAAoB,CAACiP,eAA5B,EAA8C;AAC7C;AACA;;AAED,UAAMC,KAAK,GAAGtO,MAAM,CAACuO,UAAP,uBACGnP,oBAAoB,CAACoP,kBADxB,SAAd;AAIAlQ,OAAC,CAAE0B,MAAF,CAAD,CACES,EADF,CACM,QADN,EACgB,YAAM;AACpB,YAAMgO,QAAQ,GAAG,CAAC,CAAEH,KAAK,CAAC9L,OAA1B;;AAEA,YAAKiM,QAAQ,KAAK,MAAI,CAACA,QAAvB,EAAkC;AACjC,gBAAI,CAACA,QAAL,GAAgBA,QAAhB;;AACA,gBAAI,CAACC,kBAAL;AACA;AACD,OARF,EASEC,MATF;AAUA,K,CAED;;;;WACA,qBAAaV,OAAb,EAAuB;AAAA;;AACtB,UAAMzM,IAAI,GAAG,IAAb;AAAA,UACCoN,YAAY,GAAG,SAAfA,YAAe,CAAW7J,EAAX,EAAgB;AAC9B,YAAM4F,CAAC,GAAGrM,CAAC,CAAE,IAAF,CAAX;AAAA,YACCuQ,cAAc,GAAGlE,CAAC,CAACjK,OAAF,CAAW,mBAAX,CADlB;AAAA,YAECoO,QAAQ,GAAGD,cAAc,CAAC/N,MAAf,GACR,UAAU+N,cAAc,CAAC3J,IAAf,CAAqB,UAArB,CADF,GAER,KAJJ;AAAA,YAKC2G,KAAK,GAAGlB,CAAC,CAACjK,OAAF,CAAW,cAAX,CALT;AAAA,YAMCqO,MAAM,GAAGlD,KAAK,CAAC/K,MAAN,GACN+N,cAAc,CAACvL,IAAf,CAAqB,cAArB,EAAsC0L,GAAtC,CAA2CnD,KAA3C,CADM,GAEN,EARJ;;AAUA,YAAKA,KAAK,CAACrG,EAAN,CAAU,WAAV,KAA2B,CAAEqG,KAAK,CAACrG,EAAN,CAAU,SAAV,CAAlC,EAA0D;AACzDT,YAAE,CAACC,cAAH;AACA,iBAAO,KAAP;AACA;;AAEDD,UAAE,CAACC,cAAH;AAEA+J,cAAM,CAACjO,MAAP,IACC,CAAEgO,QADH,IAECC,MAAM,CACJ/N,WADF,CACe,QADf,EAEEiO,QAFF,CAEY,OAFZ,EAGE3L,IAHF,CAGQ,QAHR,EAIEiG,IAJF,CAIQ,SAJR,EAImB,KAJnB,EAKED,MALF,CAKU,UALV,EAMEtI,WANF,CAMe,SANf,CAFD;AASA6K,aAAK,CAAC/K,MAAN,IAAgB+K,KAAK,CAAC3C,WAAN,CAAmB,QAAnB,CAAhB,CA3B8B,CA6B9B;;AACA1H,YAAI,CAAC0N,aAAL,GAAqB,KAArB;AAEA1N,YAAI,CAAC2N,WAAL,CAAkBlB,OAAlB;AACAzM,YAAI,CAAC4N,0BAAL;AACA5N,YAAI,CAAC6N,sBAAL,CAA6BR,cAA7B;AACA,OApCF,CADsB,CAuCtB;;;AACAZ,aAAO,CACL3K,IADF,CACQ,cADR,EAEE0L,GAFF,CAEO,WAFP,EAGEA,GAHF,CAGO,QAHP,EAIEvO,EAJF,CAIM,OAJN,EAIe,GAJf,EAIoB,UAAWsE,EAAX,EAAgB;AAClC,YAAM4F,CAAC,GAAGrM,CAAC,CAAE,IAAF,CAAX;AAAA,YACCuN,KAAK,GAAGlB,CAAC,CAACjK,OAAF,CAAW,cAAX,CADT;;AAGA,YAAK,CAAEpC,CAAC,CAAEyG,EAAF,aAAEA,EAAF,uBAAEA,EAAE,CAAEuK,cAAN,CAAD,CAAwB9J,EAAxB,CAA4BqG,KAA5B,CAAP,EAA6C;AAC5C,iBAAO,KAAP;AACA;;AAED+C,oBAAY,CAACW,IAAb,CAAmB,IAAnB,EAAyBxK,EAAzB;AACA,OAbF,EAxCsB,CAuDtB;;AACAkJ,aAAO,CAAC3K,IAAR,CAAc,QAAd,EAAyB7C,EAAzB,CAA6B,QAA7B,EAAuC,UAAWsE,EAAX,EAAgB;AACtD,YAAM4F,CAAC,GAAGrM,CAAC,CAAE,IAAF,CAAX;AAAA,YACCuN,KAAK,GAAGlB,CAAC,CAACjK,OAAF,CAAW,cAAX,CADT;;AAGA,YAAKmL,KAAK,CAACrG,EAAN,CAAU,WAAV,KAA2B,CAAEqG,KAAK,CAACrG,EAAN,CAAU,SAAV,CAAlC,EAA0D;AACzDmF,WAAC,CAACpB,IAAF,CAAQ,SAAR,EAAmB,KAAnB;AACA,iBAAO,KAAP;AACA;;AAEDqF,oBAAY,CAACW,IAAb,CAAmB,IAAnB,EAAyBxK,EAAzB;AACA,OAVD,EAxDsB,CAoEtB;;AACAkJ,aAAO,CAAC3K,IAAR,CAAc,WAAd,EAA4B7C,EAA5B,CAAgC,OAAhC,EAAyC,UAAWsE,EAAX,EAAgB;AACxD,YAAM4F,CAAC,GAAGrM,CAAC,CAAE,IAAF,CAAX;AAAA,YACCuN,KAAK,GAAGlB,CAAC,CAACjK,OAAF,CAAW,cAAX,CADT;AAGAqE,UAAE,CAACC,cAAH;;AAEA,YAAK6G,KAAK,CAACrG,EAAN,CAAU,WAAV,KAA2B,CAAEqG,KAAK,CAACrG,EAAN,CAAU,SAAV,CAAlC,EAA0D;AACzD,iBAAO,KAAP;AACA;;AAED,YAAMgK,MAAM,GAAG7E,CAAC,CAACrB,MAAF,GAAWhG,IAAX,CAAiB,QAAjB,CAAf;;AAEA,YACCkM,MAAM,CAAChK,EAAP,CAAW,gBAAX,KACAgK,MAAM,CAAChK,EAAP,CAAW,mBAAX,CAFD,EAGE;AACDgK,gBAAM,CAACjG,IAAP,CAAa,SAAb,EAAwB,CAAEiG,MAAM,CAACjG,IAAP,CAAa,SAAb,CAA1B;AACA;;AAEDiG,cAAM,CAAChG,MAAP;AACA,OApBD,EArEsB,CA2FtB;;AACA,WAAKiG,YAAL,CAAmBxB,OAAnB,EA5FsB,CA8FtB;;;AACA,WAAKyB,gBAAL,CAAuBzB,OAAvB,EA/FsB,CAiGtB;;;AACA,WAAK0B,aAAL,CAAoB1B,OAApB,EAlGsB,CAoGtB;;;AACA,WAAK2B,gBAAL,CAAuB3B,OAAvB,EArGsB,CAuGtB;;;AACA,WAAK4B,oBAAL,CAA2B5B,OAA3B,EAxGsB,CA0GtB;;AACA,2BAAK,KAAK7K,OAAV,0CAAK,cAAc4F,QAAd,CAAwB,cAAxB,CAAL,EAAgD;AAC/C,aAAK8G,gBAAL,CAAuB7B,OAAvB;AACA;AACD,K,CAED;;;;WACA,sBAAcA,OAAd,EAAuB8B,QAAvB,EAAkC;AACjC9B,aAAO,CAAC3K,IAAR,CAAc,cAAd,EAA+B2B,IAA/B,CAAqC,YAAY;AAChD,YAAM0F,CAAC,GAAGrM,CAAC,CAAE,IAAF,CAAX;;AAEA,YAAKqM,CAAC,CAAC3B,QAAF,CAAY,eAAZ,KAAiC,CAAE2B,CAAC,CAACzF,IAAF,CAAQ,OAAR,CAAxC,EAA4D;AAC3D;AACA;;AAEDyF,SAAC,CAAClK,EAAF,CAAM,YAAN,EAAoB,YAAY;AAC/B,cAAIuP,EAAE,GAAG1R,CAAC,CAAE,IAAF,CAAV;AAAA,cACC2R,OAAO,GAAG,IADX;AAAA,cAECC,YAAY,GAAGF,EAAE,CAACG,UAAH,EAFhB;AAAA,cAGCC,IAAI,GAAG,CAHR;AAAA,cAICC,KAAK,GAAG,CAJT;;AAMA,cACC,CAAEN,QAAF,IACE,UAAUA,QAAV,IAAsB,YAAYA,QAFrC,EAGE;AACD,gBAAMO,SAAS,GAAGN,EAAE,CAACtP,OAAH,CAAY,cAAZ,CAAlB;AAEAqP,oBAAQ,GACPO,SAAS,CAACtH,QAAV,CAAoB,OAApB,KACAsH,SAAS,CAACtH,QAAV,CAAoB,OAApB,CADA,GAEG,KAFH,GAGG,OAJJ;AAKA;;AAEDiH,iBAAO,GAAG3R,CAAC,CAAE,QAAF,EAAY;AACtB,qBAAO,mBADe;AAEtBM,gBAAI,EAAEoR,EAAE,CAAC9K,IAAH,CAAS,OAAT;AAFgB,WAAZ,CAAX;AAKA8K,YAAE,CAAC3I,MAAH,CAAW4I,OAAX;AAEAI,eAAK,GAAGJ,OAAO,CAACE,UAAR,KAAuB,CAA/B;AACAF,iBAAO,CAACE,UAAR,CAAoBE,KAApB;;AAEA,cAAK,UAAUN,QAAf,EAA0B;AACzBK,gBAAI,GAAG,CAAEF,YAAY,GAAGG,KAAjB,IAA2B,CAAlC;AACA,WAFD,MAEO;AACND,gBAAI,GAAGF,YAAY,GAAG,EAAtB;AACA;;AAEDD,iBAAO,CAACM,GAAR,CAAa;AAAEH,gBAAI,EAAEA,IAAI,CAACI,OAAL,CAAc,CAAd,IAAoB;AAA5B,WAAb,EAAkDC,MAAlD,CAA0D,GAA1D;AAEAT,YAAE,CAACjP,QAAH,CAAa,cAAb;AACA,SAvCD,EAuCIN,EAvCJ,CAuCQ,YAvCR,EAuCsB,YAAY;AACjC,cAAMuP,EAAE,GAAG1R,CAAC,CAAE,IAAF,CAAZ;AAEA0R,YAAE,CAAC1M,IAAH,CAAS,oBAAT,EAAgCoN,OAAhC,CAAyC,GAAzC,EAA8C,YAAY;AACzDV,cAAE,CAAChP,WAAH,CAAgB,cAAhB,EACEsC,IADF,CACQ,oBADR,EAEEqN,MAFF;AAGA,WAJD;AAKA,SA/CD;AAiDAhG,SAAC,CAAC5J,QAAF,CAAY,eAAZ;AACA,OAzDD;AA0DA,K,CAED;;;;WACA,uBAAekN,OAAf,EAAyB;AACxB,UAAM2C,SAAS,GAAG3C,OAAO,CAAC3K,IAAR,CAAc,wBAAd,CAAlB;;AAEA,UAAK,CAAEsN,SAAS,CAAC9P,MAAjB,EAA0B;AACzB;AACA;;AAED,UACC8P,SAAS,CAAC5H,QAAV,CAAoB,2BAApB,KACA,gBAAgB,OAAO1K,cAFxB,EAGE;AACDsS,iBAAS,CAACC,SAAV,CAAqB,SAArB;AACA;;AAED,WAAKC,mBAAL,CAA0BF,SAA1B,EAAqC;AACpC9K,gBAAQ,EAAE,IAD0B;AAEpCC,eAAO,EAAE3G,oBAAoB,CAAC2R;AAFM,OAArC;AAIA,K,CAED;;;;WACA,6BAAqBH,SAArB,EAAgCtL,IAAhC,EAAuC;AACtC,aAAO,IAAID,kBAAJ,CAAwBuL,SAAxB,EAAmCtL,IAAnC,CAAP;AACA,K,CAED;;;;WACA,0BAAkB2I,OAAlB,EAA4B;AAAA;;AAC3B,UAAK,CAAEA,OAAO,CAACjF,QAAR,CAAkB,qBAAlB,CAAP,EAAmD;AAClD;AACA;;AAED,UAAMxH,IAAI,GAAG,IAAb;AAAA,UACCsG,UAAU,GAAGmG,OAAO,CAAC3K,IAAR,CAAc,eAAd,CADd;AAAA,UAEC0N,SAAS,GAAGlJ,UAAU,CAACxE,IAAX,CAAiB,mBAAjB,CAFb;AAAA,UAGC2N,SAAS,GAAGnJ,UAAU,CAACxE,IAAX,CAAiB,mBAAjB,CAHb;AAAA,UAIC4N,GAAG,GAAGC,UAAU,CAAErJ,UAAU,CAAC5C,IAAX,CAAiB,KAAjB,CAAF,CAJjB;AAAA,UAKCkM,GAAG,GAAGD,UAAU,CAAErJ,UAAU,CAAC5C,IAAX,CAAiB,KAAjB,CAAF,CALjB;AAAA,UAMCmM,UAAU,GAAGF,UAAU,CAAEH,SAAS,CAAC7G,GAAV,EAAF,CANxB;AAAA,UAOCmH,UAAU,GAAGH,UAAU,CAAEF,SAAS,CAAC9G,GAAV,EAAF,CAPxB;AAAA,UAQCoH,IAAI,GAAGJ,UAAU,CAAErJ,UAAU,CAAC5C,IAAX,CAAiB,MAAjB,CAAF,CARlB;AAAA,UASCsM,kBAAkB,GAAG,SAArBA,kBAAqB,GAAY;AAChC,YAAKhQ,IAAI,CAACiQ,aAAV,EAA0B;AACzBC,sBAAY,CAAElQ,IAAI,CAACiQ,aAAP,CAAZ;AACA;;AAEDjQ,YAAI,CAACiQ,aAAL,GAAqBE,UAAU,CAAE,YAAM;AACtCnQ,cAAI,CAAC2N,WAAL,CAAkBlB,OAAlB;AACA,SAF8B,EAE5B,GAF4B,CAA/B;AAGA,OAjBF;;AAmBAA,aAAO,CAAC3K,IAAR,CAAc,kBAAd,EAAmCsO,cAAnC,CAAmD;AAClDC,YAAI,EAAE,OAD4C;AAElD5J,YAAI,EAAE,QAF4C;AAGlDiJ,WAAG,EAAHA,GAHkD;AAIlDE,WAAG,EAAHA,GAJkD;AAKlDG,YAAI,EAAJA,IALkD;AAMlDO,YAAI,EAAET,UAN4C;AAOlDU,UAAE,EAAET,UAP8C;AAQlDU,oBAAY,EAAET,IARoC;AASlDU,wBAAgB,EAAE,KATgC;AAUlDC,gBAAQ,EAAE,kBAAEhQ,CAAF;AAAA,iBAAS,MAAI,CAACiQ,WAAL,CAAkBjQ,CAAlB,CAAT;AAAA,SAVwC;AAWlDkQ,gBAAQ,EAAE,kBAAElN,IAAF,EAAY;AACrB8L,mBAAS,CAAC7G,GAAV,CAAejF,IAAI,CAAC4M,IAApB;AACAb,mBAAS,CAAC9G,GAAV,CAAejF,IAAI,CAAC6M,EAApB;AACA,SAdiD;AAelDM,gBAAQ,EAAEb;AAfwC,OAAnD;AAkBAR,eAAS,CACPsB,GADF,CACOrB,SADP,EAEEsB,GAFF,CAEO,QAFP,EAGE9R,EAHF,CAGM,OAHN,EAGe,YAAM;AACnB,YAAK,CAAEuQ,SAAS,CAAC7G,GAAV,EAAF,IAAqB,CAAE8G,SAAS,CAAC9G,GAAV,EAA5B,EAA8C;AAC7C;AACA;;AAEDqH,0BAAkB;AAClB,OATF;AAUA,K,CAED;;;;WACA,0BAAkBvD,OAAlB,EAA4B;AAC3B,WAAKuE,qBAAL,CAA4BvE,OAA5B;;AACA,WAAKwE,yBAAL,CAAgCxE,OAAhC;AACA,K,CAED;;;;WACA,+BAAuBA,OAAvB,EAAiC;AAChC,UAAMyE,MAAM,GAAGzE,OAAO,CAAC3K,IAAR,CAAc,cAAd,CAAf;;AAEA,UAAK,CAAEoP,MAAM,CAAC5R,MAAd,EAAuB;AACtB;AACA;;AAED,WAAK6R,WAAL,CAAkBD,MAAlB,EAA0BA,MAA1B,EAAkCzE,OAAO,CAAC3K,IAAR,CAAc,iBAAd,CAAlC;AACA,K,CAED;;;;WACA,mCAA2B2K,OAA3B,EAAqC;AACpC,UAAMc,MAAM,GAAGd,OAAO,CAAC3K,IAAR,CAAc,wBAAd,CAAf;;AAEA,UAAK,CAAEyL,MAAM,CAACjO,MAAd,EAAuB;AACtB;AACA,OALmC,CAOpC;;;AACA,UAAMU,IAAI,GAAG,IAAb;AAAA,UACCkK,MAAM,GAAGuC,OAAO,CAAC3K,IAAR,CAAc,SAAd,CADV;;AAGA,UAAKoI,MAAM,CAAC5K,MAAZ,EAAqB;AACpB4K,cAAM,CACJkH,OADF,CACW,wBADX,EAEE5R,WAFF,CAEe,QAFf,EAGED,QAHF,CAGY,QAHZ;;AAKA,YACC2K,MAAM,CAAC1C,QAAP,CAAiB,uBAAjB,KACA5J,oBAAoB,CAACyT,qBAFtB,EAGE;AACDnH,gBAAM,CAAC1K,WAAP,CAAoB,QAApB,EAA+BD,QAA/B,CAAyC,QAAzC;AACA;AACD;;AAEDgO,YAAM,CAAC9J,IAAP,CAAa,YAAY;AACxB,YAAM6N,EAAE,GAAGxU,CAAC,CAAE,IAAF,CAAZ;AAAA,YACCyU,OAAO,GAAGzU,CAAC,CAAE,SAAF,EAAa;AACvB,mBAAO;AADgB,SAAb,CADZ;AAKAyU,eAAO,CAACC,QAAR,CAAkBF,EAAlB;;AAEAtR,YAAI,CAACmR,WAAL,CAAkBI,OAAlB,EAA2BD,EAA3B,EAA+BA,EAAE,CAAC7D,QAAH,CAAa,iBAAb,CAA/B;AACA,OATD;AAUA,K,CAED;;;;WACA,qBAAa8D,OAAb,EAAsBjL,UAAtB,EAAkCnI,OAAlC,EAA4C;AAC3C,UAAKmI,UAAU,CAACkB,QAAX,CAAqB,QAArB,CAAL,EAAuC;AACtCrJ,eAAO,CAACoH,IAAR;AACA;;AAEDgM,aAAO,CAACR,GAAR,CAAa,OAAb,EAAuB9R,EAAvB,CAA2B,OAA3B,EAAoC,UAAEsE,EAAF,EAAU;AAC7CA,UAAE,CAAC2D,eAAH;AACA3D,UAAE,CAACC,cAAH;AAEArF,eAAO,CAACsT,WAAR,CAAqB,GAArB,EAA0B,YAAM;AAC/BnL,oBAAU,CAACoB,WAAX,CAAwB,QAAxB,EAAmCA,WAAnC,CAAgD,QAAhD;AACA,SAFD;AAGA,OAPD;AAQA,K,CAED;;;;WACA,0BAAkB+E,OAAlB,EAA4B;AAC3BA,aAAO,CAAC3K,IAAR,CAAc,QAAd,EAAyB2B,IAAzB,CAA+B,YAAY;AAC1C,YAAIoE,KAAK,GAAG/K,CAAC,CAAE,IAAF,CAAb;AAAA,YACC2J,IAAI,GAAGoB,KAAK,CAACyC,IAAN,CAAY,MAAZ,CADR;AAAA,YAECoH,cAAc,aAAOjL,IAAP,WAFf;AAAA,YAGCqI,SAHD;;AAKA,YAAK,eAAerI,IAAf,IAAuB,YAAYA,IAAxC,EAA+C;AAC9C;AACA;;AAED,YAAKoB,KAAK,CAAC3I,OAAN,YAAoBwS,cAApB,GAAwCpS,MAA7C,EAAsD;AACrD;AACA;;AAED,YAAKuI,KAAK,CAAC7D,EAAN,CAAU,UAAV,CAAL,EAA8B;AAC7B0N,wBAAc,IAAI,UAAlB;AACA;;AAED5C,iBAAS,GAAGhS,CAAC,CAAE,SAAF,EAAa;AACzB,mBAAO4U;AADkB,SAAb,CAAb;AAIA7J,aAAK,CAAC8J,IAAN,CAAY7C,SAAZ,EAAwB7P,EAAxB,CAA4B,QAA5B,EAAsC,YAAY;AACjD,cAAMkK,CAAC,GAAGrM,CAAC,CAAE,IAAF,CAAX;AAEAqM,WAAC,CAACpB,IAAF,CAAQ,SAAR,IACGoB,CAAC,CAACrB,MAAF,GAAWvI,QAAX,CAAqB,SAArB,CADH,GAEG4J,CAAC,CAACrB,MAAF,GAAWtI,WAAX,CAAwB,SAAxB,CAFH;AAGA,SAND;AAOA,OA7BD;AA8BA,K,CAED;;;;WACA,0BAAiB;AAChB,WAAKoS,eAAL,GAAuB,KAAKC,oBAAL,EAAvB;AACA,K,CAED;;;;WACA,8BAAqB;AACpB,UAAK,KAAK5E,QAAV,EAAqB;AAAA;;AACpB,aAAKrL,OAAL,CACErC,QADF,CACY,eADZ,EAEE+K,IAFF,CAEQ,MAFR,EAEgB,QAFhB,EAGEA,IAHF,CAGQ,UAHR,EAGoB,IAHpB,EAIE/E,IAJF;;AAMA,aAAKuM,oBAAL;;AACA,aAAKC,2BAAL;;AACA,aAAKC,qBAAL;;AAEA,qCAAKpF,cAAL,8EAAqBrH,IAArB;AACA,OAZD,MAYO;AAAA;;AACN,aAAK3D,OAAL,CACEpC,WADF,CACe,eADf,EAEEA,WAFF,CAEe,MAFf,EAGEyS,UAHF,CAGc,MAHd,EAIEA,UAJF,CAIc,UAJd,EAKE3I,IALF;AAOAxM,SAAC,CAAE,MAAF,CAAD,CACEiS,GADF,CACO,UADP,EACmB,MADnB,EAEEvP,WAFF,CAEe,6BAFf;;AAIA,aAAK0S,uBAAL;;AACA,aAAKC,8BAAL;;AACA,aAAKC,uBAAL;;AAEA,sCAAKxF,cAAL,gFAAqBtD,IAArB;AACA;AACD,K,CAED;;;;WACA,gCAAuB;AACtB,UAAM+I,YAAY,GAAGvV,CAAC,CAAE,MAAF,EAAU;AAC/B,iBAAO,cADwB;AAE/BM,YAAI,EAAE,SAFyB;AAG/B,wBAAgB,OAHe;AAI/B,sBAAcQ,oBAAoB,CAAC8G,MAArB,CAA4B4N;AAJX,OAAV,CAAtB;AAOAD,kBAAY,CACV1L,SADF,CACa,KAAK/E,OADlB,EAEE3C,EAFF,CAEM,OAFN,EAEe,KAAK2E,UAAL,CAAgBoD,IAAhB,CAAsB,IAAtB,CAFf;AAGA,WAAKuL,aAAL,CAAmBC,WAAnB,GAAiCH,YAAjC;AACA,K,CAED;;;;WACA,mCAA0B;AAAA;;AACzB,kCAAKE,aAAL,qGAAoBC,WAApB,gFAAiCrD,MAAjC;AACA,K,CAED;;;;WACA,uCAA8B;AAAA;;AAC7B,UAAMsD,aAAa,GAAG3V,CAAC,CAAE,WAAF,EAAe;AACrC,iBAAO,iCAD8B;AAErCM,YAAI,EAAEQ,oBAAoB,CAAC8G,MAArB,CAA4BgO,YAFG;AAGrC,wBAAgB;AAHqB,OAAf,CAAvB;AAMAD,mBAAa,CAACjB,QAAd,CAAwB,KAAK5P,OAA7B,EAAuC3C,EAAvC,CAA2C,OAA3C,EAAoD,YAAM;AACzD,cAAI,CAACuJ,MAAL;;AACA,cAAI,CAAC5E,UAAL;AACA,OAHD;AAIA,WAAK2O,aAAL,CAAmBI,kBAAnB,GAAwCF,aAAxC;AACA,K,CAED;;;;WACA,0CAAiC;AAAA;;AAChC,mCAAKF,aAAL,uGAAoBI,kBAApB,gFAAwCxD,MAAxC;AACA,K,CAED;;;;WACA,iCAAwB;AACvB,UAAMnP,IAAI,GAAG,IAAb;AAEA,WAAKwM,UAAL,GAAkB/I,IAAlB,CAAwB,YAAY;AACnC,YAAMgJ,OAAO,GAAG3P,CAAC,CAAE,IAAF,CAAjB;AAAA,YACCoU,MAAM,GAAGzE,OAAO,CAAC3K,IAAR,CAAc,eAAd,CADV;;AAGA,YAAK,CAAEoP,MAAM,CAAC5R,MAAT,IAAmB4R,MAAM,CAAC1J,QAAP,CAAiB,aAAjB,CAAxB,EAA2D;AAC1D;AACA;;AAED0J,cAAM,CAAC3R,QAAP,CAAiB,aAAjB,EAAiCmE,IAAjC,CAAuC,kBAAvC,EAA2D,IAA3D;;AAEA1D,YAAI,CAACgR,qBAAL,CAA4BvE,OAA5B;AACA,OAXD;AAYA,K,CAED;;;;WACA,mCAA0B;AACzB,WAAKD,UAAL,GAAkB/I,IAAlB,CAAwB,YAAY;AACnC,YAAMgJ,OAAO,GAAG3P,CAAC,CAAE,IAAF,CAAjB;AAAA,YACCoU,MAAM,GAAGzE,OAAO,CAAC3K,IAAR,CAAc,eAAd,CADV;;AAGA,YACC,CAAEoP,MAAM,CAAC5R,MAAT,IACA,CAAE4R,MAAM,CAAC1J,QAAP,CAAiB,aAAjB,CADF,IAEA,CAAE0J,MAAM,CAACxN,IAAP,CAAa,kBAAb,CAHH,EAIE;AACD;AACA;;AAEDwN,cAAM,CACJ1R,WADF,CACe,aADf,EAEEoT,UAFF,CAEc,kBAFd,EAEkC,IAFlC,EAGE7B,GAHF,CAGO,OAHP;AAKAtE,eAAO,CAAC3K,IAAR,CAAc,iBAAd,EAAkCwH,IAAlC;AACA,OAlBD;AAmBA,K,CAED;;;;WACA,gCAAuB;AAAA;;AACtB,WAAKuJ,QAAL,CACErF,GADF,CACO,WADP,EAEEA,GAFF,CAEO,UAAE3M,CAAF,EAAKH,CAAL,EAAY;AACjB,eAAO,MAAI,CAACoS,cAAL,CAAqBhW,CAAC,CAAE4D,CAAF,CAAtB,CAAP;AACA,OAJF,EAKEoB,IALF,CAKQ,iBALR,EAMEwH,IANF,GAOEyJ,GAPF,GAQEjR,IARF,CAQQ,eARR,EASEtC,WATF,CASe,QATf,EAUED,QAVF,CAUY,QAVZ;AAWA,K,CAED;;;;WACA,iCAAwB;AAAA;;AACvB,WAAKsT,QAAL,CACErF,GADF,CACO,WADP,EAEEA,GAFF,CAEO,UAAE3M,CAAF,EAAKH,CAAL,EAAY;AACjB,eAAO,MAAI,CAACoS,cAAL,CAAqBhW,CAAC,CAAE4D,CAAF,CAAtB,CAAP;AACA,OAJF,EAKEoB,IALF,CAKQ,iBALR,EAMEyD,IANF,GAOEwN,GAPF,GAQEjR,IARF,CAQQ,eARR,EASEvC,QATF,CASY,QATZ,EAUEC,WAVF,CAUe,QAVf;AAWA,K,CAED;;;;WACA,qCAA4B;AAC3B,UAAMwT,cAAc,GAAG,KAAKnB,oBAAL,EAAvB;AAAA,UACCoB,UAAU,GAAGC,IAAI,CAACC,SAAL,CAAgBH,cAAhB,CADd;AAAA,UAECI,WAAW,GAAGF,IAAI,CAACC,SAAL,CAAgB,KAAKvB,eAArB,CAFf;AAIA,WAAKyB,KAAL,GAAaJ,UAAU,KAAKG,WAA5B;AACA,K,CAED;;;;WACA,qBAAaE,UAAb,EAA0B;AACzB;AACA,WAAKC,yBAAL,GAFyB,CAIzB;;AACA,UAAK3V,oBAAoB,CAAC4V,eAArB,IAAwC,CAAE,KAAKvG,QAApD,EAA+D;AAC9D,aAAKzE,MAAL;AACA,OAFD,MAEO,IACN,CAAE5K,oBAAoB,CAAC4V,eAAvB,IACA,CAAE,KAAKvG,QAFD,EAGL;AAAA;;AACD,aAAKoG,KAAL,4BACG,KAAKzG,cADR,0DACG,sBAAqBtD,IAArB,EADH,4BAEG,KAAKsD,cAFR,0DAEG,sBAAqBrH,IAArB,EAFH;AAGA,OAPM,MAOA,IAAK,KAAK0H,QAAL,IAAiB,KAAKoG,KAA3B,EAAmC;AAAA;;AACzC,aAAKzR,OAAL,CAAarC,QAAb,CAAuB,oBAAvB;AACA,sCAAKgT,aAAL,CAAmBI,kBAAnB,gFAAuCrJ,IAAvC;AACA;AACD,K,CAED;;;;WACA,kBAAS;AAAA;AAAA;AAAA;;AACR,UAAMd,MAAM,cAAGhK,MAAH,4CAAG,QAAQiV,cAAvB;AAEAjL,YAAM,SAAN,IAAAA,MAAM,WAAN,gCAAAA,MAAM,CACHkL,QADH,CACa,KAAK7B,oBAAL,EADb,EAC0C,KAAK7T,MAD/C,EACuD,KAAKC,MAD5D,uEAEGS,IAFH,CAES,YAAM;AACb,YAAIiV,SAAS,GAAG7W,CAAC,CAAE,MAAI,CAACmB,MAAP,CAAjB;;AAEA,YACC,CAAE,MAAI,CAACgP,QAAP,IACA0G,SAAS,CAACrU,MADV,IAEA1B,oBAAoB,CAACgW,UAHtB,EAIE;AACD,cAAIC,YAAY,GAAGF,SAAS,CAACG,MAAV,GAAmBC,GAAtC;;AAEA,cAAK,CAAC,CAAEnW,oBAAoB,CAACoW,aAA7B,EAA6C;AAC5C,gBAAMC,YAAY,GAAGnX,CAAC,CACrBc,oBAAoB,CAACoW,aADA,CAAtB;AAIAH,wBAAY,GAAGI,YAAY,CAAC3U,MAAb,GACZ2U,YAAY,CAACH,MAAb,GAAsBC,GADV,GAEZF,YAFH;AAGA;;AACD/W,WAAC,CAAE,YAAF,CAAD,CAAkBoX,OAAlB,CAA2B;AAC1BC,qBAAS,EAAEN,YAAY,GAAG;AADA,WAA3B;AAGA,SAtBY,CAwBb;;;AACA,cAAI,CAACjC,eAAL,GAAuB,MAAI,CAACC,oBAAL,EAAvB;AACA,cAAI,CAACwB,KAAL,GAAa,KAAb;AACA,OA7BF;;AA+BA,UAAK,KAAKpG,QAAV,EAAqB;AAAA;;AACpB,aAAKrL,OAAL,CAAapC,WAAb,CAA0B,oBAA1B;AACA,uCAAK+S,aAAL,CAAmBI,kBAAnB,kFAAuCpN,IAAvC;AACA;AACD,K,CAED;;;;WACA,sBAAa;AACZ,UAAK,UAAU,KAAKsN,QAApB,EAA+B;AAC9B,aAAKA,QAAL,GAAgB,KAAKjR,OAAL,CAAaE,IAAb,CAAmB,mBAAnB,CAAhB;AACA;;AAED,aAAO,KAAK+Q,QAAZ;AACA,K,CAED;;;;WACA,4BAAmB;AAClB,UAAK,UAAU,KAAKnF,aAApB,EAAoC;AACnC,aAAKA,aAAL,GAAqB,KAAKmE,oBAAL,EAArB;AACA;;AAED,aAAO,KAAKnE,aAAZ;AACA,K,CAED;;;;WACA,6BAAoB;AACnB,aAAO,CAAC,CAAEtO,MAAM,CAACC,IAAP,CAAa,KAAK+U,gBAAL,EAAb,EAAuC9U,MAAjD;AACA,K,CAED;;;;WACA,wBAAgBmN,OAAhB,EAA0B;AACzB,UAAI4H,UAAU,GAAG5H,OAAO,CAAC/I,IAAR,CAAc,aAAd,CAAjB;AAAA,UACCwG,MADD;AAAA,UAECoK,cAFD;;AAIA,cAASD,UAAT;AACC,aAAK,KAAL;AACA,aAAK,QAAL;AACA,aAAK,aAAL;AACC,cAAMjF,SAAS,GAAG3C,OAAO,CAAC3K,IAAR,CAAc,kBAAd,CAAlB;;AAEA,cAAKsN,SAAS,CAAC9P,MAAf,EAAwB;AACvB,gBAAMqJ,GAAG,GAAGyG,SAAS,CAACzG,GAAV,EAAZ;AAEAuB,kBAAM,GAAG,aAAa,wBAAOvB,GAApB,IAA0B,CAAC,EAAEA,GAAF,aAAEA,GAAF,eAAEA,GAAG,CAAErJ,MAAP,CAA3B,GAA2C,CAAC,CAAEqJ,GAAvD;AACA;AACA;;AAEF;;AACA,aAAK,YAAL;AACCuB,gBAAM,GAAGuC,OAAO,CAAC3K,IAAR,CAAc,cAAd,EAA+B0G,MAA/B,CAAuC,SAAvC,EACPlJ,MADF;AAEA;;AACD,aAAK,cAAL;AACC,cAAMyQ,IAAI,GAAGJ,UAAU,CACrBlD,OAAO,CAAC3K,IAAR,CAAc,eAAd,EAAgC4B,IAAhC,CAAsC,MAAtC,CADqB,CAAvB;AAAA,cAGCgM,GAAG,GAAGC,UAAU,CACflD,OAAO,CAAC3K,IAAR,CAAc,eAAd,EAAgC4B,IAAhC,CAAsC,KAAtC,CADe,CAHjB;AAAA,cAMCkM,GAAG,GAAGD,UAAU,CACflD,OAAO,CAAC3K,IAAR,CAAc,eAAd,EAAgC4B,IAAhC,CAAsC,KAAtC,CADe,CANjB;AAAA,cASCmM,UAAU,GAAGF,UAAU,CACtBlD,OAAO,CAAC3K,IAAR,CAAc,mBAAd,EAAoC6G,GAApC,EADsB,CATxB;AAAA,cAYCmH,UAAU,GAAGH,UAAU,CACtBlD,OAAO,CAAC3K,IAAR,CAAc,mBAAd,EAAoC6G,GAApC,EADsB,CAZxB;AAgBAuB,gBAAM,GACLqK,IAAI,CAACC,GAAL,CAAU3E,UAAU,GAAGH,GAAvB,KAAgCK,IAAhC,IACAwE,IAAI,CAACC,GAAL,CAAU1E,UAAU,GAAGF,GAAvB,KAAgCG,IAFjC;AAGA;;AACD,aAAK,SAAL;AACC7F,gBAAM,GACL,iBAAiBuC,OAAO,CAAC3K,IAAR,CAAc,kBAAd,EAAmC6G,GAAnC,EADlB;AAEA;;AACD;AACCuB,gBAAM,GAAG,KAAT;AACA;AA7CF;;AAgDAoK,oBAAc,GAAG7H,OAAO,CAACpO,cAAR,CAAwB,4BAAxB,EAAsD,CACtE6L,MADsE,EAEtE,IAFsE,CAAtD,CAAjB;AAIAA,YAAM,GACL,OAAOoK,cAAP,KAA0B,WAA1B,GAAwCA,cAAxC,GAAyDpK,MAD1D;AAGA,aAAOA,MAAP;AACA,K,CAED;;;;WACA,0BAAkBuC,OAAlB,EAA4B;AAC3B,UAAI4H,UAAU,GAAG5H,OAAO,CAAC/I,IAAR,CAAc,aAAd,CAAjB;AAAA,UACC+G,KADD;;AAGA,cAAS4J,UAAT;AACC,aAAK,KAAL;AACA,aAAK,QAAL;AACA,aAAK,aAAL;AACC,cAAMjF,SAAS,GAAG3C,OAAO,CAAC3K,IAAR,CAAc,kBAAd,CAAlB;;AAEA,cAAKsN,SAAS,CAAC9P,MAAf,EAAwB;AACvB,gBAAMqJ,GAAG,GAAGyG,SAAS,CAACzG,GAAV,EAAZ;AAEA8B,iBAAK,GAAG,aAAa,wBAAO9B,GAApB,IAA0BA,GAA1B,aAA0BA,GAA1B,uBAA0BA,GAAG,CAAErJ,MAA/B,GAAwC,CAAC,CAAC,CAAEqJ,GAApD;AACA;AACA;;AAEF;;AACA,aAAK,YAAL;AACC8B,eAAK,GAAGgC,OAAO,CAAC3K,IAAR,CAAc,eAAd,EAAgCA,IAAhC,CAAsC,SAAtC,EACNxC,MADF;AAEA;;AACD,aAAK,SAAL;AACC,cAAK,KAAKwT,cAAL,CAAqBrG,OAArB,CAAL,EAAsC;AACrChC,iBAAK,GAAG,CAAR;AACA;;AACD;;AACD,aAAK,cAAL;AACA;AACCA,eAAK,GAAG,CAAR;AACA;AA1BF;;AA6BA,aAAOA,KAAP;AACA,K,CAED;;;;WACA,6BAAqBgC,OAArB,EAA+B;AAC9B,UAAI4H,UAAU,GAAG5H,OAAO,CAAC/I,IAAR,CAAc,aAAd,CAAjB;AAAA,UACC4J,QAAQ,GAAG,UAAUb,OAAO,CAAC/I,IAAR,CAAc,UAAd,CADtB;AAAA,UAEC0L,SAAS,GAAG3C,OAAO,CAAC3K,IAAR,CAAc,kBAAd,CAFb;AAAA,UAGC2S,UAAU,GAAG,EAHd;AAAA,UAICC,kBAJD;AAAA,UAKCC,OALD;;AAOA,cAASN,UAAT;AACC,aAAK,KAAL;AACC,cAAIO,WAAW,GAAG,EAAlB;AAAA,cACCC,QAAQ,GAAGpI,OAAO,CAAC/I,IAAR,CAAc,UAAd,CADZ;AAAA,cAECoR,MAAM,GAAG,MAAMD,QAAQ,CAAC5T,OAAT,CAAkB,QAAlB,CAFhB;AAAA,cAGC8T,QAAQ,GAAGtI,OAAO,CAAC/I,IAAR,CAAc,UAAd,CAHZ;;AAKA,cAAK0L,SAAS,CAAC9P,MAAf,EAAwB;AACvB,gBAAKgO,QAAL,EAAgB;AACfsH,yBAAW,GAAGxF,SAAS,CAACzG,GAAV,EAAd;AACA,aAFD,MAEO;AACNiM,yBAAW,CAACpL,IAAZ,CAAkB4F,SAAS,CAACzG,GAAV,EAAlB;AACA;AACD,WAND,MAMO;AACNgM,mBAAO,GAAGlI,OAAO,CACf3K,IADQ,CACF,cADE,EAER0G,MAFQ,CAEA,SAFA,EAGRiF,QAHQ,CAGE,UAHF,CAAV;AAKAmH,uBAAW,GAAGD,OAAO,CAACK,GAAR,GAAcxU,MAAd,CAAsB,UAAWC,CAAX,EAAcC,CAAd,EAAkB;AACrD,kBAAIiI,GAAJ;AAEAjI,eAAC,GAAG5D,CAAC,CAAE4D,CAAF,CAAL;AACAiI,iBAAG,GAAGjI,CAAC,CAACsD,EAAF,CAAM,OAAN,IACHtD,CAAC,CAACoB,IAAF,CAAQ,QAAR,EAAmB6G,GAAnB,EADG,GAEHjI,CAAC,CAACgD,IAAF,CAAQ,WAAR,CAFH;;AAIA,kBAAK,CAAEiF,GAAP,EAAa;AACZ,uBAAOlI,CAAP;AACA;;AAEDA,eAAC,CAAC+I,IAAF,CAAQb,GAAR;AAEA,qBAAOlI,CAAP;AACA,aAfa,EAeXmU,WAfW,CAAd;AAgBA;;AAED,cAAK,CAAEtH,QAAP,EAAkB;AACjBmH,sBAAU,CAAEI,QAAF,CAAV,GAAyBD,WAAW,CAACK,GAAZ,EAAzB;AACA,WAFD,MAEO;AACN,gBAAMC,IAAI,GAAG,CAAEJ,MAAF,IAAY,UAAUC,QAAtB,GAAiC,GAAjC,GAAuC,GAApD;AACAN,sBAAU,CAAEI,QAAF,CAAV,GAAyBD,WAAW,CAAC/I,IAAZ,CAAkBqJ,IAAlB,CAAzB;AACA;;AAED,cAAKJ,MAAL,EAAc;AACbL,sBAAU,CACTI,QAAQ,CAAC1U,OAAT,CAAkB,SAAlB,EAA6B,aAA7B,CADS,CAAV,GAEI4U,QAFJ;AAGA;;AAED;;AACD,aAAK,QAAL;AACC,cAAK3F,SAAS,CAAC9P,MAAf,EAAwB;AACvBmV,sBAAU,CAACU,aAAX,GAA2B/F,SAAS,CAACzG,GAAV,EAA3B;AACA,WAFD,MAEO;AACNgM,mBAAO,GAAGlI,OAAO,CACf3K,IADQ,CACF,cADE,EAER0G,MAFQ,CAEA,SAFA,EAGRiF,QAHQ,CAGE,UAHF,CAAV;;AAKA,gBAAK,CAAEH,QAAP,EAAkB;AACjBqH,qBAAO,GAAGA,OAAO,CAAC3S,KAAR,EAAV;AACAyS,wBAAU,CAACU,aAAX,GAA2BR,OAAO,CAAC3Q,EAAR,CAAY,OAAZ,IACxB2Q,OAAO,CAAC7S,IAAR,CAAc,QAAd,EAAyB6G,GAAzB,EADwB,GAExBgM,OAAO,CAACjR,IAAR,CAAc,QAAd,CAFH;AAGA,aALD,MAKO;AACN+Q,wBAAU,CAACU,aAAX,GAA2BR,OAAO,CAChCK,GADyB,GAEzBxU,MAFyB,CAEjB,UAAWC,CAAX,EAAcC,CAAd,EAAkB;AAC1B,oBAAIiI,GAAJ;AAEAjI,iBAAC,GAAG5D,CAAC,CAAE4D,CAAF,CAAL;AACAiI,mBAAG,GAAGjI,CAAC,CAACsD,EAAF,CAAM,OAAN,IACHtD,CAAC,CAACoB,IAAF,CAAQ,QAAR,EAAmB6G,GAAnB,EADG,GAEHjI,CAAC,CAACgD,IAAF,CAAQ,QAAR,CAFH;;AAIA,oBAAK,CAAEiF,GAAP,EAAa;AACZ,yBAAOlI,CAAP;AACA;;AAEDA,iBAAC,CAAC+I,IAAF,CAAQb,GAAR;AAEA,uBAAOlI,CAAP;AACA,eAjByB,EAiBvB,EAjBuB,EAkBzBoL,IAlByB,CAkBnB,GAlBmB,CAA3B;AAmBA;AACD;;AACD;;AACD,aAAK,aAAL;AACC,cAAKuD,SAAS,CAAC9P,MAAf,EAAwB;AACvB,gBAAKgO,QAAL,EAAgB;AACfmH,wBAAU,CAACW,YAAX,GAA0BhG,SAAS,CAACzG,GAAV,GAAgBkD,IAAhB,CAAsB,GAAtB,CAA1B;AACA,aAFD,MAEO;AACN4I,wBAAU,CAACY,SAAX,GAAuBjG,SAAS,CAC9BzG,GADqB,GAErBpI,KAFqB,CAEd,GAFc,EAEP,CAFO,CAAvB;AAGAkU,wBAAU,CAACa,SAAX,GAAuBlG,SAAS,CAC9BzG,GADqB,GAErBpI,KAFqB,CAEd,GAFc,EAEP,CAFO,CAAvB;AAGA;AACD,WAXD,MAWO;AACNoU,mBAAO,GAAGlI,OAAO,CACf3K,IADQ,CACF,cADE,EAER0G,MAFQ,CAEA,SAFA,EAGRiF,QAHQ,CAGE,UAHF,CAAV;;AAKA,gBAAKH,QAAL,EAAgB;AACfmH,wBAAU,CAACW,YAAX,GAA0BT,OAAO,CAC/BK,GADwB,GAExBxU,MAFwB,CAEhB,UAAEC,CAAF,EAAKC,CAAL,EAAY;AACpB,oBAAIgP,GAAG,GAAG5S,CAAC,CAAE4D,CAAF,CAAD,CAAOgD,IAAP,CAAa,WAAb,CAAV;AAAA,oBACCkM,GAAG,GAAG9S,CAAC,CAAE4D,CAAF,CAAD,CAAOgD,IAAP,CAAa,WAAb,CADP;AAGAjD,iBAAC,IAAI,CAAEmP,GAAG,aAAOF,GAAP,cAAgBE,GAAhB,IAAyBF,GAA9B,IAAsC,GAA3C;AAEA,uBAAOjP,CAAP;AACA,eATwB,EAStB,EATsB,EAUxBN,OAVwB,CAUf,SAVe,EAUJ,IAVI,CAA1B;AAWA,aAZD,MAYO;AACNsU,wBAAU,CAACY,SAAX,GAAuB1F,UAAU,CAChCgF,OAAO,CAAC3S,KAAR,GAAgB0B,IAAhB,CAAsB,WAAtB,CADgC,CAAjC;AAGA+Q,wBAAU,CAACa,SAAX,GAAuB3F,UAAU,CAChCgF,OAAO,CAAC3S,KAAR,GAAgB0B,IAAhB,CAAsB,WAAtB,CADgC,CAAjC;AAGA;AACD;;AACD;;AACD,aAAK,cAAL;AACC+Q,oBAAU,CAACY,SAAX,GAAuB1F,UAAU,CAChClD,OAAO,CAAC3K,IAAR,CAAc,mBAAd,EAAoC6G,GAApC,EADgC,CAAjC;AAGA8L,oBAAU,CAACa,SAAX,GAAuB3F,UAAU,CAChClD,OAAO,CAAC3K,IAAR,CAAc,mBAAd,EAAoC6G,GAApC,EADgC,CAAjC;AAGA;;AACD,aAAK,YAAL;AACC,cAAK8D,OAAO,CAAC3K,IAAR,CAAc,iBAAd,EAAkCkC,EAAlC,CAAsC,SAAtC,CAAL,EAAyD;AACxDyQ,sBAAU,CAACc,aAAX,GAA2B,CAA3B;AACA;;AACD,cAAK9I,OAAO,CAAC3K,IAAR,CAAc,kBAAd,EAAmCkC,EAAnC,CAAuC,SAAvC,CAAL,EAA0D;AACzDyQ,sBAAU,CAACe,cAAX,GAA4B,CAA5B;AACA;;AACD,cAAK/I,OAAO,CAAC3K,IAAR,CAAc,kBAAd,EAAmCkC,EAAnC,CAAuC,SAAvC,CAAL,EAA0D;AACzDyQ,sBAAU,CAACgB,eAAX,GAA6B,CAA7B;AACA;;AACD;;AACD,aAAK,SAAL;AACChB,oBAAU,CAACiB,OAAX,GAAqBjJ,OAAO,CAAC3K,IAAR,CAAc,kBAAd,EAAmC6G,GAAnC,EAArB;AACA;;AACD;AACC;AAvJF;;AA0JA+L,wBAAkB,GAAGjI,OAAO,CAACpO,cAAR,CACpB,6BADoB,EAEpB,CAAEoW,UAAF,EAAczU,IAAd,CAFoB,CAArB;AAIAyU,gBAAU,GACT,OAAOC,kBAAP,KAA8B,WAA9B,GACGA,kBADH,GAEGD,UAHJ;AAKA,aAAOA,UAAP;AACA,K,CAED;;;;WACA,gCAAuB;AACtB,UAAIA,UAAU,GAAG,EAAjB;AACA,UAAMzU,IAAI,GAAG,IAAb;AAEA,WAAKwM,UAAL,GAAkB/I,IAAlB,CAAwB,YAAY;AACnC,YAAMgJ,OAAO,GAAG3P,CAAC,CAAE,IAAF,CAAjB;;AAEA,YAAKkD,IAAI,CAAC8S,cAAL,CAAqBrG,OAArB,CAAL,EAAsC;AACrC,cAAMkJ,gBAAgB,GAAG3V,IAAI,CAAC4V,mBAAL,CAA0BnJ,OAA1B,CAAzB;AAEAgI,oBAAU,GAAGzU,IAAI,CAAC6V,eAAL,CACZpB,UADY,EAEZkB,gBAFY,EAGZlJ,OAHY,CAAb;AAKA;AACD,OAZD;AAcA,aAAOgI,UAAP;AACA,K,CAED;;;;WACA,gCAAwBA,UAAxB,EAAqC;AACpC,UAAMzU,IAAI,GAAG,IAAb;AAEA,aAAO,KAAKwM,UAAL,GAAkBhE,MAAlB,CAA0B,YAAY;AAC5C,YAAMiE,OAAO,GAAG3P,CAAC,CAAE,IAAF,CAAjB;;AAEA,YAAKkD,IAAI,CAAC8S,cAAL,CAAqBrG,OAArB,CAAL,EAAsC;AACrC,cAAIkJ,gBAAgB,GAAG3V,IAAI,CAAC4V,mBAAL,CAA0BnJ,OAA1B,CAAvB;AAAA,cACCqJ,OAAO,GAAG,KADX;;AAGA,eAAM,IAAM/N,IAAZ,IAAoB0M,UAApB,EAAiC;AAChC,gBACC,CAAE,WAAF,EAAe,WAAf,EAA4B,cAA5B,EAA6CsB,QAA7C,CACChO,IADD,MAGE4N,gBAAgB,CAACN,SAAjB,IACDM,gBAAgB,CAACP,YAJlB,CADD,EAME;AACDU,qBAAO,GAAG,IAAV;AACA;AACA,aATD,MASO,IAAKH,gBAAgB,CAAE5N,IAAF,CAArB,EAAgC;AACtC+N,qBAAO,GAAG,IAAV;AACA;AACA;AACD;;AAED,iBAAOA,OAAP;AACA;;AAED,eAAO,KAAP;AACA,OA3BM,CAAP;AA4BA,K,CAED;;;;WACA,gCAAwBrJ,OAAxB,EAAkC;AACjC,UAAK,CAAE,KAAKqG,cAAL,CAAqBrG,OAArB,CAAP,EAAwC;AACvC,aAAKuJ,oBAAL,CAA2BvJ,OAA3B;AACA,OAFD,MAEO;AACN,aAAK4B,oBAAL,CAA2B5B,OAA3B;AACA;AACD,K,CAED;;;;WACA,sCAA6B;AAC5B,UAAK,CAAE,KAAKwJ,iBAAL,EAAP,EAAkC;AACjC,aAAKC,wBAAL;AACA,OAFD,MAEO;AACN,aAAKvJ,wBAAL;AACA;AACD,K,CAED;;;;WACA,8BAAsBF,OAAtB,EAAgC;AAAA;;AAC/B,UACC,CAAE,KAAKqG,cAAL,CAAqBrG,OAArB,CAAF,IACA,CAAE7O,oBAAoB,CAACuY,iBAFxB,EAGE;AACD;AACA,OAN8B,CAQ/B;;;AACA1J,aAAO,CAAC3K,IAAR,CAAc,kBAAd,EAAmCqN,MAAnC,GAT+B,CAW/B;;AACArS,OAAC,CAAE,MAAF,EAAU;AACV,iBAAO,iBADG;AAEVgK,YAAI,EAAElJ,oBAAoB,CAAC8G,MAArB,CAA4B0R,eAFxB;AAGVC,YAAI,EAAE;AAHI,OAAV,CAAD,CAKE1P,SALF,CAKa8F,OAAO,CAAC3K,IAAR,CAAc,iBAAd,CALb,EAME7C,EANF,CAMM,OANN,EAMe,UAAEsE,EAAF,EAAU;AACvBA,UAAE,CAACC,cAAH;;AAEA,cAAI,CAAC8S,gBAAL,CACC7J,OADD,EAEC,KAFD,EAGC7O,oBAAoB,CAAC4V,eAHtB;;AAKA,cAAI,CAACwC,oBAAL,CAA2BvJ,OAA3B;;AAEA,YAAK7O,oBAAoB,CAAC4V,eAA1B,EAA4C;AAC3C,gBAAI,CAAC5P,UAAL;AACA;AACD,OAnBF;AAoBA,K,CAED;;;;WACA,oCAA2B;AAAA;;AAC1B,UAAK,CAAE,KAAKqS,iBAAL,EAAF,IAA8B,CAAE,KAAKhJ,QAA1C,EAAqD;AACpD;AACA,OAHyB,CAK1B;;;AACA,WAAKrL,OAAL,CAAaE,IAAb,CAAmB,kBAAnB,EAAwCqN,MAAxC,GAN0B,CAQ1B;;AACArS,OAAC,CAAE,MAAF,EAAU;AACV,iBAAO,iBADG;AAEVgK,YAAI,EAAElJ,oBAAoB,CAAC8G,MAArB,CAA4B6R,oBAFxB;AAGVF,YAAI,EAAE;AAHI,OAAV,CAAD,CAKE1P,SALF,CAKa,KAAK/E,OAAL,CAAaE,IAAb,CAAmB,oBAAnB,CALb,EAME7C,EANF,CAMM,OANN,EAMe,UAAEsE,EAAF,EAAU;AACvBA,UAAE,CAACC,cAAH;;AAEA,cAAI,CAACG,oBAAL,CACC/F,oBAAoB,CAAC4V,eADtB;;AAGA,cAAI,CAAC0C,wBAAL;;AAEA,YAAKtY,oBAAoB,CAAC4V,eAA1B,EAA4C;AAC3C,gBAAI,CAAC5P,UAAL;AACA;AACD,OAjBF;AAkBA,K,CAED;;;;WACA,8BAAsB6I,OAAtB,EAAgC;AAC/B,UACC,KAAKqG,cAAL,CAAqBrG,OAArB,KACA,CAAE7O,oBAAoB,CAACuY,iBAFxB,EAGE;AACD;AACA,OAN8B,CAQ/B;;;AACA1J,aAAO,CAAC3K,IAAR,CAAc,kBAAd,EAAmCqN,MAAnC;AACA,K,CAED;;;;WACA,oCAA2B;AAC1B,UAAK,KAAK8G,iBAAL,EAAL,EAAgC;AAC/B;AACA,OAHyB,CAK1B;;;AACA,WAAKrU,OAAL,CACEE,IADF,CACQ,oBADR,EAEE2L,QAFF,CAEY,kBAFZ,EAGE0B,MAHF;AAIA,K,CAED;;;;WACA,0BAAkB1C,OAAlB,EAA2BgI,UAA3B,EAAuCf,QAAvC,EAAkD;AACjD,UAAMW,UAAU,GAAG5H,OAAO,CAAC/I,IAAR,CAAc,aAAd,CAAnB;AAAA,UACC6J,MAAM,GAAGd,OAAO,CAAC3K,IAAR,CAAc,cAAd,CADV;AAAA,UAEC0U,YAAY,GAAGjJ,MAAM,CAAC/E,MAAP,CAAe,SAAf,CAFhB;AAAA,UAGC4G,SAAS,GAAG3C,OAAO,CAAC3K,IAAR,CAAc,kBAAd,CAHb;;AAKA,cAASuS,UAAT;AACC,aAAK,KAAL;AACC,cAAMQ,QAAQ,GAAGpI,OAAO,CAAC/I,IAAR,CAAc,UAAd,CAAjB;;AAEA,cAAK0L,SAAS,CAAC9P,MAAf,EAAwB;AACvB,gBAAK,CAAEmV,UAAP,EAAoB;AACnBrF,uBAAS,CAACtN,IAAV,CAAgB,QAAhB,EAA2BiG,IAA3B,CAAiC,UAAjC,EAA6C,KAA7C;AACA,aAFD,MAEO;AACNqH,uBAAS,CAACtN,IAAV,CAAgB,QAAhB,EAA2B2B,IAA3B,CAAiC,YAAY;AAC5C,oBAAMiI,OAAO,GAAG5O,CAAC,CAAE,IAAF,CAAjB;;AAEA,oBACC4O,OAAO,CAAC/C,GAAR,GAAcsD,QAAd,OACAwI,UAAU,CAAEI,QAAF,CAAV,CAAuB5I,QAAvB,EAFD,EAGE;AACDP,yBAAO,CAAC3D,IAAR,CAAc,UAAd,EAA0B,KAA1B;AACA;AACD,eATD;AAUA;;AAEDqH,qBAAS,CAACpH,MAAV;AACA,WAjBD,MAiBO,IAAK,CAAEyM,UAAP,EAAoB;AAC1B+B,wBAAY,CAAC/I,QAAb,CAAuB,OAAvB,EAAiCA,QAAjC,CAA2C,GAA3C,EAAiDgJ,KAAjD;AACAD,wBAAY,CAAChX,WAAb,CAA0B,QAA1B;AACA,WAHM,MAGA;AACNgX,wBAAY,CAAC/S,IAAb,CAAmB,YAAY;AAC9B,kBAAI4G,KAAK,GAAGvN,CAAC,CAAE,IAAF,CAAb;AAAA,kBACC8N,MAAM,GAAGP,KAAK,CAACoD,QAAN,CAAgB,OAAhB,CADV;AAAA,kBAEClD,OAAO,GAAGF,KAAK,CAACoD,QAAN,CAAgB,GAAhB,CAFX;AAAA,kBAGCnG,KAHD;AAKAA,mBAAK,GAAGsD,MAAM,CAACtL,MAAP,GACLsL,MAAM,CAAC9I,IAAP,CAAa,QAAb,EAAwB6G,GAAxB,EADK,GAEL4B,OAAO,CAAC7G,IAAR,CAAc,WAAd,CAFH;;AAIA,kBACC4D,KAAK,CAAC2E,QAAN,OACAwI,UAAU,CAAEI,QAAF,CAAV,CAAuB5I,QAAvB,EAFD,EAGE;AACD5B,qBAAK,CAACoD,QAAN,CAAgB,OAAhB,EAA0BA,QAA1B,CAAoC,GAApC,EAA0CgJ,KAA1C;AACApM,qBAAK,CAAC7K,WAAN,CAAmB,QAAnB;AACA;AACD,aAjBD;AAkBA;;AACD;;AACD,aAAK,QAAL;AACC,cAAK4P,SAAS,CAAC9P,MAAf,EAAwB;AACvB,gBAAK,CAAEmV,UAAP,EAAoB;AACnBrF,uBAAS,CAACtN,IAAV,CAAgB,QAAhB,EAA2BiG,IAA3B,CAAiC,UAAjC,EAA6C,KAA7C;AACA,aAFD,MAEO;AACNqH,uBAAS,CAACtN,IAAV,CAAgB,QAAhB,EAA2B2B,IAA3B,CAAiC,YAAY;AAC5C,oBAAMiI,OAAO,GAAG5O,CAAC,CAAE,IAAF,CAAjB;;AAEA,oBAAK4O,OAAO,CAAC/C,GAAR,OAAkB8L,UAAU,CAACU,aAAlC,EAAkD;AACjDzJ,yBAAO,CAAC3D,IAAR,CAAc,UAAd,EAA0B,KAA1B;AACA;AACD,eAND;AAOA;;AAEDqH,qBAAS,CAACpH,MAAV;AACA,WAdD,MAcO,IAAK,CAAEyM,UAAP,EAAoB;AAC1B+B,wBAAY,CAAC/I,QAAb,CAAuB,OAAvB,EAAiCA,QAAjC,CAA2C,GAA3C,EAAiDgJ,KAAjD;AACAD,wBAAY,CAAChX,WAAb,CAA0B,QAA1B;AACA,WAHM,MAGA;AACNgX,wBAAY,CAAC/S,IAAb,CAAmB,YAAY;AAC9B,kBAAI4G,KAAK,GAAGvN,CAAC,CAAE,IAAF,CAAb;AAAA,kBACC8N,MAAM,GAAGP,KAAK,CAACoD,QAAN,CAAgB,OAAhB,CADV;AAAA,kBAEClD,OAAO,GAAGF,KAAK,CAACoD,QAAN,CAAgB,GAAhB,CAFX;AAAA,kBAGCnG,KAHD;AAKAA,mBAAK,GAAGsD,MAAM,CAACtL,MAAP,GACLsL,MAAM,CAAC9I,IAAP,CAAa,QAAb,EAAwB6G,GAAxB,EADK,GAEL4B,OAAO,CAAC7G,IAAR,CAAc,QAAd,CAFH;;AAIA,kBAAK4D,KAAK,KAAKmN,UAAU,CAACU,aAA1B,EAA0C;AACzC9K,qBAAK,CAACoD,QAAN,CAAgB,OAAhB,EAA0BA,QAA1B,CAAoC,GAApC,EAA0CgJ,KAA1C;AACApM,qBAAK,CAAC7K,WAAN,CAAmB,QAAnB;AACA;AACD,aAdD;AAeA;;AACD;;AACD,aAAK,aAAL;AACC,cAAK4P,SAAS,CAAC9P,MAAf,EAAwB;AACvB,gBAAK,CAAEmV,UAAP,EAAoB;AACnBrF,uBAAS,CAACtN,IAAV,CAAgB,QAAhB,EAA2BiG,IAA3B,CAAiC,UAAjC,EAA6C,KAA7C;AACA,aAFD,MAEO;AACNqH,uBAAS,CAACtN,IAAV,CAAgB,QAAhB,EAA2B2B,IAA3B,CAAiC,YAAY;AAC5C,oBAAMiI,OAAO,GAAG5O,CAAC,CAAE,IAAF,CAAjB;AAAA,oBACC4Z,cAAc,GACbjC,UAAU,CAACY,SAAX,IACEZ,UAAU,CAACa,SAAX,cACMb,UAAU,CAACa,SADjB,IAEC,EAHH,CAFF;;AAOA,oBAAK5J,OAAO,CAAC/C,GAAR,OAAkB+N,cAAvB,EAAwC;AACvChL,yBAAO,CAAC3D,IAAR,CAAc,UAAd,EAA0B,KAA1B;AACA;AACD,eAXD;AAYA;;AAEDqH,qBAAS,CAACpH,MAAV;AACA,WAnBD,MAmBO,IAAK,CAAEyM,UAAP,EAAoB;AAC1B+B,wBAAY,CAAC/I,QAAb,CAAuB,OAAvB,EAAiCA,QAAjC,CAA2C,GAA3C,EAAiDgJ,KAAjD;AACAD,wBAAY,CAAChX,WAAb,CAA0B,QAA1B;AACA,WAHM,MAGA;AACNgX,wBAAY,CAAC/S,IAAb,CAAmB,YAAY;AAC9B,kBAAI4G,KAAK,GAAGvN,CAAC,CAAE,IAAF,CAAb;AAAA,kBACC8N,MAAM,GAAGP,KAAK,CAACoD,QAAN,CAAgB,OAAhB,CADV;AAAA,kBAEClD,OAAO,GAAGF,KAAK,CAACoD,QAAN,CAAgB,GAAhB,CAFX;AAAA,kBAGCiJ,cAHD;AAAA,kBAICpP,KAJD;AAMAA,mBAAK,GAAGsD,MAAM,CAACtL,MAAP,GACLsL,MAAM,CAAC9I,IAAP,CAAa,QAAb,EAAwB6G,GAAxB,EADK,GAEL4B,OAAO,CAAC7G,IAAR,CAAc,WAAd,KACE6G,OAAO,CAAC7G,IAAR,CAAc,WAAd,IACA,MAAM6G,OAAO,CAAC7G,IAAR,CAAc,WAAd,CADN,GAEA,EAHF,CAFH;;AAOA,kBAAK+Q,UAAU,CAACY,SAAhB,EAA4B;AAC3BqB,8BAAc,GACbjC,UAAU,CAACY,SAAX,IACEZ,UAAU,CAACa,SAAX,GACC,MAAMb,UAAU,CAACa,SADlB,GAEC,EAHH,CADD;AAKA,eAND,MAMO,IAAKb,UAAU,CAACW,YAAhB,EAA+B;AACrCsB,8BAAc,GAAGjC,UAAU,CAACW,YAA5B;AACA;;AAED,kBAAK9N,KAAK,KAAKoP,cAAf,EAAgC;AAC/BrM,qBAAK,CAACoD,QAAN,CAAgB,OAAhB,EAA0BA,QAA1B,CAAoC,GAApC,EAA0CgJ,KAA1C;AACApM,qBAAK,CAAC7K,WAAN,CAAmB,QAAnB;AACA;AACD,aA5BD;AA6BA;;AACD;;AACD,aAAK,cAAL;AACC,cAAMmX,YAAY,GAAGlK,OAAO,CAAC3K,IAAR,CAAc,eAAd,CAArB;AAEA2K,iBAAO,CACL3K,IADF,CACQ,mBADR,EAEE6G,GAFF,CAEOgO,YAAY,CAACjT,IAAb,CAAmB,KAAnB,CAFP;AAGA+I,iBAAO,CACL3K,IADF,CACQ,mBADR,EAEE6G,GAFF,CAEOgO,YAAY,CAACjT,IAAb,CAAmB,KAAnB,CAFP,EAGEsE,MAHF;AAIA;;AACD,aAAK,SAAL;AACCyE,iBAAO,CAAC3K,IAAR,CAAc,QAAd,EAAyB6G,GAAzB,CAA8B,YAA9B;AACA;;AACD,aAAK,YAAL;AACC,cAAK,CAAE8L,UAAP,EAAoB;AACnBhI,mBAAO,CACL3K,IADF,CACQ,kBADR,EAEEA,IAFF,CAEQ,QAFR,EAGEiG,IAHF,CAGQ,SAHR,EAGmB,KAHnB,EAIEC,MAJF;AAKAyE,mBAAO,CACL3K,IADF,CACQ,iBADR,EAEEA,IAFF,CAEQ,QAFR,EAGEiG,IAHF,CAGQ,SAHR,EAGmB,KAHnB,EAIEC,MAJF;AAKAyE,mBAAO,CACL3K,IADF,CACQ,kBADR,EAEEA,IAFF,CAEQ,QAFR,EAGEiG,IAHF,CAGQ,SAHR,EAGmB,KAHnB,EAIEC,MAJF;AAMAuF,kBAAM,CAAC/N,WAAP,CAAoB,QAApB;AACA,WAlBD,MAkBO;AACN,gBAAKiV,UAAL,aAAKA,UAAL,eAAKA,UAAU,CAAEe,cAAjB,EAAkC;AACjC/I,qBAAO,CACL3K,IADF,CACQ,kBADR,EAEEA,IAFF,CAEQ,QAFR,EAGEiG,IAHF,CAGQ,SAHR,EAGmB,KAHnB,EAIEC,MAJF,GAKE9I,OALF,CAKW,cALX,EAMEM,WANF,CAMe,QANf;AAOA;;AAED,gBAAKiV,UAAL,aAAKA,UAAL,eAAKA,UAAU,CAAEc,aAAjB,EAAiC;AAChC9I,qBAAO,CACL3K,IADF,CACQ,iBADR,EAEEA,IAFF,CAEQ,QAFR,EAGEiG,IAHF,CAGQ,SAHR,EAGmB,KAHnB,EAIEC,MAJF,GAKE9I,OALF,CAKW,cALX,EAMEM,WANF,CAMe,QANf;AAOA;;AAED,gBAAKiV,UAAL,aAAKA,UAAL,eAAKA,UAAU,CAAEgB,eAAjB,EAAmC;AAClChJ,qBAAO,CACL3K,IADF,CACQ,kBADR,EAEEA,IAFF,CAEQ,QAFR,EAGEiG,IAHF,CAGQ,SAHR,EAGmB,KAHnB,EAIEC,MAJF,GAKE9I,OALF,CAKW,cALX,EAMEM,WANF,CAMe,QANf;AAOA;AACD;;AACD;;AACD;AACC+N,gBAAM,CAAC/N,WAAP,CAAoB,QAApB;AACA;AA3MF;;AA8MA,WAAKkO,aAAL,GAAqB,KAArB;;AAEA,UAAKgG,QAAL,EAAgB;AACf,aAAKlL,MAAL;AACA;AACD,K,CAED;;;;WACA,8BAAsBkL,QAAtB,EAAiC;AAChC,UAAM1T,IAAI,GAAG,IAAb;AAAA,UACC6S,QAAQ,GAAG,KAAKrG,UAAL,EADZ;AAGAqG,cAAQ,CAACpP,IAAT,CAAe,YAAY;AAC1B,YAAMgJ,OAAO,GAAG3P,CAAC,CAAE,IAAF,CAAjB;AAEAkD,YAAI,CAACsW,gBAAL,CAAuB7J,OAAvB;AACA,OAJD;AAMA,WAAKiB,aAAL,GAAqB,KAArB;;AAEA,UAAKgG,QAAL,EAAgB;AACf,aAAKlL,MAAL;AACA;AACD,K,CAED;;;;WACA,sCAA8BiM,UAA9B,EAA0Cf,QAA1C,EAAqD;AACpD,UAAM1T,IAAI,GAAG,IAAb;AAAA,UACC6S,QAAQ,GAAG,KAAK+D,sBAAL,CAA6BnC,UAA7B,CADZ;;AAGA,UAAK,CAAE5B,QAAQ,CAACvT,MAAhB,EAAyB;AACxB;AACA;;AAEDuT,cAAQ,CAACpP,IAAT,CAAe,YAAY;AAC1B,YAAMgJ,OAAO,GAAG3P,CAAC,CAAE,IAAF,CAAjB;AAEAkD,YAAI,CAACsW,gBAAL,CAAuB7J,OAAvB,EAAgCgI,UAAhC,EAA4Cf,QAA5C;AACA,OAJD;AAKA,K,CAED;;;;WACA,qBAAY;AAAA;;AACX,UAAK,CAAE,KAAKzG,QAAZ,EAAuB;AACtB;AACA;;AAED,UAAKrP,oBAAoB,CAACiZ,qBAA1B,EAAkD;AACjD,aAAKC,oBAAL;AACA,OAFD,MAEO;AACN,aAAKC,qBAAL;AACA;;AAEDja,OAAC,CAAE,MAAF,CAAD,CACEiS,GADF,CACO,UADP,EACmB,QADnB,EAEExP,QAFF,CAEY,6BAFZ;AAIA,WAAKqC,OAAL,CAAa0H,IAAb;AAEA6G,gBAAU,CAAE,YAAM;AACjB,eAAI,CAACvO,OAAL,CAAarC,QAAb,CAAuB,MAAvB;AACA,OAFS,EAEP,GAFO,CAAV;AAGA,K,CAED;;;;WACA,sBAAa;AAAA;;AACZ,UAAK,CAAE,KAAK0N,QAAZ,EAAuB;AACtB;AACA;;AAED,WAAKrL,OAAL,CAAapC,WAAb,CAA0B,MAA1B;AAEA2Q,gBAAU,CAAE,YAAM;AACjB,eAAI,CAACvO,OAAL,CAAa2D,IAAb;;AACAzI,SAAC,CAAE,MAAF,CAAD,CACEiS,GADF,CACO,UADP,EACmB,MADnB,EAEEvP,WAFF,CAEe,6BAFf;AAGA,OALS,EAKP,GALO,CAAV;AAMA,K,CAED;;;;WACA,qBAAawX,KAAb,EAAqB;AACpB,UAAK,gBAAgB,OAAOC,UAA5B,EAAyC;AACxCD,aAAK,GAAGC,UAAU,CAACC,WAAX,CAAwBF,KAAxB,EAA+B;AACtCG,gBAAM,EAAEvZ,oBAAoB,CAACwZ,eAArB,CAAqCD,MADP;AAEtCE,iBAAO,EAAEzZ,oBAAoB,CAACwZ,eAArB,CAAqCC,OAFR;AAGtCC,kBAAQ,EAAE1Z,oBAAoB,CAACwZ,eAArB,CAAqCE,QAHT;AAItCC,mBAAS,EAAE,CAJ2B;AAKtCC,gBAAM,EAAE5Z,oBAAoB,CAACwZ,eAArB,CAAqCI;AALP,SAA/B,CAAR;AAOA;;AAED,aAAOR,KAAP;AACA,K,CAED;;;;WACA,yBAAiBS,IAAjB,EAAuBC,IAAvB,EAA6BjL,OAA7B,EAAuC;AACtC;AACA,WAAM,IAAM1E,IAAZ,IAAoB2P,IAApB,EAA2B;AAC1B,YAAK,CAAEA,IAAI,CAACC,cAAL,CAAqB5P,IAArB,CAAP,EAAqC;AACpC;AACA;;AAED,YAAK,CAAC,CAAE0P,IAAI,CAAE1P,IAAF,CAAZ,EAAuB;AACtB,kBAASA,IAAT;AACC,iBAAK,eAAL;AACA,iBAAK,WAAL;AACA,iBAAK,WAAL;AACA,iBAAK,eAAL;AACA,iBAAK,gBAAL;AACA,iBAAK,SAAL;AACC;AACA0P,kBAAI,CAAE1P,IAAF,CAAJ,GAAe2P,IAAI,CAAE3P,IAAF,CAAnB;AACA;;AACD;AACC,kBAAK,MAAMA,IAAI,CAAC9G,OAAL,CAAc,aAAd,CAAX,EAA2C;AAC1C;AACAwW,oBAAI,CAAE1P,IAAF,CAAJ,GAAe2P,IAAI,CAAE3P,IAAF,CAAnB;AACA,eAHD,MAGO;AACN;AACA,oBAAM+M,MAAM,GAAG,MAAM/M,IAAI,CAAC9G,OAAL,CAAc,SAAd,CAArB;AAAA,oBACCiU,IAAI,GAAGJ,MAAM,GAAG,GAAH,GAAS,GADvB;AAGA,oBAAI8C,QAAQ,GACXH,IAAI,CAAE1P,IAAF,CAAJ,CAAa5H,OAAb,CAAsB,GAAtB,EAA2B+U,IAA3B,IACAA,IADA,GAEAwC,IAAI,CAAE3P,IAAF,CAAJ,CAAa5H,OAAb,CAAsB,GAAtB,EAA2B+U,IAA3B,CAHD;AAKA0C,wBAAQ,GAAGA,QAAQ,CACjBrX,KADS,CACF2U,IADE,EAET1M,MAFS,CAGT,UAAElB,KAAF,EAASuQ,KAAT,EAAgBC,GAAhB;AAAA,yBACCA,GAAG,CAAC7W,OAAJ,CAAaqG,KAAb,MAAyBuQ,KAD1B;AAAA,iBAHS,EAMThM,IANS,CAMHqJ,IANG,CAAX;AAQAuC,oBAAI,CAAE1P,IAAF,CAAJ,GAAe6P,QAAf;;AAEA,oBAAK9C,MAAL,EAAc;AACb,sBAAMiD,cAAc,GAAGhQ,IAAI,CAAC5H,OAAL,CACtB,SADsB,EAEtB,aAFsB,CAAvB;AAKAsX,sBAAI,CAAEM,cAAF,CAAJ,GAAyB,KAAzB;AACAL,sBAAI,CAAEK,cAAF,CAAJ,GAAyB,KAAzB;AACA;AACD;;AA3CH;;AA8CA,iBAAOL,IAAI,CAAE3P,IAAF,CAAX;AACA;AACD;;AAEDjL,cAAA,CAAU2a,IAAV,EAAgBC,IAAhB;AAEA,aAAOD,IAAP;AACA;;;;;;;;AC//CW;AAEb;;AAEA;AACA;AACA;AAEA1a,MAAM,CAAE,UAAWD,CAAX,EAAe;AACtBA,GAAC,CAAEQ,QAAF,CAAD,CACE2B,EADF,CAEE,yEAFF,EAGE,YAAY;AACXnC,KAAC,CAAE,oBAAF,CAAD,CACE0Q,GADF,CACO,WADP,EAEE/J,IAFF,CAEQ,YAAY;AAClB,UAAI0I,gBAAJ,CAAsBrP,CAAC,CAAE,IAAF,CAAvB;AACA,KAJF;AAMAA,KAAC,CAAE,0BAAF,CAAD,CACE0Q,GADF,CACO,WADP,EAEE/J,IAFF,CAEQ,YAAY;AAClB,UAAIL,sBAAJ,CAA4BtG,CAAC,CAAE,IAAF,CAA7B;AACA,KAJF;AAKA,GAfH,EAiBEkC,OAjBF,CAiBW,2BAjBX;AAmBAgZ,YAAU,CAACvE,cAAX,GAA4B,IAAIzW,gBAAJ,EAA5B;AACA,CArBK,CAAN,C", "file": "yith-wcan-shortcodes.js", "sourcesContent": ["'use strict';\n\n/* global globalThis, jQuery, yith_wcan_shortcodes, accounting */\n\nconst $ = jQuery; // we can do this as WebPack will compact all together inside a closure.\n\nexport { $ };\n", "'use strict';\n\n/* global globalThis, jQuery, yith_wcan_shortcodes, accounting */\n\nimport { $ } from '../config.js';\n\nexport default class YITH_WCAN_Filter {\n\t// currently executing xhr\n\txhr = null;\n\n\t// flag set during ajax call handling\n\tdoingAjax = false;\n\n\t// register original url search param\n\toriginalSearch = location.search;\n\n\t// flag set once init has executed\n\tinitialized = false;\n\n\t// init object\n\tconstructor() {\n\t\tconst head = $( 'head' ).html(),\n\t\t\tpageTitle = document.title,\n\t\t\talternativeUrl = this.searchAlternativeUrl( head );\n\n\t\talternativeUrl &&\n\t\t\t! this.doingAjax &&\n\t\t\t! this.initialized &&\n\t\t\t! yith_wcan_shortcodes.ajax_filters &&\n\t\t\tthis.pushUrlToHistory( alternativeUrl, pageTitle );\n\n\t\tthis.initialized = true;\n\t}\n\n\t// execute call to filter products in current view\n\tdoFilter( filters, target, preset ) {\n\t\tlet targetUrl,\n\t\t\t$target = target ? $( target ) : $( 'body' ),\n\t\t\tcustomFilters;\n\n\t\t// filter properties\n\t\tcustomFilters = $(\n\t\t\tdocument\n\t\t).triggerHandler( 'yith_wcan_filters_parameters', [ filters ] );\n\n\t\tif ( !! customFilters ) {\n\t\t\tfilters = customFilters;\n\t\t}\n\n\t\t// block elements before filtering\n\t\t$target && this.block( $target );\n\n\t\t// calculate target url\n\t\ttargetUrl = this.buildUrl( filters );\n\n\t\t// if no ajax, simply change page url\n\t\tif ( ! yith_wcan_shortcodes.ajax_filters ) {\n\t\t\twindow.location = targetUrl;\n\t\t\treturn;\n\t\t}\n\n\t\t// start doing ajax\n\t\tthis.doingAjax = true;\n\n\t\treturn this._doAjax( targetUrl ).done( ( response ) => {\n\t\t\ttargetUrl = this.searchAlternativeUrl( response, targetUrl );\n\n\t\t\tthis._beforeFilter( response, filters );\n\n\t\t\tthis.refreshFragments( target, preset, response );\n\t\t\tthis.pushUrlToHistory( targetUrl, response.pageTitle );\n\n\t\t\t$target && this.unblock( $target );\n\n\t\t\tthis._afterFilter( response, filters );\n\n\t\t\tthis.doingAjax = false;\n\t\t} );\n\t}\n\n\t// actions performed before filter\n\t_beforeFilter( response, filters ) {\n\t\t$( document ).trigger( 'yith-wcan-ajax-loading', [\n\t\t\tresponse,\n\t\t\tfilters,\n\t\t] );\n\t}\n\n\t// actions performed after filter\n\t_afterFilter( response, filters ) {\n\t\t$( '.woocommerce-ordering' ).on(\n\t\t\t'change',\n\t\t\t'select.orderby',\n\t\t\tfunction () {\n\t\t\t\t$( this ).closest( 'form' ).submit();\n\t\t\t}\n\t\t);\n\n\t\tif ( filters && !! Object.keys( filters ).length ) {\n\t\t\t$( 'body' ).addClass( 'filtered' );\n\t\t} else {\n\t\t\t$( 'body' ).removeClass( 'filtered' );\n\t\t}\n\n\t\t$( window ).trigger( 'scroll' );\n\n\t\t$( document )\n\t\t\t.trigger( 'yith-wcan-ajax-filtered', [ response, filters ] )\n\t\t\t.trigger( 'yith_wcwl_reload_fragments' );\n\t}\n\n\t// build url to show\n\tbuildUrl( filters ) {\n\t\tlet queryParam = yith_wcan_shortcodes.query_param,\n\t\t\tparams = {},\n\t\t\tlocation = window.location,\n\t\t\turl = !! yith_wcan_shortcodes.base_url\n\t\t\t\t? yith_wcan_shortcodes.base_url\n\t\t\t\t: location?.origin + location?.pathname,\n\t\t\tsearch = '',\n\t\t\tself = this;\n\n\t\tconst haveFilters =\n\t\t\ttypeof filters === 'object' && Object.keys( filters ).length;\n\n\t\t// remove filter session from current url, if any\n\t\tif ( !! yith_wcan_shortcodes.session_param ) {\n\t\t\turl = url.replace(\n\t\t\t\tnew RegExp(\n\t\t\t\t\t'/' + yith_wcan_shortcodes.session_param + '/[^/]*/'\n\t\t\t\t),\n\t\t\t\t''\n\t\t\t);\n\t\t}\n\n\t\tif ( haveFilters ) {\n\t\t\tparams[ queryParam ] = 1;\n\t\t}\n\n\t\tif ( !! this.originalSearch ) {\n\t\t\tconst searchParams = this.originalSearch\n\t\t\t\t.replace( '?', '' )\n\t\t\t\t.split( '&' )\n\t\t\t\t.reduce( ( a, v ) => {\n\t\t\t\t\tconst items = v.split( '=' );\n\n\t\t\t\t\tif ( items.length === 2 ) {\n\t\t\t\t\t\tif ( this.isFilterParam( items[ 0 ] ) ) {\n\t\t\t\t\t\t\treturn a;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\ta[ items[ 0 ] ] = items[ 1 ];\n\t\t\t\t\t}\n\n\t\t\t\t\treturn a;\n\t\t\t\t}, {} );\n\n\t\t\tparams = $.extend( params, searchParams );\n\t\t}\n\n\t\tif ( haveFilters ) {\n\t\t\tparams = $.extend( params, filters );\n\t\t}\n\n\t\tsearch = Object.keys( params )\n\t\t\t.reduce( function ( a, i ) {\n\t\t\t\tconst v = params[ i ];\n\n\t\t\t\tif ( ! v || ! i ) {\n\t\t\t\t\treturn a;\n\t\t\t\t}\n\n\t\t\t\ta += self._cleanParam( i ) + '=' + self._cleanParam( v ) + '&';\n\n\t\t\t\treturn a;\n\t\t\t}, '?' )\n\t\t\t.replace( /&$/g, '' )\n\t\t\t.replace( /%2B/g, '+' )\n\t\t\t.replace( /%2C/g, ',' );\n\n\t\tif ( search.length > 1 ) {\n\t\t\turl += search;\n\t\t}\n\n\t\treturn url;\n\t}\n\n\t// retrieves alternative sharing url in response body\n\tsearchAlternativeUrl( response, defaultUrl = '' ) {\n\t\tlet url = defaultUrl,\n\t\t\tmatches;\n\n\t\tif ( -1 === response.indexOf( 'yith_wcan:sharing_url' ) ) {\n\t\t\treturn url;\n\t\t}\n\n\t\tmatches = response.match(\n\t\t\t/<meta name=\"yith_wcan:sharing_url\" content=\"([^\"]*)\">/\n\t\t);\n\t\turl = matches && 1 in matches ? matches[ 1 ] : url;\n\n\t\treturn url;\n\t}\n\n\t// push url to browser history\n\tpushUrlToHistory( url, title ) {\n\t\tif (\n\t\t\t! yith_wcan_shortcodes.change_browser_url ||\n\t\t\tnavigator.userAgent.match( /msie/i )\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\twindow.history.pushState(\n\t\t\t{\n\t\t\t\tpageTitle: title,\n\t\t\t},\n\t\t\t'',\n\t\t\turl\n\t\t);\n\t}\n\n\t// replaces elements in the page with refreshed ones\n\trefreshFragments( target, preset, response ) {\n\t\tconst responseDom = document.createElement( 'html' ),\n\t\t\t$response = $( responseDom );\n\n\t\tresponseDom.innerHTML = response;\n\n\t\tif ( target ) {\n\t\t\tlet $preset = $( preset ),\n\t\t\t\t$target = $( target ),\n\t\t\t\t$destination;\n\n\t\t\tif ( $preset.length ) {\n\t\t\t\t$destination = $response.find( preset );\n\n\t\t\t\tif ( $destination.length ) {\n\t\t\t\t\t$preset.replaceWith( $destination.first() );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif ( $target.length ) {\n\t\t\t\t$destination = $response.find( target );\n\n\t\t\t\tif ( $destination.length ) {\n\t\t\t\t\t$target.replaceWith( $destination.first() );\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tconst content = $( yith_wcan_shortcodes.content );\n\n\t\t\tif ( content.length ) {\n\t\t\t\tcontent.replaceWith(\n\t\t\t\t\t$response.find( yith_wcan_shortcodes.content )\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\t$( 'body' ).replaceWith( $response.find( 'body' ) );\n\t\t\t}\n\t\t}\n\n\t\t$( document ).trigger( 'yith_wcan_init_shortcodes' );\n\t}\n\n\t// clean url parameters\n\t_cleanParam( param ) {\n\t\tif (\n\t\t\t! yith_wcan_shortcodes?.process_sanitize ||\n\t\t\tyith_wcan_shortcodes?.skip_sanitize\n\t\t) {\n\t\t\treturn param;\n\t\t}\n\n\t\treturn encodeURIComponent( param );\n\t}\n\n\t// executes Ajax calls\n\t_doAjax( url, params ) {\n\t\tif ( this.xhr ) {\n\t\t\tthis.xhr.abort();\n\t\t}\n\n\t\tparams = $.extend(\n\t\t\t{\n\t\t\t\turl,\n\t\t\t\theaders: {\n\t\t\t\t\t'X-YITH-WCAN': 1,\n\t\t\t\t},\n\t\t\t},\n\t\t\tparams\n\t\t);\n\n\t\tthis.xhr = $.ajax( params );\n\n\t\treturn this.xhr;\n\t}\n\n\t// block dom elements\n\tblock( $el ) {\n\t\tif ( typeof $.fn.block === 'undefined' ) {\n\t\t\treturn;\n\t\t}\n\n\t\tlet background = '#fff center center no-repeat';\n\n\t\tif ( yith_wcan_shortcodes?.loader ) {\n\t\t\tbackground = `url('${ yith_wcan_shortcodes.loader }') ${ background }`;\n\t\t}\n\n\t\t$el.block( {\n\t\t\tmessage: null,\n\t\t\toverlayCSS: {\n\t\t\t\tbackground,\n\t\t\t\topacity: 0.7,\n\t\t\t},\n\t\t} );\n\t}\n\n\t// unblock dom elements\n\tunblock( $el ) {\n\t\tif ( typeof $.fn.unblock === 'undefined' ) {\n\t\t\treturn;\n\t\t}\n\n\t\t$el.unblock();\n\t}\n\n\t// checks if param is one used by layared nav to filter products.\n\tisFilterParam( param ) {\n\t\tlet supportedParams = [\n\t\t\t\t'rating_filter',\n\t\t\t\t'min_price',\n\t\t\t\t'max_price',\n\t\t\t\t'price_ranges',\n\t\t\t\t'onsale_filter',\n\t\t\t\t'instock_filter',\n\t\t\t\t'featured_filter',\n\t\t\t\t'orderby',\n\t\t\t\t'product-page',\n\t\t\t\tyith_wcan_shortcodes.query_param,\n\t\t\t],\n\t\t\tcustomParams;\n\n\t\t// filter properties\n\t\tcustomParams = $(\n\t\t\tdocument\n\t\t).triggerHandler( 'yith_wcan_supported_filters_parameters', [\n\t\t\tsupportedParams,\n\t\t] );\n\n\t\tif ( !! customParams ) {\n\t\t\tsupportedParams = customParams;\n\t\t}\n\n\t\tsupportedParams = supportedParams.concat(\n\t\t\tyith_wcan_shortcodes.supported_taxonomies.map( ( i ) =>\n\t\t\t\ti.replace( 'pa_', 'filter_' )\n\t\t\t)\n\t\t);\n\n\t\tif ( -1 !== supportedParams.indexOf( param ) ) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif ( -1 !== param.indexOf( 'filter_' ) ) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif ( -1 !== param.indexOf( 'query_type_' ) ) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t}\n}\n", "'use strict';\n\n/* global globalThis, jQuery, yith_wcan_shortcodes, accounting */\n\nimport { $ } from '../config.js';\n\nexport default class YITH_WCAN_Reset_Button {\n\t// current button\n\t$reset = null;\n\n\t// init object\n\tconstructor( el ) {\n\t\t// current button\n\t\tthis.$reset = el;\n\n\t\tthis.$reset.on( 'click', function ( ev ) {\n\t\t\tev.preventDefault();\n\n\t\t\t$( '.yith-wcan-filters' ).each( function () {\n\t\t\t\tconst preset = $( this ).data( 'preset' );\n\n\t\t\t\tpreset.deactivateAllFilters( true );\n\t\t\t\tpreset.closeModal();\n\t\t\t} );\n\t\t} );\n\n\t\tthis.$reset.data( 'reset', this ).addClass( 'enhanced' );\n\t}\n}\n", "'use strict';\n\n/* global globalThis, jQuery, yith_wcan_shortcodes, accounting */\n\nimport { $ } from '../config.js';\n\nexport default class YITH_WCAN_Dropdown {\n\t// current button\n\t$originalSelect = null;\n\n\t// main element\n\t$_main = null;\n\n\t// label element\n\t$_label = null;\n\n\t// dropdown\n\t$_dropdown = null;\n\n\t// search input\n\t$_search = null;\n\n\t// show more link\n\t$_showMore = null;\n\n\t// items list\n\t$_items = null;\n\n\t// current page\n\tcurrentPage = 1;\n\n\t// options\n\toptions = {};\n\n\t// init object\n\tconstructor( el, opts ) {\n\t\tthis.$originalSelect = el;\n\n\t\tif ( ! this.$originalSelect.is( 'select' ) ) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst defaultPerPage = this.$originalSelect.data( 'per_page' ),\n\t\t\tdefaultOrder = this.$originalSelect.data( 'order' ),\n\t\t\tdefaultAll = this.$originalSelect.data( 'all-label' ),\n\t\t\tdefaults = {\n\t\t\t\tshowSearch: this.$originalSelect.data( 'show_search' ),\n\t\t\t\tpaginate: this.$originalSelect.data( 'paginate' ),\n\t\t\t\tperPage: defaultPerPage ? defaultPerPage : 10,\n\t\t\t\torder: defaultOrder ? defaultOrder : 'ASC',\n\t\t\t\tgetElements: null,\n\t\t\t\tlabels: {\n\t\t\t\t\temptyLabel: defaultAll\n\t\t\t\t\t\t? defaultAll\n\t\t\t\t\t\t: yith_wcan_shortcodes.labels.empty_option,\n\t\t\t\t\tsearchPlaceholder:\n\t\t\t\t\t\tyith_wcan_shortcodes.labels.search_placeholder,\n\t\t\t\t\tnoItemsFound: yith_wcan_shortcodes.labels.no_items,\n\t\t\t\t\tshowMore: yith_wcan_shortcodes.labels.show_more,\n\t\t\t\t},\n\t\t\t};\n\n\t\tthis.options = $.extend( defaults, opts );\n\n\t\tthis._hideSelect();\n\t\tthis._initTemplate();\n\t\tthis._initActions();\n\n\t\tthis.$originalSelect.data( 'dropdown', this ).addClass( 'enhanced' );\n\t}\n\n\t// hide select\n\t_hideSelect() {\n\t\tthis.$originalSelect.hide();\n\t}\n\n\t// create dropdown\n\t_initTemplate() {\n\t\tconst $mainSpan = $( '<div/>', {\n\t\t\t\tclass: 'yith-wcan-dropdown closed',\n\t\t\t} ),\n\t\t\t$labelSpan = $( '<div/>', {\n\t\t\t\tclass: 'dropdown-label',\n\t\t\t\thtml: this.getLabel(),\n\t\t\t} ),\n\t\t\t$dropdownSpan = $( '<div>', {\n\t\t\t\tclass: 'dropdown-wrapper',\n\t\t\t} ),\n\t\t\t$matchingItemsList = $( '<ul/>', {\n\t\t\t\tclass: 'matching-items filter-items',\n\t\t\t} );\n\n\t\t$dropdownSpan.append( $matchingItemsList );\n\t\t$mainSpan.append( $labelSpan ).append( $dropdownSpan );\n\n\t\tif ( this.options.showSearch ) {\n\t\t\tthis._initSearchTemplate( $dropdownSpan );\n\t\t}\n\n\t\tif ( this.options.paginate ) {\n\t\t\tthis._initShowMoreTemplate( $dropdownSpan );\n\t\t}\n\n\t\tthis.$originalSelect.after( $mainSpan );\n\t\tthis.$_main = $mainSpan;\n\t\tthis.$_label = $labelSpan;\n\t\tthis.$_dropdown = $dropdownSpan;\n\t\tthis.$_items = $matchingItemsList;\n\t}\n\n\t// create search field\n\t_initSearchTemplate( $dropdwonSpan ) {\n\t\tconst $container = $( '<div/>', {\n\t\t\t\tclass: 'search-field-container',\n\t\t\t} ),\n\t\t\t$search = $( '<input/>', {\n\t\t\t\tname: 's',\n\t\t\t\tclass: 'search-field',\n\t\t\t\ttype: 'search',\n\t\t\t\tplaceholder: this.options.labels.searchPlaceholder,\n\t\t\t} );\n\n\t\t$container.append( $search ).prependTo( $dropdwonSpan );\n\t\tthis.$_search = $search;\n\t}\n\n\t// create showMore field\n\t_initShowMoreTemplate( $dropdwonSpan ) {\n\t\tconst $showMore = $( '<a/>', {\n\t\t\tclass: 'show-more',\n\t\t\ttext: this.options.labels.showMore.replace(\n\t\t\t\t'%d',\n\t\t\t\tthis.options.perPage\n\t\t\t),\n\t\t} );\n\n\t\t$showMore.on( 'click', this.loadNextPage.bind( this ) ).hide();\n\n\t\t$dropdwonSpan.append( $showMore );\n\t\tthis.$_showMore = $showMore;\n\t}\n\n\t// init actions performed over dropdown elements\n\t_initActions() {\n\t\tconst self = this;\n\n\t\t// main open event\n\t\tthis.$_main?.on( 'click', ( ev ) => {\n\t\t\tev.stopPropagation();\n\t\t\tself.toggleDropdown();\n\t\t} );\n\t\tthis.$_dropdown.on( 'click', ( ev ) => {\n\t\t\tev.stopPropagation();\n\t\t} );\n\n\t\t// search event\n\t\tthis.$_search?.on( 'keyup search', () => {\n\t\t\tself._populateItems();\n\t\t} );\n\n\t\t// select event\n\t\tthis.$_items.on( 'change', ':input', function () {\n\t\t\tlet $li = $( this ).closest( 'li' ),\n\t\t\t\tvalue = $li.data( 'value' ),\n\t\t\t\tisActive = false;\n\n\t\t\tif (\n\t\t\t\t$li.hasClass( 'disabled' ) &&\n\t\t\t\t! self.isValueSelected( value )\n\t\t\t) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t$li.toggleClass( 'active' );\n\t\t\tisActive = $li.hasClass( 'active' );\n\n\t\t\tself._changeItemStatus( value, isActive );\n\t\t} );\n\t\tthis.$_items.on( 'click', 'li:not(.checkbox) a', function ( ev ) {\n\t\t\tlet $li = $( this ).closest( 'li' ),\n\t\t\t\tvalue = $li.data( 'value' ),\n\t\t\t\tisActive = false;\n\n\t\t\tev.preventDefault();\n\n\t\t\tif (\n\t\t\t\t$li.hasClass( 'disabled' ) &&\n\t\t\t\t! self.isValueSelected( value )\n\t\t\t) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t$li.toggleClass( 'active' );\n\t\t\tisActive = $li.hasClass( 'active' );\n\n\t\t\tif ( isActive ) {\n\t\t\t\t$li.siblings().removeClass( 'active' );\n\t\t\t}\n\n\t\t\tself._changeItemStatus( value, isActive );\n\t\t} );\n\t\tthis.$_items.on( 'click', 'label > a', function ( ev ) {\n\t\t\tconst input = $( this ).parent().find( ':input' );\n\n\t\t\tev.preventDefault();\n\n\t\t\tif (\n\t\t\t\tinput.is( '[type=\"radio\"]' ) ||\n\t\t\t\tinput.is( '[type=\"checkbox\"]' )\n\t\t\t) {\n\t\t\t\tinput.prop( 'checked', ! input.prop( 'checked' ) );\n\t\t\t}\n\n\t\t\tinput.change();\n\t\t} );\n\n\t\t// select change\n\t\tthis.$originalSelect.on( 'change', ( ev, selfOriginated ) => {\n\t\t\tif ( selfOriginated ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tself.$_items.find( 'li' ).each( function () {\n\t\t\t\tconst value = $( this ).data( 'value' );\n\n\t\t\t\tif ( self.isValueSelected( value ) ) {\n\t\t\t\t\tself._selectItem( value );\n\t\t\t\t} else {\n\t\t\t\t\tself._deselectItem( value );\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\tself.updateLabel();\n\t\t} );\n\n\t\t// close dropdown on external click\n\t\t$( document ).on( 'click', this.closeDropdown.bind( this ) );\n\t}\n\n\t// open dropdown\n\topenDropdown() {\n\t\tthis.$_main?.addClass( 'open' ).removeClass( 'closed' );\n\t\tthis._afterDropdownOpen();\n\t}\n\n\t// close dropdown\n\tcloseDropdown() {\n\t\tthis.$_main?.removeClass( 'open' ).addClass( 'closed' );\n\t}\n\n\t// close other dropdowns\n\t_closeOtherDropdowns() {\n\t\tconst self = this,\n\t\t\tdropdowns = $( document )\n\t\t\t\t.find( 'select.enhanced' )\n\t\t\t\t.filter( function ( i, select ) {\n\t\t\t\t\tconst $el = $( select );\n\n\t\t\t\t\treturn (\n\t\t\t\t\t\t!! $el.data( 'dropdown' ) &&\n\t\t\t\t\t\t! $el.is( self.$originalSelect )\n\t\t\t\t\t);\n\t\t\t\t} );\n\n\t\tdropdowns.each( function () {\n\t\t\t$( this ).data( 'dropdown' ).closeDropdown();\n\t\t} );\n\t}\n\n\t// toggle dropdown\n\ttoggleDropdown() {\n\t\tthis.$_main?.toggleClass( 'open' ).toggleClass( 'closed' );\n\n\t\tif ( this.$_main?.hasClass( 'open' ) ) {\n\t\t\tthis._afterDropdownOpen();\n\t\t}\n\t}\n\n\t// perform operations after dropdown is open\n\t_afterDropdownOpen() {\n\t\tthis._closeOtherDropdowns();\n\n\t\tif ( this.$_search?.length ) {\n\t\t\tthis.$_search.val( '' );\n\t\t}\n\n\t\tthis._populateItems();\n\t}\n\n\t// get elements\n\tgetMatchingElements( search, limit ) {\n\t\tlet matchingElements = [],\n\t\t\t$options = this.getOptions(),\n\t\t\tpromise;\n\n\t\tpromise = new Promise( ( resolve ) => {\n\t\t\t// first of all, search across select option\n\t\t\t$options.each( function () {\n\t\t\t\tconst t = $( this ),\n\t\t\t\t\tvalue = t.val(),\n\t\t\t\t\tlabel = t.html(),\n\t\t\t\t\tregex = new RegExp( '.*' + search + '.*', 'i' ),\n\t\t\t\t\tshow =\n\t\t\t\t\t\t! search || regex.test( value ) || regex.test( label );\n\n\t\t\t\tif ( show ) {\n\t\t\t\t\tmatchingElements.push( {\n\t\t\t\t\t\tvalue,\n\t\t\t\t\t\tlabel,\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// then retrieve additional items\n\t\t\tif ( this.options.getElements ) {\n\t\t\t\t// we're expecting key => value pairs\n\t\t\t\tthis.options\n\t\t\t\t\t.getElements( search )\n\t\t\t\t\t.then( ( retrievedElements ) => {\n\t\t\t\t\t\tif ( retrievedElements ) {\n\t\t\t\t\t\t\t// reformat retrieved array\n\t\t\t\t\t\t\tretrievedElements = retrievedElements.reduce(\n\t\t\t\t\t\t\t\t( a, v, i ) => {\n\t\t\t\t\t\t\t\t\ta.push( { label: i, value: v } );\n\t\t\t\t\t\t\t\t\treturn a;\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t[]\n\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\t// merge found results with options\n\t\t\t\t\t\t\tmatchingElements = $.extend(\n\t\t\t\t\t\t\t\tmatchingElements,\n\t\t\t\t\t\t\t\tretrievedElements\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresolve( this._formatItems( matchingElements, limit ) );\n\t\t\t\t\t} );\n\t\t\t} else {\n\t\t\t\tresolve( this._formatItems( matchingElements, limit ) );\n\t\t\t}\n\t\t} );\n\n\t\treturn promise;\n\t}\n\n\t// format items as key/value pairs for further processing\n\t_formatItems( items, limit ) {\n\t\tlet indexes = [],\n\t\t\thasMore = false;\n\n\t\t// remove duplicates and sort array of results\n\t\titems\n\t\t\t.filter( ( v ) => {\n\t\t\t\tif ( -1 === indexes.indexOf( v.value ) ) {\n\t\t\t\t\tindexes.push( v.value );\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\t\t\t} )\n\t\t\t.sort( ( a, b ) => {\n\t\t\t\tconst order = this.options.order,\n\t\t\t\t\tmod = order === 'ASC' ? 1 : -1;\n\n\t\t\t\tif ( a.value < b.value ) {\n\t\t\t\t\treturn -1 * mod;\n\t\t\t\t} else if ( a.value > b.value ) {\n\t\t\t\t\treturn mod;\n\t\t\t\t}\n\n\t\t\t\treturn 0;\n\t\t\t} );\n\n\t\t// paginate when needed\n\t\tif ( limit ) {\n\t\t\thasMore = limit < Object.keys( items ).length;\n\t\t\titems = items.slice( 0, limit );\n\t\t}\n\n\t\treturn {\n\t\t\titems,\n\t\t\thasMore,\n\t\t};\n\t}\n\n\t// generate item to append to items list\n\t_generateItem( value, label ) {\n\t\tlet active = this.isValueSelected( value ),\n\t\t\toption = this.getOptionByValue( value ),\n\t\t\t$item = $( '<li/>', {\n\t\t\t\t'data-value': value,\n\t\t\t\tclass: option.length ? option.attr( 'class' ) : '',\n\t\t\t} ),\n\t\t\t$anchor;\n\n\t\tif ( option.length ) {\n\t\t\tconst template = option.data( 'template' ),\n\t\t\t\tcount = option.data( 'count' );\n\n\t\t\tlabel = template ? template : label;\n\n\t\t\tif ( !! count ) {\n\t\t\t\tlabel += count;\n\t\t\t}\n\t\t}\n\n\t\t$anchor = $( '<a/>', {\n\t\t\thref: option.length ? option.data( 'filter_url' ) : '#',\n\t\t\thtml: label,\n\t\t\t'data-title': option.length ? option.data( 'title' ) : '',\n\t\t} );\n\n\t\tif ( this.$originalSelect.prop( 'multiple' ) ) {\n\t\t\tconst $checkbox = $( '<input/>', {\n\t\t\t\t\ttype: 'checkbox',\n\t\t\t\t\tvalue,\n\t\t\t\t} ),\n\t\t\t\t$label = $( '<label>' );\n\n\t\t\t$checkbox.prop( 'checked', active );\n\t\t\t$label.prepend( $checkbox ).append( $anchor );\n\t\t\t$item.append( $label ).addClass( 'checkbox' );\n\t\t} else {\n\t\t\t$item.append( $anchor );\n\t\t}\n\n\t\tactive ? $item.addClass( 'active' ) : $item.removeClass( 'active' );\n\n\t\treturn $item;\n\t}\n\n\t// populate items list\n\t_populateItems( page ) {\n\t\tlet search = this.$_search?.length ? this.$_search.val() : '',\n\t\t\tperPage = this.options.paginate ? this.options.perPage : 0,\n\t\t\tlimit;\n\n\t\tpage = page ? parseInt( page ) : 1;\n\t\tlimit = page * perPage;\n\n\t\tthis.getMatchingElements( search, limit ).then( ( resultSet ) => {\n\t\t\tlet matchingItems = resultSet.items,\n\t\t\t\titems = [],\n\t\t\t\thasMore = false;\n\n\t\t\t// remove all previous items\n\t\t\tthis._emptyItems();\n\t\t\tthis._hideLoadMore();\n\n\t\t\tif ( ! matchingItems.length ) {\n\t\t\t\titems.push(\n\t\t\t\t\t$( '<li/>', { text: this.options.labels.noItemsFound } )\n\t\t\t\t);\n\n\t\t\t\tthis.currentPage = 1;\n\t\t\t} else {\n\t\t\t\tfor ( const v of matchingItems ) {\n\t\t\t\t\tif ( v.value === '' ) {\n\t\t\t\t\t\titems.unshift( this._generateItem( v.value, v.label ) );\n\t\t\t\t\t} else {\n\t\t\t\t\t\titems.push( this._generateItem( v.value, v.label ) );\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tthis.currentPage = page;\n\t\t\t\thasMore = resultSet.hasMore;\n\t\t\t}\n\n\t\t\tthis.$_items.append( items );\n\n\t\t\t$( document ).trigger( 'yith_wcan_dropdown_updated' );\n\n\t\t\tif ( hasMore ) {\n\t\t\t\tthis._showLoadMore();\n\t\t\t}\n\t\t} );\n\t}\n\n\t// load next page of items\n\tloadNextPage() {\n\t\tconst page = this.currentPage + 1;\n\n\t\tthis._populateItems( page );\n\t}\n\n\t// set an item as active\n\t_selectItem( value ) {\n\t\treturn this._changeItemStatus( value, true );\n\t}\n\n\t// disable an item\n\t_deselectItem( value ) {\n\t\treturn this._changeItemStatus( value, false );\n\t}\n\n\t// change item status\n\t_changeItemStatus( value, status ) {\n\t\tconst $option = this.$originalSelect.find(\n\t\t\t`option[value=\"${ value }\"]`\n\t\t);\n\n\t\tif ( $option.length ) {\n\t\t\t$option.prop( 'selected', status );\n\n\t\t\tthis.closeDropdown();\n\t\t\tthis.updateLabel();\n\n\t\t\tthis.$originalSelect.trigger( 'change', [ true ] );\n\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t// empty items list\n\t_emptyItems() {\n\t\tthis.$_items.html( '' );\n\t}\n\n\t// show \"Load more\" link\n\t_showLoadMore() {\n\t\tthis.$_showMore.show();\n\t}\n\n\t// hide \"Load more\" link\n\t_hideLoadMore() {\n\t\tthis.$_showMore.hide();\n\t}\n\n\t// returns select label\n\tgetLabel() {\n\t\treturn this.hasSelectedValues()\n\t\t\t? this.getSelectedLabels().join( ', ' )\n\t\t\t: this.options.labels.emptyLabel;\n\t}\n\n\t// update label to match new selection\n\tupdateLabel() {\n\t\tconst label = this.getLabel();\n\n\t\tthis.$_label?.html( label );\n\t}\n\n\t// returns select options\n\tgetOptions() {\n\t\treturn this.$originalSelect.find( 'option' );\n\t}\n\n\t// checks whether select has selected values\n\thasSelectedValues() {\n\t\treturn this.getSelectedOptions().length;\n\t}\n\n\t// checks whether a value is selected\n\tisValueSelected( value ) {\n\t\tconst found = this.getSelectedValues().indexOf( value.toString() );\n\n\t\treturn -1 !== found;\n\t}\n\n\t// retrieve selected options\n\tgetSelectedOptions() {\n\t\treturn this.$originalSelect.find( 'option' ).filter( ':selected' );\n\t}\n\n\t// retrieves an option node by value\n\tgetOptionByValue( value ) {\n\t\treturn this.$originalSelect.find( `option[value=\"${ value }\"]` );\n\t}\n\n\t// retrieve labels for selected options\n\tgetSelectedLabels() {\n\t\tconst labels = [];\n\n\t\tthis.getSelectedOptions().each( function () {\n\t\t\tlet $option = $( this ),\n\t\t\t\ttemplate = $option.data( 'template' );\n\n\t\t\ttemplate = template\n\t\t\t\t? template\n\t\t\t\t: $option.html().replace( /\\([0-9]*\\)/, '' );\n\n\t\t\tlabels.push( template );\n\t\t} );\n\n\t\treturn labels;\n\t}\n\n\t// retrieve values for selected options\n\tgetSelectedValues() {\n\t\tconst values = [];\n\n\t\tthis.getSelectedOptions().each( function () {\n\t\t\tvalues.push( $( this ).val() );\n\t\t} );\n\n\t\treturn values;\n\t}\n\n\tdestroy() {\n\t\t// TBD\n\t}\n}\n", "'use strict';\n\n/* global globalThis, jQuery, yith_wcan_shortcodes, accounting */\n\nimport { $ } from '../config.js';\nimport YITH_WCAN_Dropdown from './yith-wcan-dropdown';\n\nexport default class YITH_WCAN_Preset {\n\t// main preset node\n\tpreset = false;\n\t$preset = false;\n\n\t// target of the filter, if any\n\ttarget = false;\n\t$target = false;\n\n\t// filters node\n\t$filters = false;\n\n\t// filter button\n\t$filterButtons = false;\n\n\t// nodes created just for modal layout\n\tmodalElements = {};\n\n\t// retains current status of filters\n\tactiveFilters = false;\n\n\t// mobile flag\n\tisMobile = false;\n\n\t// slider timeout\n\tsliderTimeout = false;\n\n\t// registers when status has changed\n\toriginalFilters = null;\n\tdirty = false;\n\n\t// init object\n\tconstructor( el ) {\n\t\t// main preset node\n\t\tthis.preset = '#' + el.attr( 'id' );\n\t\tthis.$preset = el;\n\n\t\t// target of the filter, if any\n\t\tthis.target = this.$preset.data( 'target' );\n\t\tthis.$target = this.target ? $( this.target ) : false;\n\n\t\tthis._regiterStatus();\n\t\tthis._initFilterButton();\n\t\tthis._initResponsive();\n\t\tthis._initFilters();\n\t\tthis._initActions();\n\n\t\tthis.$preset\n\t\t\t.data( 'preset', this )\n\t\t\t.addClass( 'enhanced' )\n\t\t\t.trigger( 'yith_wcan_preset_initialized', [ this ] );\n\t}\n\n\t// init filters\n\t_initFilters() {\n\t\tconst self = this;\n\n\t\tthis.getFilters().each( function () {\n\t\t\tconst $filter = $( this );\n\n\t\t\tself._initFilter( $filter );\n\t\t} );\n\n\t\tthis.maybeShowClearAllFilters();\n\t}\n\n\t// init filter button\n\t_initFilterButton() {\n\t\tthis.$filterButtons = this.$preset.find( '.apply-filters' );\n\n\t\tif ( ! this.$filterButtons.length ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// manage filter button\n\t\tthis.$filterButtons\n\t\t\t.on( 'click', ( ev ) => {\n\t\t\t\tev.preventDefault();\n\t\t\t\tthis.filter();\n\t\t\t} )\n\t\t\t.hide();\n\t}\n\n\t// init generic actions\n\t_initActions() {\n\t\tthis.$preset.find( 'form' ).on( 'submit', ( ev ) => {\n\t\t\tev.preventDefault();\n\t\t} );\n\t}\n\n\t// init responsive\n\t_initResponsive() {\n\t\tif ( ! yith_wcan_shortcodes.modal_on_mobile ) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst media = window.matchMedia(\n\t\t\t`(max-width: ${ yith_wcan_shortcodes.mobile_media_query }px)`\n\t\t);\n\n\t\t$( window )\n\t\t\t.on( 'resize', () => {\n\t\t\t\tconst isMobile = !! media.matches;\n\n\t\t\t\tif ( isMobile !== this.isMobile ) {\n\t\t\t\t\tthis.isMobile = isMobile;\n\t\t\t\t\tthis._afterLayoutChange();\n\t\t\t\t}\n\t\t\t} )\n\t\t\t.resize();\n\t}\n\n\t// init filter\n\t_initFilter( $filter ) {\n\t\tconst self = this,\n\t\t\thandleChange = function ( ev ) {\n\t\t\t\tconst t = $( this ),\n\t\t\t\t\t$currentFilter = t.closest( '.yith-wcan-filter' ),\n\t\t\t\t\tmultiple = $currentFilter.length\n\t\t\t\t\t\t? 'yes' === $currentFilter.data( 'multiple' )\n\t\t\t\t\t\t: false,\n\t\t\t\t\t$item = t.closest( '.filter-item' ),\n\t\t\t\t\t$items = $item.length\n\t\t\t\t\t\t? $currentFilter.find( '.filter-item' ).not( $item )\n\t\t\t\t\t\t: [];\n\n\t\t\t\tif ( $item.is( '.disabled' ) && ! $item.is( '.active' ) ) {\n\t\t\t\t\tev.preventDefault();\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tev.preventDefault();\n\n\t\t\t\t$items.length &&\n\t\t\t\t\t! multiple &&\n\t\t\t\t\t$items\n\t\t\t\t\t\t.removeClass( 'active' )\n\t\t\t\t\t\t.children( 'label' )\n\t\t\t\t\t\t.find( ':input' )\n\t\t\t\t\t\t.prop( 'checked', false )\n\t\t\t\t\t\t.parent( '.checked' )\n\t\t\t\t\t\t.removeClass( 'checked' );\n\t\t\t\t$item.length && $item.toggleClass( 'active' );\n\n\t\t\t\t// reset active filters.\n\t\t\t\tself.activeFilters = false;\n\n\t\t\t\tself.maybeFilter( $filter );\n\t\t\t\tself.maybeToggleClearAllFilters();\n\t\t\t\tself.maybeToggleClearFilter( $currentFilter );\n\t\t\t};\n\n\t\t// handle filter activation/deactivation by click on label (no input involved)\n\t\t$filter\n\t\t\t.find( '.filter-item' )\n\t\t\t.not( '.checkbox' )\n\t\t\t.not( '.radio' )\n\t\t\t.on( 'click', 'a', function ( ev ) {\n\t\t\t\tconst t = $( this ),\n\t\t\t\t\t$item = t.closest( '.filter-item' );\n\n\t\t\t\tif ( ! $( ev?.delegateTarget ).is( $item ) ) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\thandleChange.call( this, ev );\n\t\t\t} );\n\n\t\t// handle filter activation/deactivation from input change\n\t\t$filter.find( ':input' ).on( 'change', function ( ev ) {\n\t\t\tconst t = $( this ),\n\t\t\t\t$item = t.closest( '.filter-item' );\n\n\t\t\tif ( $item.is( '.disabled' ) && ! $item.is( '.active' ) ) {\n\t\t\t\tt.prop( 'checked', false );\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\thandleChange.call( this, ev );\n\t\t} );\n\n\t\t// handle filter activation/deactivation by click on label (there is an input whose state can be switched)\n\t\t$filter.find( 'label > a' ).on( 'click', function ( ev ) {\n\t\t\tconst t = $( this ),\n\t\t\t\t$item = t.closest( '.filter-item' );\n\n\t\t\tev.preventDefault();\n\n\t\t\tif ( $item.is( '.disabled' ) && ! $item.is( '.active' ) ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tconst $input = t.parent().find( ':input' );\n\n\t\t\tif (\n\t\t\t\t$input.is( '[type=\"radio\"]' ) ||\n\t\t\t\t$input.is( '[type=\"checkbox\"]' )\n\t\t\t) {\n\t\t\t\t$input.prop( 'checked', ! $input.prop( 'checked' ) );\n\t\t\t}\n\n\t\t\t$input.change();\n\t\t} );\n\n\t\t// init tooltip\n\t\tthis._initTooltip( $filter );\n\n\t\t// init price slider\n\t\tthis._initPriceSlider( $filter );\n\n\t\t// init dropdown\n\t\tthis._initDropdown( $filter );\n\n\t\t// init collapsable\n\t\tthis._initCollapsable( $filter );\n\n\t\t// init clear anchors\n\t\tthis.maybeShowClearFilter( $filter );\n\n\t\t// init custom inputs\n\t\tif ( this.$preset?.hasClass( 'custom-style' ) ) {\n\t\t\tthis._initCustomInput( $filter );\n\t\t}\n\t}\n\n\t// init tooltip\n\t_initTooltip( $filter, position ) {\n\t\t$filter.find( '[data-title]' ).each( function () {\n\t\t\tconst t = $( this );\n\n\t\t\tif ( t.hasClass( 'tooltip-added' ) || ! t.data( 'title' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tt.on( 'mouseenter', function () {\n\t\t\t\tlet th = $( this ),\n\t\t\t\t\ttooltip = null,\n\t\t\t\t\twrapperWidth = th.outerWidth(),\n\t\t\t\t\tleft = 0,\n\t\t\t\t\twidth = 0;\n\n\t\t\t\tif (\n\t\t\t\t\t! position ||\n\t\t\t\t\t( 'top' !== position && 'right' !== position )\n\t\t\t\t) {\n\t\t\t\t\tconst container = th.closest( '.filter-item' );\n\n\t\t\t\t\tposition =\n\t\t\t\t\t\tcontainer.hasClass( 'color' ) ||\n\t\t\t\t\t\tcontainer.hasClass( 'label' )\n\t\t\t\t\t\t\t? 'top'\n\t\t\t\t\t\t\t: 'right';\n\t\t\t\t}\n\n\t\t\t\ttooltip = $( '<span>', {\n\t\t\t\t\tclass: 'yith-wcan-tooltip',\n\t\t\t\t\thtml: th.data( 'title' ),\n\t\t\t\t} );\n\n\t\t\t\tth.append( tooltip );\n\n\t\t\t\twidth = tooltip.outerWidth() + 6;\n\t\t\t\ttooltip.outerWidth( width );\n\n\t\t\t\tif ( 'top' === position ) {\n\t\t\t\t\tleft = ( wrapperWidth - width ) / 2;\n\t\t\t\t} else {\n\t\t\t\t\tleft = wrapperWidth + 15;\n\t\t\t\t}\n\n\t\t\t\ttooltip.css( { left: left.toFixed( 0 ) + 'px' } ).fadeIn( 200 );\n\n\t\t\t\tth.addClass( 'with-tooltip' );\n\t\t\t} ).on( 'mouseleave', function () {\n\t\t\t\tconst th = $( this );\n\n\t\t\t\tth.find( '.yith-wcan-tooltip' ).fadeOut( 200, function () {\n\t\t\t\t\tth.removeClass( 'with-tooltip' )\n\t\t\t\t\t\t.find( '.yith-wcan-tooltip' )\n\t\t\t\t\t\t.remove();\n\t\t\t\t} );\n\t\t\t} );\n\n\t\t\tt.addClass( 'tooltip-added' );\n\t\t} );\n\t}\n\n\t// init dropdown\n\t_initDropdown( $filter ) {\n\t\tconst $dropdown = $filter.find( 'select.filter-dropdown' );\n\n\t\tif ( ! $dropdown.length ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (\n\t\t\t$dropdown.hasClass( 'select2-hidden-accessible' ) &&\n\t\t\t'undefined' !== typeof $.fn.selectWoo\n\t\t) {\n\t\t\t$dropdown.selectWoo( 'destroy' );\n\t\t}\n\n\t\tthis._initDropdownObject( $dropdown, {\n\t\t\tpaginate: true,\n\t\t\tperPage: yith_wcan_shortcodes.terms_per_page,\n\t\t} );\n\t}\n\n\t// init dropdown object\n\t_initDropdownObject( $dropdown, opts ) {\n\t\treturn new YITH_WCAN_Dropdown( $dropdown, opts );\n\t}\n\n\t// init price slider\n\t_initPriceSlider( $filter ) {\n\t\tif ( ! $filter.hasClass( 'filter-price-slider' ) ) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst self = this,\n\t\t\t$container = $filter.find( '.price-slider' ),\n\t\t\t$minInput = $container.find( '.price-slider-min' ),\n\t\t\t$maxInput = $container.find( '.price-slider-max' ),\n\t\t\tmin = parseFloat( $container.data( 'min' ) ),\n\t\t\tmax = parseFloat( $container.data( 'max' ) ),\n\t\t\tcurrentMin = parseFloat( $minInput.val() ),\n\t\t\tcurrentMax = parseFloat( $maxInput.val() ),\n\t\t\tstep = parseFloat( $container.data( 'step' ) ),\n\t\t\thandleSliderChange = function () {\n\t\t\t\tif ( self.sliderTimeout ) {\n\t\t\t\t\tclearTimeout( self.sliderTimeout );\n\t\t\t\t}\n\n\t\t\t\tself.sliderTimeout = setTimeout( () => {\n\t\t\t\t\tself.maybeFilter( $filter );\n\t\t\t\t}, 200 );\n\t\t\t};\n\n\t\t$filter.find( '.price-slider-ui' ).ionRangeSlider( {\n\t\t\tskin: 'round',\n\t\t\ttype: 'double',\n\t\t\tmin,\n\t\t\tmax,\n\t\t\tstep,\n\t\t\tfrom: currentMin,\n\t\t\tto: currentMax,\n\t\t\tmin_interval: step,\n\t\t\tvalues_separator: ' - ',\n\t\t\tprettify: ( v ) => this.formatPrice( v ),\n\t\t\tonChange: ( data ) => {\n\t\t\t\t$minInput.val( data.from );\n\t\t\t\t$maxInput.val( data.to );\n\t\t\t},\n\t\t\tonFinish: handleSliderChange,\n\t\t} );\n\n\t\t$minInput\n\t\t\t.add( $maxInput )\n\t\t\t.off( 'change' )\n\t\t\t.on( 'keyup', () => {\n\t\t\t\tif ( ! $minInput.val() || ! $maxInput.val() ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\thandleSliderChange();\n\t\t\t} );\n\t}\n\n\t// init collapsable\n\t_initCollapsable( $filter ) {\n\t\tthis._initTitleCollapsable( $filter );\n\t\tthis._initHierarchyCollapsable( $filter );\n\t}\n\n\t// init toggle on click of the title\n\t_initTitleCollapsable( $filter ) {\n\t\tconst $title = $filter.find( '.collapsable' );\n\n\t\tif ( ! $title.length ) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._initToggle( $title, $title, $filter.find( '.filter-content' ) );\n\t}\n\n\t// init toggle on click of the parent li\n\t_initHierarchyCollapsable( $filter ) {\n\t\tconst $items = $filter.find( '.hierarchy-collapsable' );\n\n\t\tif ( ! $items.length ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// set parents of currently active term as open\n\t\tconst self = this,\n\t\t\tactive = $filter.find( '.active' );\n\n\t\tif ( active.length ) {\n\t\t\tactive\n\t\t\t\t.parents( '.hierarchy-collapsable' )\n\t\t\t\t.removeClass( 'closed' )\n\t\t\t\t.addClass( 'opened' );\n\n\t\t\tif (\n\t\t\t\tactive.hasClass( 'hierarchy-collapsable' ) &&\n\t\t\t\tyith_wcan_shortcodes.show_current_children\n\t\t\t) {\n\t\t\t\tactive.removeClass( 'closed' ).addClass( 'opened' );\n\t\t\t}\n\t\t}\n\n\t\t$items.each( function () {\n\t\t\tconst $t = $( this ),\n\t\t\t\t$toggle = $( '<span/>', {\n\t\t\t\t\tclass: 'toggle-handle',\n\t\t\t\t} );\n\n\t\t\t$toggle.appendTo( $t );\n\n\t\t\tself._initToggle( $toggle, $t, $t.children( 'ul.filter-items' ) );\n\t\t} );\n\t}\n\n\t// init toggle to generic toggle/target pair\n\t_initToggle( $toggle, $container, $target ) {\n\t\tif ( $container.hasClass( 'closed' ) ) {\n\t\t\t$target.hide();\n\t\t}\n\n\t\t$toggle.off( 'click' ).on( 'click', ( ev ) => {\n\t\t\tev.stopPropagation();\n\t\t\tev.preventDefault();\n\n\t\t\t$target.slideToggle( 400, () => {\n\t\t\t\t$container.toggleClass( 'opened' ).toggleClass( 'closed' );\n\t\t\t} );\n\t\t} );\n\t}\n\n\t// init custom input\n\t_initCustomInput( $filter ) {\n\t\t$filter.find( ':input' ).each( function () {\n\t\t\tlet input = $( this ),\n\t\t\t\ttype = input.attr( 'type' ),\n\t\t\t\tcontainerClass = `${ type }button`,\n\t\t\t\tcontainer;\n\n\t\t\tif ( 'checkbox' !== type && 'radio' !== type ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif ( input.closest( `.${ containerClass }` ).length ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif ( input.is( ':checked' ) ) {\n\t\t\t\tcontainerClass += ' checked';\n\t\t\t}\n\n\t\t\tcontainer = $( '<span/>', {\n\t\t\t\tclass: containerClass,\n\t\t\t} );\n\n\t\t\tinput.wrap( container ).on( 'change', function () {\n\t\t\t\tconst t = $( this );\n\n\t\t\t\tt.prop( 'checked' )\n\t\t\t\t\t? t.parent().addClass( 'checked' )\n\t\t\t\t\t: t.parent().removeClass( 'checked' );\n\t\t\t} );\n\t\t} );\n\t}\n\n\t// register initial status\n\t_regiterStatus() {\n\t\tthis.originalFilters = this.getFiltersProperties();\n\t}\n\n\t// trigger handling after layout change\n\t_afterLayoutChange() {\n\t\tif ( this.isMobile ) {\n\t\t\tthis.$preset\n\t\t\t\t.addClass( 'filters-modal' )\n\t\t\t\t.attr( 'role', 'dialog' )\n\t\t\t\t.attr( 'tabindex', '-1' )\n\t\t\t\t.hide();\n\n\t\t\tthis._addCloseModalButton();\n\t\t\tthis._addApplyFiltersModalButton();\n\t\t\tthis._switchToCollapsables();\n\n\t\t\tthis.$filterButtons?.hide();\n\t\t} else {\n\t\t\tthis.$preset\n\t\t\t\t.removeClass( 'filters-modal' )\n\t\t\t\t.removeClass( 'open' )\n\t\t\t\t.removeAttr( 'role' )\n\t\t\t\t.removeAttr( 'tabindex' )\n\t\t\t\t.show();\n\n\t\t\t$( 'body' )\n\t\t\t\t.css( 'overflow', 'auto' )\n\t\t\t\t.removeClass( 'yith-wcan-preset-modal-open' );\n\n\t\t\tthis._removeCloseModalButton();\n\t\t\tthis._removeApplyFiltersModalButton();\n\t\t\tthis._switchBackCollapsables();\n\n\t\t\tthis.$filterButtons?.show();\n\t\t}\n\t}\n\n\t// add modal close button\n\t_addCloseModalButton() {\n\t\tconst $closeButton = $( '<a/>', {\n\t\t\tclass: 'close-button',\n\t\t\thtml: '&times;',\n\t\t\t'data-dismiss': 'modal',\n\t\t\t'aria-label': yith_wcan_shortcodes.labels.close,\n\t\t} );\n\n\t\t$closeButton\n\t\t\t.prependTo( this.$preset )\n\t\t\t.on( 'click', this.closeModal.bind( this ) );\n\t\tthis.modalElements.closeButton = $closeButton;\n\t}\n\n\t// remove modal close button\n\t_removeCloseModalButton() {\n\t\tthis.modalElements?.closeButton?.remove();\n\t}\n\n\t// show main filter button for the modal\n\t_addApplyFiltersModalButton() {\n\t\tconst $filterButton = $( '<button/>', {\n\t\t\tclass: 'apply-filters main-modal-button',\n\t\t\thtml: yith_wcan_shortcodes.labels.show_results,\n\t\t\t'data-dismiss': 'modal',\n\t\t} );\n\n\t\t$filterButton.appendTo( this.$preset ).on( 'click', () => {\n\t\t\tthis.filter();\n\t\t\tthis.closeModal();\n\t\t} );\n\t\tthis.modalElements.applyFiltersButton = $filterButton;\n\t}\n\n\t// hide main filter button for the modal\n\t_removeApplyFiltersModalButton() {\n\t\tthis.modalElements?.applyFiltersButton?.remove();\n\t}\n\n\t// convert all filters to collapsable\n\t_switchToCollapsables() {\n\t\tconst self = this;\n\n\t\tthis.getFilters().each( function () {\n\t\t\tconst $filter = $( this ),\n\t\t\t\t$title = $filter.find( '.filter-title' );\n\n\t\t\tif ( ! $title.length || $title.hasClass( 'collapsable' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t$title.addClass( 'collapsable' ).data( 'disable-collapse', true );\n\n\t\t\tself._initTitleCollapsable( $filter );\n\t\t} );\n\t}\n\n\t// switch back filters to their previous collapsable state\n\t_switchBackCollapsables() {\n\t\tthis.getFilters().each( function () {\n\t\t\tconst $filter = $( this ),\n\t\t\t\t$title = $filter.find( '.filter-title' );\n\n\t\t\tif (\n\t\t\t\t! $title.length ||\n\t\t\t\t! $title.hasClass( 'collapsable' ) ||\n\t\t\t\t! $title.data( 'disable-collapse' )\n\t\t\t) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t$title\n\t\t\t\t.removeClass( 'collapsable' )\n\t\t\t\t.removeData( 'disable-collapse', true )\n\t\t\t\t.off( 'click' );\n\n\t\t\t$filter.find( '.filter-content' ).show();\n\t\t} );\n\t}\n\n\t// close all collpasable before showing modal\n\t_openAllCollapsables() {\n\t\tthis.$filters\n\t\t\t.not( '.no-title' )\n\t\t\t.not( ( i, v ) => {\n\t\t\t\treturn this.isFilterActive( $( v ) );\n\t\t\t} )\n\t\t\t.find( '.filter-content' )\n\t\t\t.show()\n\t\t\t.end()\n\t\t\t.find( '.filter-title' )\n\t\t\t.removeClass( 'closed' )\n\t\t\t.addClass( 'opened' );\n\t}\n\n\t// close all collpasable before showing modal\n\t_closeAllCollapsables() {\n\t\tthis.$filters\n\t\t\t.not( '.no-title' )\n\t\t\t.not( ( i, v ) => {\n\t\t\t\treturn this.isFilterActive( $( v ) );\n\t\t\t} )\n\t\t\t.find( '.filter-content' )\n\t\t\t.hide()\n\t\t\t.end()\n\t\t\t.find( '.filter-title' )\n\t\t\t.addClass( 'closed' )\n\t\t\t.removeClass( 'opened' );\n\t}\n\n\t// update status change flag, if filters have changed\n\tmaybeRegisterStatusChange() {\n\t\tconst currentFilters = this.getFiltersProperties(),\n\t\t\tcurrentStr = JSON.stringify( currentFilters ),\n\t\t\toriginalStr = JSON.stringify( this.originalFilters );\n\n\t\tthis.dirty = currentStr !== originalStr;\n\t}\n\n\t// apply filters when possible\n\tmaybeFilter( $initiator ) {\n\t\t// register status change\n\t\tthis.maybeRegisterStatusChange();\n\n\t\t// filter, or show filter button.\n\t\tif ( yith_wcan_shortcodes.instant_filters && ! this.isMobile ) {\n\t\t\tthis.filter();\n\t\t} else if (\n\t\t\t! yith_wcan_shortcodes.instant_filters &&\n\t\t\t! this.isMobile\n\t\t) {\n\t\t\tthis.dirty\n\t\t\t\t? this.$filterButtons?.show()\n\t\t\t\t: this.$filterButtons?.hide();\n\t\t} else if ( this.isMobile && this.dirty ) {\n\t\t\tthis.$preset.addClass( 'with-filter-button' );\n\t\t\tthis.modalElements.applyFiltersButton?.show();\n\t\t}\n\t}\n\n\t// main filtering method\n\tfilter() {\n\t\tconst filter = window?.product_filter;\n\n\t\tfilter\n\t\t\t?.doFilter( this.getFiltersProperties(), this.target, this.preset )\n\t\t\t?.done( () => {\n\t\t\t\tlet newPreset = $( this.preset );\n\n\t\t\t\tif (\n\t\t\t\t\t! this.isMobile &&\n\t\t\t\t\tnewPreset.length &&\n\t\t\t\t\tyith_wcan_shortcodes.scroll_top\n\t\t\t\t) {\n\t\t\t\t\tlet targetOffset = newPreset.offset().top;\n\n\t\t\t\t\tif ( !! yith_wcan_shortcodes.scroll_target ) {\n\t\t\t\t\t\tconst scrollTarget = $(\n\t\t\t\t\t\t\tyith_wcan_shortcodes.scroll_target\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\ttargetOffset = scrollTarget.length\n\t\t\t\t\t\t\t? scrollTarget.offset().top\n\t\t\t\t\t\t\t: targetOffset;\n\t\t\t\t\t}\n\t\t\t\t\t$( 'body, html' ).animate( {\n\t\t\t\t\t\tscrollTop: targetOffset - 100,\n\t\t\t\t\t} );\n\t\t\t\t}\n\n\t\t\t\t// register new filters, clear status flag\n\t\t\t\tthis.originalFilters = this.getFiltersProperties();\n\t\t\t\tthis.dirty = false;\n\t\t\t} );\n\n\t\tif ( this.isMobile ) {\n\t\t\tthis.$preset.removeClass( 'with-filter-button' );\n\t\t\tthis.modalElements.applyFiltersButton?.hide();\n\t\t}\n\t}\n\n\t// get all filter nodes\n\tgetFilters() {\n\t\tif ( false === this.$filters ) {\n\t\t\tthis.$filters = this.$preset.find( '.yith-wcan-filter' );\n\t\t}\n\n\t\treturn this.$filters;\n\t}\n\n\t// retrieves all filters that we want to apply\n\tgetActiveFilters() {\n\t\tif ( false === this.activeFilters ) {\n\t\t\tthis.activeFilters = this.getFiltersProperties();\n\t\t}\n\n\t\treturn this.activeFilters;\n\t}\n\n\t// check whether there is any filter active\n\tisAnyFilterActive() {\n\t\treturn !! Object.keys( this.getActiveFilters() ).length;\n\t}\n\n\t// checks whether current filter is active\n\tisFilterActive( $filter ) {\n\t\tlet filterType = $filter.data( 'filter-type' ),\n\t\t\tactive,\n\t\t\tfilteredActive;\n\n\t\tswitch ( filterType ) {\n\t\t\tcase 'tax':\n\t\t\tcase 'review':\n\t\t\tcase 'price_range':\n\t\t\t\tconst $dropdown = $filter.find( '.filter-dropdown' );\n\n\t\t\t\tif ( $dropdown.length ) {\n\t\t\t\t\tconst val = $dropdown.val();\n\n\t\t\t\t\tactive = 'object' === typeof val ? !! val?.length : !! val;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t// if we use type other than dropdown, fallthrough\n\t\t\tcase 'stock_sale':\n\t\t\t\tactive = $filter.find( '.filter-item' ).filter( '.active' )\n\t\t\t\t\t.length;\n\t\t\t\tbreak;\n\t\t\tcase 'price_slider':\n\t\t\t\tconst step = parseFloat(\n\t\t\t\t\t\t$filter.find( '.price-slider' ).data( 'step' )\n\t\t\t\t\t),\n\t\t\t\t\tmin = parseFloat(\n\t\t\t\t\t\t$filter.find( '.price-slider' ).data( 'min' )\n\t\t\t\t\t),\n\t\t\t\t\tmax = parseFloat(\n\t\t\t\t\t\t$filter.find( '.price-slider' ).data( 'max' )\n\t\t\t\t\t),\n\t\t\t\t\tcurrentMin = parseFloat(\n\t\t\t\t\t\t$filter.find( '.price-slider-min' ).val()\n\t\t\t\t\t),\n\t\t\t\t\tcurrentMax = parseFloat(\n\t\t\t\t\t\t$filter.find( '.price-slider-max' ).val()\n\t\t\t\t\t);\n\n\t\t\t\tactive =\n\t\t\t\t\tMath.abs( currentMin - min ) >= step ||\n\t\t\t\t\tMath.abs( currentMax - max ) >= step;\n\t\t\t\tbreak;\n\t\t\tcase 'orderby':\n\t\t\t\tactive =\n\t\t\t\t\t'menu_order' !== $filter.find( '.filter-order-by' ).val();\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tactive = false;\n\t\t\t\tbreak;\n\t\t}\n\n\t\tfilteredActive = $filter.triggerHandler( 'yith_wcan_is_filter_active', [\n\t\t\tactive,\n\t\t\tthis,\n\t\t] );\n\t\tactive =\n\t\t\ttypeof filteredActive !== 'undefined' ? filteredActive : active;\n\n\t\treturn active;\n\t}\n\n\t// count the number of active items per filter\n\tcountActiveItems( $filter ) {\n\t\tlet filterType = $filter.data( 'filter-type' ),\n\t\t\tcount;\n\n\t\tswitch ( filterType ) {\n\t\t\tcase 'tax':\n\t\t\tcase 'review':\n\t\t\tcase 'price_range':\n\t\t\t\tconst $dropdown = $filter.find( '.filter-dropdown' );\n\n\t\t\t\tif ( $dropdown.length ) {\n\t\t\t\t\tconst val = $dropdown.val();\n\n\t\t\t\t\tcount = 'object' === typeof val ? val?.length : +!! val;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t// if we use type other than dropdown, fallthrough\n\t\t\tcase 'stock_sale':\n\t\t\t\tcount = $filter.find( '.filter-items' ).find( '.active' )\n\t\t\t\t\t.length;\n\t\t\t\tbreak;\n\t\t\tcase 'orderby':\n\t\t\t\tif ( this.isFilterActive( $filter ) ) {\n\t\t\t\t\tcount = 1;\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'price_slider':\n\t\t\tdefault:\n\t\t\t\tcount = 0;\n\t\t\t\tbreak;\n\t\t}\n\n\t\treturn count;\n\t}\n\n\t// retrieves filter properties for the filter\n\tgetFilterProperties( $filter ) {\n\t\tlet filterType = $filter.data( 'filter-type' ),\n\t\t\tmultiple = 'yes' === $filter.data( 'multiple' ),\n\t\t\t$dropdown = $filter.find( '.filter-dropdown' ),\n\t\t\tproperties = {},\n\t\t\tfilteredProperties,\n\t\t\t$active;\n\n\t\tswitch ( filterType ) {\n\t\t\tcase 'tax':\n\t\t\t\tlet activeTerms = [],\n\t\t\t\t\ttaxonomy = $filter.data( 'taxonomy' ),\n\t\t\t\t\tisAttr = 0 === taxonomy.indexOf( 'filter' ),\n\t\t\t\t\trelation = $filter.data( 'relation' );\n\n\t\t\t\tif ( $dropdown.length ) {\n\t\t\t\t\tif ( multiple ) {\n\t\t\t\t\t\tactiveTerms = $dropdown.val();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tactiveTerms.push( $dropdown.val() );\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t$active = $filter\n\t\t\t\t\t\t.find( '.filter-item' )\n\t\t\t\t\t\t.filter( '.active' )\n\t\t\t\t\t\t.children( 'a, label' );\n\n\t\t\t\t\tactiveTerms = $active.get().reduce( function ( a, v ) {\n\t\t\t\t\t\tlet val;\n\n\t\t\t\t\t\tv = $( v );\n\t\t\t\t\t\tval = v.is( 'label' )\n\t\t\t\t\t\t\t? v.find( ':input' ).val()\n\t\t\t\t\t\t\t: v.data( 'term-slug' );\n\n\t\t\t\t\t\tif ( ! val ) {\n\t\t\t\t\t\t\treturn a;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\ta.push( val );\n\n\t\t\t\t\t\treturn a;\n\t\t\t\t\t}, activeTerms );\n\t\t\t\t}\n\n\t\t\t\tif ( ! multiple ) {\n\t\t\t\t\tproperties[ taxonomy ] = activeTerms.pop();\n\t\t\t\t} else {\n\t\t\t\t\tconst glue = ! isAttr && 'and' === relation ? '+' : ',';\n\t\t\t\t\tproperties[ taxonomy ] = activeTerms.join( glue );\n\t\t\t\t}\n\n\t\t\t\tif ( isAttr ) {\n\t\t\t\t\tproperties[\n\t\t\t\t\t\ttaxonomy.replace( 'filter_', 'query_type_' )\n\t\t\t\t\t] = relation;\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\tcase 'review':\n\t\t\t\tif ( $dropdown.length ) {\n\t\t\t\t\tproperties.rating_filter = $dropdown.val();\n\t\t\t\t} else {\n\t\t\t\t\t$active = $filter\n\t\t\t\t\t\t.find( '.filter-item' )\n\t\t\t\t\t\t.filter( '.active' )\n\t\t\t\t\t\t.children( 'a, label' );\n\n\t\t\t\t\tif ( ! multiple ) {\n\t\t\t\t\t\t$active = $active.first();\n\t\t\t\t\t\tproperties.rating_filter = $active.is( 'label' )\n\t\t\t\t\t\t\t? $active.find( ':input' ).val()\n\t\t\t\t\t\t\t: $active.data( 'rating' );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tproperties.rating_filter = $active\n\t\t\t\t\t\t\t.get()\n\t\t\t\t\t\t\t.reduce( function ( a, v ) {\n\t\t\t\t\t\t\t\tlet val;\n\n\t\t\t\t\t\t\t\tv = $( v );\n\t\t\t\t\t\t\t\tval = v.is( 'label' )\n\t\t\t\t\t\t\t\t\t? v.find( ':input' ).val()\n\t\t\t\t\t\t\t\t\t: v.data( 'rating' );\n\n\t\t\t\t\t\t\t\tif ( ! val ) {\n\t\t\t\t\t\t\t\t\treturn a;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\ta.push( val );\n\n\t\t\t\t\t\t\t\treturn a;\n\t\t\t\t\t\t\t}, [] )\n\t\t\t\t\t\t\t.join( ',' );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'price_range':\n\t\t\t\tif ( $dropdown.length ) {\n\t\t\t\t\tif ( multiple ) {\n\t\t\t\t\t\tproperties.price_ranges = $dropdown.val().join( ',' );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tproperties.min_price = $dropdown\n\t\t\t\t\t\t\t.val()\n\t\t\t\t\t\t\t.split( '-' )[ 0 ];\n\t\t\t\t\t\tproperties.max_price = $dropdown\n\t\t\t\t\t\t\t.val()\n\t\t\t\t\t\t\t.split( '-' )[ 1 ];\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t$active = $filter\n\t\t\t\t\t\t.find( '.filter-item' )\n\t\t\t\t\t\t.filter( '.active' )\n\t\t\t\t\t\t.children( 'a, label' );\n\n\t\t\t\t\tif ( multiple ) {\n\t\t\t\t\t\tproperties.price_ranges = $active\n\t\t\t\t\t\t\t.get()\n\t\t\t\t\t\t\t.reduce( ( a, v ) => {\n\t\t\t\t\t\t\t\tlet min = $( v ).data( 'range-min' ),\n\t\t\t\t\t\t\t\t\tmax = $( v ).data( 'range-max' );\n\n\t\t\t\t\t\t\t\ta += ( max ? `${ min }-${ max }` : min ) + ',';\n\n\t\t\t\t\t\t\t\treturn a;\n\t\t\t\t\t\t\t}, '' )\n\t\t\t\t\t\t\t.replace( /^(.*),$/, '$1' );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tproperties.min_price = parseFloat(\n\t\t\t\t\t\t\t$active.first().data( 'range-min' )\n\t\t\t\t\t\t);\n\t\t\t\t\t\tproperties.max_price = parseFloat(\n\t\t\t\t\t\t\t$active.first().data( 'range-max' )\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'price_slider':\n\t\t\t\tproperties.min_price = parseFloat(\n\t\t\t\t\t$filter.find( '.price-slider-min' ).val()\n\t\t\t\t);\n\t\t\t\tproperties.max_price = parseFloat(\n\t\t\t\t\t$filter.find( '.price-slider-max' ).val()\n\t\t\t\t);\n\t\t\t\tbreak;\n\t\t\tcase 'stock_sale':\n\t\t\t\tif ( $filter.find( '.filter-on-sale' ).is( '.active' ) ) {\n\t\t\t\t\tproperties.onsale_filter = 1;\n\t\t\t\t}\n\t\t\t\tif ( $filter.find( '.filter-in-stock' ).is( '.active' ) ) {\n\t\t\t\t\tproperties.instock_filter = 1;\n\t\t\t\t}\n\t\t\t\tif ( $filter.find( '.filter-featured' ).is( '.active' ) ) {\n\t\t\t\t\tproperties.featured_filter = 1;\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'orderby':\n\t\t\t\tproperties.orderby = $filter.find( '.filter-order-by' ).val();\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\n\t\tfilteredProperties = $filter.triggerHandler(\n\t\t\t'yith_wcan_filter_properties',\n\t\t\t[ properties, self ]\n\t\t);\n\t\tproperties =\n\t\t\ttypeof filteredProperties !== 'undefined'\n\t\t\t\t? filteredProperties\n\t\t\t\t: properties;\n\n\t\treturn properties;\n\t}\n\n\t// retrieves properties for all filters of the preset\n\tgetFiltersProperties() {\n\t\tlet properties = {};\n\t\tconst self = this;\n\n\t\tthis.getFilters().each( function () {\n\t\t\tconst $filter = $( this );\n\n\t\t\tif ( self.isFilterActive( $filter ) ) {\n\t\t\t\tconst filterProperties = self.getFilterProperties( $filter );\n\n\t\t\t\tproperties = self.mergeProperties(\n\t\t\t\t\tproperties,\n\t\t\t\t\tfilterProperties,\n\t\t\t\t\t$filter\n\t\t\t\t);\n\t\t\t}\n\t\t} );\n\n\t\treturn properties;\n\t}\n\n\t// retrieve filters matching any of the properties passed\n\tgetFiltersByProperties( properties ) {\n\t\tconst self = this;\n\n\t\treturn this.getFilters().filter( function () {\n\t\t\tconst $filter = $( this );\n\n\t\t\tif ( self.isFilterActive( $filter ) ) {\n\t\t\t\tlet filterProperties = self.getFilterProperties( $filter ),\n\t\t\t\t\thasProp = false;\n\n\t\t\t\tfor ( const prop in properties ) {\n\t\t\t\t\tif (\n\t\t\t\t\t\t[ 'min_price', 'max_price', 'price_ranges' ].includes(\n\t\t\t\t\t\t\tprop\n\t\t\t\t\t\t) &&\n\t\t\t\t\t\t( filterProperties.min_price ||\n\t\t\t\t\t\t\tfilterProperties.price_ranges )\n\t\t\t\t\t) {\n\t\t\t\t\t\thasProp = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t} else if ( filterProperties[ prop ] ) {\n\t\t\t\t\t\thasProp = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn hasProp;\n\t\t\t}\n\n\t\t\treturn false;\n\t\t} );\n\t}\n\n\t// show clear selection anchor\n\tmaybeToggleClearFilter( $filter ) {\n\t\tif ( ! this.isFilterActive( $filter ) ) {\n\t\t\tthis.maybeHideClearFilter( $filter );\n\t\t} else {\n\t\t\tthis.maybeShowClearFilter( $filter );\n\t\t}\n\t}\n\n\t// show clear all selections anchor\n\tmaybeToggleClearAllFilters() {\n\t\tif ( ! this.isAnyFilterActive() ) {\n\t\t\tthis.maybeHideClearAllFilters();\n\t\t} else {\n\t\t\tthis.maybeShowClearAllFilters();\n\t\t}\n\t}\n\n\t// show clear selection anchor\n\tmaybeShowClearFilter( $filter ) {\n\t\tif (\n\t\t\t! this.isFilterActive( $filter ) ||\n\t\t\t! yith_wcan_shortcodes.show_clear_filter\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\t// remove clear selection link if already added.\n\t\t$filter.find( '.clear-selection' ).remove();\n\n\t\t// add new clear selection link.\n\t\t$( '<a/>', {\n\t\t\tclass: 'clear-selection',\n\t\t\ttext: yith_wcan_shortcodes.labels.clear_selection,\n\t\t\trole: 'button',\n\t\t} )\n\t\t\t.prependTo( $filter.find( '.filter-content' ) )\n\t\t\t.on( 'click', ( ev ) => {\n\t\t\t\tev.preventDefault();\n\n\t\t\t\tthis.deactivateFilter(\n\t\t\t\t\t$filter,\n\t\t\t\t\tfalse,\n\t\t\t\t\tyith_wcan_shortcodes.instant_filters\n\t\t\t\t);\n\t\t\t\tthis.maybeHideClearFilter( $filter );\n\n\t\t\t\tif ( yith_wcan_shortcodes.instant_filters ) {\n\t\t\t\t\tthis.closeModal();\n\t\t\t\t}\n\t\t\t} );\n\t}\n\n\t// show clearAll anchor, when on mobile layout\n\tmaybeShowClearAllFilters() {\n\t\tif ( ! this.isAnyFilterActive() || ! this.isMobile ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// remove clear selection link if already added.\n\t\tthis.$preset.find( '.clear-selection' ).remove();\n\n\t\t// add new clear selection link.\n\t\t$( '<a/>', {\n\t\t\tclass: 'clear-selection',\n\t\t\ttext: yith_wcan_shortcodes.labels.clear_all_selections,\n\t\t\trole: 'button',\n\t\t} )\n\t\t\t.prependTo( this.$preset.find( '.filters-container' ) )\n\t\t\t.on( 'click', ( ev ) => {\n\t\t\t\tev.preventDefault();\n\n\t\t\t\tthis.deactivateAllFilters(\n\t\t\t\t\tyith_wcan_shortcodes.instant_filters\n\t\t\t\t);\n\t\t\t\tthis.maybeHideClearAllFilters();\n\n\t\t\t\tif ( yith_wcan_shortcodes.instant_filters ) {\n\t\t\t\t\tthis.closeModal();\n\t\t\t\t}\n\t\t\t} );\n\t}\n\n\t// hide clear selection anchor\n\tmaybeHideClearFilter( $filter ) {\n\t\tif (\n\t\t\tthis.isFilterActive( $filter ) ||\n\t\t\t! yith_wcan_shortcodes.show_clear_filter\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\t// remove clear selection link.\n\t\t$filter.find( '.clear-selection' ).remove();\n\t}\n\n\t// show clearAll anchor, when on mobile layout\n\tmaybeHideClearAllFilters() {\n\t\tif ( this.isAnyFilterActive() ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// remove clear selection link.\n\t\tthis.$preset\n\t\t\t.find( '.filters-container' )\n\t\t\t.children( '.clear-selection' )\n\t\t\t.remove();\n\t}\n\n\t// deactivate filter\n\tdeactivateFilter( $filter, properties, doFilter ) {\n\t\tconst filterType = $filter.data( 'filter-type' ),\n\t\t\t$items = $filter.find( '.filter-item' ),\n\t\t\t$activeItems = $items.filter( '.active' ),\n\t\t\t$dropdown = $filter.find( '.filter-dropdown' );\n\n\t\tswitch ( filterType ) {\n\t\t\tcase 'tax':\n\t\t\t\tconst taxonomy = $filter.data( 'taxonomy' );\n\n\t\t\t\tif ( $dropdown.length ) {\n\t\t\t\t\tif ( ! properties ) {\n\t\t\t\t\t\t$dropdown.find( 'option' ).prop( 'selected', false );\n\t\t\t\t\t} else {\n\t\t\t\t\t\t$dropdown.find( 'option' ).each( function () {\n\t\t\t\t\t\t\tconst $option = $( this );\n\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t$option.val().toString() ===\n\t\t\t\t\t\t\t\tproperties[ taxonomy ].toString()\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\t$option.prop( 'selected', false );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} );\n\t\t\t\t\t}\n\n\t\t\t\t\t$dropdown.change();\n\t\t\t\t} else if ( ! properties ) {\n\t\t\t\t\t$activeItems.children( 'label' ).children( 'a' ).click();\n\t\t\t\t\t$activeItems.removeClass( 'active' );\n\t\t\t\t} else {\n\t\t\t\t\t$activeItems.each( function () {\n\t\t\t\t\t\tlet $item = $( this ),\n\t\t\t\t\t\t\t$label = $item.children( 'label' ),\n\t\t\t\t\t\t\t$anchor = $item.children( 'a' ),\n\t\t\t\t\t\t\tvalue;\n\n\t\t\t\t\t\tvalue = $label.length\n\t\t\t\t\t\t\t? $label.find( ':input' ).val()\n\t\t\t\t\t\t\t: $anchor.data( 'term-slug' );\n\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tvalue.toString() ===\n\t\t\t\t\t\t\tproperties[ taxonomy ].toString()\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t$item.children( 'label' ).children( 'a' ).click();\n\t\t\t\t\t\t\t$item.removeClass( 'active' );\n\t\t\t\t\t\t}\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'review':\n\t\t\t\tif ( $dropdown.length ) {\n\t\t\t\t\tif ( ! properties ) {\n\t\t\t\t\t\t$dropdown.find( 'option' ).prop( 'selected', false );\n\t\t\t\t\t} else {\n\t\t\t\t\t\t$dropdown.find( 'option' ).each( function () {\n\t\t\t\t\t\t\tconst $option = $( this );\n\n\t\t\t\t\t\t\tif ( $option.val() === properties.rating_filter ) {\n\t\t\t\t\t\t\t\t$option.prop( 'selected', false );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} );\n\t\t\t\t\t}\n\n\t\t\t\t\t$dropdown.change();\n\t\t\t\t} else if ( ! properties ) {\n\t\t\t\t\t$activeItems.children( 'label' ).children( 'a' ).click();\n\t\t\t\t\t$activeItems.removeClass( 'active' );\n\t\t\t\t} else {\n\t\t\t\t\t$activeItems.each( function () {\n\t\t\t\t\t\tlet $item = $( this ),\n\t\t\t\t\t\t\t$label = $item.children( 'label' ),\n\t\t\t\t\t\t\t$anchor = $item.children( 'a' ),\n\t\t\t\t\t\t\tvalue;\n\n\t\t\t\t\t\tvalue = $label.length\n\t\t\t\t\t\t\t? $label.find( ':input' ).val()\n\t\t\t\t\t\t\t: $anchor.data( 'rating' );\n\n\t\t\t\t\t\tif ( value === properties.rating_filter ) {\n\t\t\t\t\t\t\t$item.children( 'label' ).children( 'a' ).click();\n\t\t\t\t\t\t\t$item.removeClass( 'active' );\n\t\t\t\t\t\t}\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'price_range':\n\t\t\t\tif ( $dropdown.length ) {\n\t\t\t\t\tif ( ! properties ) {\n\t\t\t\t\t\t$dropdown.find( 'option' ).prop( 'selected', false );\n\t\t\t\t\t} else {\n\t\t\t\t\t\t$dropdown.find( 'option' ).each( function () {\n\t\t\t\t\t\t\tconst $option = $( this ),\n\t\t\t\t\t\t\t\tformattedRange =\n\t\t\t\t\t\t\t\t\tproperties.min_price +\n\t\t\t\t\t\t\t\t\t( properties.max_price\n\t\t\t\t\t\t\t\t\t\t? `-${ properties.max_price }`\n\t\t\t\t\t\t\t\t\t\t: '' );\n\n\t\t\t\t\t\t\tif ( $option.val() === formattedRange ) {\n\t\t\t\t\t\t\t\t$option.prop( 'selected', false );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} );\n\t\t\t\t\t}\n\n\t\t\t\t\t$dropdown.change();\n\t\t\t\t} else if ( ! properties ) {\n\t\t\t\t\t$activeItems.children( 'label' ).children( 'a' ).click();\n\t\t\t\t\t$activeItems.removeClass( 'active' );\n\t\t\t\t} else {\n\t\t\t\t\t$activeItems.each( function () {\n\t\t\t\t\t\tlet $item = $( this ),\n\t\t\t\t\t\t\t$label = $item.children( 'label' ),\n\t\t\t\t\t\t\t$anchor = $item.children( 'a' ),\n\t\t\t\t\t\t\tformattedRange,\n\t\t\t\t\t\t\tvalue;\n\n\t\t\t\t\t\tvalue = $label.length\n\t\t\t\t\t\t\t? $label.find( ':input' ).val()\n\t\t\t\t\t\t\t: $anchor.data( 'min_price' ) +\n\t\t\t\t\t\t\t  ( $anchor.data( 'max_price' )\n\t\t\t\t\t\t\t\t\t? '-' + $anchor.data( 'max_price' )\n\t\t\t\t\t\t\t\t\t: '' );\n\n\t\t\t\t\t\tif ( properties.min_price ) {\n\t\t\t\t\t\t\tformattedRange =\n\t\t\t\t\t\t\t\tproperties.min_price +\n\t\t\t\t\t\t\t\t( properties.max_price\n\t\t\t\t\t\t\t\t\t? '-' + properties.max_price\n\t\t\t\t\t\t\t\t\t: '' );\n\t\t\t\t\t\t} else if ( properties.price_ranges ) {\n\t\t\t\t\t\t\tformattedRange = properties.price_ranges;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif ( value === formattedRange ) {\n\t\t\t\t\t\t\t$item.children( 'label' ).children( 'a' ).click();\n\t\t\t\t\t\t\t$item.removeClass( 'active' );\n\t\t\t\t\t\t}\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'price_slider':\n\t\t\t\tconst $priceSlider = $filter.find( '.price-slider' );\n\n\t\t\t\t$filter\n\t\t\t\t\t.find( '.price-slider-min' )\n\t\t\t\t\t.val( $priceSlider.data( 'min' ) );\n\t\t\t\t$filter\n\t\t\t\t\t.find( '.price-slider-max' )\n\t\t\t\t\t.val( $priceSlider.data( 'max' ) )\n\t\t\t\t\t.change();\n\t\t\t\tbreak;\n\t\t\tcase 'orderby':\n\t\t\t\t$filter.find( 'select' ).val( 'menu_order' );\n\t\t\t\tbreak;\n\t\t\tcase 'stock_sale':\n\t\t\t\tif ( ! properties ) {\n\t\t\t\t\t$filter\n\t\t\t\t\t\t.find( '.filter-in-stock' )\n\t\t\t\t\t\t.find( ':input' )\n\t\t\t\t\t\t.prop( 'checked', false )\n\t\t\t\t\t\t.change();\n\t\t\t\t\t$filter\n\t\t\t\t\t\t.find( '.filter-on-sale' )\n\t\t\t\t\t\t.find( ':input' )\n\t\t\t\t\t\t.prop( 'checked', false )\n\t\t\t\t\t\t.change();\n\t\t\t\t\t$filter\n\t\t\t\t\t\t.find( '.filter-featured' )\n\t\t\t\t\t\t.find( ':input' )\n\t\t\t\t\t\t.prop( 'checked', false )\n\t\t\t\t\t\t.change();\n\n\t\t\t\t\t$items.removeClass( 'active' );\n\t\t\t\t} else {\n\t\t\t\t\tif ( properties?.instock_filter ) {\n\t\t\t\t\t\t$filter\n\t\t\t\t\t\t\t.find( '.filter-in-stock' )\n\t\t\t\t\t\t\t.find( ':input' )\n\t\t\t\t\t\t\t.prop( 'checked', false )\n\t\t\t\t\t\t\t.change()\n\t\t\t\t\t\t\t.closest( '.filter-item' )\n\t\t\t\t\t\t\t.removeClass( 'active' );\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( properties?.onsale_filter ) {\n\t\t\t\t\t\t$filter\n\t\t\t\t\t\t\t.find( '.filter-on-sale' )\n\t\t\t\t\t\t\t.find( ':input' )\n\t\t\t\t\t\t\t.prop( 'checked', false )\n\t\t\t\t\t\t\t.change()\n\t\t\t\t\t\t\t.closest( '.filter-item' )\n\t\t\t\t\t\t\t.removeClass( 'active' );\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( properties?.featured_filter ) {\n\t\t\t\t\t\t$filter\n\t\t\t\t\t\t\t.find( '.filter-featured' )\n\t\t\t\t\t\t\t.find( ':input' )\n\t\t\t\t\t\t\t.prop( 'checked', false )\n\t\t\t\t\t\t\t.change()\n\t\t\t\t\t\t\t.closest( '.filter-item' )\n\t\t\t\t\t\t\t.removeClass( 'active' );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\t$items.removeClass( 'active' );\n\t\t\t\tbreak;\n\t\t}\n\n\t\tthis.activeFilters = false;\n\n\t\tif ( doFilter ) {\n\t\t\tthis.filter();\n\t\t}\n\t}\n\n\t// deactivate all filters\n\tdeactivateAllFilters( doFilter ) {\n\t\tconst self = this,\n\t\t\t$filters = this.getFilters();\n\n\t\t$filters.each( function () {\n\t\t\tconst $filter = $( this );\n\n\t\t\tself.deactivateFilter( $filter );\n\t\t} );\n\n\t\tthis.activeFilters = false;\n\n\t\tif ( doFilter ) {\n\t\t\tthis.filter();\n\t\t}\n\t}\n\n\t// deactivate filters that matches a specific set of properties\n\tdeactivateFilterByProperties( properties, doFilter ) {\n\t\tconst self = this,\n\t\t\t$filters = this.getFiltersByProperties( properties );\n\n\t\tif ( ! $filters.length ) {\n\t\t\treturn;\n\t\t}\n\n\t\t$filters.each( function () {\n\t\t\tconst $filter = $( this );\n\n\t\t\tself.deactivateFilter( $filter, properties, doFilter );\n\t\t} );\n\t}\n\n\t// open filters as a modal, when in mobile layout\n\topenModal() {\n\t\tif ( ! this.isMobile ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( yith_wcan_shortcodes.toggles_open_on_modal ) {\n\t\t\tthis._openAllCollapsables();\n\t\t} else {\n\t\t\tthis._closeAllCollapsables();\n\t\t}\n\n\t\t$( 'body' )\n\t\t\t.css( 'overflow', 'hidden' )\n\t\t\t.addClass( 'yith-wcan-preset-modal-open' );\n\n\t\tthis.$preset.show();\n\n\t\tsetTimeout( () => {\n\t\t\tthis.$preset.addClass( 'open' );\n\t\t}, 100 );\n\t}\n\n\t// close filters modal, when in mobile layout\n\tcloseModal() {\n\t\tif ( ! this.isMobile ) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.$preset.removeClass( 'open' );\n\n\t\tsetTimeout( () => {\n\t\t\tthis.$preset.hide();\n\t\t\t$( 'body' )\n\t\t\t\t.css( 'overflow', 'auto' )\n\t\t\t\t.removeClass( 'yith-wcan-preset-modal-open' );\n\t\t}, 300 );\n\t}\n\n\t// utility that formats the price according to store configuration.\n\tformatPrice( price ) {\n\t\tif ( 'undefined' !== typeof accounting ) {\n\t\t\tprice = accounting.formatMoney( price, {\n\t\t\t\tsymbol: yith_wcan_shortcodes.currency_format.symbol,\n\t\t\t\tdecimal: yith_wcan_shortcodes.currency_format.decimal,\n\t\t\t\tthousand: yith_wcan_shortcodes.currency_format.thousand,\n\t\t\t\tprecision: 0,\n\t\t\t\tformat: yith_wcan_shortcodes.currency_format.format,\n\t\t\t} );\n\t\t}\n\n\t\treturn price;\n\t}\n\n\t// utility that merges together sets of filter properties\n\tmergeProperties( set1, set2, $filter ) {\n\t\t// search for common properties\n\t\tfor ( const prop in set2 ) {\n\t\t\tif ( ! set2.hasOwnProperty( prop ) ) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif ( !! set1[ prop ] ) {\n\t\t\t\tswitch ( prop ) {\n\t\t\t\t\tcase 'rating_filter':\n\t\t\t\t\tcase 'min_price':\n\t\t\t\t\tcase 'max_price':\n\t\t\t\t\tcase 'onsale_filter':\n\t\t\t\t\tcase 'instock_filter':\n\t\t\t\t\tcase 'orderby':\n\t\t\t\t\t\t// just override default value\n\t\t\t\t\t\tset1[ prop ] = set2[ prop ];\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tif ( 0 === prop.indexOf( 'query_type_' ) ) {\n\t\t\t\t\t\t\t// query_type param\n\t\t\t\t\t\t\tset1[ prop ] = set2[ prop ];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// we're dealing with taxonomy\n\t\t\t\t\t\t\tconst isAttr = 0 === prop.indexOf( 'filter_' ),\n\t\t\t\t\t\t\t\tglue = isAttr ? ',' : '+';\n\n\t\t\t\t\t\t\tlet newValue =\n\t\t\t\t\t\t\t\tset1[ prop ].replace( ',', glue ) +\n\t\t\t\t\t\t\t\tglue +\n\t\t\t\t\t\t\t\tset2[ prop ].replace( ',', glue );\n\n\t\t\t\t\t\t\tnewValue = newValue\n\t\t\t\t\t\t\t\t.split( glue )\n\t\t\t\t\t\t\t\t.filter(\n\t\t\t\t\t\t\t\t\t( value, index, arr ) =>\n\t\t\t\t\t\t\t\t\t\tarr.indexOf( value ) === index\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t.join( glue );\n\n\t\t\t\t\t\t\tset1[ prop ] = newValue;\n\n\t\t\t\t\t\t\tif ( isAttr ) {\n\t\t\t\t\t\t\t\tconst queryTypeParam = prop.replace(\n\t\t\t\t\t\t\t\t\t'filter_',\n\t\t\t\t\t\t\t\t\t'query_type_'\n\t\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\t\tset1[ queryTypeParam ] = 'and';\n\t\t\t\t\t\t\t\tset2[ queryTypeParam ] = 'and';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tdelete set2[ prop ];\n\t\t\t}\n\t\t}\n\n\t\t$.extend( set1, set2 );\n\n\t\treturn set1;\n\t}\n}\n", "'use strict';\n\n/* global globalThis, jQ<PERSON>y, yith_wcan_shortcodes, accounting */\n\nimport YITH_WCAN_Filter from './modules/yith-wcan-filter';\nimport YITH_WCAN_Reset_Button from './modules/yith-wcan-reset-button';\nimport YITH_WCAN_Preset from './modules/yith-wcan-preset';\n\njQuery( function ( $ ) {\n\t$( document )\n\t\t.on(\n\t\t\t'yith_wcan_init_shortcodes yith_plugin_fw_gutenberg_success_do_shortcode',\n\t\t\tfunction () {\n\t\t\t\t$( '.yith-wcan-filters' )\n\t\t\t\t\t.not( '.enhanced' )\n\t\t\t\t\t.each( function () {\n\t\t\t\t\t\tnew YITH_WCAN_Preset( $( this ) );\n\t\t\t\t\t} );\n\n\t\t\t\t$( '.yith-wcan-reset-filters' )\n\t\t\t\t\t.not( '.enhanced' )\n\t\t\t\t\t.each( function () {\n\t\t\t\t\t\tnew YITH_WCAN_Reset_Button( $( this ) );\n\t\t\t\t\t} );\n\t\t\t}\n\t\t)\n\t\t.trigger( 'yith_wcan_init_shortcodes' );\n\n\tglobalThis.product_filter = new YITH_WCAN_Filter();\n} );\n"], "sourceRoot": ""}