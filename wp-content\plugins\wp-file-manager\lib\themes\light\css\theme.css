/**
 * elFinder Theme Template
 * <AUTHOR>
 */

/* Reset */
@import url('reset.css');

/* Google Fonts */
@import url('//fonts.googleapis.com/css?family=Open+Sans:300');

/* Main features of the whole UI */
@import url('main.css');

/* Icons */
@import url('icons.css');

/* Toolbar (top panel) */
@import url('toolbar.css');

/* Navbar (left panel) */
@import url('navbar.css');

/* Views (List and Thumbnail) */
@import url('view-list.css');
@import url('view-thumbnail.css');

/* Context menu */
@import url('contextmenu.css');

/* (Modal) Dialogs */
@import url('dialog.css');

/* Status Bar */
@import url('statusbar.css');

.ui-widget-content.elfinder-edit-editor{
	width:auto;
}
.std42-dialog .ui-dialog-titlebar .elfinder-titlebar-button.elfinder-titlebar-button-right .ui-icon.ui-icon-closethick {
	display: none;
}
.elfinder-toolbar .elfinder-button-search .ui-icon-close {
    margin: -10px 4px 0 4px;
    width: 17px;
}