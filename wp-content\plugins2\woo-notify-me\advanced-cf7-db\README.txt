=== Advanced Contact form 7 DB ===
Contributors: vsourz1td
Tags: contact form 7 db, contact form db, advanced cf7 db, contact form 7, database
Requires at least: 4.0
Tested up to: 6.7.1
Stable tag: 2.0.8
Requires PHP: 7.4
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Save all contact form 7 form submitted data to the database, View, Ordering, Change field labels and Import/Export data using CSV.

== Description ==
Easy plug & play plugin to store all enquiry details received through website Contact Form 7 forms. Simply install & activate plugin to store all your enquiries in wp-admin.

Every form that is submitted in the website will be captured into database using “Advanced Contact Form 7” plugin. Follow below steps to view form details:

* Select form from drop-down
* View all form data for that particular form
* Option to search / filter forms by keyword or date range
* View attachments in wp-content/uploads/advanced-cf7-upload
* Option to download attachments from wp-admin


= Features =
* Compatible with **Multisite feature** – Efficient data management by storing form details website wise
* Securely stores form data into database
* Easily modify enquiry data
* View list of all forms received through Contact Form 7
* Search / filter form details using keyword
* Search / filter enquiries using custom date-range filter
* Export data in **CSV, Excel** format by applying filters
* View attachment download link
* Advanced pagination with an option to jump to any page to view specific entries
* Enable/Disable columns with personalized tabular structure
* Drag & Drop with ease to view data in customized tabular view
* Option to select multiple entries at a time for deleting / exporting entries
* Rename field label names easily
* Import form details using CSV
* Filter to exclude data from creating record in CF7 database
* Modify form data before creating record in CF7 database
* Generate shortcode using different parameters to display specific data only. For more details, please refer plugin “Shortcode” screen

== Commercial Features ==
**1. Advanced CF7 DB - GDPR Compliant**
Advanced CF7 DB – GDPR Compliant plugin assists website and web-shop owners to comply with European privacy regulations known as GDPR. Advanced CF7 DB – GDPR Compliant is an add-on of Advanced CF7 DB, it is based on GDPR rules to export or erase user’s personal data stored with advanced CF7 DB.

Download Advanced CF7 DB – GDPR Complaint plugin from <a target="_blank" href="https://codecanyon.net/item/advanced-cf7-db-gdpr-compliant/22386060">here</a>

= Advanced CF7 DB - GDPR Compliant Plugin Features =
* Compatible with the latest WordPress version 5.1.1 and later for GDPR compliances
* Manages data as per new regulations and up to the mark performance
* View personal data of individual record On-Request, categorized as per CF7 forms and websites
* Erase personal data specific to CF7 that are not required
* Website owners can export a ZIP file containing a user’s personal data, including data collected by Advanced CF7 DB plugin
* Website owners can erase a user’s personal data, including data collected by Advanced CF7 DB plugin

**2. Schedule Report**
We have introduced a new add-on “Schedule Report” focusing on day-to-day business specific requirement like:

* Daily Report
* Weekly Report
* Monthly Report

Reports will be automatically generated and will be available to you in your e-mail address as per scheduled routine.

Download Schedule Report plugin from <a target="_blank" href="https://codecanyon.net/item/schedule-report-for-advanced-cf7-db/21560647?s_rank=8">here</a>

= Schedule Report Plugin Features =
* Auto-generated CSV report are sent in emails at scheduled time
* Schedule multiple email events for receiving different enquiry form reports
* Option to select datasheet columns for reports to be generated
* Option to filter data while creating the scheduled event for a particular report
* Option to create email templates by defining To, From, Email message for schedule reports
* Scheduled Report will also be added to wp-cron and will be triggered as per schedule

**3. Advanced CF7 DB - User Access Manager**
Need to provide access to other users? Your, search ends now, this plugin provides access to individual users or user groups and accordingly user(s) can view or edit their contact form DB data.

Download Advanced CF7 DB – User Access Manager plugin from <a target="_blank" href="https://codecanyon.net/item/advanced-cf7-db-user-access-manager/22058788">here</a>

= Advanced CF7 DB - User Access Manager Plugin Features =
* Provide access of CF7 DB to view & update data to individual users or user groups
* Provide access to single user for creating single/multiple forms

== Plugin Customization ==
Restrict IP address storage – Various countries restrict websites to store end users IP address. We have a provision to Restrict IP address storage. Now, IP address can be neglected while storing form’s data into database.

**How to restrict IP address storage?**
Navigate to -> wp-content/themes/{active theme folder}/functions.php
Open the functions.php file and place the code do_shortcode( ‘[cf7-db-display-ip]’ ); at the end of the file.

For support, email us at: <<EMAIL>>

= How to use? =
1. Install Plugin via WordPress Admin – Go to Admin > Plugins > Add New.
2. View form entries – Go To Admin >> Advanced CF7 DB >> Advanced CF7 DB >> Select form name.
3. Import CSV file – Go To Admin >> Advanced CF7 DB >> Import CSV >> Select form name.

== Installation ==

= Install via WordPress Admin =
1. Go to Admin > Plugins > Add New
2. On the upper portion click the Upload link.
3. Using the file upload field, upload the plugin zip file here and activate the plugin.

= Install via FTP =
1. Unzip the plugin file.
2. Using FTP go to your server’s wp-content/plugins directory.
3. Upload the unzipped plugin here.
4. Once finished, login into your WP Admin and go to Plugins.
5. Find Advanced CF7 DB and activate it.


== Frequently Asked Questions ==

= Can I use this plugin if Contact Form 7 is not installed or activated? =
No, this plugin work only when Contact Form 7 is installed & activated.

= How can I import CSV sheet? =
Go to "Field Setting Tab" under "Import CSV" screen, define/add column names as per your sheet, save those values and click "Import CSV".

= Can I modify field names? =
No, you can only change field’s label name from “display settings” screen.

= Are you facing difficulty in viewing data in CSV? =
Please note while exporting data in CSV format, the sheet must be opened with delimiter an it - (,)comma separated otherwise you may face difficulty in viewing the data sheet.

= What needs to be done if advanced CF7 DB is not working? =
Please contact our support team at <<EMAIL>> in case you face any difficulty. In case you have spotted a bug, please report at <<EMAIL>> & our support team will get is resolved in 24 hours.

= Can I restrict the plugin from storing IP address of the user to contact form DB? =
Yes, you can restrict the plugin from storing user's IP address.

= Can I display Contact Form DB data to user ? =
Yes, you can use short codes for displaying contact form data to users. For more detail please visit “Shortcode” plugin's description page.

= How to restrict the plugin from storing IP address of the user to contact form DB? =
Restriction is simple, just place "do_shortcode( ‘[cf7-db-display-ip]’ );" in the theme folders functions.php file By placing the code, IP address of the user's will not been stored.

Please follow below steps:
– Goto -> wp-content/themes/{active theme folder}/functions.php
– Open the functions.php file and place the code do_shortcode( ‘[cf7-db-display-ip]’ ); at the end of the file.
**For Multisite** - do_shortcode( ‘[cf7-db-display-ip site_id=”(your-site-id)”]’ );
– You need to add different shortcode for each site with specific siteId. If you need to restrict for all the sites then just place the shortcode without the parameter.

= How to restrict the plugin from storing form entry to Contact Form DB? =
Please follow below steps for restricting the plugin to store data
– Goto -> wp-content/themes/{active theme folder}/functions.php
– Open the functions.php file and place below code at the end of the file.
add_filter(‘vsz_cf7_unwanted_form_data_submission’,’vsz_restrict_form_data_submission’);
function vsz_restrict_form_data_submission($contact_form_ids){
$contact_form_ids[] = {your-contact-form-id};
return $contact_form_ids;
}

**For Multiple Forms**
add_filter(‘vsz_cf7_unwanted_form_data_submission’,’vsz_restrict_form_data_submission’);
function vsz_restrict_form_data_submission($contact_form_ids){
$contact_form_ids[] = [‘{your-contact-form-id}’,'{your-contact-form-id}’];
return $contact_form_ids;
}


== Screenshots ==

1. Display form related records.
2. Display Setting popup screen.
3. Edit information popup screen.
4. Setup import file fields.
5. Import CSV file.
6. Developer Support


== Changelog ==
= 2.0.8 =
* Minor Bug Fixing causing Website Crash.

= 2.0.7 =
* Updated Freemius SDK to latest version.

= 2.0.6 =
* We have upgraded with Security patches
* Compatible with latest version wordpress 6.7.1

= 2.0.5 =
* Minor bug fixing

= 2.0.4 =
* We have upgraded with Security patches
* Minor bug fixing when changing language 

= 2.0.3 =
* We have upgraded with Security patches
* Minor bug fixing when changing language 

= 2.0.2 =
* Compatible with latest version wordpress 6.5.3
* Compatible up to PHP 8.2
* Fixed SHA-1 hash-based contact form identification

= 2.0.1 =
* Integrate Freemius.
* Minor bug fixing.

= 2.0.0 =
* Compatible with latest version wordpress 6.2.2
* Enhancement of Export excel functionality with composer

= 1.9.3 =
* Compatible with latest version wordpress 6.2
* Minor bug fixing.

= 1.9 =
* We have upgraded with Security patches

= 1.8.8 =
* We have upgraded with Security patches

= 1.8.7 =
* We have upgraded with Security patches

= 1.8.6 =
* We have upgraded with Security patches

= 1.8.5 =
* We have upgraded with Security patches

= 1.8.4 =
* We have upgraded with Security patches
* Remove Export to PDF support


= 1.8.3 =
* We have upgraded with Security patch.
* Compatible with latest version wordpress 5.8.2

= 1.8.2 =
* Fixed save file related issue.
* Compatible with latest version wordpress 5.7.2

= 1.8.1 =
* Minor bug fixes

= 1.8.0 =
* Excel issues fixed while importing and exporting data.
* Compatible with latest version wordpress 5.5

= 1.7.2 =
* Fixed the delete data case as throws database error.

= 1.7.1 =
* Minor tweaks in the plugin related to the edit data

= 1.7.0 =
* We have upgraded with Security patch.
* Changes related to the premium plugin **User Access Manager** for the edit case to be handled.

= 1.6.2 =
* We have fixed SQL injection related bugs at the back office query.

= 1.6.1 =
* We have upgraded with Security patched for shortcode.

= 1.6.0 =
* We have upgraded with Security patched to securing user's uploaded data which managed on advanced-cf7-upload directory.

= 1.5.1 =
* Minor bug fixing related to variable mismatch for wordpress 5.0.1.

= 1.5.0 =
* **Developer Support:** Provide screen for the developer to manage Display Enquiry, Ban IP and Actions & Filters.
* **Add-ons:** Provision to select other add-ons of Advanced CF7 DB.

= 1.4.4 =
* Fixed issue related to export records in PDF.
* Provided filter for excluding particular contact form entry to the database
* Provided filter to add, modify, remove CF7 fields and data before submitting to the database.

= 1.4.3 =
* Fixed issue related to export records in PDF.
* Multisite support for IP restrict.

= 1.4.2 =
* Fixed issue related to hide ip address
* Delete attachment when record deleted
* Search with special characters issue fixed
* Accents and other languages special characters support added for export file with Excel and CSV
* Added the New library for the PDF


= 1.4.1 =
* Hidden field : Provision to update hidden field value.
* Export data in EXCEL : Resolved Special characters related issue.

= 1.4.0 =
* Export data in EXCEL & PDF file.
* IP address storage restriction.
* Mobile UI compatible
* **Schedule Report:** We have also introduced new feature **Schedule Report.**, now You can download our Commercial plugin from <a target="_blank" href="https://codecanyon.net/item/schedule-report-for-advanced-cf7-db/21560647?s_rank=8">here</a>

= 1.3.0 =
* Fixed issue related to the Contact Form tel field while editing the form data entry.
* Provision to change the number of records to be displayed in listing page from display setting option.
* Fixed issue related to redirection to the first page when a record is been updated from edit data popup.
* Compatible up to PHP 7.1

= 1.2.0 =
* Fix error related to PHP strings.

= 1.1.2 =
* Fix error related to PHP 7.1.

= 1.1.1 =
* Made changes to resolve the issue of user feasibility when editing the form fields.
* Minor tweak related to export functionality and attachment download functionality.

= 1.1.0 =
* Update Import Functionality.
* Fix CF7 Version related issue.

= 1.0.0 =
* Initial