#wpforms-panel-fields .wpforms-stripe-payment-element,
#wpforms-panel-revisions .wpforms-stripe-payment-element,
#wpforms-panel-ai-form .wpforms-stripe-payment-element {
  width: 60%;
}

#wpforms-panel-fields .wpforms-stripe-payment-element.above input::-webkit-input-placeholder,
#wpforms-panel-revisions .wpforms-stripe-payment-element.above input::-webkit-input-placeholder,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.above input::-webkit-input-placeholder {
  color: transparent;
}

#wpforms-panel-fields .wpforms-stripe-payment-element.above input:-moz-placeholder,
#wpforms-panel-revisions .wpforms-stripe-payment-element.above input:-moz-placeholder,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.above input:-moz-placeholder {
  color: transparent;
}

#wpforms-panel-fields .wpforms-stripe-payment-element.above input::-moz-placeholder,
#wpforms-panel-revisions .wpforms-stripe-payment-element.above input::-moz-placeholder,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.above input::-moz-placeholder {
  color: transparent;
}

#wpforms-panel-fields .wpforms-stripe-payment-element.above input:-ms-input-placeholder,
#wpforms-panel-revisions .wpforms-stripe-payment-element.above input:-ms-input-placeholder,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.above input:-ms-input-placeholder {
  color: transparent;
}

#wpforms-panel-fields .wpforms-stripe-payment-element.floating input::-webkit-input-placeholder,
#wpforms-panel-fields .wpforms-stripe-payment-element.floating select::-webkit-input-placeholder,
#wpforms-panel-revisions .wpforms-stripe-payment-element.floating input::-webkit-input-placeholder,
#wpforms-panel-revisions .wpforms-stripe-payment-element.floating select::-webkit-input-placeholder,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.floating input::-webkit-input-placeholder,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.floating select::-webkit-input-placeholder {
  color: #999999;
}

#wpforms-panel-fields .wpforms-stripe-payment-element.floating input:-moz-placeholder,
#wpforms-panel-fields .wpforms-stripe-payment-element.floating select:-moz-placeholder,
#wpforms-panel-revisions .wpforms-stripe-payment-element.floating input:-moz-placeholder,
#wpforms-panel-revisions .wpforms-stripe-payment-element.floating select:-moz-placeholder,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.floating input:-moz-placeholder,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.floating select:-moz-placeholder {
  color: #999999;
}

#wpforms-panel-fields .wpforms-stripe-payment-element.floating input::-moz-placeholder,
#wpforms-panel-fields .wpforms-stripe-payment-element.floating select::-moz-placeholder,
#wpforms-panel-revisions .wpforms-stripe-payment-element.floating input::-moz-placeholder,
#wpforms-panel-revisions .wpforms-stripe-payment-element.floating select::-moz-placeholder,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.floating input::-moz-placeholder,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.floating select::-moz-placeholder {
  color: #999999;
}

#wpforms-panel-fields .wpforms-stripe-payment-element.floating input:-ms-input-placeholder,
#wpforms-panel-fields .wpforms-stripe-payment-element.floating select:-ms-input-placeholder,
#wpforms-panel-revisions .wpforms-stripe-payment-element.floating input:-ms-input-placeholder,
#wpforms-panel-revisions .wpforms-stripe-payment-element.floating select:-ms-input-placeholder,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.floating input:-ms-input-placeholder,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.floating select:-ms-input-placeholder {
  color: #999999;
}

#wpforms-panel-fields .wpforms-stripe-payment-element.floating label,
#wpforms-panel-revisions .wpforms-stripe-payment-element.floating label,
#wpforms-panel-ai-form .wpforms-stripe-payment-element.floating label {
  display: none;
}

#wpforms-panel-fields .wpforms-stripe-payment-element input[type=text],
#wpforms-panel-fields .wpforms-stripe-payment-element select,
#wpforms-panel-revisions .wpforms-stripe-payment-element input[type=text],
#wpforms-panel-revisions .wpforms-stripe-payment-element select,
#wpforms-panel-ai-form .wpforms-stripe-payment-element input[type=text],
#wpforms-panel-ai-form .wpforms-stripe-payment-element select {
  width: 100%;
  min-width: initial;
}

#wpforms-panel-fields .wpforms-stripe-payment-element .wpforms-sub-label,
#wpforms-panel-revisions .wpforms-stripe-payment-element .wpforms-sub-label,
#wpforms-panel-ai-form .wpforms-stripe-payment-element .wpforms-sub-label {
  margin-bottom: 5px;
  margin-inline-start: 1px;
}

#wpforms-panel-fields .wpforms-stripe-payment-element .wpforms-field-row,
#wpforms-panel-revisions .wpforms-stripe-payment-element .wpforms-field-row,
#wpforms-panel-ai-form .wpforms-stripe-payment-element .wpforms-field-row {
  overflow: hidden;
  container-type: inline-size;
  container-name: wpforms-field-row-responsive;
}

@container wpforms-field-row-responsive (max-width: 200px) {
  #wpforms-panel-fields .wpforms-stripe-payment-element .wpforms-field-row .wpforms-one-half,
  #wpforms-panel-revisions .wpforms-stripe-payment-element .wpforms-field-row .wpforms-one-half,
  #wpforms-panel-ai-form .wpforms-stripe-payment-element .wpforms-field-row .wpforms-one-half {
    float: none;
    width: 100%;
  }
  #wpforms-panel-fields .wpforms-stripe-payment-element .wpforms-field-row .wpforms-stripe-cvc,
  #wpforms-panel-revisions .wpforms-stripe-payment-element .wpforms-field-row .wpforms-stripe-cvc,
  #wpforms-panel-ai-form .wpforms-stripe-payment-element .wpforms-field-row .wpforms-stripe-cvc {
    margin-top: 10px;
  }
  #wpforms-panel-fields .wpforms-stripe-payment-element .wpforms-field-row .wpforms-stripe-cardnumber-pics,
  #wpforms-panel-revisions .wpforms-stripe-payment-element .wpforms-field-row .wpforms-stripe-cardnumber-pics,
  #wpforms-panel-ai-form .wpforms-stripe-payment-element .wpforms-field-row .wpforms-stripe-cardnumber-pics {
    display: none;
  }
}

#wpforms-panel-fields .wpforms-stripe-payment-element .wpforms-stripe-cvc,
#wpforms-panel-revisions .wpforms-stripe-payment-element .wpforms-stripe-cvc,
#wpforms-panel-ai-form .wpforms-stripe-payment-element .wpforms-stripe-cvc {
  position: relative;
}

#wpforms-panel-fields .wpforms-stripe-payment-element .wpforms-stripe-cvc svg,
#wpforms-panel-revisions .wpforms-stripe-payment-element .wpforms-stripe-cvc svg,
#wpforms-panel-ai-form .wpforms-stripe-payment-element .wpforms-stripe-cvc svg {
  position: absolute;
  bottom: 8px;
  inset-inline-end: 12px;
  opacity: 0.75;
}

#wpforms-panel-fields .wpforms-stripe-payment-element .wpforms-stripe-cardnumber-pics,
#wpforms-panel-revisions .wpforms-stripe-payment-element .wpforms-stripe-cardnumber-pics,
#wpforms-panel-ai-form .wpforms-stripe-payment-element .wpforms-stripe-cardnumber-pics {
  position: absolute;
  bottom: 7px;
  inset-inline-end: 12px;
  width: 136px;
  max-width: calc( 100% - 24px);
  height: 24px;
  background-image: url("../../../images/integrations/stripe/cc-preview.png");
  background-repeat: no-repeat;
  background-size: 136px 24px;
  background-position: 100% 50%;
}

.rtl #wpforms-panel-fields .wpforms-stripe-payment-element .wpforms-stripe-cardnumber-pics, .rtl
#wpforms-panel-revisions .wpforms-stripe-payment-element .wpforms-stripe-cardnumber-pics, .rtl
#wpforms-panel-ai-form .wpforms-stripe-payment-element .wpforms-stripe-cardnumber-pics {
  background-position: 0 50%;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card.size-small .wpforms-stripe-payment-element,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card.size-small .wpforms-stripe-payment-element,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card.size-small .wpforms-stripe-payment-element {
  width: 25%;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card.size-large .wpforms-stripe-payment-element,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card.size-large .wpforms-stripe-payment-element,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card.size-large .wpforms-stripe-payment-element {
  width: 100%;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card .format-selected:not(.wpforms-stripe-payment-element),
#wpforms-panel-revisions .wpforms-field-stripe-credit-card .format-selected:not(.wpforms-stripe-payment-element),
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card .format-selected:not(.wpforms-stripe-payment-element) {
  width: 100%;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card.sublabel_hide .floating input::-webkit-input-placeholder,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card.sublabel_hide .floating input::-webkit-input-placeholder,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card.sublabel_hide .floating input::-webkit-input-placeholder {
  color: transparent;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card.sublabel_hide .floating input:-moz-placeholder,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card.sublabel_hide .floating input:-moz-placeholder,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card.sublabel_hide .floating input:-moz-placeholder {
  color: transparent;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card.sublabel_hide .floating input::-moz-placeholder,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card.sublabel_hide .floating input::-moz-placeholder,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card.sublabel_hide .floating input::-moz-placeholder {
  color: transparent;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card.sublabel_hide .floating input:-ms-input-placeholder,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card.sublabel_hide .floating input:-ms-input-placeholder,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card.sublabel_hide .floating input:-ms-input-placeholder {
  color: transparent;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card .wpforms-field-preview-wrap,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card .wpforms-field-preview-wrap,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card .wpforms-field-preview-wrap {
  position: absolute;
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  width: auto;
  height: 40px;
  top: 0;
  inset-inline-start: 0;
  bottom: 0;
  font-size: 16px;
  line-height: 40px;
  color: #666666;
  container-type: inline-size;
  container-name: wpforms-field-preview-wrap-small;
}

@container wpforms-field-preview-wrap-small (max-width: 280px) {
  #wpforms-panel-fields .wpforms-field-stripe-credit-card .wpforms-field-preview-wrap .wpforms-field-stripe-credit-card-number-expcvc-preview,
  #wpforms-panel-revisions .wpforms-field-stripe-credit-card .wpforms-field-preview-wrap .wpforms-field-stripe-credit-card-number-expcvc-preview,
  #wpforms-panel-ai-form .wpforms-field-stripe-credit-card .wpforms-field-preview-wrap .wpforms-field-stripe-credit-card-number-expcvc-preview {
    display: none;
  }
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card.size-small .wpforms-field-preview-wrap,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card.size-small .wpforms-field-preview-wrap,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card.size-small .wpforms-field-preview-wrap {
  width: 25%;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card.size-medium .wpforms-field-preview-wrap,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card.size-medium .wpforms-field-preview-wrap,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card.size-medium .wpforms-field-preview-wrap {
  width: 60%;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card.size-large .wpforms-field-preview-wrap,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card.size-large .wpforms-field-preview-wrap,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card.size-large .wpforms-field-preview-wrap {
  width: 100%;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card.wpforms-field-drag-to-column .wpforms-field-preview-wrap,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card.wpforms-field-drag-to-column .wpforms-field-preview-wrap,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card.wpforms-field-drag-to-column .wpforms-field-preview-wrap {
  width: 100%;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview {
  display: flex;
  line-height: 40px;
  padding-block: 0;
  padding-inline-end: 0;
  padding-inline-start: 10px;
  overflow-x: hidden;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview svg,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview svg,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview svg {
  width: 24px;
  min-width: 24px;
  fill: #000000;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview span,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview span,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-placeholder-preview span {
  opacity: 0.5;
  padding-inline-start: 10px;
  white-space: nowrap;
}

#wpforms-panel-fields .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-expcvc-preview,
#wpforms-panel-revisions .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-expcvc-preview,
#wpforms-panel-ai-form .wpforms-field-stripe-credit-card .wpforms-field-stripe-credit-card-number-expcvc-preview {
  display: block;
  width: fit-content;
  padding: 0 20px 0 0;
  opacity: 0.5;
  white-space: nowrap;
}

#wpforms-panel-fields .wpforms-layout-column .wpforms-field-stripe-credit-card.size-small .wpforms-field-preview-wrap, #wpforms-panel-fields .wpforms-layout-column .wpforms-field-stripe-credit-card.size-medium .wpforms-field-preview-wrap, #wpforms-panel-fields .wpforms-layout-column .wpforms-field-stripe-credit-card.size-large .wpforms-field-preview-wrap,
#wpforms-panel-revisions .wpforms-layout-column .wpforms-field-stripe-credit-card.size-small .wpforms-field-preview-wrap,
#wpforms-panel-revisions .wpforms-layout-column .wpforms-field-stripe-credit-card.size-medium .wpforms-field-preview-wrap,
#wpforms-panel-revisions .wpforms-layout-column .wpforms-field-stripe-credit-card.size-large .wpforms-field-preview-wrap,
#wpforms-panel-ai-form .wpforms-layout-column .wpforms-field-stripe-credit-card.size-small .wpforms-field-preview-wrap,
#wpforms-panel-ai-form .wpforms-layout-column .wpforms-field-stripe-credit-card.size-medium .wpforms-field-preview-wrap,
#wpforms-panel-ai-form .wpforms-layout-column .wpforms-field-stripe-credit-card.size-large .wpforms-field-preview-wrap {
  width: 100%;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
