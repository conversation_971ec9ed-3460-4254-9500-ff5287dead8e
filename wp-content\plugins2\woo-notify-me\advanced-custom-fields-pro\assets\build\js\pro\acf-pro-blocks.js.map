{"version": 3, "file": "acf-pro-blocks.js", "mappings": ";;;;;;;;;;;;;;;;;;AAAA,MAAMA,GAAG,GAAGC,mBAAO,CAAE,sCAAM,CAAC;AAE5B,CAAE,CAAEC,CAAC,EAAEC,SAAS,KAAM;EACrB;EACA,MAAM;IACLC,aAAa;IACbC,iBAAiB;IACjBC,WAAW;IACXC,aAAa;IACbC,gBAAgB;IAChBC;EACD,CAAC,GAAGC,EAAE,CAACC,WAAW;EAElB,MAAM;IAAEC,YAAY;IAAEC,aAAa;IAAEC,WAAW;IAAEC;EAAQ,CAAC,GAAGL,EAAE,CAACM,UAAU;EAC3E,MAAM;IAAEC;EAAS,CAAC,GAAGP,EAAE,CAACQ,OAAO;EAC/B,MAAM;IAAEC;EAAU,CAAC,GAAGC,KAAK;EAC3B,MAAM;IAAEC;EAAW,CAAC,GAAGX,EAAE,CAACY,IAAI;EAC9B,MAAM;IAAEC;EAA2B,CAAC,GAAGb,EAAE,CAACc,OAAO;;EAEjD;EACA,MAAMC,2BAA2B,GAChCf,EAAE,CAACC,WAAW,CAACe,yCAAyC,IACxDhB,EAAE,CAACC,WAAW,CAACc,2BAA2B;EAC3C;EACA,MAAME,2BAA2B,GAChCjB,EAAE,CAACC,WAAW,CAACiB,yCAAyC,IACxDlB,EAAE,CAACC,WAAW,CAACgB,2BAA2B;EAC3C,MAAME,+BAA+B,GACpCnB,EAAE,CAACC,WAAW,CAACmB,4CAA4C,IAC3DpB,EAAE,CAACC,WAAW,CAACoB,6CAA6C,IAC5DrB,EAAE,CAACC,WAAW,CAACkB,+BAA+B;EAC/C,MAAMG,mBAAmB,GACxBtB,EAAE,CAACC,WAAW,CAACsB,iCAAiC,IAChDvB,EAAE,CAACC,WAAW,CAACqB,mBAAmB;;EAEnC;AACD;AACA;AACA;AACA;AACA;EACC,MAAME,UAAU,GAAG,CAAC,CAAC;;EAErB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,YAAYA,CAAEC,IAAI,EAAG;IAC7B,OAAOF,UAAU,CAAEE,IAAI,CAAE,IAAI,KAAK;EACnC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,eAAeA,CAAED,IAAI,EAAG;IAChC,MAAME,SAAS,GAAGH,YAAY,CAAEC,IAAK,CAAC;IACtC,OAAOE,SAAS,CAACC,iBAAiB,IAAI,CAAC;EACxC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,kBAAkBA,CAAEC,QAAQ,EAAG;IACvC,MAAMC,OAAO,GAAGhC,EAAE,CAACY,IAAI,CACrBqB,MAAM,CAAE,mBAAoB,CAAC,CAC7BC,eAAe,CAAEH,QAAS,CAAC;IAC7B,MAAMI,WAAW,GAAGnC,EAAE,CAACY,IAAI,CACzBqB,MAAM,CAAE,mBAAoB,CAAC,CAC7BG,mBAAmB,CAAEJ,OAAQ,CAAC;IAChC,OAAOG,WAAW,CAACE,MAAM,CAAIC,KAAK,IAAMA,KAAK,CAACZ,IAAI,KAAK,YAAa,CAAC,CACnEa,MAAM;EACT;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,YAAYA,CAAA,EAAG;IACvB,OAAO,OAAOC,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,aAAa;EAChE;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,0BAA0BA,CAAA,EAAG;IACrC,MAAMC,aAAa,GAAGV,MAAM,CAAE,gBAAiB,CAAC;;IAEhD;IACA,IAAK,CAAEU,aAAa,EAAG,OAAO,IAAI;;IAElC;IACA,IAAKA,aAAa,CAACC,kCAAkC,EAAG;MACvD,OACC,SAAS,KAAKD,aAAa,CAACC,kCAAkC,CAAC,CAAC;IAElE,CAAC,MAAM,IAAKD,aAAa,CAACE,oBAAoB,EAAG;MAChD,OAAO,SAAS,KAAKF,aAAa,CAACE,oBAAoB,CAAC,CAAC;IAC1D,CAAC,MAAM;MACN,OAAO,IAAI;IACZ;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,iBAAiBA,CAAA,EAAG;IAC5B,MAAMH,aAAa,GAAGV,MAAM,CAAE,gBAAiB,CAAC;;IAEhD;IACA,IAAK,CAAEU,aAAa,EAAG,OAAO,KAAK;;IAEnC;IACA,IAAK,CAAEA,aAAa,CAACG,iBAAiB,EAAG,OAAO,KAAK;IAErD,OAAOH,aAAa,CAACG,iBAAiB,CAAC,CAAC;EACzC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,4BAA4BA,CAAA,EAAG;IACvC,OACCvD,CAAC,CAAE,4BAA6B,CAAC,CAAC+C,MAAM,IACxC,CAAEG,0BAA0B,CAAC,CAAC;EAEhC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASM,iBAAiBA,CAAEpB,SAAS,EAAG;IACvC;IACA,MAAMqB,YAAY,GAAGrB,SAAS,CAACsB,UAAU,IAAI,EAAE;IAC/C,IAAKD,YAAY,CAACV,MAAM,EAAG;MAC1B;MACAU,YAAY,CAACE,IAAI,CAAE,UAAW,CAAC;;MAE/B;MACA,MAAMC,QAAQ,GAAGC,GAAG,CAACC,GAAG,CAAE,UAAW,CAAC;MACtC,IAAK,CAAEL,YAAY,CAACM,QAAQ,CAAEH,QAAS,CAAC,EAAG;QAC1C,OAAO,KAAK;MACb;IACD;;IAEA;IACA,IACC,OAAOxB,SAAS,CAAC4B,IAAI,KAAK,QAAQ,IAClC5B,SAAS,CAAC4B,IAAI,CAACC,MAAM,CAAE,CAAC,EAAE,CAAE,CAAC,KAAK,MAAM,EACvC;MACD,MAAMC,QAAQ,GAAG9B,SAAS,CAAC4B,IAAI;MAC/B5B,SAAS,CAAC4B,IAAI,GAAGG,iEAAA,CAACC,GAAG,QAAGF,QAAe,CAAC;IACzC;;IAEA;IACA;IACA,IAAK,CAAE9B,SAAS,CAAC4B,IAAI,EAAG;MACvB,OAAO5B,SAAS,CAAC4B,IAAI;IACtB;;IAEA;IACA,MAAMK,QAAQ,GAAG7D,EAAE,CAAC8D,MAAM,CACxBC,aAAa,CAAC,CAAC,CACf1B,MAAM,CAAE,CAAE;MAAE2B;IAAK,CAAC,KAAMA,IAAI,KAAKpC,SAAS,CAACiC,QAAS,CAAC,CACrDI,GAAG,CAAC,CAAC;IACP,IAAK,CAAEJ,QAAQ,EAAG;MACjB;MACAjC,SAAS,CAACiC,QAAQ,GAAG,QAAQ;IAC9B;;IAEA;IACAjC,SAAS,GAAGyB,GAAG,CAACa,SAAS,CAAEtC,SAAS,EAAE;MACrCuC,KAAK,EAAE,EAAE;MACTzC,IAAI,EAAE,EAAE;MACRmC,QAAQ,EAAE,EAAE;MACZO,WAAW,EAAE,CAAC;MACdvC,iBAAiB,EAAE;IACpB,CAAE,CAAC;;IAEH;IACA;IACA,KAAM,MAAMwC,GAAG,IAAIzC,SAAS,CAAC0C,UAAU,EAAG;MACzC,IAAK1C,SAAS,CAAC0C,UAAU,CAAED,GAAG,CAAE,CAACE,OAAO,CAAChC,MAAM,KAAK,CAAC,EAAG;QACvD,OAAOX,SAAS,CAAC0C,UAAU,CAAED,GAAG,CAAE,CAACE,OAAO;MAC3C;IACD;;IAEA;IACA,IAAK3C,SAAS,CAAC4C,QAAQ,CAACC,MAAM,EAAG;MAChC7C,SAAS,CAAC0C,UAAU,CAACG,MAAM,GAAG;QAC7BC,IAAI,EAAE;MACP,CAAC;IACF;;IAEA;IACA,IAAIC,aAAa,GAAGC,SAAS;IAC7B,IAAIC,aAAa,GAAGC,SAAS;;IAE7B;IACA,IAAKlD,SAAS,CAAC4C,QAAQ,CAACO,SAAS,IAAInD,SAAS,CAAC4C,QAAQ,CAACQ,UAAU,EAAG;MACpEpD,SAAS,CAAC0C,UAAU,GAAGW,sBAAsB,CAC5CrD,SAAS,CAAC0C,UAAU,EACpB,YAAY,EACZ,QACD,CAAC;MACDK,aAAa,GAAGO,sBAAsB,CAAEP,aAAa,EAAE/C,SAAU,CAAC;IACnE;;IAEA;IACA,IACCA,SAAS,CAAC4C,QAAQ,CAACW,YAAY,IAC/BvD,SAAS,CAAC4C,QAAQ,CAACY,aAAa,EAC/B;MACDxD,SAAS,CAAC0C,UAAU,GAAGW,sBAAsB,CAC5CrD,SAAS,CAAC0C,UAAU,EACpB,eAAe,EACf,QACD,CAAC;MACDK,aAAa,GAAGU,yBAAyB,CACxCV,aAAa,EACb/C,SACD,CAAC;IACF;;IAEA;IACA,IAAKA,SAAS,CAAC4C,QAAQ,CAACc,UAAU,IAAI1D,SAAS,CAAC4C,QAAQ,CAACe,WAAW,EAAG;MACtE3D,SAAS,CAAC0C,UAAU,GAAGW,sBAAsB,CAC5CrD,SAAS,CAAC0C,UAAU,EACpB,aAAa,EACb,SACD,CAAC;MACDK,aAAa,GAAGa,uBAAuB,CACtCb,aAAa,EACb/C,SAAS,CAACA,SACX,CAAC;IACF;;IAEA;IACAA,SAAS,CAAC6D,IAAI,GAAKC,KAAK,IAAM/B,iEAAA,CAACgB,aAAa,EAAAgB,aAAA,KAAMD,KAAK,CAAI,CAAC;IAC5D9D,SAAS,CAACgE,IAAI,GAAG,MAAMjC,iEAAA,CAACkB,aAAa,MAAE,CAAC;;IAExC;IACArD,UAAU,CAAEI,SAAS,CAACF,IAAI,CAAE,GAAGE,SAAS;;IAExC;IACA,MAAMiE,MAAM,GAAG7F,EAAE,CAAC8D,MAAM,CAACd,iBAAiB,CAAEpB,SAAS,CAACF,IAAI,EAAEE,SAAU,CAAC;;IAEvE;IACA;IACA;IACA,IAAKiE,MAAM,CAACvB,UAAU,CAACG,MAAM,EAAG;MAC/BoB,MAAM,CAACvB,UAAU,CAACG,MAAM,GAAG;QAC1BC,IAAI,EAAE;MACP,CAAC;IACF;;IAEA;IACA,OAAOmB,MAAM;EACd;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAAS5D,MAAMA,CAAE6D,QAAQ,EAAG;IAC3B,IAAKA,QAAQ,KAAK,mBAAmB,EAAG;MACvC,OACC9F,EAAE,CAACY,IAAI,CAACqB,MAAM,CAAE,mBAAoB,CAAC,IACrCjC,EAAE,CAACY,IAAI,CAACqB,MAAM,CAAE,aAAc,CAAC;IAEjC;IACA,OAAOjC,EAAE,CAACY,IAAI,CAACqB,MAAM,CAAE6D,QAAS,CAAC;EAClC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,QAAQA,CAAED,QAAQ,EAAG;IAC7B,OAAO9F,EAAE,CAACY,IAAI,CAACmF,QAAQ,CAAED,QAAS,CAAC;EACpC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASE,SAASA,CAAEC,IAAI,EAAG;IAC1B,IAAInC,MAAM,GAAG,EAAE;;IAEf;IACA,MAAMoC,aAAa,GAAK5D,KAAK,IAAM;MAClCwB,MAAM,CAACX,IAAI,CAAEb,KAAM,CAAC;MACpBL,MAAM,CAAE,mBAAoB,CAAC,CAC3B+D,SAAS,CAAE1D,KAAK,CAACP,QAAS,CAAC,CAC3BoE,OAAO,CAAED,aAAc,CAAC;IAC3B,CAAC;;IAED;IACAjE,MAAM,CAAE,mBAAoB,CAAC,CAAC+D,SAAS,CAAC,CAAC,CAACG,OAAO,CAAED,aAAc,CAAC;;IAElE;IACA,KAAM,MAAME,CAAC,IAAIH,IAAI,EAAG;MACvBnC,MAAM,GAAGA,MAAM,CAACzB,MAAM,CACrB,CAAE;QAAEiC;MAAW,CAAC,KAAMA,UAAU,CAAE8B,CAAC,CAAE,KAAKH,IAAI,CAAEG,CAAC,CAClD,CAAC;IACF;;IAEA;IACA,OAAOtC,MAAM;EACd;;EAEA;AACD;AACA;AACA;AACA;EACC,MAAMuC,SAAS,GAAG,CAAC,CAAC;;EAEpB;AACD;AACA;AACA;AACA;AACA;EACC,MAAMC,UAAU,GAAG,CAAC,CAAC;;EAErB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,UAAUA,CAAEN,IAAI,EAAG;IAC3B,MAAM;MACL3B,UAAU,GAAG,CAAC,CAAC;MACfkC,OAAO,GAAG,CAAC,CAAC;MACZC,KAAK,GAAG,CAAC,CAAC;MACV1E,QAAQ,GAAG,IAAI;MACf2E,KAAK,GAAG;IACT,CAAC,GAAGT,IAAI;;IAER;IACA,MAAMU,OAAO,GAAGrH,GAAG,CAClBsH,IAAI,CAACC,SAAS,CAAAlB,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAOrB,UAAU,GAAKkC,OAAO,GAAKC,KAAK,CAAG,CACzD,CAAC;IAED,MAAM7F,IAAI,GAAGyF,SAAS,CAAEM,OAAO,CAAE,IAAI;MACpCF,KAAK,EAAE,CAAC,CAAC;MACTK,OAAO,EAAE,KAAK;MACdC,OAAO,EAAEvH,CAAC,CAACwH,QAAQ,CAAC,CAAC;MACrBC,OAAO,EAAE;IACV,CAAC;;IAED;IACArG,IAAI,CAAC6F,KAAK,GAAAd,aAAA,CAAAA,aAAA,KAAQ/E,IAAI,CAAC6F,KAAK,GAAKA,KAAK,CAAE;IAExC,IAAK7F,IAAI,CAACqG,OAAO,EAAG,OAAOrG,IAAI,CAACmG,OAAO;;IAEvC;IACAG,YAAY,CAAEtG,IAAI,CAACkG,OAAQ,CAAC;IAC5BlG,IAAI,CAACkG,OAAO,GAAGK,UAAU,CAAE,MAAM;MAChCvG,IAAI,CAACqG,OAAO,GAAG,IAAI;MACnB,IAAKX,UAAU,CAAEK,OAAO,CAAE,EAAG;QAC5BN,SAAS,CAAEM,OAAO,CAAE,GAAG,IAAI;QAC3B/F,IAAI,CAACmG,OAAO,CAACK,OAAO,CAACC,KAAK,CACzBf,UAAU,CAAEK,OAAO,CAAE,CAAE,CAAC,CAAE,EAC1BL,UAAU,CAAEK,OAAO,CAAE,CAAE,CAAC,CACzB,CAAC;MACF,CAAC,MAAM;QACNnH,CAAC,CAAC8H,IAAI,CAAE;UACPC,GAAG,EAAElE,GAAG,CAACC,GAAG,CAAE,SAAU,CAAC;UACzBkE,QAAQ,EAAE,MAAM;UAChB9C,IAAI,EAAE,MAAM;UACZ+C,KAAK,EAAE,KAAK;UACZ7G,IAAI,EAAEyC,GAAG,CAACqE,cAAc,CAAE;YACzBC,MAAM,EAAE,sBAAsB;YAC9BrF,KAAK,EAAEsE,IAAI,CAACC,SAAS,CAAEvC,UAAW,CAAC;YACnCvC,QAAQ,EAAEA,QAAQ;YAClByE,OAAO,EAAEI,IAAI,CAACC,SAAS,CAAEL,OAAQ,CAAC;YAClCC,KAAK,EAAE7F,IAAI,CAAC6F;UACb,CAAE;QACH,CAAE,CAAC,CACDmB,MAAM,CAAE,MAAM;UACd;UACAvB,SAAS,CAAEM,OAAO,CAAE,GAAG,IAAI;QAC5B,CAAE,CAAC,CACFkB,IAAI,CAAE,YAAY;UAClBvB,UAAU,CAAEK,OAAO,CAAE,GAAG,CAAE,IAAI,EAAEmB,SAAS,CAAE;UAC3ClH,IAAI,CAACmG,OAAO,CAACK,OAAO,CAACC,KAAK,CAAE,IAAI,EAAES,SAAU,CAAC;QAC9C,CAAE,CAAC,CACFC,IAAI,CAAE,YAAY;UAClBnH,IAAI,CAACmG,OAAO,CAACiB,MAAM,CAACX,KAAK,CAAE,IAAI,EAAES,SAAU,CAAC;QAC7C,CAAE,CAAC;MACL;IACD,CAAC,EAAEpB,KAAM,CAAC;;IAEV;IACAL,SAAS,CAAEM,OAAO,CAAE,GAAG/F,IAAI;;IAE3B;IACA,OAAOA,IAAI,CAACmG,OAAO;EACpB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASkB,cAAcA,CAAEC,IAAI,EAAEC,IAAI,EAAG;IACrC,OAAOvB,IAAI,CAACC,SAAS,CAAEqB,IAAK,CAAC,KAAKtB,IAAI,CAACC,SAAS,CAAEsB,IAAK,CAAC;EACzD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC9E,GAAG,CAAC+E,QAAQ,GAAG,CAAEC,IAAI,EAAEC,eAAe,KAAM;IAC3C;IACAD,IAAI,GAAG,OAAO,GAAGA,IAAI,GAAG,QAAQ;IAChC;IACAA,IAAI,GAAGA,IAAI,CAACE,OAAO,CAClB,yBAAyB,EACzB,+BACD,CAAC;IACD,OAAOC,SAAS,CAAEhJ,CAAC,CAAE6I,IAAK,CAAC,CAAE,CAAC,CAAE,EAAEC,eAAe,EAAE,CAAE,CAAC,CAAC5C,KAAK,CAAC+C,QAAQ;EACtE,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASD,SAASA,CAAEE,IAAI,EAAEJ,eAAe,EAAEK,KAAK,GAAG,CAAC,EAAG;IACtD;IACA,MAAMC,QAAQ,GAAGC,aAAa,CAC7BH,IAAI,CAACE,QAAQ,CAACE,WAAW,CAAC,CAAC,EAC3BR,eACD,CAAC;IACD,IAAK,CAAEM,QAAQ,EAAG;MACjB,OAAO,IAAI;IACZ;;IAEA;IACA,MAAMG,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAKJ,KAAK,KAAK,CAAC,IAAIC,QAAQ,KAAK,gBAAgB,EAAG;MACnD;MACAG,SAAS,CAACC,GAAG,GAAGtI,KAAK,CAACuI,SAAS,CAAC,CAAC;IAClC;IAEA5F,GAAG,CAAC6F,SAAS,CAAER,IAAI,CAACpE,UAAW,CAAC,CAC9B6E,GAAG,CAAEC,aAAc,CAAC,CACpBjD,OAAO,CAAE,CAAE;MAAEzE,IAAI;MAAE2H;IAAM,CAAC,KAAM;MAChCN,SAAS,CAAErH,IAAI,CAAE,GAAG2H,KAAK;IAC1B,CAAE,CAAC;IAEJ,IAAK,gBAAgB,KAAKT,QAAQ,EAAG;MACpC,OAAOjF,iEAAA,CAAC2F,cAAc,EAAA3D,aAAA,KAAMoD,SAAS,CAAI,CAAC;IAC3C;;IAEA;IACA,MAAM9C,IAAI,GAAG,CAAE2C,QAAQ,EAAEG,SAAS,CAAE;IACpC1F,GAAG,CAAC6F,SAAS,CAAER,IAAI,CAACa,UAAW,CAAC,CAACpD,OAAO,CAAIqD,KAAK,IAAM;MACtD,IAAKA,KAAK,YAAYC,IAAI,EAAG;QAC5B,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW;QAC9B,IAAKD,IAAI,EAAG;UACXzD,IAAI,CAAC9C,IAAI,CAAEuG,IAAK,CAAC;QAClB;MACD,CAAC,MAAM;QACNzD,IAAI,CAAC9C,IAAI,CAAEqF,SAAS,CAAEgB,KAAK,EAAElB,eAAe,EAAEK,KAAK,GAAG,CAAE,CAAE,CAAC;MAC5D;IACD,CAAE,CAAC;;IAEH;IACA,OAAOjI,KAAK,CAACiD,aAAa,CAAC0D,KAAK,CAAE,IAAI,EAAEpB,IAAK,CAAC;EAC/C;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAAS2D,UAAUA,CAAElI,IAAI,EAAG;IAC3B,MAAMmI,WAAW,GAAGxG,GAAG,CAACyG,KAAK,CAAEzG,GAAG,EAAE,qBAAqB,EAAE3B,IAAK,CAAC;IACjE,IAAKmI,WAAW,EAAG,OAAOA,WAAW;IACrC,OAAOnI,IAAI;EACZ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASmH,aAAaA,CAAEnH,IAAI,EAAE4G,eAAe,EAAG;IAC/C,QAAS5G,IAAI;MACZ,KAAK,aAAa;QACjB,IAAK4G,eAAe,GAAG,CAAC,EAAG;UAC1B,OAAO1I,WAAW;QACnB;QACA,OAAO,gBAAgB;MACxB,KAAK,QAAQ;QACZ,OAAOmK,MAAM;MACd,KAAK,UAAU;QACd,OAAO,IAAI;MACZ;QACC;QACArI,IAAI,GAAGkI,UAAU,CAAElI,IAAK,CAAC;IAC3B;IACA,OAAOA,IAAI;EACZ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAAS4H,cAAcA,CAAE5D,KAAK,EAAG;IAChC,MAAM;MAAEsE,SAAS,GAAG;IAA4B,CAAC,GAAGtE,KAAK;IACzD,MAAMuE,eAAe,GAAG3I,mBAAmB,CAC1C;MAAE0I,SAAS,EAAEA;IAAU,CAAC,EACxBtE,KACD,CAAC;IAED,OAAO/B,iEAAA,QAAAgC,aAAA,KAAUsE,eAAe,GAAKA,eAAe,CAACxB,QAAe,CAAC;EACtE;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASW,aAAaA,CAAEc,QAAQ,EAAG;IAClC,IAAIxI,IAAI,GAAGwI,QAAQ,CAACxI,IAAI;IACxB,IAAI2H,KAAK,GAAGa,QAAQ,CAACb,KAAK;;IAE1B;IACA,IAAIc,QAAQ,GAAG9G,GAAG,CAAC+G,YAAY,CAC9B,4BAA4B,EAC5B,KAAK,EACLF,QACD,CAAC;IAED,IAAKC,QAAQ,EAAG,OAAOA,QAAQ;IAE/B,QAASzI,IAAI;MACZ;MACA,KAAK,OAAO;QACXA,IAAI,GAAG,WAAW;QAClB;;MAED;MACA,KAAK,OAAO;QACX,MAAM2I,GAAG,GAAG,CAAC,CAAC;QACdhB,KAAK,CAACiB,KAAK,CAAE,GAAI,CAAC,CAACnE,OAAO,CAAIoE,CAAC,IAAM;UACpC,MAAMC,GAAG,GAAGD,CAAC,CAACE,OAAO,CAAE,GAAI,CAAC;UAC5B,IAAKD,GAAG,GAAG,CAAC,EAAG;YACd,IAAIE,QAAQ,GAAGH,CAAC,CAAC9G,MAAM,CAAE,CAAC,EAAE+G,GAAI,CAAC,CAACG,IAAI,CAAC,CAAC;YACxC,MAAMC,SAAS,GAAGL,CAAC,CAAC9G,MAAM,CAAE+G,GAAG,GAAG,CAAE,CAAC,CAACG,IAAI,CAAC,CAAC;;YAE5C;YACA,IAAKD,QAAQ,CAACG,MAAM,CAAE,CAAE,CAAC,KAAK,GAAG,EAAG;cACnCH,QAAQ,GAAGrH,GAAG,CAACyH,YAAY,CAAEJ,QAAS,CAAC;YACxC;YACAL,GAAG,CAAEK,QAAQ,CAAE,GAAGE,SAAS;UAC5B;QACD,CAAE,CAAC;QACHvB,KAAK,GAAGgB,GAAG;QACX;;MAED;MACA;QACC;QACA,IAAK3I,IAAI,CAAC+I,OAAO,CAAE,OAAQ,CAAC,KAAK,CAAC,EAAG;UACpC;QACD;;QAEA;QACA/I,IAAI,GAAGkI,UAAU,CAAElI,IAAK,CAAC;;QAEzB;QACA,MAAMqJ,EAAE,GAAG1B,KAAK,CAACwB,MAAM,CAAE,CAAE,CAAC;QAC5B,IAAKE,EAAE,KAAK,GAAG,IAAIA,EAAE,KAAK,GAAG,EAAG;UAC/B1B,KAAK,GAAGzC,IAAI,CAACoE,KAAK,CAAE3B,KAAM,CAAC;QAC5B;;QAEA;QACA,IAAKA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,EAAG;UAC5CA,KAAK,GAAGA,KAAK,KAAK,MAAM;QACzB;QACA;IACF;IACA,OAAO;MACN3H,IAAI;MACJ2H;IACD,CAAC;EACF;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,MAAM4B,qBAAqB,GAAGpK,0BAA0B,CACrDqK,cAAc,IACf,MAAMC,gBAAgB,SAAS1K,SAAS,CAAC;IACxC2K,WAAWA,CAAE1F,KAAK,EAAG;MACpB,KAAK,CAAEA,KAAM,CAAC;;MAEd;MACA,MAAM;QAAEhE,IAAI;QAAE4C;MAAW,CAAC,GAAG,IAAI,CAACoB,KAAK;;MAEvC;MACA,MAAM9D,SAAS,GAAGH,YAAY,CAAEC,IAAK,CAAC;MACtC,IAAK,CAAEE,SAAS,EAAG;QAClB;MACD;;MAEA;MACAyJ,MAAM,CAACC,IAAI,CAAEhH,UAAW,CAAC,CAAC6B,OAAO,CAAI9B,GAAG,IAAM;QAC7C,IAAKC,UAAU,CAAED,GAAG,CAAE,KAAK,EAAE,EAAG;UAC/B,OAAOC,UAAU,CAAED,GAAG,CAAE;QACzB;MACD,CAAE,CAAC;;MAEH;MACA,MAAMkH,QAAQ,GAAG;QAChBhG,WAAW,EAAE,YAAY;QACzBH,aAAa,EAAE,cAAc;QAC7BJ,UAAU,EAAE;MACb,CAAC;MAEDqG,MAAM,CAACC,IAAI,CAAEC,QAAS,CAAC,CAACpF,OAAO,CAAI9B,GAAG,IAAM;QAC3C,IAAKC,UAAU,CAAED,GAAG,CAAE,KAAK5E,SAAS,EAAG;UACtC6E,UAAU,CAAEiH,QAAQ,CAAElH,GAAG,CAAE,CAAE,GAAGC,UAAU,CAAED,GAAG,CAAE;QAClD,CAAC,MAAM,IACNC,UAAU,CAAEiH,QAAQ,CAAElH,GAAG,CAAE,CAAE,KAAK5E,SAAS,EAC1C;UACD;UACA,IAAKmC,SAAS,CAAEyC,GAAG,CAAE,KAAK5E,SAAS,EAAG;YACrC6E,UAAU,CAAEiH,QAAQ,CAAElH,GAAG,CAAE,CAAE,GAC5BzC,SAAS,CAAEyC,GAAG,CAAE;UAClB;QACD;QACA,OAAOzC,SAAS,CAAEyC,GAAG,CAAE;QACvB,OAAOC,UAAU,CAAED,GAAG,CAAE;MACzB,CAAE,CAAC;;MAEH;MACA,KAAM,IAAImH,SAAS,IAAI5J,SAAS,CAAC0C,UAAU,EAAG;QAC7C,IACCA,UAAU,CAAEkH,SAAS,CAAE,KAAK/L,SAAS,IACrCmC,SAAS,CAAE4J,SAAS,CAAE,KAAK/L,SAAS,EACnC;UACD6E,UAAU,CAAEkH,SAAS,CAAE,GAAG5J,SAAS,CAAE4J,SAAS,CAAE;QACjD;MACD;IACD;IACAC,MAAMA,CAAA,EAAG;MACR,OAAO9H,iEAAA,CAACuH,cAAc,EAAAvF,aAAA,KAAM,IAAI,CAACD,KAAK,CAAI,CAAC;IAC5C;EACD,CAAC,EACF,uBACD,CAAC;EACD1F,EAAE,CAAC0L,KAAK,CAACC,SAAS,CACjB,uBAAuB,EACvB,6BAA6B,EAC7BV,qBACD,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;EACC,SAASnG,SAASA,CAAA,EAAG;IACpB,OAAOnB,iEAAA,CAAC/D,WAAW,CAACgM,OAAO,MAAE,CAAC;EAC/B;;EAEA;AACD;AACA;AACA;AACA;AACA;EACC,MAAMhH,SAAS,SAASnE,SAAS,CAAC;IACjC2K,WAAWA,CAAE1F,KAAK,EAAG;MACpB,KAAK,CAAEA,KAAM,CAAC;MACd,IAAI,CAACmG,KAAK,CAAC,CAAC;IACb;IAEAA,KAAKA,CAAA,EAAG;MACP,MAAM;QAAEnK,IAAI;QAAE4C,UAAU;QAAEvC;MAAS,CAAC,GAAG,IAAI,CAAC2D,KAAK;MACjD,MAAM9D,SAAS,GAAGH,YAAY,CAAEC,IAAK,CAAC;;MAEtC;MACA,SAASoK,YAAYA,CAAEC,KAAK,EAAG;QAC9B,IAAK,CAAEA,KAAK,CAACxI,QAAQ,CAAEe,UAAU,CAAC0H,IAAK,CAAC,EAAG;UAC1C1H,UAAU,CAAC0H,IAAI,GAAGD,KAAK,CAAE,CAAC,CAAE;QAC7B;MACD;MAEA,IACCjK,kBAAkB,CAAEC,QAAS,CAAC,IAC9BS,YAAY,CAAC,CAAC,IACdO,4BAA4B,CAAC,CAAC,IAC9BD,iBAAiB,CAAC,CAAC,EAClB;QACDgJ,YAAY,CAAE,CAAE,SAAS,CAAG,CAAC;MAC9B,CAAC,MAAM;QACN,QAASlK,SAAS,CAACoK,IAAI;UACtB,KAAK,MAAM;YACVF,YAAY,CAAE,CAAE,MAAM,EAAE,SAAS,CAAG,CAAC;YACrC;UACD,KAAK,SAAS;YACbA,YAAY,CAAE,CAAE,SAAS,EAAE,MAAM,CAAG,CAAC;YACrC;UACD;YACCA,YAAY,CAAE,CAAE,MAAM,CAAG,CAAC;YAC1B;QACF;MACD;IACD;IAEAL,MAAMA,CAAA,EAAG;MACR,MAAM;QAAE/J,IAAI;QAAE4C,UAAU;QAAE2H,aAAa;QAAElK;MAAS,CAAC,GAAG,IAAI,CAAC2D,KAAK;MAChE,MAAM9D,SAAS,GAAGH,YAAY,CAAEC,IAAK,CAAC;MACtC,MAAMwK,YAAY,GACjBpK,kBAAkB,CAAEC,QAAS,CAAC,IAC9BS,YAAY,CAAC,CAAC,IACdO,4BAA4B,CAAC,CAAC,IAC9BD,iBAAiB,CAAC,CAAC;MACpB,IAAI;QAAEkJ;MAAK,CAAC,GAAG1H,UAAU;MAEzB,IAAK4H,YAAY,EAAG;QACnBF,IAAI,GAAG,SAAS;MACjB;;MAEA;MACA,IAAIG,UAAU,GAAGvK,SAAS,CAAC4C,QAAQ,CAACwH,IAAI;MACxC,IAAKA,IAAI,KAAK,MAAM,IAAIE,YAAY,EAAG;QACtCC,UAAU,GAAG,KAAK;MACnB;;MAEA;MACA,MAAMC,UAAU,GACfJ,IAAI,KAAK,SAAS,GACf3I,GAAG,CAACgJ,EAAE,CAAE,gBAAiB,CAAC,GAC1BhJ,GAAG,CAACgJ,EAAE,CAAE,mBAAoB,CAAC;MACjC,MAAMC,UAAU,GACfN,IAAI,KAAK,SAAS,GAAG,MAAM,GAAG,mBAAmB;MAClD,SAASO,UAAUA,CAAA,EAAG;QACrBN,aAAa,CAAE;UACdD,IAAI,EAAEA,IAAI,KAAK,SAAS,GAAG,MAAM,GAAG;QACrC,CAAE,CAAC;MACJ;;MAEA;MACA,OACCrI,iEAAA,CAACpD,QAAQ,QACRoD,iEAAA,CAACjE,aAAa,QACXyM,UAAU,IACXxI,iEAAA,CAACzD,YAAY,QACZyD,iEAAA,CAACxD,aAAa;QACb6J,SAAS,EAAC,oDAAoD;QAC9DwC,KAAK,EAAGJ,UAAY;QACpB5I,IAAI,EAAG8I,UAAY;QACnBG,OAAO,EAAGF;MAAY,CACtB,CACY,CAED,CAAC,EAEhB5I,iEAAA,CAAChE,iBAAiB,QACfqM,IAAI,KAAK,SAAS,IACnBrI,iEAAA;QAAKqG,SAAS,EAAC;MAAqC,GACnDrG,iEAAA,CAAC+I,SAAS,EAAA/G,aAAA,KAAM,IAAI,CAACD,KAAK,CAAI,CAC1B,CAEY,CAAC,EAEpB/B,iEAAA,CAACgJ,SAAS,EAAAhH,aAAA,KAAM,IAAI,CAACD,KAAK,CAAI,CACrB,CAAC;IAEb;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;EACC,SAASkH,UAAUA,CAAElH,KAAK,EAAG;IAC5B,MAAM;MAAEpB,UAAU;MAAEuI,UAAU;MAAEnL;IAAK,CAAC,GAAGgE,KAAK;IAC9C,MAAM;MAAEsG;IAAK,CAAC,GAAG1H,UAAU;IAE3B,IAAIwI,QAAQ,GAAG,IAAI;IACnB,IAAIC,iBAAiB,GAAG,oCAAoC;IAE5D,IAAOf,IAAI,KAAK,MAAM,IAAI,CAAEa,UAAU,IAAMb,IAAI,KAAK,SAAS,EAAG;MAChEe,iBAAiB,IAAI,oBAAoB;MACzCD,QAAQ,GAAG,KAAK;IACjB;IAEA,IAAKnL,eAAe,CAAED,IAAK,CAAC,GAAG,CAAC,EAAG;MAClC,OACCiC,iEAAA,QAAAgC,aAAA,KAAU9F,aAAa,CAAE;QAAEmK,SAAS,EAAE+C;MAAkB,CAAE,CAAC,GACxDD,QAAQ,GACTnJ,iEAAA,CAAC+I,SAAS,EAAA/G,aAAA,KAAMD,KAAK,CAAI,CAAC,GAE1B/B,iEAAA,CAACqJ,YAAY,EAAArH,aAAA,KAAMD,KAAK,CAAI,CAEzB,CAAC;IAER,CAAC,MAAM;MACN,OACC/B,iEAAA,QAAAgC,aAAA,KAAU9F,aAAa,CAAC,CAAC,GACxB8D,iEAAA;QAAKqG,SAAS,EAAC;MAAoC,GAChD8C,QAAQ,GACTnJ,iEAAA,CAAC+I,SAAS,EAAA/G,aAAA,KAAMD,KAAK,CAAI,CAAC,GAE1B/B,iEAAA,CAACqJ,YAAY,EAAArH,aAAA,KAAMD,KAAK,CAAI,CAEzB,CACD,CAAC;IAER;EACD;;EAEA;EACA,MAAMiH,SAAS,GAAGhM,UAAU,CAAE,CAAEsB,MAAM,EAAEgL,QAAQ,KAAM;IACrD,MAAM;MAAElL;IAAS,CAAC,GAAGkL,QAAQ;IAC7B;IACA,MAAMC,YAAY,GACjBjL,MAAM,CAAE,mBAAoB,CAAC,CAACkL,oBAAoB,CAAEpL,QAAS,CAAC;IAC/D,MAAMqL,KAAK,GAAGnL,MAAM,CAAE,mBAAoB,CAAC,CAACoL,aAAa,CACxDtL,QAAQ,EACRmL,YACD,CAAC;IACD,OAAO;MACNE;IACD,CAAC;EACF,CAAE,CAAC,CAAER,UAAW,CAAC;;EAEjB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,MAAMhJ,GAAG,SAASnD,SAAS,CAAC;IAC3BgL,MAAMA,CAAA,EAAG;MACR,OACC9H,iEAAA;QACC2J,uBAAuB,EAAG;UAAEC,MAAM,EAAE,IAAI,CAAC7H,KAAK,CAAC+C;QAAS;MAAG,CAC3D,CAAC;IAEJ;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,MAAMsB,MAAM,SAAStJ,SAAS,CAAC;IAC9BgL,MAAMA,CAAA,EAAG;MACR,OAAO9H,iEAAA;QAAKqF,GAAG,EAAKwE,EAAE,IAAQ,IAAI,CAACA,EAAE,GAAGA;MAAM,CAAE,CAAC;IAClD;IACAC,OAAOA,CAAEpF,IAAI,EAAG;MACf7I,CAAC,CAAE,IAAI,CAACgO,EAAG,CAAC,CAACnF,IAAI,CAAG,WAAWA,IAAM,WAAW,CAAC;IAClD;IACAqF,kBAAkBA,CAAA,EAAG;MACpB,IAAI,CAACD,OAAO,CAAE,IAAI,CAAC/H,KAAK,CAAC+C,QAAS,CAAC;IACpC;IACAkF,iBAAiBA,CAAA,EAAG;MACnB,IAAI,CAACF,OAAO,CAAE,IAAI,CAAC/H,KAAK,CAAC+C,QAAS,CAAC;IACpC;EACD;;EAEA;EACA,MAAMmF,KAAK,GAAG,CAAC,CAAC;;EAEhB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,MAAMC,WAAW,SAASpN,SAAS,CAAC;IACnC2K,WAAWA,CAAE1F,KAAK,EAAG;MACpB,KAAK,CAAEA,KAAM,CAAC;;MAEd;MACA,IAAI,CAACoI,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,IAAI,CAAE,IAAK,CAAC;;MAEtC;MACA,IAAI,CAACC,EAAE,GAAG,EAAE;MACZ,IAAI,CAACR,EAAE,GAAG,KAAK;MACf,IAAI,CAACS,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,YAAY,GAAG,QAAQ;MAC5B,IAAI,CAACrC,KAAK,CAAEnG,KAAM,CAAC;;MAEnB;MACA,IAAI,CAACyI,SAAS,CAAC,CAAC;IACjB;IAEAtC,KAAKA,CAAEnG,KAAK,EAAG;MACd;IAAA;IAGD0I,KAAKA,CAAA,EAAG;MACP;IAAA;IAGDC,YAAYA,CAAEC,OAAO,EAAEvM,QAAQ,EAAEwM,IAAI,EAAG;MACvC,IACC,IAAI,CAACC,KAAK,CAACnG,IAAI,KAAK5I,SAAS,IAC7B,CAAEqC,kBAAkB,CAAE,IAAI,CAAC4D,KAAK,CAAC3D,QAAS,CAAC,EAC1C;QACD,MAAM0M,eAAe,GAAGpL,GAAG,CAACC,GAAG,CAAE,iBAAkB,CAAC;QACpD,MAAMoL,QAAQ,GAAGH,IAAI,GAAG,MAAM,GAAG,SAAS;QAE1C,IAAKE,eAAe,IAAIA,eAAe,CAAEH,OAAO,CAAE,EAAG;UACpD;UACA,IACGC,IAAI,IAAI,CAAEE,eAAe,CAAEH,OAAO,CAAE,CAACC,IAAI,IACzC,CAAEA,IAAI,IAAIE,eAAe,CAAEH,OAAO,CAAE,CAACC,IAAM,EAE7C,OAAO,KAAK;;UAEb;UACA,OAAOE,eAAe,CAAEH,OAAO,CAAE,CAACjG,IAAI,CAACsG,UAAU,CAChDL,OAAO,EACPvM,QACD,CAAC;QACF;MACD;MACA,OAAO,KAAK;IACb;IAEAoM,SAASA,CAAA,EAAG;MACX,IAAI,CAACK,KAAK,GAAGZ,KAAK,CAAE,IAAI,CAACI,EAAE,CAAE,IAAI,CAAC,CAAC;IACpC;IAEAY,QAAQA,CAAEJ,KAAK,EAAG;MACjBZ,KAAK,CAAE,IAAI,CAACI,EAAE,CAAE,GAAArI,aAAA,CAAAA,aAAA,KAAQ,IAAI,CAAC6I,KAAK,GAAKA,KAAK,CAAE;;MAE9C;MACA;MACA,IAAK,IAAI,CAACP,UAAU,EAAG;QACtB,KAAK,CAACW,QAAQ,CAAEJ,KAAM,CAAC;MACxB;IACD;IAEAK,OAAOA,CAAExG,IAAI,EAAG;MACfA,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAACsC,IAAI,CAAC,CAAC,GAAG,EAAE;;MAE9B;MACA,IAAKtC,IAAI,KAAK,IAAI,CAACmG,KAAK,CAACnG,IAAI,EAAG;QAC/B;MACD;;MAEA;MACA,MAAMmG,KAAK,GAAG;QACbnG;MACD,CAAC;MAED,IAAK,IAAI,CAAC6F,YAAY,KAAK,KAAK,EAAG;QAClCM,KAAK,CAACM,GAAG,GAAGzL,GAAG,CAAC+E,QAAQ,CACvBC,IAAI,EACJ1G,eAAe,CAAE,IAAI,CAAC+D,KAAK,CAAChE,IAAK,CAClC,CAAC;;QAED;QACA,IAAK,CAAE8M,KAAK,CAACM,GAAG,EAAG;UAClBC,OAAO,CAACC,IAAI,CACX,4GACD,CAAC;UACDR,KAAK,CAACnG,IAAI,IAAI,aAAa;UAC3BmG,KAAK,CAACM,GAAG,GAAGzL,GAAG,CAAC+E,QAAQ,CACvBoG,KAAK,CAACnG,IAAI,EACV1G,eAAe,CAAE,IAAI,CAAC+D,KAAK,CAAChE,IAAK,CAClC,CAAC;QACF;;QAEA;QACA,IAAKuN,KAAK,CAACC,OAAO,CAAEV,KAAK,CAACM,GAAI,CAAC,EAAG;UACjC,IAAIK,UAAU,GAAGX,KAAK,CAACM,GAAG,CAACM,IAAI,CAAI5O,OAAO,IACzCE,KAAK,CAAC2O,cAAc,CAAE7O,OAAQ,CAC/B,CAAC;UACDgO,KAAK,CAACxF,GAAG,GAAGmG,UAAU,CAACnG,GAAG;QAC3B,CAAC,MAAM;UACNwF,KAAK,CAACxF,GAAG,GAAGwF,KAAK,CAACM,GAAG,CAAC9F,GAAG;QAC1B;QACAwF,KAAK,CAACc,GAAG,GAAG9P,CAAC,CAAE,IAAI,CAACgO,EAAG,CAAC;MACzB,CAAC,MAAM;QACNgB,KAAK,CAACc,GAAG,GAAG9P,CAAC,CAAE6I,IAAK,CAAC;MACtB;MACA,IAAI,CAACuG,QAAQ,CAAEJ,KAAM,CAAC;IACvB;IAEAV,MAAMA,CAAEN,EAAE,EAAG;MACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACb;IAEA/B,MAAMA,CAAA,EAAG;MACR;MACA,IAAK,IAAI,CAAC+C,KAAK,CAACM,GAAG,EAAG;QACrB;QACA,IAAKnN,eAAe,CAAE,IAAI,CAAC+D,KAAK,CAAChE,IAAK,CAAC,GAAG,CAAC,EAAG;UAC7C,IAAI,CAACoM,MAAM,CAAE,IAAI,CAACU,KAAK,CAACM,GAAI,CAAC;UAC7B,OAAO,IAAI,CAACN,KAAK,CAACM,GAAG;QACtB,CAAC,MAAM;UACN,OAAOnL,iEAAA;YAAKqF,GAAG,EAAG,IAAI,CAAC8E;UAAQ,GAAG,IAAI,CAACU,KAAK,CAACM,GAAU,CAAC;QACzD;MACD;;MAEA;MACA,OACCnL,iEAAA;QAAKqF,GAAG,EAAG,IAAI,CAAC8E;MAAQ,GACvBnK,iEAAA,CAACvD,WAAW,QACXuD,iEAAA,CAACtD,OAAO,MAAE,CACE,CACT,CAAC;IAER;IAEAkP,qBAAqBA,CAAE;MAAEnC;IAAM,CAAC,EAAE;MAAE/E;IAAK,CAAC,EAAG;MAC5C,IAAK+E,KAAK,KAAK,IAAI,CAAC1H,KAAK,CAAC0H,KAAK,EAAG;QACjC,IAAI,CAACoC,iBAAiB,CAAC,CAAC;MACzB;MACA,OAAOnH,IAAI,KAAK,IAAI,CAACmG,KAAK,CAACnG,IAAI;IAChC;IAEAoH,OAAOA,CAAEjJ,OAAO,EAAG;MAClB;MACA;MACA,IAAK,IAAI,CAAC0H,YAAY,KAAK,QAAQ,EAAG;QACrC,MAAMoB,GAAG,GAAG,IAAI,CAACd,KAAK,CAACc,GAAG;QAC1B,MAAMI,WAAW,GAAGJ,GAAG,CAACK,MAAM,CAAC,CAAC;QAChC,MAAMC,WAAW,GAAGpQ,CAAC,CAAE,IAAI,CAACgO,EAAG,CAAC;;QAEhC;QACAoC,WAAW,CAACvH,IAAI,CAAEiH,GAAI,CAAC;;QAEvB;QACA;QACA;QACA;QACA;QACA,IACCI,WAAW,CAACnN,MAAM,IAClBmN,WAAW,CAAE,CAAC,CAAE,KAAKE,WAAW,CAAE,CAAC,CAAE,EACpC;UACDF,WAAW,CAACrH,IAAI,CAAEiH,GAAG,CAACO,KAAK,CAAC,CAAE,CAAC;QAChC;MACD;;MAEA;MACA,QAASrJ,OAAO;QACf,KAAK,QAAQ;UACZ,IAAI,CAACsJ,kBAAkB,CAAC,CAAC;UACzB;QACD,KAAK,SAAS;UACb,IAAI,CAACC,mBAAmB,CAAC,CAAC;UAC1B;MACF;IACD;IAEApC,iBAAiBA,CAAA,EAAG;MACnB;MACA,IAAK,IAAI,CAACa,KAAK,CAACnG,IAAI,KAAK5I,SAAS,EAAG;QACpC,IAAI,CAAC2O,KAAK,CAAC,CAAC;;QAEZ;MACD,CAAC,MAAM;QACN,IAAI,CAACqB,OAAO,CAAE,SAAU,CAAC;MAC1B;IACD;IAEA/B,kBAAkBA,CAAEsC,SAAS,EAAEC,SAAS,EAAG;MAC1C;MACA,IAAI,CAACR,OAAO,CAAE,QAAS,CAAC;IACzB;IAEAK,kBAAkBA,CAAA,EAAG;MACpBzM,GAAG,CAAC6M,QAAQ,CAAE,QAAQ,EAAE,IAAI,CAAC1B,KAAK,CAACc,GAAI,CAAC;IACzC;IAEAa,oBAAoBA,CAAA,EAAG;MACtB9M,GAAG,CAAC6M,QAAQ,CAAE,SAAS,EAAE,IAAI,CAAC1B,KAAK,CAACc,GAAI,CAAC;;MAEzC;MACA,IAAI,CAACrB,UAAU,GAAG,KAAK;IACxB;IAEA8B,mBAAmBA,CAAA,EAAG;MACrB,IAAI,CAAC9B,UAAU,GAAG,IAAI;;MAEtB;MACA;MACA;MACA;MACA;MACA9G,UAAU,CAAE,MAAM;QACjB9D,GAAG,CAAC6M,QAAQ,CAAE,SAAS,EAAE,IAAI,CAAC1B,KAAK,CAACc,GAAI,CAAC;MAC1C,CAAE,CAAC;IACJ;IAEAE,iBAAiBA,CAAA,EAAG;MACnBnM,GAAG,CAAC6M,QAAQ,CAAE,SAAS,EAAE,IAAI,CAAC1B,KAAK,CAACc,GAAI,CAAC;MACzCnI,UAAU,CAAE,MAAM;QACjB9D,GAAG,CAAC6M,QAAQ,CAAE,SAAS,EAAE,IAAI,CAAC1B,KAAK,CAACc,GAAI,CAAC;MAC1C,CAAE,CAAC;IACJ;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,MAAM5C,SAAS,SAASmB,WAAW,CAAC;IACnChC,KAAKA,CAAE;MAAE9J;IAAS,CAAC,EAAG;MACrB,IAAI,CAACiM,EAAE,GAAI,aAAajM,QAAU,EAAC;IACpC;IAEAqM,KAAKA,CAAA,EAAG;MACP;MACA,MAAM;QAAE9J,UAAU;QAAEkC,OAAO;QAAEzE;MAAS,CAAC,GAAG,IAAI,CAAC2D,KAAK;MAEpD,MAAM0K,IAAI,GAAGC,yBAAyB,CAAE/L,UAAU,EAAEkC,OAAQ,CAAC;;MAE7D;MACA,MAAM8J,SAAS,GAAG,IAAI,CAACjC,YAAY,CAAE+B,IAAI,EAAErO,QAAQ,EAAE,IAAK,CAAC;MAE3D,IAAKuO,SAAS,EAAG;QAChB,IAAI,CAACzB,OAAO,CAAEyB,SAAU,CAAC;QACzB;MACD;;MAEA;MACA/J,UAAU,CAAE;QACXjC,UAAU;QACVkC,OAAO;QACPzE,QAAQ;QACR0E,KAAK,EAAE;UACN8H,IAAI,EAAE;QACP;MACD,CAAE,CAAC,CAAC1G,IAAI,CAAE,CAAE;QAAEjH;MAAK,CAAC,KAAM;QACzB,IAAI,CAACiO,OAAO,CAAEjO,IAAI,CAAC2N,IAAI,CAACI,UAAU,CAAE/N,IAAI,CAACmB,QAAQ,EAAEA,QAAS,CAAE,CAAC;MAChE,CAAE,CAAC;IACJ;IAEAgO,mBAAmBA,CAAA,EAAG;MACrB,KAAK,CAACA,mBAAmB,CAAC,CAAC;MAE3B,MAAM;QAAET;MAAI,CAAC,GAAG,IAAI,CAACd,KAAK;;MAE1B;MACA,IAAKc,GAAG,CAAC1O,IAAI,CAAE,kBAAmB,CAAC,KAAK,IAAI,EAAG;QAC9C,IAAI,CAACkP,kBAAkB,CAAC,CAAC;MAC1B;IACD;IAEAA,kBAAkBA,CAAA,EAAG;MACpB,KAAK,CAACA,kBAAkB,CAAC,CAAC;;MAE1B;MACA,MAAM;QAAExL,UAAU;QAAE2H,aAAa;QAAElK;MAAS,CAAC,GAAG,IAAI,CAAC2D,KAAK;MAC1D,MAAMA,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,MAAM;QAAE4J;MAAI,CAAC,GAAG,IAAI,CAACd,KAAK;;MAE1B;MACA,SAAS+B,aAAaA,CAAEC,MAAM,GAAG,KAAK,EAAG;QACxC,MAAM5P,IAAI,GAAGyC,GAAG,CAACoN,SAAS,CAAEnB,GAAG,EAAG,OAAOvN,QAAU,EAAE,CAAC;QACtD,IAAKyO,MAAM,EAAG;UACblM,UAAU,CAAC1D,IAAI,GAAGA,IAAI;QACvB,CAAC,MAAM;UACNqL,aAAa,CAAE;YACdrL;UACD,CAAE,CAAC;QACJ;MACD;;MAEA;MACA,IAAIkG,OAAO,GAAG,KAAK;MACnBwI,GAAG,CAACoB,EAAE,CAAE,cAAc,EAAE,MAAM;QAC7BxJ,YAAY,CAAEJ,OAAQ,CAAC;QACvBA,OAAO,GAAGK,UAAU,CAAEoJ,aAAa,EAAE,GAAI,CAAC;MAC3C,CAAE,CAAC;;MAEH;MACAjB,GAAG,CAAC1O,IAAI,CAAE,kBAAkB,EAAE,IAAK,CAAC;;MAEpC;MACA;MACA,IAAK,CAAE0D,UAAU,CAAC1D,IAAI,EAAG;QACxB2P,aAAa,CAAE,IAAK,CAAC;MACtB;IACD;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,MAAMvD,YAAY,SAASa,WAAW,CAAC;IACtChC,KAAKA,CAAE;MAAE9J,QAAQ;MAAEL;IAAK,CAAC,EAAG;MAC3B,MAAME,SAAS,GAAGH,YAAY,CAAEC,IAAK,CAAC;MACtC,MAAMiP,aAAa,GAAGtN,GAAG,CAACyG,KAAK,CAAE,IAAI,CAACpE,KAAK,EAAE,SAAS,EAAE,QAAS,CAAC;MAElE,IAAI,CAACsI,EAAE,GAAI,gBAAgBjM,QAAU,EAAC;;MAEtC;MACA,IAAK4O,aAAa,EAAG;QACpB,IAAI,CAAC3C,EAAE,GAAI,gBAAgBjM,QAAU,IAAI4O,aAAe,EAAC;MAC1D;MAEA,IAAK/O,SAAS,CAAC4C,QAAQ,CAACsK,GAAG,EAAG;QAC7B,IAAI,CAACZ,YAAY,GAAG,KAAK;MAC1B;IACD;IAEAE,KAAKA,CAAEnI,IAAI,GAAG,CAAC,CAAC,EAAG;MAClB,MAAM;QACL3B,UAAU,GAAG,IAAI,CAACoB,KAAK,CAACpB,UAAU;QAClCvC,QAAQ,GAAG,IAAI,CAAC2D,KAAK,CAAC3D,QAAQ;QAC9ByE,OAAO,GAAG,IAAI,CAACd,KAAK,CAACc,OAAO;QAC5BE,KAAK,GAAG;MACT,CAAC,GAAGT,IAAI;MAER,MAAM;QAAEvE;MAAK,CAAC,GAAG,IAAI,CAACgE,KAAK;;MAE3B;MACA,IAAI,CAACkJ,QAAQ,CAAE;QACdgC,cAAc,EAAEtM,UAAU;QAC1BuM,WAAW,EAAErK;MACd,CAAE,CAAC;MAEH,MAAM4J,IAAI,GAAGC,yBAAyB,CAAE/L,UAAU,EAAEkC,OAAQ,CAAC;;MAE7D;MACA,IAAI8J,SAAS,GAAG,IAAI,CAACjC,YAAY,CAAE+B,IAAI,EAAErO,QAAQ,EAAE,KAAM,CAAC;MAE1D,IAAKuO,SAAS,EAAG;QAChB,IAAK3O,eAAe,CAAED,IAAK,CAAC,IAAI,CAAC,EAAG;UACnC4O,SAAS,GACR,iCAAiC,GACjCA,SAAS,GACT,QAAQ;QACV;QACA,IAAI,CAACzB,OAAO,CAAEyB,SAAU,CAAC;QACzB;MACD;;MAEA;MACA/J,UAAU,CAAE;QACXjC,UAAU;QACVkC,OAAO;QACPzE,QAAQ;QACR0E,KAAK,EAAE;UACNqK,OAAO,EAAE;QACV,CAAC;QACDpK;MACD,CAAE,CAAC,CAACmB,IAAI,CAAE,CAAE;QAAEjH;MAAK,CAAC,KAAM;QACzB,IAAImQ,WAAW,GAAGnQ,IAAI,CAACkQ,OAAO,CAACnC,UAAU,CACxC/N,IAAI,CAACmB,QAAQ,EACbA,QACD,CAAC;QACD,IAAKJ,eAAe,CAAED,IAAK,CAAC,IAAI,CAAC,EAAG;UACnCqP,WAAW,GACV,iCAAiC,GACjCA,WAAW,GACX,QAAQ;QACV;QACA,IAAI,CAAClC,OAAO,CAAEkC,WAAY,CAAC;MAC5B,CAAE,CAAC;IACJ;IAEAjB,kBAAkBA,CAAA,EAAG;MACpB,KAAK,CAACA,kBAAkB,CAAC,CAAC;MAC1B,IAAI,CAACkB,uBAAuB,CAAC,CAAC;IAC/B;IAEAzB,qBAAqBA,CAAE0B,SAAS,EAAEC,SAAS,EAAG;MAC7C,MAAMC,cAAc,GAAGF,SAAS,CAAC3M,UAAU;MAC3C,MAAM8M,cAAc,GAAG,IAAI,CAAC1L,KAAK,CAACpB,UAAU;;MAE5C;MACA,IACC,CAAE2D,cAAc,CAAEkJ,cAAc,EAAEC,cAAe,CAAC,IAClD,CAAEnJ,cAAc,CAAEgJ,SAAS,CAACzK,OAAO,EAAE,IAAI,CAACd,KAAK,CAACc,OAAQ,CAAC,EACxD;QACD,IAAIE,KAAK,GAAG,CAAC;;QAEb;QACA,IAAKyK,cAAc,CAACnH,SAAS,KAAKoH,cAAc,CAACpH,SAAS,EAAG;UAC5DtD,KAAK,GAAG,GAAG;QACZ;QACA,IAAKyK,cAAc,CAAC1M,MAAM,KAAK2M,cAAc,CAAC3M,MAAM,EAAG;UACtDiC,KAAK,GAAG,GAAG;QACZ;QAEA,IAAI,CAAC0H,KAAK,CAAE;UACX9J,UAAU,EAAE6M,cAAc;UAC1B3K,OAAO,EAAEyK,SAAS,CAACzK,OAAO;UAC1BE;QACD,CAAE,CAAC;MACJ;MACA,OAAO,KAAK,CAAC6I,qBAAqB,CAAE0B,SAAS,EAAEC,SAAU,CAAC;IAC3D;IAEAF,uBAAuBA,CAAA,EAAG;MACzB;MACA,MAAM;QAAE1M,UAAU;QAAE5C;MAAK,CAAC,GAAG,IAAI,CAACgE,KAAK;MACvC,MAAM;QAAE4J,GAAG;QAAEtG;MAAI,CAAC,GAAG,IAAI,CAACwF,KAAK;MAC/B,IAAI6C,YAAY;;MAEhB;MACA,MAAM3M,IAAI,GAAGJ,UAAU,CAAC5C,IAAI,CAAC6G,OAAO,CAAE,MAAM,EAAE,EAAG,CAAC;MAElD,IAAKS,GAAG,IAAIA,GAAG,CAACsI,OAAO,EAAG;QACzB;QACAD,YAAY,GAAG7R,CAAC,CAAEwJ,GAAG,CAACsI,OAAQ,CAAC,CAAC3B,MAAM,CAAC,CAAC;MACzC,CAAC,MAAM,IAAKhO,eAAe,CAAED,IAAK,CAAC,IAAI,CAAC,EAAG;QAC1C2P,YAAY,GAAG/B,GAAG;MACnB,CAAC,MAAM;QACN+B,YAAY,GAAG/B,GAAG,CAACtN,OAAO,CAAE,oBAAqB,CAAC;MACnD;;MAEA;MACAqB,GAAG,CAAC6M,QAAQ,CAAE,sBAAsB,EAAEmB,YAAY,EAAE/M,UAAW,CAAC;MAChEjB,GAAG,CAAC6M,QAAQ,CACV,6BAA6BxL,IAAM,EAAC,EACrC2M,YAAY,EACZ/M,UACD,CAAC;IACF;IAEAyL,mBAAmBA,CAAA,EAAG;MACrB,KAAK,CAACA,mBAAmB,CAAC,CAAC;;MAE3B;MACA,IACC,CAAE9H,cAAc,CACf,IAAI,CAACuG,KAAK,CAACoC,cAAc,EACzB,IAAI,CAAClL,KAAK,CAACpB,UACZ,CAAC,IACD,CAAE2D,cAAc,CAAE,IAAI,CAACuG,KAAK,CAACqC,WAAW,EAAE,IAAI,CAACnL,KAAK,CAACc,OAAQ,CAAC,EAC7D;QACD,IAAI,CAAC4H,KAAK,CAAC,CAAC;MACb;;MAEA;MACA;MACA,IAAI,CAAC4C,uBAAuB,CAAC,CAAC;IAC/B;EACD;;EAEA;AACD;AACA;AACA;AACA;EACC,SAASO,UAAUA,CAAA,EAAG;IACrB;IACA,IAAK,CAAEvR,EAAE,CAACC,WAAW,EAAG;MACvBD,EAAE,CAACC,WAAW,GAAGD,EAAE,CAACwR,MAAM;IAC3B;;IAEA;IACA,MAAMhQ,UAAU,GAAG6B,GAAG,CAACC,GAAG,CAAE,YAAa,CAAC;IAC1C,IAAK9B,UAAU,EAAG;MACjBA,UAAU,CAAC2H,GAAG,CAAEnG,iBAAkB,CAAC;IACpC;EACD;;EAEA;EACA;EACAK,GAAG,CAACoO,SAAS,CAAE,SAAS,EAAEF,UAAW,CAAC;;EAEtC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASG,yBAAyBA,CAAEC,KAAK,EAAG;IAC3C,MAAMC,UAAU,GAAG,CAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAE;IAChD,MAAMC,OAAO,GAAG,KAAK;IACrB,OAAOD,UAAU,CAACrO,QAAQ,CAAEoO,KAAM,CAAC,GAAGA,KAAK,GAAGE,OAAO;EACtD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,2BAA2BA,CAAEH,KAAK,EAAG;IAC7C,MAAMC,UAAU,GAAG,CAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAE;IAChD,MAAMC,OAAO,GAAGxO,GAAG,CAACC,GAAG,CAAE,KAAM,CAAC,GAAG,OAAO,GAAG,MAAM;IACnD,OAAOsO,UAAU,CAACrO,QAAQ,CAAEoO,KAAM,CAAC,GAAGA,KAAK,GAAGE,OAAO;EACtD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASE,uBAAuBA,CAAEJ,KAAK,EAAG;IACzC,MAAME,OAAO,GAAG,eAAe;IAC/B,IAAKF,KAAK,EAAG;MACZ,MAAM,CAAEK,CAAC,EAAEC,CAAC,CAAE,GAAGN,KAAK,CAACrH,KAAK,CAAE,GAAI,CAAC;MACnC,OAAQ,GAAGoH,yBAAyB,CACnCM,CACD,CAAG,IAAIF,2BAA2B,CAAEG,CAAE,CAAG,EAAC;IAC3C;IACA,OAAOJ,OAAO;EACf;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASxM,yBAAyBA,CAAE6M,iBAAiB,EAAEtQ,SAAS,EAAG;IAClE;IACA,IAAI8C,IAAI,GACP9C,SAAS,CAAC4C,QAAQ,CAACY,aAAa,IAAIxD,SAAS,CAAC4C,QAAQ,CAACW,YAAY;IACpE,IAAIgN,kBAAkB;IACtB,IAAIC,iBAAiB;IACrB,QAAS1N,IAAI;MACZ,KAAK,QAAQ;QACZyN,kBAAkB,GACjBlR,2BAA2B,IAAIF,2BAA2B;QAC3DqR,iBAAiB,GAAGL,uBAAuB;QAC3C;MACD;QACCI,kBAAkB,GAAGpS,6BAA6B;QAClDqS,iBAAiB,GAAGV,yBAAyB;QAC7C;IACF;;IAEA;IACA,IAAKS,kBAAkB,KAAK1S,SAAS,EAAG;MACvCsP,OAAO,CAACC,IAAI,CACV,QAAQtK,IAAM,sCAChB,CAAC;MACD,OAAOwN,iBAAiB;IACzB;;IAEA;IACAtQ,SAAS,CAACuD,YAAY,GAAGiN,iBAAiB,CAAExQ,SAAS,CAACuD,YAAa,CAAC;;IAEpE;IACA,OAAO,MAAMgG,gBAAgB,SAAS1K,SAAS,CAAC;MAC/CgL,MAAMA,CAAA,EAAG;QACR,MAAM;UAAEnH,UAAU;UAAE2H;QAAc,CAAC,GAAG,IAAI,CAACvG,KAAK;QAChD,MAAM;UAAEP;QAAa,CAAC,GAAGb,UAAU;QACnC,SAAS+N,oBAAoBA,CAAElN,YAAY,EAAG;UAC7C8G,aAAa,CAAE;YACd9G,YAAY,EAAEiN,iBAAiB,CAAEjN,YAAa;UAC/C,CAAE,CAAC;QACJ;QACA,OACCxB,iEAAA,CAACpD,QAAQ,QACRoD,iEAAA,CAACjE,aAAa;UAAC4S,KAAK,EAAC;QAAO,GAC3B3O,iEAAA,CAACwO,kBAAkB;UAClB3F,KAAK,EAAGnJ,GAAG,CAACgJ,EAAE,CAAE,0BAA2B,CAAG;UAC9ChD,KAAK,EAAG+I,iBAAiB,CAAEjN,YAAa,CAAG;UAC3CoN,QAAQ,EAAGF;QAAsB,CACjC,CACa,CAAC,EAChB1O,iEAAA,CAACuO,iBAAiB,EAAAvM,aAAA,KAAM,IAAI,CAACD,KAAK,CAAI,CAC7B,CAAC;MAEb;IACD,CAAC;EACF;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASR,sBAAsBA,CAAEgN,iBAAiB,EAAEtQ,SAAS,EAAG;IAC/D,MAAMwQ,iBAAiB,GAAGN,2BAA2B;;IAErD;IACAlQ,SAAS,CAACmD,SAAS,GAAGqN,iBAAiB,CAAExQ,SAAS,CAACmD,SAAU,CAAC;;IAE9D;IACA,OAAO,MAAMoG,gBAAgB,SAAS1K,SAAS,CAAC;MAC/CgL,MAAMA,CAAA,EAAG;QACR,MAAM;UAAEnH,UAAU;UAAE2H;QAAc,CAAC,GAAG,IAAI,CAACvG,KAAK;QAChD,MAAM;UAAEX;QAAU,CAAC,GAAGT,UAAU;QAEhC,SAASkO,iBAAiBA,CAAEzN,SAAS,EAAG;UACvCkH,aAAa,CAAE;YACdlH,SAAS,EAAEqN,iBAAiB,CAAErN,SAAU;UACzC,CAAE,CAAC;QACJ;QAEA,OACCpB,iEAAA,CAACpD,QAAQ,QACRoD,iEAAA,CAACjE,aAAa;UAAC4S,KAAK,EAAC;QAAO,GAC3B3O,iEAAA,CAAC7D,gBAAgB;UAChBuJ,KAAK,EAAG+I,iBAAiB,CAAErN,SAAU,CAAG;UACxCwN,QAAQ,EAAGC;QAAmB,CAC9B,CACa,CAAC,EAChB7O,iEAAA,CAACuO,iBAAiB,EAAAvM,aAAA,KAAM,IAAI,CAACD,KAAK,CAAI,CAC7B,CAAC;MAEb;IACD,CAAC;EACF;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASF,uBAAuBA,CAAE0M,iBAAiB,EAAEtQ,SAAS,EAAG;IAChE,IAAK,CAAET,+BAA+B,EAAG,OAAO+Q,iBAAiB;;IAEjE;IACA,OAAO,MAAM/G,gBAAgB,SAAS1K,SAAS,CAAC;MAC/CgL,MAAMA,CAAA,EAAG;QACR,MAAM;UAAEnH,UAAU;UAAE2H;QAAc,CAAC,GAAG,IAAI,CAACvG,KAAK;QAChD,MAAM;UAAEJ;QAAW,CAAC,GAAGhB,UAAU;QAEjC,SAASmO,kBAAkBA,CAAEnN,UAAU,EAAG;UACzC2G,aAAa,CAAE;YACd3G;UACD,CAAE,CAAC;QACJ;QAEA,OACC3B,iEAAA,CAACpD,QAAQ,QACRoD,iEAAA,CAACjE,aAAa;UAAC4S,KAAK,EAAC;QAAO,GAC3B3O,iEAAA,CAACxC,+BAA+B;UAC/BuR,QAAQ,EAAGpN,UAAY;UACvBqN,QAAQ,EAAGF;QAAoB,CAC/B,CACa,CAAC,EAChB9O,iEAAA,CAACuO,iBAAiB,EAAAvM,aAAA,KAAM,IAAI,CAACD,KAAK,CAAI,CAC7B,CAAC;MAEb;IACD,CAAC;EACF;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAAST,sBAAsBA,CAAEX,UAAU,EAAEsO,aAAa,EAAElO,IAAI,EAAG;IAClEJ,UAAU,CAAEsO,aAAa,CAAE,GAAG;MAC7BlO,IAAI,EAAEA;IACP,CAAC;IACD,OAAOJ,UAAU;EAClB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAAS+L,yBAAyBA,CAAE/L,UAAU,EAAEkC,OAAO,EAAG;IACzDlC,UAAU,CAAE,cAAc,CAAE,GAAGkC,OAAO;IACtC,OAAOlH,GAAG,CACTsH,IAAI,CAACC,SAAS,CACbwE,MAAM,CAACC,IAAI,CAAEhH,UAAW,CAAC,CACvBuO,IAAI,CAAC,CAAC,CACNC,MAAM,CAAE,CAAEC,GAAG,EAAEC,SAAS,KAAM;MAC9BD,GAAG,CAAEC,SAAS,CAAE,GAAG1O,UAAU,CAAE0O,SAAS,CAAE;MAC1C,OAAOD,GAAG;IACX,CAAC,EAAE,CAAC,CAAE,CACR,CACD,CAAC;EACF;AACD,CAAC,EAAIE,MAAO,CAAC;;;;;;;;;;ACxtDb,CAAE,UAAWzT,CAAC,EAAEC,SAAS,EAAG;EAC3B4D,GAAG,CAAC6P,mBAAmB,GAAG;IACzB,eAAe,EAAE,cAAc;IAC/BC,YAAY,EAAE,cAAc;IAC5B,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,eAAe;IAC9BC,SAAS,EAAE,WAAW;IACtB,oBAAoB,EAAE,mBAAmB;IACzCC,iBAAiB,EAAE,mBAAmB;IACtCC,aAAa,EAAE,eAAe;IAC9BC,eAAe,EAAE,iBAAiB;IAClCC,YAAY,EAAE,cAAc;IAC5B,aAAa,EAAE,YAAY;IAC3BC,UAAU,EAAE,YAAY;IACxBC,aAAa,EAAE,eAAe;IAC9BC,aAAa,EAAE,eAAe;IAC9BC,cAAc,EAAE,gBAAgB;IAChCC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,aAAa;IAC1BC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,UAAU;IACpBC,aAAa,EAAE,eAAe;IAC9B,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,eAAe;IAC9BC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,UAAU;IACpB,YAAY,EAAE,WAAW;IACzBC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,WAAW;IAClBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,WAAW;IACtB,WAAW,EAAE,UAAU;IACvB,WAAW,EAAE,UAAU;IACvBC,QAAQ,EAAE,UAAU;IACpBC,aAAa,EAAE,eAAe;IAC9BC,QAAQ,EAAE,UAAU;IACpB,qBAAqB,EAAE,oBAAoB;IAC3C,6BAA6B,EAAE,2BAA2B;IAC1D,eAAe,EAAE,cAAc;IAC/B,iBAAiB,EAAE,gBAAgB;IACnCC,kBAAkB,EAAE,oBAAoB;IACxCC,yBAAyB,EAAE,2BAA2B;IACtDC,YAAY,EAAE,cAAc;IAC5BC,cAAc,EAAE,gBAAgB;IAChCC,OAAO,EAAE,SAAS;IAClBC,eAAe,EAAE,iBAAiB;IAClCC,iBAAiB,EAAE,mBAAmB;IACtCC,gBAAgB,EAAE,kBAAkB;IACpCC,WAAW,EAAE,aAAa;IAC1BC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,aAAa;IAC1BC,uBAAuB,EAAE,yBAAyB;IAClDC,QAAQ,EAAE,UAAU;IACpBC,cAAc,EAAE,gBAAgB;IAChCC,YAAY,EAAE,cAAc;IAC5BC,eAAe,EAAE,iBAAiB;IAClCC,uBAAuB,EAAE,yBAAyB;IAClDC,qBAAqB,EAAE,uBAAuB;IAC9C,mBAAmB,EAAE,kBAAkB;IACvCC,gBAAgB,EAAE,kBAAkB;IACpCC,QAAQ,EAAE,UAAU;IACpB,mBAAmB,EAAE,kBAAkB;IACvCC,gBAAgB,EAAE,kBAAkB;IACpCC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,cAAc;IAC5BC,yBAAyB,EAAE,2BAA2B;IACtD,cAAc,EAAE,aAAa;IAC7B,WAAW,EAAE,UAAU;IACvBC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,aAAa;IAC1B,aAAa,EAAE,YAAY;IAC3B,eAAe,EAAE,cAAc;IAC/BC,UAAU,EAAE,YAAY;IACxBC,YAAY,EAAE,cAAc;IAC5B,aAAa,EAAE,YAAY;IAC3B,WAAW,EAAE,UAAU;IACvB,kBAAkB,EAAE,gBAAgB;IACpC,cAAc,EAAE,aAAa;IAC7B,YAAY,EAAE,WAAW;IACzB,cAAc,EAAE,aAAa;IAC7B,aAAa,EAAE,YAAY;IAC3BC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,UAAU;IACpBC,cAAc,EAAE,gBAAgB;IAChCC,WAAW,EAAE,aAAa;IAC1BC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,aAAa;IAC1BC,UAAU,EAAE,YAAY;IACxBC,GAAG,EAAE,SAAS;IACdC,aAAa,EAAE,eAAe;IAC9BC,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,aAAa;IAC1BC,UAAU,EAAE,YAAY;IACxBC,cAAc,EAAE,gBAAgB;IAChCC,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,aAAa;IAC1B,YAAY,EAAE,WAAW;IACzB,8BAA8B,EAAE,4BAA4B;IAC5D,4BAA4B,EAAE,0BAA0B;IACxDC,SAAS,EAAE,WAAW;IACtBC,0BAA0B,EAAE,4BAA4B;IACxDC,wBAAwB,EAAE,0BAA0B;IACpDC,QAAQ,EAAE,UAAU;IACpBC,iBAAiB,EAAE,mBAAmB;IACtCC,aAAa,EAAE,eAAe;IAC9B,aAAa,EAAE,WAAW;IAC1B,gBAAgB,EAAE,cAAc;IAChCC,SAAS,EAAE,WAAW;IACtBC,YAAY,EAAE,cAAc;IAC5BC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,WAAW;IACzBC,SAAS,EAAE,WAAW;IACtB,iBAAiB,EAAE,gBAAgB;IACnCC,cAAc,EAAE,gBAAgB;IAChCC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE,cAAc;IAC5BC,gBAAgB,EAAE,kBAAkB;IACpCC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,cAAc;IAC5B,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,eAAe;IAC9B,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,eAAe;IAC9BC,iBAAiB,EAAE,mBAAmB;IACtCC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,aAAa;IAC1B,YAAY,EAAE,WAAW;IACzB,YAAY,EAAE,WAAW;IACzB,cAAc,EAAE,aAAa;IAC7BC,SAAS,EAAE,WAAW;IACtBC,YAAY,EAAE,cAAc;IAC5BC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE,aAAa;IAC1BC,gBAAgB,EAAE,kBAAkB;IACpCC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,YAAY;IACxBC,UAAU,EAAE,YAAY;IACxB,mBAAmB,EAAE,kBAAkB;IACvC,oBAAoB,EAAE,mBAAmB;IACzCC,gBAAgB,EAAE,kBAAkB;IACpCC,iBAAiB,EAAE,mBAAmB;IACtC,aAAa,EAAE,YAAY;IAC3BC,UAAU,EAAE,YAAY;IACxB,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,YAAY;IACxBC,mBAAmB,EAAE,qBAAqB;IAC1CC,gBAAgB,EAAE,kBAAkB;IACpCC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,aAAa;IAC1B,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,eAAe;IAC9BC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,aAAa,EAAE,eAAe;IAC9BC,mBAAmB,EAAE,qBAAqB;IAC1CC,cAAc,EAAE,gBAAgB;IAChCC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,UAAU;IACpBC,cAAc,EAAE,gBAAgB;IAChCC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZ,kBAAkB,EAAE,iBAAiB;IACrCC,eAAe,EAAE,iBAAiB;IAClCC,WAAW,EAAE,aAAa;IAC1BC,SAAS,EAAE,WAAW;IACtBC,kBAAkB,EAAE,oBAAoB;IACxCC,gBAAgB,EAAE,kBAAkB;IACpCC,OAAO,EAAE,SAAS;IAClB,iBAAiB,EAAE,gBAAgB;IACnCC,cAAc,EAAE,gBAAgB;IAChCC,gBAAgB,EAAE,kBAAkB;IACpCC,gBAAgB,EAAE,kBAAkB;IACpCC,UAAU,EAAE,YAAY;IACxBC,YAAY,EAAE,cAAc;IAC5BC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE,aAAa;IAC1BC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,aAAa;IAC1B,YAAY,EAAE,WAAW;IACzB,cAAc,EAAE,aAAa;IAC7BC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,aAAa;IAC1B,wBAAwB,EAAE,uBAAuB;IACjD,yBAAyB,EAAE,wBAAwB;IACnDC,qBAAqB,EAAE,uBAAuB;IAC9CC,sBAAsB,EAAE,wBAAwB;IAChD,kBAAkB,EAAE,iBAAiB;IACrC,mBAAmB,EAAE,kBAAkB;IACvC,gBAAgB,EAAE,eAAe;IACjC,iBAAiB,EAAE,gBAAgB;IACnC,mBAAmB,EAAE,kBAAkB;IACvC,gBAAgB,EAAE,eAAe;IACjC,cAAc,EAAE,aAAa;IAC7BC,eAAe,EAAE,iBAAiB;IAClCC,gBAAgB,EAAE,kBAAkB;IACpCC,aAAa,EAAE,eAAe;IAC9BC,cAAc,EAAE,gBAAgB;IAChCC,gBAAgB,EAAE,kBAAkB;IACpCC,aAAa,EAAE,eAAe;IAC9BC,WAAW,EAAE,aAAa;IAC1BC,8BAA8B,EAAE,gCAAgC;IAChEC,wBAAwB,EAAE,0BAA0B;IACpDC,YAAY,EAAE,cAAc;IAC5BC,cAAc,EAAE,gBAAgB;IAChCC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,cAAc;IAC5B,aAAa,EAAE,YAAY;IAC3B,iBAAiB,EAAE,gBAAgB;IACnC,gBAAgB,EAAE,eAAe;IACjCC,UAAU,EAAE,YAAY;IACxBC,cAAc,EAAE,gBAAgB;IAChCC,UAAU,EAAE,YAAY;IACxBC,aAAa,EAAE,eAAe;IAC9B,oBAAoB,EAAE,mBAAmB;IACzC,qBAAqB,EAAE,oBAAoB;IAC3CC,iBAAiB,EAAE,mBAAmB;IACtCC,kBAAkB,EAAE,oBAAoB;IACxC,cAAc,EAAE,aAAa;IAC7B,eAAe,EAAE,cAAc;IAC/BC,WAAW,EAAE,aAAa;IAC1BC,YAAY,EAAE,cAAc;IAC5B,cAAc,EAAE,YAAY;IAC5BC,UAAU,EAAE,YAAY;IACxBC,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,aAAa;IAC7B,WAAW,EAAE,UAAU;IACvB,eAAe,EAAE,cAAc;IAC/B,gBAAgB,EAAE,eAAe;IACjCC,WAAW,EAAE,aAAa;IAC1B,eAAe,EAAE,cAAc;IAC/BC,YAAY,EAAE,cAAc;IAC5B,YAAY,EAAE,UAAU;IACxB,eAAe,EAAE,aAAa;IAC9B,eAAe,EAAE,aAAa;IAC9BC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE,cAAc;IAC5BC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,YAAY;IACxBC,aAAa,EAAE,eAAe;IAC9B,cAAc,EAAE,aAAa;IAC7BC,WAAW,EAAE,aAAa;IAC1B,cAAc,EAAE,aAAa;IAC7BC,WAAW,EAAE,aAAa;IAC1B,UAAU,EAAE,SAAS;IACrBC,gBAAgB,EAAE,kBAAkB;IACpCC,OAAO,EAAE,SAAS;IAClB,eAAe,EAAE,cAAc;IAC/B,eAAe,EAAE,cAAc;IAC/B,YAAY,EAAE,WAAW;IACzB,YAAY,EAAE,WAAW;IACzB,YAAY,EAAE,WAAW;IACzB,aAAa,EAAE,YAAY;IAC3B,YAAY,EAAE,WAAW;IACzBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,cAAc;IAC5BC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,WAAW;IACtB,UAAU,EAAE,SAAS;IACrB,UAAU,EAAE,SAAS;IACrB,WAAW,EAAE,UAAU;IACvBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClB,aAAa,EAAE,YAAY;IAC3BC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,UAAU;IACpBC,gBAAgB,EAAE,kBAAkB;IACpCC,UAAU,EAAE;EACb,CAAC;AACF,CAAC,EAAIzN,MAAO,CAAC;;;;;;;;;;AChTb;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,kCAAkC,gBAAgB;AAClD;AACA;AACA,KAAK;;AAEL;AACA;AACA,gCAAgC,kBAAkB;AAClD;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;AChCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,sBAAsB,cAAc;AACpC;AACA;AACA,KAAK;;AAEL;AACA;AACA,2BAA2B,OAAO;AAClC;AACA;AACA,KAAK;;AAEL;AACA;AACA,yCAAyC,kBAAkB;AAC3D;AACA;AACA,KAAK;;AAEL;AACA;AACA,kCAAkC,uBAAuB;AACzD;AACA;AACA,KAAK;;AAEL;AACA;AACA,gCAAgC,kBAAkB;AAClD;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA,kCAAkC,gBAAgB;AAClD;AACA;AACA,KAAK;;AAEL;AACA;AACA,mCAAmC,kBAAkB;AACrD;AACA,wBAAwB,OAAO;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;;AAEA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,CAAC;;;;;;;;;;;AC/FD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;ACpBA;AACA,cAAc,mBAAO,CAAC,4CAAO;AAC7B,aAAa,8EAAuB;AACpC,iBAAiB,mBAAO,CAAC,oDAAW;AACpC,YAAY,6EAAsB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;AC/JD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb,IAAI,IAAqC;AACzC;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;;AAGL;;AAEA;AACA,oBAAoB;;AAEpB;AACA;AACA,MAAM;;;AAGN;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,4BAA4B;AAC5B;AACA,qCAAqC;;AAErC,gCAAgC;AAChC;AACA;;AAEA,gCAAgC;;AAEhC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,6FAA6F,aAAa;AAC1G;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iGAAiG,eAAe;AAChH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;;;AAGN;AACA;AACA,KAAK,GAAG;;AAER,kDAAkD;AAClD;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,8MAA8M;;AAE9M;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA,aAAa,YAAY;AACzB,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,YAAY;AACzB,aAAa,WAAW;AACxB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,YAAY;AACzB,aAAa,QAAQ;AACrB,aAAa,WAAW;AACxB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,YAAY;AACzB,aAAa,QAAQ;AACrB,aAAa,WAAW;AACxB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,0BAA0B;;AAE1B,2BAA2B;AAC3B;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,iBAAiB;AAC5B;AACA,WAAW,WAAW;AACtB;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,WAAW;AACtB;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,0BAA0B;;AAE1B;AACA;AACA;;AAEA;AACA,oDAAoD;;AAEpD;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,iCAAiC;;AAEjC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;;;AAGF;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,wCAAwC;AACxC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,EAAE;;;AAGF;AACA;AACA,EAAE;;;AAGF;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,GAAG;AACd,WAAW,GAAG;AACd,WAAW,eAAe;AAC1B,WAAW,GAAG;AACd,WAAW,GAAG;AACd;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;;AAER;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;AACR;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAgB;;AAEhB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,qEAAqE;;AAErE;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;;;AAGA;;AAEA;AACA;AACA,IAAI;AACJ;;AAEA,oBAAoB,oBAAoB;AACxC;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI;;;AAGJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,gBAAgB;;AAEhB,uBAAuB,kBAAkB;;AAEzC;AACA,yBAAyB;;AAEzB,4BAA4B;AAC5B;AACA;;AAEA,gCAAgC;;AAEhC;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,MAAM;;;AAGN;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,IAAI;AACJ;;;AAGA;;AAEA;AACA;AACA,IAAI;AACJ;;AAEA,oBAAoB,oBAAoB;AACxC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB,YAAY,SAAS;AACrB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,QAAQ;AACnB,YAAY;AACZ;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI;;;AAGJ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,wCAAwC;AACxC;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,wBAAwB;;AAExB;;AAEA;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,qIAAqI,yCAAyC;AAC9K;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,IAAI;AACf,WAAW,kBAAkB;AAC7B,WAAW,GAAG;AACd,YAAY,QAAQ;AACpB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,IAAI;AACf,YAAY,QAAQ;AACpB;;;AAGA;AACA;AACA;AACA,SAAS;AACT,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,IAAI;AACf,WAAW,kBAAkB;AAC7B,WAAW,GAAG;AACd;AACA;AACA;AACA,wCAAwC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB,YAAY,cAAc;AAC1B;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK,GAAG;;AAER;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mBAAmB;;AAEnB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA,0CAA0C;AAC1C;;AAEA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA,oCAAoC;AACpC;;AAEA;AACA;AACA,WAAW;AACX;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA,+CAA+C,IAAI;AACnD;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;;;AAGJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA,oCAAoC,IAAI;AACxC;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,0CAA0C;AAC1C;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;;AAEvC;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA,sBAAsB;AACtB;AACA,SAAS;AACT,uBAAuB;AACvB;AACA,SAAS;AACT,uBAAuB;AACvB;AACA,SAAS;AACT,wBAAwB;AACxB;AACA,SAAS;AACT,wBAAwB;AACxB;AACA,SAAS;AACT,iCAAiC;AACjC;AACA,SAAS;AACT,2BAA2B;AAC3B;AACA,SAAS;AACT,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,MAAM;;;AAGN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,2DAA2D;;AAE3D;AACA;;AAEA;AACA,2DAA2D;AAC3D;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;;AAGT;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,aAAa,kBAAkB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;;AAEA;AACA;AACA,gFAAgF;AAChF;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,kBAAkB;;;AAGlB;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA;;AAEA;AACA,IAAI;;;AAGJ;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,8BAA8B;AAC9B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,2HAA2H;AAC3H;AACA;AACA;;AAEA;AACA,UAAU;AACV;AACA;;AAEA;AACA;;AAEA,oEAAoE;;AAEpE;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,GAAG;AACd;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,2DAA2D;AAC3D;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,WAAW;AACtB,WAAW,GAAG;AACd;;;AAGA;AACA;AACA;AACA;;AAEA;AACA,oBAAoB,iBAAiB;AACrC;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN,4CAA4C;;AAE5C;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB;;;AAGA;AACA;AACA;;AAEA,oBAAoB,iBAAiB;AACrC;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,MAAM;AACN;AACA;;AAEA;;AAEA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;;AAEA,sDAAsD;AACtD;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;;AAGA;AACA,oBAAoB,sBAAsB;AAC1C;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,MAAM;;;AAGN;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;;AAEA,kBAAkB,sBAAsB;AACxC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD;;AAEA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA,mCAAmC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;;AAEA;AACA;AACA;AACA,+CAA+C;AAC/C;AACA;;AAEA,0OAA0O;AAC1O;AACA,WAAW;AACX;AACA;;AAEA;AACA,MAAM;AACN,gCAAgC;AAChC;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAU;AACV;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA,SAAS;AACT,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,eAAe,kBAAkB;AACjC;;AAEA;AACA;AACA,YAAY;AACZ;;AAEA;AACA,QAAQ;AACR;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,gBAAgB;AAChB,iBAAiB;AACjB,gBAAgB;AAChB,gBAAgB;AAChB,qBAAqB;AACrB,kBAAkB;AAClB,gBAAgB;AAChB,0DAA0D;AAC1D,oBAAoB;AACpB,qBAAqB;AACrB,qBAAqB;AACrB,qBAAqB;AACrB,iBAAiB;AACjB,kBAAkB;AAClB,sBAAsB;AACtB,YAAY;AACZ,YAAY;AACZ,uBAAuB;AACvB,oBAAoB;AACpB,mBAAmB;AACnB,kBAAkB;AAClB,qBAAqB;AACrB,wBAAwB;AACxB,iBAAiB;AACjB,aAAa;AACb,2BAA2B;AAC3B,0BAA0B;AAC1B,uBAAuB;AACvB,eAAe;AACf,kBAAkB;AAClB,cAAc;AACd,gBAAgB;AAChB,4BAA4B;AAC5B,qBAAqB;AACrB,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;AClrFa;;AAEb,IAAI,KAAqC,EAAE,EAE1C,CAAC;AACF,EAAE,uHAAsD;AACxD;;;;;;;;;;;;;;;;;ACN+C;AAChC;AACf,QAAQ,6DAAa;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACdkC;AACnB;AACf,MAAM,sDAAO;AACb;AACA;AACA;AACA,QAAQ,sDAAO;AACf;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACVkC;AACS;AAC5B;AACf,YAAY,2DAAW;AACvB,SAAS,sDAAO;AAChB;;;;;;;;;;;;;;;ACLe;AACf;;AAEA;AACA;AACA,IAAI;AACJ;AACA,GAAG;AACH;;;;;;UCRA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCzBA;WACA;WACA;WACA,eAAe,4BAA4B;WAC3C,eAAe;WACf,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA,8CAA8C;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;WCNA;WACA;WACA;WACA;WACA;;;;;;;;;;;;;;;ACJ6B", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-blocks.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-jsx-names.js", "webpack://advanced-custom-fields-pro/./node_modules/charenc/charenc.js", "webpack://advanced-custom-fields-pro/./node_modules/crypt/crypt.js", "webpack://advanced-custom-fields-pro/./node_modules/is-buffer/index.js", "webpack://advanced-custom-fields-pro/./node_modules/md5/md5.js", "webpack://advanced-custom-fields-pro/./node_modules/react/cjs/react.development.js", "webpack://advanced-custom-fields-pro/./node_modules/react/index.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/defineProperty.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/toPrimitive.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/toPropertyKey.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/typeof.js", "webpack://advanced-custom-fields-pro/webpack/bootstrap", "webpack://advanced-custom-fields-pro/webpack/runtime/compat get default export", "webpack://advanced-custom-fields-pro/webpack/runtime/define property getters", "webpack://advanced-custom-fields-pro/webpack/runtime/hasOwnProperty shorthand", "webpack://advanced-custom-fields-pro/webpack/runtime/make namespace object", "webpack://advanced-custom-fields-pro/webpack/runtime/node module decorator", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/acf-pro-blocks.js"], "sourcesContent": ["const md5 = require( 'md5' );\n\n( ( $, undefined ) => {\n\t// Dependencies.\n\tconst {\n\t\tBlockControls,\n\t\tInspectorControls,\n\t\tInnerBlocks,\n\t\tuseBlockProps,\n\t\tAlignmentToolbar,\n\t\tBlockVerticalAlignmentToolbar,\n\t} = wp.blockEditor;\n\n\tconst { ToolbarGroup, ToolbarButton, Placeholder, Spinner } = wp.components;\n\tconst { Fragment } = wp.element;\n\tconst { Component } = React;\n\tconst { withSelect } = wp.data;\n\tconst { createHigherOrderComponent } = wp.compose;\n\n\t// Potentially experimental dependencies.\n\tconst BlockAlignmentMatrixToolbar =\n\t\twp.blockEditor.__experimentalBlockAlignmentMatrixToolbar ||\n\t\twp.blockEditor.BlockAlignmentMatrixToolbar;\n\t// Gutenberg v10.x begins transition from Toolbar components to Control components.\n\tconst BlockAlignmentMatrixControl =\n\t\twp.blockEditor.__experimentalBlockAlignmentMatrixControl ||\n\t\twp.blockEditor.BlockAlignmentMatrixControl;\n\tconst BlockFullHeightAlignmentControl =\n\t\twp.blockEditor.__experimentalBlockFullHeightAligmentControl ||\n\t\twp.blockEditor.__experimentalBlockFullHeightAlignmentControl ||\n\t\twp.blockEditor.BlockFullHeightAlignmentControl;\n\tconst useInnerBlocksProps =\n\t\twp.blockEditor.__experimentalUseInnerBlocksProps ||\n\t\twp.blockEditor.useInnerBlocksProps;\n\n\t/**\n\t * Storage for registered block types.\n\t *\n\t * @since 5.8.0\n\t * @var object\n\t */\n\tconst blockTypes = {};\n\n\t/**\n\t * Returns a block type for the given name.\n\t *\n\t * @date\t20/2/19\n\t * @since\t5.8.0\n\t *\n\t * @param\tstring name The block name.\n\t * @return\t(object|false)\n\t */\n\tfunction getBlockType( name ) {\n\t\treturn blockTypes[ name ] || false;\n\t}\n\n\t/**\n\t * Returns a block version for a given block name\n\t *\n\t * @date 8/6/22\n\t * @since 6.0\n\t *\n\t * @param string name The block name\n\t * @return int\n\t */\n\tfunction getBlockVersion( name ) {\n\t\tconst blockType = getBlockType( name );\n\t\treturn blockType.acf_block_version || 1;\n\t}\n\n\t/**\n\t * Returns true if a block (identified by client ID) is nested in a query loop block.\n\t *\n\t * @date 17/1/22\n\t * @since 5.12\n\t *\n\t * @param {string} clientId A block client ID\n\t * @return boolean\n\t */\n\tfunction isBlockInQueryLoop( clientId ) {\n\t\tconst parents = wp.data\n\t\t\t.select( 'core/block-editor' )\n\t\t\t.getBlockParents( clientId );\n\t\tconst parentsData = wp.data\n\t\t\t.select( 'core/block-editor' )\n\t\t\t.getBlocksByClientId( parents );\n\t\treturn parentsData.filter( ( block ) => block.name === 'core/query' )\n\t\t\t.length;\n\t}\n\n\t/**\n\t * Returns true if we're currently inside the WP 5.9+ site editor.\n\t *\n\t * @date 08/02/22\n\t * @since 5.12\n\t *\n\t * @return boolean\n\t */\n\tfunction isSiteEditor() {\n\t\treturn typeof pagenow === 'string' && pagenow === 'site-editor';\n\t}\n\n\t/**\n\t * Returns true if the block editor is currently showing the desktop device type preview.\n\t *\n\t * This function will always return true in the site editor as it uses the\n\t * edit-post store rather than the edit-site store.\n\t *\n\t * @date 15/02/22\n\t * @since 5.12\n\t *\n\t * @return boolean\n\t */\n\tfunction isDesktopPreviewDeviceType() {\n\t\tconst editPostStore = select( 'core/edit-post' );\n\n\t\t// Return true if the edit post store isn't available (such as in the widget editor)\n\t\tif ( ! editPostStore ) return true;\n\n\t\t// Check if function exists (experimental or not) and return true if it's Desktop, or doesn't exist.\n\t\tif ( editPostStore.__experimentalGetPreviewDeviceType ) {\n\t\t\treturn (\n\t\t\t\t'Desktop' === editPostStore.__experimentalGetPreviewDeviceType()\n\t\t\t);\n\t\t} else if ( editPostStore.getPreviewDeviceType ) {\n\t\t\treturn 'Desktop' === editPostStore.getPreviewDeviceType();\n\t\t} else {\n\t\t\treturn true;\n\t\t}\n\t}\n\n\t/**\n\t * Returns true if the block editor is currently in template edit mode.\n\t *\n\t * @date 16/02/22\n\t * @since 5.12\n\t *\n\t * @return boolean\n\t */\n\tfunction isEditingTemplate() {\n\t\tconst editPostStore = select( 'core/edit-post' );\n\n\t\t// Return false if the edit post store isn't available (such as in the widget editor)\n\t\tif ( ! editPostStore ) return false;\n\n\t\t// Return false if the function doesn't exist\n\t\tif ( ! editPostStore.isEditingTemplate ) return false;\n\n\t\treturn editPostStore.isEditingTemplate();\n\t}\n\n\t/**\n\t * Returns true if we're currently inside an iFramed non-desktop device preview type (WP5.9+)\n\t *\n\t * @date 15/02/22\n\t * @since 5.12\n\t *\n\t * @return boolean\n\t */\n\tfunction isiFramedMobileDevicePreview() {\n\t\treturn (\n\t\t\t$( 'iframe[name=editor-canvas]' ).length &&\n\t\t\t! isDesktopPreviewDeviceType()\n\t\t);\n\t}\n\n\t/**\n\t * Registers a block type.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.8.0\n\t *\n\t * @param\tobject blockType The block type settings localized from PHP.\n\t * @return\tobject The result from wp.blocks.registerBlockType().\n\t */\n\tfunction registerBlockType( blockType ) {\n\t\t// Bail early if is excluded post_type.\n\t\tconst allowedTypes = blockType.post_types || [];\n\t\tif ( allowedTypes.length ) {\n\t\t\t// Always allow block to appear on \"Edit reusable Block\" screen.\n\t\t\tallowedTypes.push( 'wp_block' );\n\n\t\t\t// Check post type.\n\t\t\tconst postType = acf.get( 'postType' );\n\t\t\tif ( ! allowedTypes.includes( postType ) ) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\t// Handle svg HTML.\n\t\tif (\n\t\t\ttypeof blockType.icon === 'string' &&\n\t\t\tblockType.icon.substr( 0, 4 ) === '<svg'\n\t\t) {\n\t\t\tconst iconHTML = blockType.icon;\n\t\t\tblockType.icon = <Div>{ iconHTML }</Div>;\n\t\t}\n\n\t\t// Remove icon if empty to allow for default \"block\".\n\t\t// Avoids JS error preventing block from being registered.\n\t\tif ( ! blockType.icon ) {\n\t\t\tdelete blockType.icon;\n\t\t}\n\n\t\t// Check category exists and fallback to \"common\".\n\t\tconst category = wp.blocks\n\t\t\t.getCategories()\n\t\t\t.filter( ( { slug } ) => slug === blockType.category )\n\t\t\t.pop();\n\t\tif ( ! category ) {\n\t\t\t//console.warn( `The block \"${blockType.name}\" is registered with an unknown category \"${blockType.category}\".` );\n\t\t\tblockType.category = 'common';\n\t\t}\n\n\t\t// Merge in block settings before local additions.\n\t\tblockType = acf.parseArgs( blockType, {\n\t\t\ttitle: '',\n\t\t\tname: '',\n\t\t\tcategory: '',\n\t\t\tapi_version: 2,\n\t\t\tacf_block_version: 1,\n\t\t} );\n\n\t\t// Remove all empty attribute defaults from PHP values to allow serialisation.\n\t\t// https://github.com/WordPress/gutenberg/issues/7342\n\t\tfor ( const key in blockType.attributes ) {\n\t\t\tif ( blockType.attributes[ key ].default.length === 0 ) {\n\t\t\t\tdelete blockType.attributes[ key ].default;\n\t\t\t}\n\t\t}\n\n\t\t// Apply anchor supports to avoid block editor default writing to ID.\n\t\tif ( blockType.supports.anchor ) {\n\t\t\tblockType.attributes.anchor = {\n\t\t\t\ttype: 'string',\n\t\t\t};\n\t\t}\n\n\t\t// Append edit and save functions.\n\t\tlet ThisBlockEdit = BlockEdit;\n\t\tlet ThisBlockSave = BlockSave;\n\n\t\t// Apply alignText functionality.\n\t\tif ( blockType.supports.alignText || blockType.supports.align_text ) {\n\t\t\tblockType.attributes = addBackCompatAttribute(\n\t\t\t\tblockType.attributes,\n\t\t\t\t'align_text',\n\t\t\t\t'string'\n\t\t\t);\n\t\t\tThisBlockEdit = withAlignTextComponent( ThisBlockEdit, blockType );\n\t\t}\n\n\t\t// Apply alignContent functionality.\n\t\tif (\n\t\t\tblockType.supports.alignContent ||\n\t\t\tblockType.supports.align_content\n\t\t) {\n\t\t\tblockType.attributes = addBackCompatAttribute(\n\t\t\t\tblockType.attributes,\n\t\t\t\t'align_content',\n\t\t\t\t'string'\n\t\t\t);\n\t\t\tThisBlockEdit = withAlignContentComponent(\n\t\t\t\tThisBlockEdit,\n\t\t\t\tblockType\n\t\t\t);\n\t\t}\n\n\t\t// Apply fullHeight functionality.\n\t\tif ( blockType.supports.fullHeight || blockType.supports.full_height ) {\n\t\t\tblockType.attributes = addBackCompatAttribute(\n\t\t\t\tblockType.attributes,\n\t\t\t\t'full_height',\n\t\t\t\t'boolean'\n\t\t\t);\n\t\t\tThisBlockEdit = withFullHeightComponent(\n\t\t\t\tThisBlockEdit,\n\t\t\t\tblockType.blockType\n\t\t\t);\n\t\t}\n\n\t\t// Set edit and save functions.\n\t\tblockType.edit = ( props ) => <ThisBlockEdit { ...props } />;\n\t\tblockType.save = () => <ThisBlockSave />;\n\n\t\t// Add to storage.\n\t\tblockTypes[ blockType.name ] = blockType;\n\n\t\t// Register with WP.\n\t\tconst result = wp.blocks.registerBlockType( blockType.name, blockType );\n\n\t\t// Fix bug in 'core/anchor/attribute' filter overwriting attribute.\n\t\t// Required for < WP5.9\n\t\t// See https://github.com/WordPress/gutenberg/issues/15240\n\t\tif ( result.attributes.anchor ) {\n\t\t\tresult.attributes.anchor = {\n\t\t\t\ttype: 'string',\n\t\t\t};\n\t\t}\n\n\t\t// Return result.\n\t\treturn result;\n\t}\n\n\t/**\n\t * Returns the wp.data.select() response with backwards compatibility.\n\t *\n\t * @date\t17/06/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring selector The selector name.\n\t * @return\tmixed\n\t */\n\tfunction select( selector ) {\n\t\tif ( selector === 'core/block-editor' ) {\n\t\t\treturn (\n\t\t\t\twp.data.select( 'core/block-editor' ) ||\n\t\t\t\twp.data.select( 'core/editor' )\n\t\t\t);\n\t\t}\n\t\treturn wp.data.select( selector );\n\t}\n\n\t/**\n\t * Returns the wp.data.dispatch() response with backwards compatibility.\n\t *\n\t * @date\t17/06/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring selector The selector name.\n\t * @return\tmixed\n\t */\n\tfunction dispatch( selector ) {\n\t\treturn wp.data.dispatch( selector );\n\t}\n\n\t/**\n\t * Returns an array of all blocks for the given args.\n\t *\n\t * @date\t27/2/19\n\t * @since\t5.7.13\n\t *\n\t * @param\t{object} args An object of key=>value pairs used to filter results.\n\t * @return\tarray.\n\t */\n\tfunction getBlocks( args ) {\n\t\tlet blocks = [];\n\n\t\t// Local function to recurse through all child blocks and add to the blocks array.\n\t\tconst recurseBlocks = ( block ) => {\n\t\t\tblocks.push( block );\n\t\t\tselect( 'core/block-editor' )\n\t\t\t\t.getBlocks( block.clientId )\n\t\t\t\t.forEach( recurseBlocks );\n\t\t};\n\n\t\t// Trigger initial recursion for parent level blocks.\n\t\tselect( 'core/block-editor' ).getBlocks().forEach( recurseBlocks );\n\n\t\t// Loop over args and filter.\n\t\tfor ( const k in args ) {\n\t\t\tblocks = blocks.filter(\n\t\t\t\t( { attributes } ) => attributes[ k ] === args[ k ]\n\t\t\t);\n\t\t}\n\n\t\t// Return results.\n\t\treturn blocks;\n\t}\n\n\t/**\n\t * Storage for the AJAX queue.\n\t *\n\t * @const {array}\n\t */\n\tconst ajaxQueue = {};\n\n\t/**\n\t * Storage for cached AJAX requests for block content.\n\t *\n\t * @since 5.12\n\t * @const {array}\n\t */\n\tconst fetchCache = {};\n\n\t/**\n\t * Fetches a JSON result from the AJAX API.\n\t *\n\t * @date\t28/2/19\n\t * @since\t5.7.13\n\t *\n\t * @param\tobject block The block props.\n\t * @query\tobject The query args used in AJAX callback.\n\t * @return\tobject The AJAX promise.\n\t */\n\tfunction fetchBlock( args ) {\n\t\tconst {\n\t\t\tattributes = {},\n\t\t\tcontext = {},\n\t\t\tquery = {},\n\t\t\tclientId = null,\n\t\t\tdelay = 0,\n\t\t} = args;\n\n\t\t// Build a unique queue ID from block data, including the clientId for edit forms.\n\t\tconst queueId = md5(\n\t\t\tJSON.stringify( { ...attributes, ...context, ...query } )\n\t\t);\n\n\t\tconst data = ajaxQueue[ queueId ] || {\n\t\t\tquery: {},\n\t\t\ttimeout: false,\n\t\t\tpromise: $.Deferred(),\n\t\t\tstarted: false,\n\t\t};\n\n\t\t// Append query args to storage.\n\t\tdata.query = { ...data.query, ...query };\n\n\t\tif ( data.started ) return data.promise;\n\n\t\t// Set fresh timeout.\n\t\tclearTimeout( data.timeout );\n\t\tdata.timeout = setTimeout( () => {\n\t\t\tdata.started = true;\n\t\t\tif ( fetchCache[ queueId ] ) {\n\t\t\t\tajaxQueue[ queueId ] = null;\n\t\t\t\tdata.promise.resolve.apply(\n\t\t\t\t\tfetchCache[ queueId ][ 0 ],\n\t\t\t\t\tfetchCache[ queueId ][ 1 ]\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\t$.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdataType: 'json',\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tcache: false,\n\t\t\t\t\tdata: acf.prepareForAjax( {\n\t\t\t\t\t\taction: 'acf/ajax/fetch-block',\n\t\t\t\t\t\tblock: JSON.stringify( attributes ),\n\t\t\t\t\t\tclientId: clientId,\n\t\t\t\t\t\tcontext: JSON.stringify( context ),\n\t\t\t\t\t\tquery: data.query,\n\t\t\t\t\t} ),\n\t\t\t\t} )\n\t\t\t\t\t.always( () => {\n\t\t\t\t\t\t// Clean up queue after AJAX request is complete.\n\t\t\t\t\t\tajaxQueue[ queueId ] = null;\n\t\t\t\t\t} )\n\t\t\t\t\t.done( function () {\n\t\t\t\t\t\tfetchCache[ queueId ] = [ this, arguments ];\n\t\t\t\t\t\tdata.promise.resolve.apply( this, arguments );\n\t\t\t\t\t} )\n\t\t\t\t\t.fail( function () {\n\t\t\t\t\t\tdata.promise.reject.apply( this, arguments );\n\t\t\t\t\t} );\n\t\t\t}\n\t\t}, delay );\n\n\t\t// Update storage.\n\t\tajaxQueue[ queueId ] = data;\n\n\t\t// Return promise.\n\t\treturn data.promise;\n\t}\n\n\t/**\n\t * Returns true if both object are the same.\n\t *\n\t * @date\t19/05/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tobject obj1\n\t * @param\tobject obj2\n\t * @return\tbool\n\t */\n\tfunction compareObjects( obj1, obj2 ) {\n\t\treturn JSON.stringify( obj1 ) === JSON.stringify( obj2 );\n\t}\n\n\t/**\n\t * Converts HTML into a React element.\n\t *\n\t * @date\t19/05/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring html The HTML to convert.\n\t * @param\tint acfBlockVersion The ACF block version number.\n\t * @return\tobject Result of React.createElement().\n\t */\n\tacf.parseJSX = ( html, acfBlockVersion ) => {\n\t\t// Apply a temporary wrapper for the jQuery parse to prevent text nodes triggering errors.\n\t\thtml = '<div>' + html + '</div>';\n\t\t// Correctly balance InnerBlocks tags for jQuery's initial parse.\n\t\thtml = html.replace(\n\t\t\t/<InnerBlocks([^>]+)?\\/>/,\n\t\t\t'<InnerBlocks$1></InnerBlocks>'\n\t\t);\n\t\treturn parseNode( $( html )[ 0 ], acfBlockVersion, 0 ).props.children;\n\t};\n\n\t/**\n\t * Converts a DOM node into a React element.\n\t *\n\t * @date\t19/05/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tDOM node The DOM node.\n\t * @param\tint acfBlockVersion The ACF block version number.\n\t * @param\tint level The recursion level.\n\t * @return\tobject Result of React.createElement().\n\t */\n\tfunction parseNode( node, acfBlockVersion, level = 0 ) {\n\t\t// Get node name.\n\t\tconst nodeName = parseNodeName(\n\t\t\tnode.nodeName.toLowerCase(),\n\t\t\tacfBlockVersion\n\t\t);\n\t\tif ( ! nodeName ) {\n\t\t\treturn null;\n\t\t}\n\n\t\t// Get node attributes in React friendly format.\n\t\tconst nodeAttrs = {};\n\n\t\tif ( level === 1 && nodeName !== 'ACFInnerBlocks' ) {\n\t\t\t// Top level (after stripping away the container div), create a ref for passing through to ACF's JS API.\n\t\t\tnodeAttrs.ref = React.createRef();\n\t\t}\n\n\t\tacf.arrayArgs( node.attributes )\n\t\t\t.map( parseNodeAttr )\n\t\t\t.forEach( ( { name, value } ) => {\n\t\t\t\tnodeAttrs[ name ] = value;\n\t\t\t} );\n\n\t\tif ( 'ACFInnerBlocks' === nodeName ) {\n\t\t\treturn <ACFInnerBlocks { ...nodeAttrs } />;\n\t\t}\n\n\t\t// Define args for React.createElement().\n\t\tconst args = [ nodeName, nodeAttrs ];\n\t\tacf.arrayArgs( node.childNodes ).forEach( ( child ) => {\n\t\t\tif ( child instanceof Text ) {\n\t\t\t\tconst text = child.textContent;\n\t\t\t\tif ( text ) {\n\t\t\t\t\targs.push( text );\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\targs.push( parseNode( child, acfBlockVersion, level + 1 ) );\n\t\t\t}\n\t\t} );\n\n\t\t// Return element.\n\t\treturn React.createElement.apply( this, args );\n\t}\n\n\t/**\n\t * Converts a node or attribute name into it's JSX compliant name\n\t *\n\t * @date     05/07/2021\n\t * @since    5.9.8\n\t *\n\t * @param    string name The node or attribute name.\n\t * @return  string\n\t */\n\tfunction getJSXName( name ) {\n\t\tconst replacement = acf.isget( acf, 'jsxNameReplacements', name );\n\t\tif ( replacement ) return replacement;\n\t\treturn name;\n\t}\n\n\t/**\n\t * Converts the given name into a React friendly name or component.\n\t *\n\t * @date\t19/05/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring name The node name in lowercase.\n\t * @param\tint acfBlockVersion The ACF block version number.\n\t * @return\tmixed\n\t */\n\tfunction parseNodeName( name, acfBlockVersion ) {\n\t\tswitch ( name ) {\n\t\t\tcase 'innerblocks':\n\t\t\t\tif ( acfBlockVersion < 2 ) {\n\t\t\t\t\treturn InnerBlocks;\n\t\t\t\t}\n\t\t\t\treturn 'ACFInnerBlocks';\n\t\t\tcase 'script':\n\t\t\t\treturn Script;\n\t\t\tcase '#comment':\n\t\t\t\treturn null;\n\t\t\tdefault:\n\t\t\t\t// Replace names for JSX counterparts.\n\t\t\t\tname = getJSXName( name );\n\t\t}\n\t\treturn name;\n\t}\n\n\t/**\n\t * Functional component for ACFInnerBlocks.\n\t *\n\t * @since 6.0.0\n\t *\n\t * @param obj props element properties.\n\t * @return DOM element\n\t */\n\tfunction ACFInnerBlocks( props ) {\n\t\tconst { className = 'acf-innerblocks-container' } = props;\n\t\tconst innerBlockProps = useInnerBlocksProps(\n\t\t\t{ className: className },\n\t\t\tprops\n\t\t);\n\n\t\treturn <div { ...innerBlockProps }>{ innerBlockProps.children }</div>;\n\t}\n\n\t/**\n\t * Converts the given attribute into a React friendly name and value object.\n\t *\n\t * @date\t19/05/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tobj nodeAttr The node attribute.\n\t * @return\tobj\n\t */\n\tfunction parseNodeAttr( nodeAttr ) {\n\t\tlet name = nodeAttr.name;\n\t\tlet value = nodeAttr.value;\n\n\t\t// Allow overrides for third party libraries who might use specific attributes.\n\t\tlet shortcut = acf.applyFilters(\n\t\t\t'acf_blocks_parse_node_attr',\n\t\t\tfalse,\n\t\t\tnodeAttr\n\t\t);\n\n\t\tif ( shortcut ) return shortcut;\n\n\t\tswitch ( name ) {\n\t\t\t// Class.\n\t\t\tcase 'class':\n\t\t\t\tname = 'className';\n\t\t\t\tbreak;\n\n\t\t\t// Style.\n\t\t\tcase 'style':\n\t\t\t\tconst css = {};\n\t\t\t\tvalue.split( ';' ).forEach( ( s ) => {\n\t\t\t\t\tconst pos = s.indexOf( ':' );\n\t\t\t\t\tif ( pos > 0 ) {\n\t\t\t\t\t\tlet ruleName = s.substr( 0, pos ).trim();\n\t\t\t\t\t\tconst ruleValue = s.substr( pos + 1 ).trim();\n\n\t\t\t\t\t\t// Rename core properties, but not CSS variables.\n\t\t\t\t\t\tif ( ruleName.charAt( 0 ) !== '-' ) {\n\t\t\t\t\t\t\truleName = acf.strCamelCase( ruleName );\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcss[ ruleName ] = ruleValue;\n\t\t\t\t\t}\n\t\t\t\t} );\n\t\t\t\tvalue = css;\n\t\t\t\tbreak;\n\n\t\t\t// Default.\n\t\t\tdefault:\n\t\t\t\t// No formatting needed for \"data-x\" attributes.\n\t\t\t\tif ( name.indexOf( 'data-' ) === 0 ) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\t// Replace names for JSX counterparts.\n\t\t\t\tname = getJSXName( name );\n\n\t\t\t\t// Convert JSON values.\n\t\t\t\tconst c1 = value.charAt( 0 );\n\t\t\t\tif ( c1 === '[' || c1 === '{' ) {\n\t\t\t\t\tvalue = JSON.parse( value );\n\t\t\t\t}\n\n\t\t\t\t// Convert bool values.\n\t\t\t\tif ( value === 'true' || value === 'false' ) {\n\t\t\t\t\tvalue = value === 'true';\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t}\n\t\treturn {\n\t\t\tname,\n\t\t\tvalue,\n\t\t};\n\t}\n\n\t/**\n\t * Higher Order Component used to set default block attribute values.\n\t *\n\t * By modifying block attributes directly, instead of defining defaults in registerBlockType(),\n\t * WordPress will include them always within the saved block serialized JSON.\n\t *\n\t * @date\t31/07/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tComponent BlockListBlock The BlockListBlock Component.\n\t * @return\tComponent\n\t */\n\tconst withDefaultAttributes = createHigherOrderComponent(\n\t\t( BlockListBlock ) =>\n\t\t\tclass WrappedBlockEdit extends Component {\n\t\t\t\tconstructor( props ) {\n\t\t\t\t\tsuper( props );\n\n\t\t\t\t\t// Extract vars.\n\t\t\t\t\tconst { name, attributes } = this.props;\n\n\t\t\t\t\t// Only run on ACF Blocks.\n\t\t\t\t\tconst blockType = getBlockType( name );\n\t\t\t\t\tif ( ! blockType ) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Check and remove any empty string attributes to match PHP behaviour.\n\t\t\t\t\tObject.keys( attributes ).forEach( ( key ) => {\n\t\t\t\t\t\tif ( attributes[ key ] === '' ) {\n\t\t\t\t\t\t\tdelete attributes[ key ];\n\t\t\t\t\t\t}\n\t\t\t\t\t} );\n\n\t\t\t\t\t// Backward compatibility attribute replacement.\n\t\t\t\t\tconst upgrades = {\n\t\t\t\t\t\tfull_height: 'fullHeight',\n\t\t\t\t\t\talign_content: 'alignContent',\n\t\t\t\t\t\talign_text: 'alignText',\n\t\t\t\t\t};\n\n\t\t\t\t\tObject.keys( upgrades ).forEach( ( key ) => {\n\t\t\t\t\t\tif ( attributes[ key ] !== undefined ) {\n\t\t\t\t\t\t\tattributes[ upgrades[ key ] ] = attributes[ key ];\n\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\tattributes[ upgrades[ key ] ] === undefined\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t//Check for a default\n\t\t\t\t\t\t\tif ( blockType[ key ] !== undefined ) {\n\t\t\t\t\t\t\t\tattributes[ upgrades[ key ] ] =\n\t\t\t\t\t\t\t\t\tblockType[ key ];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tdelete blockType[ key ];\n\t\t\t\t\t\tdelete attributes[ key ];\n\t\t\t\t\t} );\n\n\t\t\t\t\t// Set default attributes for those undefined.\n\t\t\t\t\tfor ( let attribute in blockType.attributes ) {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tattributes[ attribute ] === undefined &&\n\t\t\t\t\t\t\tblockType[ attribute ] !== undefined\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tattributes[ attribute ] = blockType[ attribute ];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\trender() {\n\t\t\t\t\treturn <BlockListBlock { ...this.props } />;\n\t\t\t\t}\n\t\t\t},\n\t\t'withDefaultAttributes'\n\t);\n\twp.hooks.addFilter(\n\t\t'editor.BlockListBlock',\n\t\t'acf/with-default-attributes',\n\t\twithDefaultAttributes\n\t);\n\n\t/**\n\t * The BlockSave functional component.\n\t *\n\t * @date\t08/07/2020\n\t * @since\t5.9.0\n\t */\n\tfunction BlockSave() {\n\t\treturn <InnerBlocks.Content />;\n\t}\n\n\t/**\n\t * The BlockEdit component.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.7.12\n\t */\n\tclass BlockEdit extends Component {\n\t\tconstructor( props ) {\n\t\t\tsuper( props );\n\t\t\tthis.setup();\n\t\t}\n\n\t\tsetup() {\n\t\t\tconst { name, attributes, clientId } = this.props;\n\t\t\tconst blockType = getBlockType( name );\n\n\t\t\t// Restrict current mode.\n\t\t\tfunction restrictMode( modes ) {\n\t\t\t\tif ( ! modes.includes( attributes.mode ) ) {\n\t\t\t\t\tattributes.mode = modes[ 0 ];\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\tisBlockInQueryLoop( clientId ) ||\n\t\t\t\tisSiteEditor() ||\n\t\t\t\tisiFramedMobileDevicePreview() ||\n\t\t\t\tisEditingTemplate()\n\t\t\t) {\n\t\t\t\trestrictMode( [ 'preview' ] );\n\t\t\t} else {\n\t\t\t\tswitch ( blockType.mode ) {\n\t\t\t\t\tcase 'edit':\n\t\t\t\t\t\trestrictMode( [ 'edit', 'preview' ] );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'preview':\n\t\t\t\t\t\trestrictMode( [ 'preview', 'edit' ] );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\trestrictMode( [ 'auto' ] );\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\trender() {\n\t\t\tconst { name, attributes, setAttributes, clientId } = this.props;\n\t\t\tconst blockType = getBlockType( name );\n\t\t\tconst forcePreview =\n\t\t\t\tisBlockInQueryLoop( clientId ) ||\n\t\t\t\tisSiteEditor() ||\n\t\t\t\tisiFramedMobileDevicePreview() ||\n\t\t\t\tisEditingTemplate();\n\t\t\tlet { mode } = attributes;\n\n\t\t\tif ( forcePreview ) {\n\t\t\t\tmode = 'preview';\n\t\t\t}\n\n\t\t\t// Show toggle only for edit/preview modes and for blocks not in a query loop/FSE.\n\t\t\tlet showToggle = blockType.supports.mode;\n\t\t\tif ( mode === 'auto' || forcePreview ) {\n\t\t\t\tshowToggle = false;\n\t\t\t}\n\n\t\t\t// Configure toggle variables.\n\t\t\tconst toggleText =\n\t\t\t\tmode === 'preview'\n\t\t\t\t\t? acf.__( 'Switch to Edit' )\n\t\t\t\t\t: acf.__( 'Switch to Preview' );\n\t\t\tconst toggleIcon =\n\t\t\t\tmode === 'preview' ? 'edit' : 'welcome-view-site';\n\t\t\tfunction toggleMode() {\n\t\t\t\tsetAttributes( {\n\t\t\t\t\tmode: mode === 'preview' ? 'edit' : 'preview',\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\t// Return template.\n\t\t\treturn (\n\t\t\t\t<Fragment>\n\t\t\t\t\t<BlockControls>\n\t\t\t\t\t\t{ showToggle && (\n\t\t\t\t\t\t\t<ToolbarGroup>\n\t\t\t\t\t\t\t\t<ToolbarButton\n\t\t\t\t\t\t\t\t\tclassName=\"components-icon-button components-toolbar__control\"\n\t\t\t\t\t\t\t\t\tlabel={ toggleText }\n\t\t\t\t\t\t\t\t\ticon={ toggleIcon }\n\t\t\t\t\t\t\t\t\tonClick={ toggleMode }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</ToolbarGroup>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</BlockControls>\n\n\t\t\t\t\t<InspectorControls>\n\t\t\t\t\t\t{ mode === 'preview' && (\n\t\t\t\t\t\t\t<div className=\"acf-block-component acf-block-panel\">\n\t\t\t\t\t\t\t\t<BlockForm { ...this.props } />\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</InspectorControls>\n\n\t\t\t\t\t<BlockBody { ...this.props } />\n\t\t\t\t</Fragment>\n\t\t\t);\n\t\t}\n\t}\n\n\t/**\n\t * The BlockBody functional component.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.7.12\n\t */\n\tfunction _BlockBody( props ) {\n\t\tconst { attributes, isSelected, name } = props;\n\t\tconst { mode } = attributes;\n\n\t\tlet showForm = true;\n\t\tlet additionalClasses = 'acf-block-component acf-block-body';\n\n\t\tif ( ( mode === 'auto' && ! isSelected ) || mode === 'preview' ) {\n\t\t\tadditionalClasses += ' acf-block-preview';\n\t\t\tshowForm = false;\n\t\t}\n\n\t\tif ( getBlockVersion( name ) > 1 ) {\n\t\t\treturn (\n\t\t\t\t<div { ...useBlockProps( { className: additionalClasses } ) }>\n\t\t\t\t\t{ showForm ? (\n\t\t\t\t\t\t<BlockForm { ...props } />\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<BlockPreview { ...props } />\n\t\t\t\t\t) }\n\t\t\t\t</div>\n\t\t\t);\n\t\t} else {\n\t\t\treturn (\n\t\t\t\t<div { ...useBlockProps() }>\n\t\t\t\t\t<div className=\"acf-block-component acf-block-body\">\n\t\t\t\t\t\t{ showForm ? (\n\t\t\t\t\t\t\t<BlockForm { ...props } />\n\t\t\t\t\t\t) : (\n\t\t\t\t\t\t\t<BlockPreview { ...props } />\n\t\t\t\t\t\t) }\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t);\n\t\t}\n\t}\n\n\t// Append blockIndex to component props.\n\tconst BlockBody = withSelect( ( select, ownProps ) => {\n\t\tconst { clientId } = ownProps;\n\t\t// Use optional rootClientId to allow discoverability of child blocks.\n\t\tconst rootClientId =\n\t\t\tselect( 'core/block-editor' ).getBlockRootClientId( clientId );\n\t\tconst index = select( 'core/block-editor' ).getBlockIndex(\n\t\t\tclientId,\n\t\t\trootClientId\n\t\t);\n\t\treturn {\n\t\t\tindex,\n\t\t};\n\t} )( _BlockBody );\n\n\t/**\n\t * A react component to append HTMl.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.7.12\n\t *\n\t * @param\tstring children The html to insert.\n\t * @return\tvoid\n\t */\n\tclass Div extends Component {\n\t\trender() {\n\t\t\treturn (\n\t\t\t\t<div\n\t\t\t\t\tdangerouslySetInnerHTML={ { __html: this.props.children } }\n\t\t\t\t/>\n\t\t\t);\n\t\t}\n\t}\n\n\t/**\n\t * A react Component for inline scripts.\n\t *\n\t * This Component uses a combination of React references and jQuery to append the\n\t * inline <script> HTML each time the component is rendered.\n\t *\n\t * @date\t29/05/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\ttype Var Description.\n\t * @return\ttype Description.\n\t */\n\tclass Script extends Component {\n\t\trender() {\n\t\t\treturn <div ref={ ( el ) => ( this.el = el ) } />;\n\t\t}\n\t\tsetHTML( html ) {\n\t\t\t$( this.el ).html( `<script>${ html }</script>` );\n\t\t}\n\t\tcomponentDidUpdate() {\n\t\t\tthis.setHTML( this.props.children );\n\t\t}\n\t\tcomponentDidMount() {\n\t\t\tthis.setHTML( this.props.children );\n\t\t}\n\t}\n\n\t// Data storage for DynamicHTML components.\n\tconst store = {};\n\n\t/**\n\t * DynamicHTML Class.\n\t *\n\t * A react componenet to load and insert dynamic HTML.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.7.12\n\t *\n\t * @param\tvoid\n\t * @return\tvoid\n\t */\n\tclass DynamicHTML extends Component {\n\t\tconstructor( props ) {\n\t\t\tsuper( props );\n\n\t\t\t// Bind callbacks.\n\t\t\tthis.setRef = this.setRef.bind( this );\n\n\t\t\t// Define default props and call setup().\n\t\t\tthis.id = '';\n\t\t\tthis.el = false;\n\t\t\tthis.subscribed = true;\n\t\t\tthis.renderMethod = 'jQuery';\n\t\t\tthis.setup( props );\n\n\t\t\t// Load state.\n\t\t\tthis.loadState();\n\t\t}\n\n\t\tsetup( props ) {\n\t\t\t// Do nothing.\n\t\t}\n\n\t\tfetch() {\n\t\t\t// Do nothing.\n\t\t}\n\n\t\tmaybePreload( blockId, clientId, form ) {\n\t\t\tif (\n\t\t\t\tthis.state.html === undefined &&\n\t\t\t\t! isBlockInQueryLoop( this.props.clientId )\n\t\t\t) {\n\t\t\t\tconst preloadedBlocks = acf.get( 'preloadedBlocks' );\n\t\t\t\tconst modeText = form ? 'form' : 'preview';\n\n\t\t\t\tif ( preloadedBlocks && preloadedBlocks[ blockId ] ) {\n\t\t\t\t\t// Ensure we only preload the correct block state (form or preview).\n\t\t\t\t\tif (\n\t\t\t\t\t\t( form && ! preloadedBlocks[ blockId ].form ) ||\n\t\t\t\t\t\t( ! form && preloadedBlocks[ blockId ].form )\n\t\t\t\t\t)\n\t\t\t\t\t\treturn false;\n\n\t\t\t\t\t// Set HTML to the preloaded version.\n\t\t\t\t\treturn preloadedBlocks[ blockId ].html.replaceAll(\n\t\t\t\t\t\tblockId,\n\t\t\t\t\t\tclientId\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tloadState() {\n\t\t\tthis.state = store[ this.id ] || {};\n\t\t}\n\n\t\tsetState( state ) {\n\t\t\tstore[ this.id ] = { ...this.state, ...state };\n\n\t\t\t// Update component state if subscribed.\n\t\t\t// - Allows AJAX callback to update store without modifying state of an unmounted component.\n\t\t\tif ( this.subscribed ) {\n\t\t\t\tsuper.setState( state );\n\t\t\t}\n\t\t}\n\n\t\tsetHtml( html ) {\n\t\t\thtml = html ? html.trim() : '';\n\n\t\t\t// Bail early if html has not changed.\n\t\t\tif ( html === this.state.html ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Update state.\n\t\t\tconst state = {\n\t\t\t\thtml,\n\t\t\t};\n\n\t\t\tif ( this.renderMethod === 'jsx' ) {\n\t\t\t\tstate.jsx = acf.parseJSX(\n\t\t\t\t\thtml,\n\t\t\t\t\tgetBlockVersion( this.props.name )\n\t\t\t\t);\n\n\t\t\t\t// Handle templates which don't contain any valid JSX parsable elements.\n\t\t\t\tif ( ! state.jsx ) {\n\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\t'Your ACF block template contains no valid HTML elements. Appending a empty div to prevent React JS errors.'\n\t\t\t\t\t);\n\t\t\t\t\tstate.html += '<div></div>';\n\t\t\t\t\tstate.jsx = acf.parseJSX(\n\t\t\t\t\t\tstate.html,\n\t\t\t\t\t\tgetBlockVersion( this.props.name )\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\t// If we've got an object (as an array) find the first valid React ref.\n\t\t\t\tif ( Array.isArray( state.jsx ) ) {\n\t\t\t\t\tlet refElement = state.jsx.find( ( element ) =>\n\t\t\t\t\t\tReact.isValidElement( element )\n\t\t\t\t\t);\n\t\t\t\t\tstate.ref = refElement.ref;\n\t\t\t\t} else {\n\t\t\t\t\tstate.ref = state.jsx.ref;\n\t\t\t\t}\n\t\t\t\tstate.$el = $( this.el );\n\t\t\t} else {\n\t\t\t\tstate.$el = $( html );\n\t\t\t}\n\t\t\tthis.setState( state );\n\t\t}\n\n\t\tsetRef( el ) {\n\t\t\tthis.el = el;\n\t\t}\n\n\t\trender() {\n\t\t\t// Render JSX.\n\t\t\tif ( this.state.jsx ) {\n\t\t\t\t// If we're a v2+ block, use the jsx element itself as our ref.\n\t\t\t\tif ( getBlockVersion( this.props.name ) > 1 ) {\n\t\t\t\t\tthis.setRef( this.state.jsx );\n\t\t\t\t\treturn this.state.jsx;\n\t\t\t\t} else {\n\t\t\t\t\treturn <div ref={ this.setRef }>{ this.state.jsx }</div>;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Return HTML.\n\t\t\treturn (\n\t\t\t\t<div ref={ this.setRef }>\n\t\t\t\t\t<Placeholder>\n\t\t\t\t\t\t<Spinner />\n\t\t\t\t\t</Placeholder>\n\t\t\t\t</div>\n\t\t\t);\n\t\t}\n\n\t\tshouldComponentUpdate( { index }, { html } ) {\n\t\t\tif ( index !== this.props.index ) {\n\t\t\t\tthis.componentWillMove();\n\t\t\t}\n\t\t\treturn html !== this.state.html;\n\t\t}\n\n\t\tdisplay( context ) {\n\t\t\t// This method is called after setting new HTML and the Component render.\n\t\t\t// The jQuery render method simply needs to move $el into place.\n\t\t\tif ( this.renderMethod === 'jQuery' ) {\n\t\t\t\tconst $el = this.state.$el;\n\t\t\t\tconst $prevParent = $el.parent();\n\t\t\t\tconst $thisParent = $( this.el );\n\n\t\t\t\t// Move $el into place.\n\t\t\t\t$thisParent.html( $el );\n\n\t\t\t\t// Special case for reusable blocks.\n\t\t\t\t// Multiple instances of the same reusable block share the same block id.\n\t\t\t\t// This causes all instances to share the same state (cool), which unfortunately\n\t\t\t\t// pulls $el back and forth between the last rendered reusable block.\n\t\t\t\t// This simple fix leaves a \"clone\" behind :)\n\t\t\t\tif (\n\t\t\t\t\t$prevParent.length &&\n\t\t\t\t\t$prevParent[ 0 ] !== $thisParent[ 0 ]\n\t\t\t\t) {\n\t\t\t\t\t$prevParent.html( $el.clone() );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Call context specific method.\n\t\t\tswitch ( context ) {\n\t\t\t\tcase 'append':\n\t\t\t\t\tthis.componentDidAppend();\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'remount':\n\t\t\t\t\tthis.componentDidRemount();\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tcomponentDidMount() {\n\t\t\t// Fetch on first load.\n\t\t\tif ( this.state.html === undefined ) {\n\t\t\t\tthis.fetch();\n\n\t\t\t\t// Or remount existing HTML.\n\t\t\t} else {\n\t\t\t\tthis.display( 'remount' );\n\t\t\t}\n\t\t}\n\n\t\tcomponentDidUpdate( prevProps, prevState ) {\n\t\t\t// HTML has changed.\n\t\t\tthis.display( 'append' );\n\t\t}\n\n\t\tcomponentDidAppend() {\n\t\t\tacf.doAction( 'append', this.state.$el );\n\t\t}\n\n\t\tcomponentWillUnmount() {\n\t\t\tacf.doAction( 'unmount', this.state.$el );\n\n\t\t\t// Unsubscribe this component from state.\n\t\t\tthis.subscribed = false;\n\t\t}\n\n\t\tcomponentDidRemount() {\n\t\t\tthis.subscribed = true;\n\n\t\t\t// Use setTimeout to avoid incorrect timing of events.\n\t\t\t// React will unmount and mount components in DOM order.\n\t\t\t// This means a new component can be mounted before an old one is unmounted.\n\t\t\t// ACF shares $el across new/old components which is un-React-like.\n\t\t\t// This timout ensures that unmounting occurs before remounting.\n\t\t\tsetTimeout( () => {\n\t\t\t\tacf.doAction( 'remount', this.state.$el );\n\t\t\t} );\n\t\t}\n\n\t\tcomponentWillMove() {\n\t\t\tacf.doAction( 'unmount', this.state.$el );\n\t\t\tsetTimeout( () => {\n\t\t\t\tacf.doAction( 'remount', this.state.$el );\n\t\t\t} );\n\t\t}\n\t}\n\n\t/**\n\t * BlockForm Class.\n\t *\n\t * A react componenet to handle the block form.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.7.12\n\t *\n\t * @param\tstring id the block id.\n\t * @return\tvoid\n\t */\n\tclass BlockForm extends DynamicHTML {\n\t\tsetup( { clientId } ) {\n\t\t\tthis.id = `BlockForm-${ clientId }`;\n\t\t}\n\n\t\tfetch() {\n\t\t\t// Extract props.\n\t\t\tconst { attributes, context, clientId } = this.props;\n\n\t\t\tconst hash = createBlockAttributesHash( attributes, context );\n\n\t\t\t// Try preloaded data first.\n\t\t\tconst preloaded = this.maybePreload( hash, clientId, true );\n\n\t\t\tif ( preloaded ) {\n\t\t\t\tthis.setHtml( preloaded );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Request AJAX and update HTML on complete.\n\t\t\tfetchBlock( {\n\t\t\t\tattributes,\n\t\t\t\tcontext,\n\t\t\t\tclientId,\n\t\t\t\tquery: {\n\t\t\t\t\tform: true,\n\t\t\t\t},\n\t\t\t} ).done( ( { data } ) => {\n\t\t\t\tthis.setHtml( data.form.replaceAll( data.clientId, clientId ) );\n\t\t\t} );\n\t\t}\n\n\t\tcomponentDidRemount() {\n\t\t\tsuper.componentDidRemount();\n\n\t\t\tconst { $el } = this.state;\n\n\t\t\t// Make sure our on append events are registered.\n\t\t\tif ( $el.data( 'acf-events-added' ) !== true ) {\n\t\t\t\tthis.componentDidAppend();\n\t\t\t}\n\t\t}\n\n\t\tcomponentDidAppend() {\n\t\t\tsuper.componentDidAppend();\n\n\t\t\t// Extract props.\n\t\t\tconst { attributes, setAttributes, clientId } = this.props;\n\t\t\tconst props = this.props;\n\t\t\tconst { $el } = this.state;\n\n\t\t\t// Callback for updating block data.\n\t\t\tfunction serializeData( silent = false ) {\n\t\t\t\tconst data = acf.serialize( $el, `acf-${ clientId }` );\n\t\t\t\tif ( silent ) {\n\t\t\t\t\tattributes.data = data;\n\t\t\t\t} else {\n\t\t\t\t\tsetAttributes( {\n\t\t\t\t\t\tdata,\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Add events.\n\t\t\tlet timeout = false;\n\t\t\t$el.on( 'change keyup', () => {\n\t\t\t\tclearTimeout( timeout );\n\t\t\t\ttimeout = setTimeout( serializeData, 300 );\n\t\t\t} );\n\n\t\t\t// Log initialization for remount check on the persistent element.\n\t\t\t$el.data( 'acf-events-added', true );\n\n\t\t\t// Ensure newly added block is saved with data.\n\t\t\t// Do it silently to avoid triggering a preview render.\n\t\t\tif ( ! attributes.data ) {\n\t\t\t\tserializeData( true );\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * BlockPreview Class.\n\t *\n\t * A react componenet to handle the block preview.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.7.12\n\t *\n\t * @param\tstring id the block id.\n\t * @return\tvoid\n\t */\n\tclass BlockPreview extends DynamicHTML {\n\t\tsetup( { clientId, name } ) {\n\t\t\tconst blockType = getBlockType( name );\n\t\t\tconst contextPostId = acf.isget( this.props, 'context', 'postId' );\n\n\t\t\tthis.id = `BlockPreview-${ clientId }`;\n\n\t\t\t// Apply the contextPostId to the ID if set to stop query loop ID duplication.\n\t\t\tif ( contextPostId ) {\n\t\t\t\tthis.id = `BlockPreview-${ clientId }-${ contextPostId }`;\n\t\t\t}\n\n\t\t\tif ( blockType.supports.jsx ) {\n\t\t\t\tthis.renderMethod = 'jsx';\n\t\t\t}\n\t\t}\n\n\t\tfetch( args = {} ) {\n\t\t\tconst {\n\t\t\t\tattributes = this.props.attributes,\n\t\t\t\tclientId = this.props.clientId,\n\t\t\t\tcontext = this.props.context,\n\t\t\t\tdelay = 0,\n\t\t\t} = args;\n\n\t\t\tconst { name } = this.props;\n\n\t\t\t// Remember attributes used to fetch HTML.\n\t\t\tthis.setState( {\n\t\t\t\tprevAttributes: attributes,\n\t\t\t\tprevContext: context,\n\t\t\t} );\n\n\t\t\tconst hash = createBlockAttributesHash( attributes, context );\n\n\t\t\t// Try preloaded data first.\n\t\t\tlet preloaded = this.maybePreload( hash, clientId, false );\n\n\t\t\tif ( preloaded ) {\n\t\t\t\tif ( getBlockVersion( name ) == 1 ) {\n\t\t\t\t\tpreloaded =\n\t\t\t\t\t\t'<div class=\"acf-block-preview\">' +\n\t\t\t\t\t\tpreloaded +\n\t\t\t\t\t\t'</div>';\n\t\t\t\t}\n\t\t\t\tthis.setHtml( preloaded );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Request AJAX and update HTML on complete.\n\t\t\tfetchBlock( {\n\t\t\t\tattributes,\n\t\t\t\tcontext,\n\t\t\t\tclientId,\n\t\t\t\tquery: {\n\t\t\t\t\tpreview: true,\n\t\t\t\t},\n\t\t\t\tdelay,\n\t\t\t} ).done( ( { data } ) => {\n\t\t\t\tlet replaceHtml = data.preview.replaceAll(\n\t\t\t\t\tdata.clientId,\n\t\t\t\t\tclientId\n\t\t\t\t);\n\t\t\t\tif ( getBlockVersion( name ) == 1 ) {\n\t\t\t\t\treplaceHtml =\n\t\t\t\t\t\t'<div class=\"acf-block-preview\">' +\n\t\t\t\t\t\treplaceHtml +\n\t\t\t\t\t\t'</div>';\n\t\t\t\t}\n\t\t\t\tthis.setHtml( replaceHtml );\n\t\t\t} );\n\t\t}\n\n\t\tcomponentDidAppend() {\n\t\t\tsuper.componentDidAppend();\n\t\t\tthis.renderBlockPreviewEvent();\n\t\t}\n\n\t\tshouldComponentUpdate( nextProps, nextState ) {\n\t\t\tconst nextAttributes = nextProps.attributes;\n\t\t\tconst thisAttributes = this.props.attributes;\n\n\t\t\t// Update preview if block data has changed.\n\t\t\tif (\n\t\t\t\t! compareObjects( nextAttributes, thisAttributes ) ||\n\t\t\t\t! compareObjects( nextProps.context, this.props.context )\n\t\t\t) {\n\t\t\t\tlet delay = 0;\n\n\t\t\t\t// Delay fetch when editing className or anchor to simulate consistent logic to custom fields.\n\t\t\t\tif ( nextAttributes.className !== thisAttributes.className ) {\n\t\t\t\t\tdelay = 300;\n\t\t\t\t}\n\t\t\t\tif ( nextAttributes.anchor !== thisAttributes.anchor ) {\n\t\t\t\t\tdelay = 300;\n\t\t\t\t}\n\n\t\t\t\tthis.fetch( {\n\t\t\t\t\tattributes: nextAttributes,\n\t\t\t\t\tcontext: nextProps.context,\n\t\t\t\t\tdelay,\n\t\t\t\t} );\n\t\t\t}\n\t\t\treturn super.shouldComponentUpdate( nextProps, nextState );\n\t\t}\n\n\t\trenderBlockPreviewEvent() {\n\t\t\t// Extract props.\n\t\t\tconst { attributes, name } = this.props;\n\t\t\tconst { $el, ref } = this.state;\n\t\t\tvar blockElement;\n\n\t\t\t// Generate action friendly type.\n\t\t\tconst type = attributes.name.replace( 'acf/', '' );\n\n\t\t\tif ( ref && ref.current ) {\n\t\t\t\t// We've got a react ref from a JSX container. Use the parent as the blockElement\n\t\t\t\tblockElement = $( ref.current ).parent();\n\t\t\t} else if ( getBlockVersion( name ) == 1 ) {\n\t\t\t\tblockElement = $el;\n\t\t\t} else {\n\t\t\t\tblockElement = $el.parents( '.acf-block-preview' );\n\t\t\t}\n\n\t\t\t// Do action.\n\t\t\tacf.doAction( 'render_block_preview', blockElement, attributes );\n\t\t\tacf.doAction(\n\t\t\t\t`render_block_preview/type=${ type }`,\n\t\t\t\tblockElement,\n\t\t\t\tattributes\n\t\t\t);\n\t\t}\n\n\t\tcomponentDidRemount() {\n\t\t\tsuper.componentDidRemount();\n\n\t\t\t// Update preview if data has changed since last render (changing from \"edit\" to \"preview\").\n\t\t\tif (\n\t\t\t\t! compareObjects(\n\t\t\t\t\tthis.state.prevAttributes,\n\t\t\t\t\tthis.props.attributes\n\t\t\t\t) ||\n\t\t\t\t! compareObjects( this.state.prevContext, this.props.context )\n\t\t\t) {\n\t\t\t\tthis.fetch();\n\t\t\t}\n\n\t\t\t// Fire the block preview event so blocks can reinit JS elements.\n\t\t\t// React reusing DOM elements covers any potential race condition from the above fetch.\n\t\t\tthis.renderBlockPreviewEvent();\n\t\t}\n\t}\n\n\t/**\n\t * Initializes ACF Blocks logic and registration.\n\t *\n\t * @since 5.9.0\n\t */\n\tfunction initialize() {\n\t\t// Add support for WordPress versions before 5.2.\n\t\tif ( ! wp.blockEditor ) {\n\t\t\twp.blockEditor = wp.editor;\n\t\t}\n\n\t\t// Register block types.\n\t\tconst blockTypes = acf.get( 'blockTypes' );\n\t\tif ( blockTypes ) {\n\t\t\tblockTypes.map( registerBlockType );\n\t\t}\n\t}\n\n\t// Run the initialize callback during the \"prepare\" action.\n\t// This ensures that all localized data is available and that blocks are registered before the WP editor has been instantiated.\n\tacf.addAction( 'prepare', initialize );\n\n\t/**\n\t * Returns a valid vertical alignment.\n\t *\n\t * @date\t07/08/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring align A vertical alignment.\n\t * @return\tstring\n\t */\n\tfunction validateVerticalAlignment( align ) {\n\t\tconst ALIGNMENTS = [ 'top', 'center', 'bottom' ];\n\t\tconst DEFAULT = 'top';\n\t\treturn ALIGNMENTS.includes( align ) ? align : DEFAULT;\n\t}\n\n\t/**\n\t * Returns a valid horizontal alignment.\n\t *\n\t * @date\t07/08/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring align A horizontal alignment.\n\t * @return\tstring\n\t */\n\tfunction validateHorizontalAlignment( align ) {\n\t\tconst ALIGNMENTS = [ 'left', 'center', 'right' ];\n\t\tconst DEFAULT = acf.get( 'rtl' ) ? 'right' : 'left';\n\t\treturn ALIGNMENTS.includes( align ) ? align : DEFAULT;\n\t}\n\n\t/**\n\t * Returns a valid matrix alignment.\n\t *\n\t * Written for \"upgrade-path\" compatibility from vertical alignment to matrix alignment.\n\t *\n\t * @date\t07/08/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring align A matrix alignment.\n\t * @return\tstring\n\t */\n\tfunction validateMatrixAlignment( align ) {\n\t\tconst DEFAULT = 'center center';\n\t\tif ( align ) {\n\t\t\tconst [ y, x ] = align.split( ' ' );\n\t\t\treturn `${ validateVerticalAlignment(\n\t\t\t\ty\n\t\t\t) } ${ validateHorizontalAlignment( x ) }`;\n\t\t}\n\t\treturn DEFAULT;\n\t}\n\n\t/**\n\t * A higher order component adding alignContent editing functionality.\n\t *\n\t * @date\t08/07/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tcomponent OriginalBlockEdit The original BlockEdit component.\n\t * @param\tobject blockType The block type settings.\n\t * @return\tcomponent\n\t */\n\tfunction withAlignContentComponent( OriginalBlockEdit, blockType ) {\n\t\t// Determine alignment vars\n\t\tlet type =\n\t\t\tblockType.supports.align_content || blockType.supports.alignContent;\n\t\tlet AlignmentComponent;\n\t\tlet validateAlignment;\n\t\tswitch ( type ) {\n\t\t\tcase 'matrix':\n\t\t\t\tAlignmentComponent =\n\t\t\t\t\tBlockAlignmentMatrixControl || BlockAlignmentMatrixToolbar;\n\t\t\t\tvalidateAlignment = validateMatrixAlignment;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tAlignmentComponent = BlockVerticalAlignmentToolbar;\n\t\t\t\tvalidateAlignment = validateVerticalAlignment;\n\t\t\t\tbreak;\n\t\t}\n\n\t\t// Ensure alignment component exists.\n\t\tif ( AlignmentComponent === undefined ) {\n\t\t\tconsole.warn(\n\t\t\t\t`The \"${ type }\" alignment component was not found.`\n\t\t\t);\n\t\t\treturn OriginalBlockEdit;\n\t\t}\n\n\t\t// Ensure correct block attribute data is sent in intial preview AJAX request.\n\t\tblockType.alignContent = validateAlignment( blockType.alignContent );\n\n\t\t// Return wrapped component.\n\t\treturn class WrappedBlockEdit extends Component {\n\t\t\trender() {\n\t\t\t\tconst { attributes, setAttributes } = this.props;\n\t\t\t\tconst { alignContent } = attributes;\n\t\t\t\tfunction onChangeAlignContent( alignContent ) {\n\t\t\t\t\tsetAttributes( {\n\t\t\t\t\t\talignContent: validateAlignment( alignContent ),\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t\treturn (\n\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t<BlockControls group=\"block\">\n\t\t\t\t\t\t\t<AlignmentComponent\n\t\t\t\t\t\t\t\tlabel={ acf.__( 'Change content alignment' ) }\n\t\t\t\t\t\t\t\tvalue={ validateAlignment( alignContent ) }\n\t\t\t\t\t\t\t\tonChange={ onChangeAlignContent }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</BlockControls>\n\t\t\t\t\t\t<OriginalBlockEdit { ...this.props } />\n\t\t\t\t\t</Fragment>\n\t\t\t\t);\n\t\t\t}\n\t\t};\n\t}\n\n\t/**\n\t * A higher order component adding alignText editing functionality.\n\t *\n\t * @date\t08/07/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tcomponent OriginalBlockEdit The original BlockEdit component.\n\t * @param\tobject blockType The block type settings.\n\t * @return\tcomponent\n\t */\n\tfunction withAlignTextComponent( OriginalBlockEdit, blockType ) {\n\t\tconst validateAlignment = validateHorizontalAlignment;\n\n\t\t// Ensure correct block attribute data is sent in intial preview AJAX request.\n\t\tblockType.alignText = validateAlignment( blockType.alignText );\n\n\t\t// Return wrapped component.\n\t\treturn class WrappedBlockEdit extends Component {\n\t\t\trender() {\n\t\t\t\tconst { attributes, setAttributes } = this.props;\n\t\t\t\tconst { alignText } = attributes;\n\n\t\t\t\tfunction onChangeAlignText( alignText ) {\n\t\t\t\t\tsetAttributes( {\n\t\t\t\t\t\talignText: validateAlignment( alignText ),\n\t\t\t\t\t} );\n\t\t\t\t}\n\n\t\t\t\treturn (\n\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t<BlockControls group=\"block\">\n\t\t\t\t\t\t\t<AlignmentToolbar\n\t\t\t\t\t\t\t\tvalue={ validateAlignment( alignText ) }\n\t\t\t\t\t\t\t\tonChange={ onChangeAlignText }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</BlockControls>\n\t\t\t\t\t\t<OriginalBlockEdit { ...this.props } />\n\t\t\t\t\t</Fragment>\n\t\t\t\t);\n\t\t\t}\n\t\t};\n\t}\n\n\t/**\n\t * A higher order component adding full height support.\n\t *\n\t * @date\t19/07/2021\n\t * @since\t5.10.0\n\t *\n\t * @param\tcomponent OriginalBlockEdit The original BlockEdit component.\n\t * @param\tobject blockType The block type settings.\n\t * @return\tcomponent\n\t */\n\tfunction withFullHeightComponent( OriginalBlockEdit, blockType ) {\n\t\tif ( ! BlockFullHeightAlignmentControl ) return OriginalBlockEdit;\n\n\t\t// Return wrapped component.\n\t\treturn class WrappedBlockEdit extends Component {\n\t\t\trender() {\n\t\t\t\tconst { attributes, setAttributes } = this.props;\n\t\t\t\tconst { fullHeight } = attributes;\n\n\t\t\t\tfunction onToggleFullHeight( fullHeight ) {\n\t\t\t\t\tsetAttributes( {\n\t\t\t\t\t\tfullHeight,\n\t\t\t\t\t} );\n\t\t\t\t}\n\n\t\t\t\treturn (\n\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t<BlockControls group=\"block\">\n\t\t\t\t\t\t\t<BlockFullHeightAlignmentControl\n\t\t\t\t\t\t\t\tisActive={ fullHeight }\n\t\t\t\t\t\t\t\tonToggle={ onToggleFullHeight }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</BlockControls>\n\t\t\t\t\t\t<OriginalBlockEdit { ...this.props } />\n\t\t\t\t\t</Fragment>\n\t\t\t\t);\n\t\t\t}\n\t\t};\n\t}\n\n\t/**\n\t * Appends a backwards compatibility attribute for conversion.\n\t *\n\t * @since\t6.0\n\t *\n\t * @param\tobject attributes The block type attributes.\n\t * @return\tobject\n\t */\n\tfunction addBackCompatAttribute( attributes, new_attribute, type ) {\n\t\tattributes[ new_attribute ] = {\n\t\t\ttype: type,\n\t\t};\n\t\treturn attributes;\n\t}\n\n\t/**\n\t * Create a block hash from attributes\n\t *\n\t * @since 6.0\n\t *\n\t * @param object attributes The block type attributes.\n\t * @param object context The current block context object.\n\t * @return string\n\t */\n\tfunction createBlockAttributesHash( attributes, context ) {\n\t\tattributes[ '_acf_context' ] = context;\n\t\treturn md5(\n\t\t\tJSON.stringify(\n\t\t\t\tObject.keys( attributes )\n\t\t\t\t\t.sort()\n\t\t\t\t\t.reduce( ( acc, currValue ) => {\n\t\t\t\t\t\tacc[ currValue ] = attributes[ currValue ];\n\t\t\t\t\t\treturn acc;\n\t\t\t\t\t}, {} )\n\t\t\t)\n\t\t);\n\t}\n} )( jQuery );\n", "( function ( $, undefined ) {\n\tacf.jsxNameReplacements = {\n\t\t'accent-height': 'accentHeight',\n\t\taccentheight: 'accentHeight',\n\t\t'accept-charset': 'acceptCharset',\n\t\tacceptcharset: 'acceptCharset',\n\t\taccesskey: 'accessKey',\n\t\t'alignment-baseline': 'alignmentBaseline',\n\t\talignmentbaseline: 'alignmentBaseline',\n\t\tallowedblocks: 'allowedBlocks',\n\t\tallowfullscreen: 'allowFullScreen',\n\t\tallowreorder: 'allowReorder',\n\t\t'arabic-form': 'arabicForm',\n\t\tarabicform: 'arabicForm',\n\t\tattributename: 'attributeName',\n\t\tattributetype: 'attributeType',\n\t\tautocapitalize: 'autoCapitalize',\n\t\tautocomplete: 'autoComplete',\n\t\tautocorrect: 'autoCorrect',\n\t\tautofocus: 'autoFocus',\n\t\tautoplay: 'autoPlay',\n\t\tautoreverse: 'autoReverse',\n\t\tautosave: 'autoSave',\n\t\tbasefrequency: 'baseFrequency',\n\t\t'baseline-shift': 'baselineShift',\n\t\tbaselineshift: 'baselineShift',\n\t\tbaseprofile: 'baseProfile',\n\t\tcalcmode: 'calcMode',\n\t\t'cap-height': 'capHeight',\n\t\tcapheight: 'capHeight',\n\t\tcellpadding: 'cellPadding',\n\t\tcellspacing: 'cellSpacing',\n\t\tcharset: 'charSet',\n\t\tclass: 'className',\n\t\tclassid: 'classID',\n\t\tclassname: 'className',\n\t\t'clip-path': 'clipPath',\n\t\t'clip-rule': 'clipRule',\n\t\tclippath: 'clipPath',\n\t\tclippathunits: 'clipPathUnits',\n\t\tcliprule: 'clipRule',\n\t\t'color-interpolation': 'colorInterpolation',\n\t\t'color-interpolation-filters': 'colorInterpolationFilters',\n\t\t'color-profile': 'colorProfile',\n\t\t'color-rendering': 'colorRendering',\n\t\tcolorinterpolation: 'colorInterpolation',\n\t\tcolorinterpolationfilters: 'colorInterpolationFilters',\n\t\tcolorprofile: 'colorProfile',\n\t\tcolorrendering: 'colorRendering',\n\t\tcolspan: 'colSpan',\n\t\tcontenteditable: 'contentEditable',\n\t\tcontentscripttype: 'contentScriptType',\n\t\tcontentstyletype: 'contentStyleType',\n\t\tcontextmenu: 'contextMenu',\n\t\tcontrolslist: 'controlsList',\n\t\tcrossorigin: 'crossOrigin',\n\t\tdangerouslysetinnerhtml: 'dangerouslySetInnerHTML',\n\t\tdatetime: 'dateTime',\n\t\tdefaultchecked: 'defaultChecked',\n\t\tdefaultvalue: 'defaultValue',\n\t\tdiffuseconstant: 'diffuseConstant',\n\t\tdisablepictureinpicture: 'disablePictureInPicture',\n\t\tdisableremoteplayback: 'disableRemotePlayback',\n\t\t'dominant-baseline': 'dominantBaseline',\n\t\tdominantbaseline: 'dominantBaseline',\n\t\tedgemode: 'edgeMode',\n\t\t'enable-background': 'enableBackground',\n\t\tenablebackground: 'enableBackground',\n\t\tenctype: 'encType',\n\t\tenterkeyhint: 'enterKeyHint',\n\t\texternalresourcesrequired: 'externalResourcesRequired',\n\t\t'fill-opacity': 'fillOpacity',\n\t\t'fill-rule': 'fillRule',\n\t\tfillopacity: 'fillOpacity',\n\t\tfillrule: 'fillRule',\n\t\tfilterres: 'filterRes',\n\t\tfilterunits: 'filterUnits',\n\t\t'flood-color': 'floodColor',\n\t\t'flood-opacity': 'floodOpacity',\n\t\tfloodcolor: 'floodColor',\n\t\tfloodopacity: 'floodOpacity',\n\t\t'font-family': 'fontFamily',\n\t\t'font-size': 'fontSize',\n\t\t'font-size-adjust': 'fontSizeAdjust',\n\t\t'font-stretch': 'fontStretch',\n\t\t'font-style': 'fontStyle',\n\t\t'font-variant': 'fontVariant',\n\t\t'font-weight': 'fontWeight',\n\t\tfontfamily: 'fontFamily',\n\t\tfontsize: 'fontSize',\n\t\tfontsizeadjust: 'fontSizeAdjust',\n\t\tfontstretch: 'fontStretch',\n\t\tfontstyle: 'fontStyle',\n\t\tfontvariant: 'fontVariant',\n\t\tfontweight: 'fontWeight',\n\t\tfor: 'htmlFor',\n\t\tforeignobject: 'foreignObject',\n\t\tformaction: 'formAction',\n\t\tformenctype: 'formEncType',\n\t\tformmethod: 'formMethod',\n\t\tformnovalidate: 'formNoValidate',\n\t\tformtarget: 'formTarget',\n\t\tframeborder: 'frameBorder',\n\t\t'glyph-name': 'glyphName',\n\t\t'glyph-orientation-horizontal': 'glyphOrientationHorizontal',\n\t\t'glyph-orientation-vertical': 'glyphOrientationVertical',\n\t\tglyphname: 'glyphName',\n\t\tglyphorientationhorizontal: 'glyphOrientationHorizontal',\n\t\tglyphorientationvertical: 'glyphOrientationVertical',\n\t\tglyphref: 'glyphRef',\n\t\tgradienttransform: 'gradientTransform',\n\t\tgradientunits: 'gradientUnits',\n\t\t'horiz-adv-x': 'horizAdvX',\n\t\t'horiz-origin-x': 'horizOriginX',\n\t\thorizadvx: 'horizAdvX',\n\t\thorizoriginx: 'horizOriginX',\n\t\threflang: 'hrefLang',\n\t\thtmlfor: 'htmlFor',\n\t\t'http-equiv': 'httpEquiv',\n\t\thttpequiv: 'httpEquiv',\n\t\t'image-rendering': 'imageRendering',\n\t\timagerendering: 'imageRendering',\n\t\tinnerhtml: 'innerHTML',\n\t\tinputmode: 'inputMode',\n\t\titemid: 'itemID',\n\t\titemprop: 'itemProp',\n\t\titemref: 'itemRef',\n\t\titemscope: 'itemScope',\n\t\titemtype: 'itemType',\n\t\tkernelmatrix: 'kernelMatrix',\n\t\tkernelunitlength: 'kernelUnitLength',\n\t\tkeyparams: 'keyParams',\n\t\tkeypoints: 'keyPoints',\n\t\tkeysplines: 'keySplines',\n\t\tkeytimes: 'keyTimes',\n\t\tkeytype: 'keyType',\n\t\tlengthadjust: 'lengthAdjust',\n\t\t'letter-spacing': 'letterSpacing',\n\t\tletterspacing: 'letterSpacing',\n\t\t'lighting-color': 'lightingColor',\n\t\tlightingcolor: 'lightingColor',\n\t\tlimitingconeangle: 'limitingConeAngle',\n\t\tmarginheight: 'marginHeight',\n\t\tmarginwidth: 'marginWidth',\n\t\t'marker-end': 'markerEnd',\n\t\t'marker-mid': 'markerMid',\n\t\t'marker-start': 'markerStart',\n\t\tmarkerend: 'markerEnd',\n\t\tmarkerheight: 'markerHeight',\n\t\tmarkermid: 'markerMid',\n\t\tmarkerstart: 'markerStart',\n\t\tmarkerunits: 'markerUnits',\n\t\tmarkerwidth: 'markerWidth',\n\t\tmaskcontentunits: 'maskContentUnits',\n\t\tmaskunits: 'maskUnits',\n\t\tmaxlength: 'maxLength',\n\t\tmediagroup: 'mediaGroup',\n\t\tminlength: 'minLength',\n\t\tnomodule: 'noModule',\n\t\tnovalidate: 'noValidate',\n\t\tnumoctaves: 'numOctaves',\n\t\t'overline-position': 'overlinePosition',\n\t\t'overline-thickness': 'overlineThickness',\n\t\toverlineposition: 'overlinePosition',\n\t\toverlinethickness: 'overlineThickness',\n\t\t'paint-order': 'paintOrder',\n\t\tpaintorder: 'paintOrder',\n\t\t'panose-1': 'panose1',\n\t\tpathlength: 'pathLength',\n\t\tpatterncontentunits: 'patternContentUnits',\n\t\tpatterntransform: 'patternTransform',\n\t\tpatternunits: 'patternUnits',\n\t\tplaysinline: 'playsInline',\n\t\t'pointer-events': 'pointerEvents',\n\t\tpointerevents: 'pointerEvents',\n\t\tpointsatx: 'pointsAtX',\n\t\tpointsaty: 'pointsAtY',\n\t\tpointsatz: 'pointsAtZ',\n\t\tpreservealpha: 'preserveAlpha',\n\t\tpreserveaspectratio: 'preserveAspectRatio',\n\t\tprimitiveunits: 'primitiveUnits',\n\t\tradiogroup: 'radioGroup',\n\t\treadonly: 'readOnly',\n\t\treferrerpolicy: 'referrerPolicy',\n\t\trefx: 'refX',\n\t\trefy: 'refY',\n\t\t'rendering-intent': 'renderingIntent',\n\t\trenderingintent: 'renderingIntent',\n\t\trepeatcount: 'repeatCount',\n\t\trepeatdur: 'repeatDur',\n\t\trequiredextensions: 'requiredExtensions',\n\t\trequiredfeatures: 'requiredFeatures',\n\t\trowspan: 'rowSpan',\n\t\t'shape-rendering': 'shapeRendering',\n\t\tshaperendering: 'shapeRendering',\n\t\tspecularconstant: 'specularConstant',\n\t\tspecularexponent: 'specularExponent',\n\t\tspellcheck: 'spellCheck',\n\t\tspreadmethod: 'spreadMethod',\n\t\tsrcdoc: 'srcDoc',\n\t\tsrclang: 'srcLang',\n\t\tsrcset: 'srcSet',\n\t\tstartoffset: 'startOffset',\n\t\tstddeviation: 'stdDeviation',\n\t\tstitchtiles: 'stitchTiles',\n\t\t'stop-color': 'stopColor',\n\t\t'stop-opacity': 'stopOpacity',\n\t\tstopcolor: 'stopColor',\n\t\tstopopacity: 'stopOpacity',\n\t\t'strikethrough-position': 'strikethroughPosition',\n\t\t'strikethrough-thickness': 'strikethroughThickness',\n\t\tstrikethroughposition: 'strikethroughPosition',\n\t\tstrikethroughthickness: 'strikethroughThickness',\n\t\t'stroke-dasharray': 'strokeDasharray',\n\t\t'stroke-dashoffset': 'strokeDashoffset',\n\t\t'stroke-linecap': 'strokeLinecap',\n\t\t'stroke-linejoin': 'strokeLinejoin',\n\t\t'stroke-miterlimit': 'strokeMiterlimit',\n\t\t'stroke-opacity': 'strokeOpacity',\n\t\t'stroke-width': 'strokeWidth',\n\t\tstrokedasharray: 'strokeDasharray',\n\t\tstrokedashoffset: 'strokeDashoffset',\n\t\tstrokelinecap: 'strokeLinecap',\n\t\tstrokelinejoin: 'strokeLinejoin',\n\t\tstrokemiterlimit: 'strokeMiterlimit',\n\t\tstrokeopacity: 'strokeOpacity',\n\t\tstrokewidth: 'strokeWidth',\n\t\tsuppresscontenteditablewarning: 'suppressContentEditableWarning',\n\t\tsuppresshydrationwarning: 'suppressHydrationWarning',\n\t\tsurfacescale: 'surfaceScale',\n\t\tsystemlanguage: 'systemLanguage',\n\t\ttabindex: 'tabIndex',\n\t\ttablevalues: 'tableValues',\n\t\ttargetx: 'targetX',\n\t\ttargety: 'targetY',\n\t\ttemplatelock: 'templateLock',\n\t\t'text-anchor': 'textAnchor',\n\t\t'text-decoration': 'textDecoration',\n\t\t'text-rendering': 'textRendering',\n\t\ttextanchor: 'textAnchor',\n\t\ttextdecoration: 'textDecoration',\n\t\ttextlength: 'textLength',\n\t\ttextrendering: 'textRendering',\n\t\t'underline-position': 'underlinePosition',\n\t\t'underline-thickness': 'underlineThickness',\n\t\tunderlineposition: 'underlinePosition',\n\t\tunderlinethickness: 'underlineThickness',\n\t\t'unicode-bidi': 'unicodeBidi',\n\t\t'unicode-range': 'unicodeRange',\n\t\tunicodebidi: 'unicodeBidi',\n\t\tunicoderange: 'unicodeRange',\n\t\t'units-per-em': 'unitsPerEm',\n\t\tunitsperem: 'unitsPerEm',\n\t\tusemap: 'useMap',\n\t\t'v-alphabetic': 'vAlphabetic',\n\t\t'v-hanging': 'vHanging',\n\t\t'v-ideographic': 'vIdeographic',\n\t\t'v-mathematical': 'vMathematical',\n\t\tvalphabetic: 'vAlphabetic',\n\t\t'vector-effect': 'vectorEffect',\n\t\tvectoreffect: 'vectorEffect',\n\t\t'vert-adv-y': 'vertAdvY',\n\t\t'vert-origin-x': 'vertOriginX',\n\t\t'vert-origin-y': 'vertOriginY',\n\t\tvertadvy: 'vertAdvY',\n\t\tvertoriginx: 'vertOriginX',\n\t\tvertoriginy: 'vertOriginY',\n\t\tvhanging: 'vHanging',\n\t\tvideographic: 'vIdeographic',\n\t\tviewbox: 'viewBox',\n\t\tviewtarget: 'viewTarget',\n\t\tvmathematical: 'vMathematical',\n\t\t'word-spacing': 'wordSpacing',\n\t\twordspacing: 'wordSpacing',\n\t\t'writing-mode': 'writingMode',\n\t\twritingmode: 'writingMode',\n\t\t'x-height': 'xHeight',\n\t\txchannelselector: 'xChannelSelector',\n\t\txheight: 'xHeight',\n\t\t'xlink:actuate': 'xlinkActuate',\n\t\t'xlink:arcrole': 'xlinkArcrole',\n\t\t'xlink:href': 'xlinkHref',\n\t\t'xlink:role': 'xlinkRole',\n\t\t'xlink:show': 'xlinkShow',\n\t\t'xlink:title': 'xlinkTitle',\n\t\t'xlink:type': 'xlinkType',\n\t\txlinkactuate: 'xlinkActuate',\n\t\txlinkarcrole: 'xlinkArcrole',\n\t\txlinkhref: 'xlinkHref',\n\t\txlinkrole: 'xlinkRole',\n\t\txlinkshow: 'xlinkShow',\n\t\txlinktitle: 'xlinkTitle',\n\t\txlinktype: 'xlinkType',\n\t\t'xml:base': 'xmlBase',\n\t\t'xml:lang': 'xmlLang',\n\t\t'xml:space': 'xmlSpace',\n\t\txmlbase: 'xmlBase',\n\t\txmllang: 'xmlLang',\n\t\t'xmlns:xlink': 'xmlnsXlink',\n\t\txmlnsxlink: 'xmlnsXlink',\n\t\txmlspace: 'xmlSpace',\n\t\tychannelselector: 'yChannelSelector',\n\t\tzoomandpan: 'zoomAndPan',\n\t};\n} )( jQuery );\n", "var charenc = {\n  // UTF-8 encoding\n  utf8: {\n    // Convert a string to a byte array\n    stringToBytes: function(str) {\n      return charenc.bin.stringToBytes(unescape(encodeURIComponent(str)));\n    },\n\n    // Convert a byte array to a string\n    bytesToString: function(bytes) {\n      return decodeURIComponent(escape(charenc.bin.bytesToString(bytes)));\n    }\n  },\n\n  // Binary encoding\n  bin: {\n    // Convert a string to a byte array\n    stringToBytes: function(str) {\n      for (var bytes = [], i = 0; i < str.length; i++)\n        bytes.push(str.charCodeAt(i) & 0xFF);\n      return bytes;\n    },\n\n    // Convert a byte array to a string\n    bytesToString: function(bytes) {\n      for (var str = [], i = 0; i < bytes.length; i++)\n        str.push(String.fromCharCode(bytes[i]));\n      return str.join('');\n    }\n  }\n};\n\nmodule.exports = charenc;\n", "(function() {\n  var base64map\n      = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',\n\n  crypt = {\n    // Bit-wise rotation left\n    rotl: function(n, b) {\n      return (n << b) | (n >>> (32 - b));\n    },\n\n    // Bit-wise rotation right\n    rotr: function(n, b) {\n      return (n << (32 - b)) | (n >>> b);\n    },\n\n    // Swap big-endian to little-endian and vice versa\n    endian: function(n) {\n      // If number given, swap endian\n      if (n.constructor == Number) {\n        return crypt.rotl(n, 8) & 0x00FF00FF | crypt.rotl(n, 24) & 0xFF00FF00;\n      }\n\n      // Else, assume array and swap all items\n      for (var i = 0; i < n.length; i++)\n        n[i] = crypt.endian(n[i]);\n      return n;\n    },\n\n    // Generate an array of any length of random bytes\n    randomBytes: function(n) {\n      for (var bytes = []; n > 0; n--)\n        bytes.push(Math.floor(Math.random() * 256));\n      return bytes;\n    },\n\n    // Convert a byte array to big-endian 32-bit words\n    bytesToWords: function(bytes) {\n      for (var words = [], i = 0, b = 0; i < bytes.length; i++, b += 8)\n        words[b >>> 5] |= bytes[i] << (24 - b % 32);\n      return words;\n    },\n\n    // Convert big-endian 32-bit words to a byte array\n    wordsToBytes: function(words) {\n      for (var bytes = [], b = 0; b < words.length * 32; b += 8)\n        bytes.push((words[b >>> 5] >>> (24 - b % 32)) & 0xFF);\n      return bytes;\n    },\n\n    // Convert a byte array to a hex string\n    bytesToHex: function(bytes) {\n      for (var hex = [], i = 0; i < bytes.length; i++) {\n        hex.push((bytes[i] >>> 4).toString(16));\n        hex.push((bytes[i] & 0xF).toString(16));\n      }\n      return hex.join('');\n    },\n\n    // Convert a hex string to a byte array\n    hexToBytes: function(hex) {\n      for (var bytes = [], c = 0; c < hex.length; c += 2)\n        bytes.push(parseInt(hex.substr(c, 2), 16));\n      return bytes;\n    },\n\n    // Convert a byte array to a base-64 string\n    bytesToBase64: function(bytes) {\n      for (var base64 = [], i = 0; i < bytes.length; i += 3) {\n        var triplet = (bytes[i] << 16) | (bytes[i + 1] << 8) | bytes[i + 2];\n        for (var j = 0; j < 4; j++)\n          if (i * 8 + j * 6 <= bytes.length * 8)\n            base64.push(base64map.charAt((triplet >>> 6 * (3 - j)) & 0x3F));\n          else\n            base64.push('=');\n      }\n      return base64.join('');\n    },\n\n    // Convert a base-64 string to a byte array\n    base64ToBytes: function(base64) {\n      // Remove non-base-64 characters\n      base64 = base64.replace(/[^A-Z0-9+\\/]/ig, '');\n\n      for (var bytes = [], i = 0, imod4 = 0; i < base64.length;\n          imod4 = ++i % 4) {\n        if (imod4 == 0) continue;\n        bytes.push(((base64map.indexOf(base64.charAt(i - 1))\n            & (Math.pow(2, -2 * imod4 + 8) - 1)) << (imod4 * 2))\n            | (base64map.indexOf(base64.charAt(i)) >>> (6 - imod4 * 2)));\n      }\n      return bytes;\n    }\n  };\n\n  module.exports = crypt;\n})();\n", "/*!\n * Determine if an object is a Buffer\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n\n// The _isBuffer check is for Safari 5-7 support, because it's missing\n// Object.prototype.constructor. Remove this eventually\nmodule.exports = function (obj) {\n  return obj != null && (isBuffer(obj) || isSlowBuffer(obj) || !!obj._isBuffer)\n}\n\nfunction isBuffer (obj) {\n  return !!obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj)\n}\n\n// For Node v0.10 support. Remove this eventually.\nfunction isSlowBuffer (obj) {\n  return typeof obj.readFloatLE === 'function' && typeof obj.slice === 'function' && isBuffer(obj.slice(0, 0))\n}\n", "(function(){\r\n  var crypt = require('crypt'),\r\n      utf8 = require('charenc').utf8,\r\n      isBuffer = require('is-buffer'),\r\n      bin = require('charenc').bin,\r\n\r\n  // The core\r\n  md5 = function (message, options) {\r\n    // Convert to byte array\r\n    if (message.constructor == String)\r\n      if (options && options.encoding === 'binary')\r\n        message = bin.stringToBytes(message);\r\n      else\r\n        message = utf8.stringToBytes(message);\r\n    else if (isBuffer(message))\r\n      message = Array.prototype.slice.call(message, 0);\r\n    else if (!Array.isArray(message) && message.constructor !== Uint8Array)\r\n      message = message.toString();\r\n    // else, assume byte array already\r\n\r\n    var m = crypt.bytesToWords(message),\r\n        l = message.length * 8,\r\n        a =  1732584193,\r\n        b = -271733879,\r\n        c = -1732584194,\r\n        d =  271733878;\r\n\r\n    // Swap endian\r\n    for (var i = 0; i < m.length; i++) {\r\n      m[i] = ((m[i] <<  8) | (m[i] >>> 24)) & 0x00FF00FF |\r\n             ((m[i] << 24) | (m[i] >>>  8)) & 0xFF00FF00;\r\n    }\r\n\r\n    // Padding\r\n    m[l >>> 5] |= 0x80 << (l % 32);\r\n    m[(((l + 64) >>> 9) << 4) + 14] = l;\r\n\r\n    // Method shortcuts\r\n    var FF = md5._ff,\r\n        GG = md5._gg,\r\n        HH = md5._hh,\r\n        II = md5._ii;\r\n\r\n    for (var i = 0; i < m.length; i += 16) {\r\n\r\n      var aa = a,\r\n          bb = b,\r\n          cc = c,\r\n          dd = d;\r\n\r\n      a = FF(a, b, c, d, m[i+ 0],  7, -680876936);\r\n      d = FF(d, a, b, c, m[i+ 1], 12, -389564586);\r\n      c = FF(c, d, a, b, m[i+ 2], 17,  606105819);\r\n      b = FF(b, c, d, a, m[i+ 3], 22, -1044525330);\r\n      a = FF(a, b, c, d, m[i+ 4],  7, -176418897);\r\n      d = FF(d, a, b, c, m[i+ 5], 12,  1200080426);\r\n      c = FF(c, d, a, b, m[i+ 6], 17, -1473231341);\r\n      b = FF(b, c, d, a, m[i+ 7], 22, -45705983);\r\n      a = FF(a, b, c, d, m[i+ 8],  7,  1770035416);\r\n      d = FF(d, a, b, c, m[i+ 9], 12, -1958414417);\r\n      c = FF(c, d, a, b, m[i+10], 17, -42063);\r\n      b = FF(b, c, d, a, m[i+11], 22, -1990404162);\r\n      a = FF(a, b, c, d, m[i+12],  7,  1804603682);\r\n      d = FF(d, a, b, c, m[i+13], 12, -40341101);\r\n      c = FF(c, d, a, b, m[i+14], 17, -1502002290);\r\n      b = FF(b, c, d, a, m[i+15], 22,  1236535329);\r\n\r\n      a = GG(a, b, c, d, m[i+ 1],  5, -165796510);\r\n      d = GG(d, a, b, c, m[i+ 6],  9, -1069501632);\r\n      c = GG(c, d, a, b, m[i+11], 14,  643717713);\r\n      b = GG(b, c, d, a, m[i+ 0], 20, -373897302);\r\n      a = GG(a, b, c, d, m[i+ 5],  5, -701558691);\r\n      d = GG(d, a, b, c, m[i+10],  9,  38016083);\r\n      c = GG(c, d, a, b, m[i+15], 14, -660478335);\r\n      b = GG(b, c, d, a, m[i+ 4], 20, -405537848);\r\n      a = GG(a, b, c, d, m[i+ 9],  5,  568446438);\r\n      d = GG(d, a, b, c, m[i+14],  9, -1019803690);\r\n      c = GG(c, d, a, b, m[i+ 3], 14, -187363961);\r\n      b = GG(b, c, d, a, m[i+ 8], 20,  1163531501);\r\n      a = GG(a, b, c, d, m[i+13],  5, -1444681467);\r\n      d = GG(d, a, b, c, m[i+ 2],  9, -51403784);\r\n      c = GG(c, d, a, b, m[i+ 7], 14,  1735328473);\r\n      b = GG(b, c, d, a, m[i+12], 20, -1926607734);\r\n\r\n      a = HH(a, b, c, d, m[i+ 5],  4, -378558);\r\n      d = HH(d, a, b, c, m[i+ 8], 11, -2022574463);\r\n      c = HH(c, d, a, b, m[i+11], 16,  1839030562);\r\n      b = HH(b, c, d, a, m[i+14], 23, -35309556);\r\n      a = HH(a, b, c, d, m[i+ 1],  4, -1530992060);\r\n      d = HH(d, a, b, c, m[i+ 4], 11,  1272893353);\r\n      c = HH(c, d, a, b, m[i+ 7], 16, -155497632);\r\n      b = HH(b, c, d, a, m[i+10], 23, -1094730640);\r\n      a = HH(a, b, c, d, m[i+13],  4,  681279174);\r\n      d = HH(d, a, b, c, m[i+ 0], 11, -358537222);\r\n      c = HH(c, d, a, b, m[i+ 3], 16, -722521979);\r\n      b = HH(b, c, d, a, m[i+ 6], 23,  76029189);\r\n      a = HH(a, b, c, d, m[i+ 9],  4, -640364487);\r\n      d = HH(d, a, b, c, m[i+12], 11, -421815835);\r\n      c = HH(c, d, a, b, m[i+15], 16,  530742520);\r\n      b = HH(b, c, d, a, m[i+ 2], 23, -995338651);\r\n\r\n      a = II(a, b, c, d, m[i+ 0],  6, -198630844);\r\n      d = II(d, a, b, c, m[i+ 7], 10,  1126891415);\r\n      c = II(c, d, a, b, m[i+14], 15, -1416354905);\r\n      b = II(b, c, d, a, m[i+ 5], 21, -57434055);\r\n      a = II(a, b, c, d, m[i+12],  6,  1700485571);\r\n      d = II(d, a, b, c, m[i+ 3], 10, -1894986606);\r\n      c = II(c, d, a, b, m[i+10], 15, -1051523);\r\n      b = II(b, c, d, a, m[i+ 1], 21, -2054922799);\r\n      a = II(a, b, c, d, m[i+ 8],  6,  1873313359);\r\n      d = II(d, a, b, c, m[i+15], 10, -30611744);\r\n      c = II(c, d, a, b, m[i+ 6], 15, -1560198380);\r\n      b = II(b, c, d, a, m[i+13], 21,  1309151649);\r\n      a = II(a, b, c, d, m[i+ 4],  6, -145523070);\r\n      d = II(d, a, b, c, m[i+11], 10, -1120210379);\r\n      c = II(c, d, a, b, m[i+ 2], 15,  718787259);\r\n      b = II(b, c, d, a, m[i+ 9], 21, -343485551);\r\n\r\n      a = (a + aa) >>> 0;\r\n      b = (b + bb) >>> 0;\r\n      c = (c + cc) >>> 0;\r\n      d = (d + dd) >>> 0;\r\n    }\r\n\r\n    return crypt.endian([a, b, c, d]);\r\n  };\r\n\r\n  // Auxiliary functions\r\n  md5._ff  = function (a, b, c, d, x, s, t) {\r\n    var n = a + (b & c | ~b & d) + (x >>> 0) + t;\r\n    return ((n << s) | (n >>> (32 - s))) + b;\r\n  };\r\n  md5._gg  = function (a, b, c, d, x, s, t) {\r\n    var n = a + (b & d | c & ~d) + (x >>> 0) + t;\r\n    return ((n << s) | (n >>> (32 - s))) + b;\r\n  };\r\n  md5._hh  = function (a, b, c, d, x, s, t) {\r\n    var n = a + (b ^ c ^ d) + (x >>> 0) + t;\r\n    return ((n << s) | (n >>> (32 - s))) + b;\r\n  };\r\n  md5._ii  = function (a, b, c, d, x, s, t) {\r\n    var n = a + (c ^ (b | ~d)) + (x >>> 0) + t;\r\n    return ((n << s) | (n >>> (32 - s))) + b;\r\n  };\r\n\r\n  // Package private blocksize\r\n  md5._blocksize = 16;\r\n  md5._digestsize = 16;\r\n\r\n  module.exports = function (message, options) {\r\n    if (message === undefined || message === null)\r\n      throw new Error('Illegal argument ' + message);\r\n\r\n    var digestbytes = crypt.wordsToBytes(md5(message, options));\r\n    return options && options.asBytes ? digestbytes :\r\n        options && options.asString ? bin.bytesToString(digestbytes) :\r\n        crypt.bytesToHex(digestbytes);\r\n  };\r\n\r\n})();\r\n", "/**\n * @license React\n * react.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var ReactVersion = '18.2.0';\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\n/**\n * Keeps track of the current dispatcher.\n */\nvar ReactCurrentDispatcher = {\n  /**\n   * @internal\n   * @type {ReactComponent}\n   */\n  current: null\n};\n\n/**\n * Keeps track of the current batch's configuration such as how long an update\n * should suspend for if it needs to.\n */\nvar ReactCurrentBatchConfig = {\n  transition: null\n};\n\nvar ReactCurrentActQueue = {\n  current: null,\n  // Used to reproduce behavior of `batchedUpdates` in legacy mode.\n  isBatchingLegacy: false,\n  didScheduleLegacyUpdate: false\n};\n\n/**\n * Keeps track of the current owner.\n *\n * The current owner is the component who should own any components that are\n * currently being constructed.\n */\nvar ReactCurrentOwner = {\n  /**\n   * @internal\n   * @type {ReactComponent}\n   */\n  current: null\n};\n\nvar ReactDebugCurrentFrame = {};\nvar currentExtraStackFrame = null;\nfunction setExtraStackFrame(stack) {\n  {\n    currentExtraStackFrame = stack;\n  }\n}\n\n{\n  ReactDebugCurrentFrame.setExtraStackFrame = function (stack) {\n    {\n      currentExtraStackFrame = stack;\n    }\n  }; // Stack implementation injected by the current renderer.\n\n\n  ReactDebugCurrentFrame.getCurrentStack = null;\n\n  ReactDebugCurrentFrame.getStackAddendum = function () {\n    var stack = ''; // Add an extra top frame while an element is being validated\n\n    if (currentExtraStackFrame) {\n      stack += currentExtraStackFrame;\n    } // Delegate to the injected renderer-specific implementation\n\n\n    var impl = ReactDebugCurrentFrame.getCurrentStack;\n\n    if (impl) {\n      stack += impl() || '';\n    }\n\n    return stack;\n  };\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar ReactSharedInternals = {\n  ReactCurrentDispatcher: ReactCurrentDispatcher,\n  ReactCurrentBatchConfig: ReactCurrentBatchConfig,\n  ReactCurrentOwner: ReactCurrentOwner\n};\n\n{\n  ReactSharedInternals.ReactDebugCurrentFrame = ReactDebugCurrentFrame;\n  ReactSharedInternals.ReactCurrentActQueue = ReactCurrentActQueue;\n}\n\n// by calls to these methods by a Babel plugin.\n//\n// In PROD (or in packages without access to React internals),\n// they are left as they are instead.\n\nfunction warn(format) {\n  {\n    {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      printWarning('warn', format, args);\n    }\n  }\n}\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\nvar didWarnStateUpdateForUnmountedComponent = {};\n\nfunction warnNoop(publicInstance, callerName) {\n  {\n    var _constructor = publicInstance.constructor;\n    var componentName = _constructor && (_constructor.displayName || _constructor.name) || 'ReactClass';\n    var warningKey = componentName + \".\" + callerName;\n\n    if (didWarnStateUpdateForUnmountedComponent[warningKey]) {\n      return;\n    }\n\n    error(\"Can't call %s on a component that is not yet mounted. \" + 'This is a no-op, but it might indicate a bug in your application. ' + 'Instead, assign to `this.state` directly or define a `state = {};` ' + 'class property with the desired state in the %s component.', callerName, componentName);\n\n    didWarnStateUpdateForUnmountedComponent[warningKey] = true;\n  }\n}\n/**\n * This is the abstract API for an update queue.\n */\n\n\nvar ReactNoopUpdateQueue = {\n  /**\n   * Checks whether or not this composite component is mounted.\n   * @param {ReactClass} publicInstance The instance we want to test.\n   * @return {boolean} True if mounted, false otherwise.\n   * @protected\n   * @final\n   */\n  isMounted: function (publicInstance) {\n    return false;\n  },\n\n  /**\n   * Forces an update. This should only be invoked when it is known with\n   * certainty that we are **not** in a DOM transaction.\n   *\n   * You may want to call this when you know that some deeper aspect of the\n   * component's state has changed but `setState` was not called.\n   *\n   * This will not invoke `shouldComponentUpdate`, but it will invoke\n   * `componentWillUpdate` and `componentDidUpdate`.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} callerName name of the calling function in the public API.\n   * @internal\n   */\n  enqueueForceUpdate: function (publicInstance, callback, callerName) {\n    warnNoop(publicInstance, 'forceUpdate');\n  },\n\n  /**\n   * Replaces all of the state. Always use this or `setState` to mutate state.\n   * You should treat `this.state` as immutable.\n   *\n   * There is no guarantee that `this.state` will be immediately updated, so\n   * accessing `this.state` after calling this method may return the old value.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {object} completeState Next state.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} callerName name of the calling function in the public API.\n   * @internal\n   */\n  enqueueReplaceState: function (publicInstance, completeState, callback, callerName) {\n    warnNoop(publicInstance, 'replaceState');\n  },\n\n  /**\n   * Sets a subset of the state. This only exists because _pendingState is\n   * internal. This provides a merging strategy that is not available to deep\n   * properties which is confusing. TODO: Expose pendingState or don't use it\n   * during the merge.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {object} partialState Next partial state to be merged with state.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} Name of the calling function in the public API.\n   * @internal\n   */\n  enqueueSetState: function (publicInstance, partialState, callback, callerName) {\n    warnNoop(publicInstance, 'setState');\n  }\n};\n\nvar assign = Object.assign;\n\nvar emptyObject = {};\n\n{\n  Object.freeze(emptyObject);\n}\n/**\n * Base class helpers for the updating state of a component.\n */\n\n\nfunction Component(props, context, updater) {\n  this.props = props;\n  this.context = context; // If a component has string refs, we will assign a different object later.\n\n  this.refs = emptyObject; // We initialize the default updater but the real one gets injected by the\n  // renderer.\n\n  this.updater = updater || ReactNoopUpdateQueue;\n}\n\nComponent.prototype.isReactComponent = {};\n/**\n * Sets a subset of the state. Always use this to mutate\n * state. You should treat `this.state` as immutable.\n *\n * There is no guarantee that `this.state` will be immediately updated, so\n * accessing `this.state` after calling this method may return the old value.\n *\n * There is no guarantee that calls to `setState` will run synchronously,\n * as they may eventually be batched together.  You can provide an optional\n * callback that will be executed when the call to setState is actually\n * completed.\n *\n * When a function is provided to setState, it will be called at some point in\n * the future (not synchronously). It will be called with the up to date\n * component arguments (state, props, context). These values can be different\n * from this.* because your function may be called after receiveProps but before\n * shouldComponentUpdate, and this new state, props, and context will not yet be\n * assigned to this.\n *\n * @param {object|function} partialState Next partial state or function to\n *        produce next partial state to be merged with current state.\n * @param {?function} callback Called after state is updated.\n * @final\n * @protected\n */\n\nComponent.prototype.setState = function (partialState, callback) {\n  if (typeof partialState !== 'object' && typeof partialState !== 'function' && partialState != null) {\n    throw new Error('setState(...): takes an object of state variables to update or a ' + 'function which returns an object of state variables.');\n  }\n\n  this.updater.enqueueSetState(this, partialState, callback, 'setState');\n};\n/**\n * Forces an update. This should only be invoked when it is known with\n * certainty that we are **not** in a DOM transaction.\n *\n * You may want to call this when you know that some deeper aspect of the\n * component's state has changed but `setState` was not called.\n *\n * This will not invoke `shouldComponentUpdate`, but it will invoke\n * `componentWillUpdate` and `componentDidUpdate`.\n *\n * @param {?function} callback Called after update is complete.\n * @final\n * @protected\n */\n\n\nComponent.prototype.forceUpdate = function (callback) {\n  this.updater.enqueueForceUpdate(this, callback, 'forceUpdate');\n};\n/**\n * Deprecated APIs. These APIs used to exist on classic React classes but since\n * we would like to deprecate them, we're not going to move them over to this\n * modern base class. Instead, we define a getter that warns if it's accessed.\n */\n\n\n{\n  var deprecatedAPIs = {\n    isMounted: ['isMounted', 'Instead, make sure to clean up subscriptions and pending requests in ' + 'componentWillUnmount to prevent memory leaks.'],\n    replaceState: ['replaceState', 'Refactor your code to use setState instead (see ' + 'https://github.com/facebook/react/issues/3236).']\n  };\n\n  var defineDeprecationWarning = function (methodName, info) {\n    Object.defineProperty(Component.prototype, methodName, {\n      get: function () {\n        warn('%s(...) is deprecated in plain JavaScript React classes. %s', info[0], info[1]);\n\n        return undefined;\n      }\n    });\n  };\n\n  for (var fnName in deprecatedAPIs) {\n    if (deprecatedAPIs.hasOwnProperty(fnName)) {\n      defineDeprecationWarning(fnName, deprecatedAPIs[fnName]);\n    }\n  }\n}\n\nfunction ComponentDummy() {}\n\nComponentDummy.prototype = Component.prototype;\n/**\n * Convenience component with default shallow equality check for sCU.\n */\n\nfunction PureComponent(props, context, updater) {\n  this.props = props;\n  this.context = context; // If a component has string refs, we will assign a different object later.\n\n  this.refs = emptyObject;\n  this.updater = updater || ReactNoopUpdateQueue;\n}\n\nvar pureComponentPrototype = PureComponent.prototype = new ComponentDummy();\npureComponentPrototype.constructor = PureComponent; // Avoid an extra prototype jump for these methods.\n\nassign(pureComponentPrototype, Component.prototype);\npureComponentPrototype.isPureReactComponent = true;\n\n// an immutable object with a single mutable value\nfunction createRef() {\n  var refObject = {\n    current: null\n  };\n\n  {\n    Object.seal(refObject);\n  }\n\n  return refObject;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown, specialPropRefWarningShown, didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  var warnAboutAccessingKey = function () {\n    {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    }\n  };\n\n  warnAboutAccessingKey.isReactWarning = true;\n  Object.defineProperty(props, 'key', {\n    get: warnAboutAccessingKey,\n    configurable: true\n  });\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  var warnAboutAccessingRef = function () {\n    {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    }\n  };\n\n  warnAboutAccessingRef.isReactWarning = true;\n  Object.defineProperty(props, 'ref', {\n    get: warnAboutAccessingRef,\n    configurable: true\n  });\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && config.__self && ReactCurrentOwner.current.stateNode !== config.__self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', componentName, config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * Create and return a new ReactElement of the given type.\n * See https://reactjs.org/docs/react-api.html#createelement\n */\n\nfunction createElement(type, config, children) {\n  var propName; // Reserved names are extracted\n\n  var props = {};\n  var key = null;\n  var ref = null;\n  var self = null;\n  var source = null;\n\n  if (config != null) {\n    if (hasValidRef(config)) {\n      ref = config.ref;\n\n      {\n        warnIfStringRefCannotBeAutoConverted(config);\n      }\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    self = config.__self === undefined ? null : config.__self;\n    source = config.__source === undefined ? null : config.__source; // Remaining properties are added to a new props object\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    }\n  } // Children can be more than one argument, and those are transferred onto\n  // the newly allocated props object.\n\n\n  var childrenLength = arguments.length - 2;\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = Array(childrenLength);\n\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 2];\n    }\n\n    {\n      if (Object.freeze) {\n        Object.freeze(childArray);\n      }\n    }\n\n    props.children = childArray;\n  } // Resolve default props\n\n\n  if (type && type.defaultProps) {\n    var defaultProps = type.defaultProps;\n\n    for (propName in defaultProps) {\n      if (props[propName] === undefined) {\n        props[propName] = defaultProps[propName];\n      }\n    }\n  }\n\n  {\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n  }\n\n  return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n}\nfunction cloneAndReplaceKey(oldElement, newKey) {\n  var newElement = ReactElement(oldElement.type, newKey, oldElement.ref, oldElement._self, oldElement._source, oldElement._owner, oldElement.props);\n  return newElement;\n}\n/**\n * Clone and return a new ReactElement using element as the starting point.\n * See https://reactjs.org/docs/react-api.html#cloneelement\n */\n\nfunction cloneElement(element, config, children) {\n  if (element === null || element === undefined) {\n    throw new Error(\"React.cloneElement(...): The argument must be a React element, but you passed \" + element + \".\");\n  }\n\n  var propName; // Original props are copied\n\n  var props = assign({}, element.props); // Reserved names are extracted\n\n  var key = element.key;\n  var ref = element.ref; // Self is preserved since the owner is preserved.\n\n  var self = element._self; // Source is preserved since cloneElement is unlikely to be targeted by a\n  // transpiler, and the original source is probably a better indicator of the\n  // true owner.\n\n  var source = element._source; // Owner will be preserved, unless ref is overridden\n\n  var owner = element._owner;\n\n  if (config != null) {\n    if (hasValidRef(config)) {\n      // Silently steal the ref from the parent.\n      ref = config.ref;\n      owner = ReactCurrentOwner.current;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    } // Remaining properties override existing props\n\n\n    var defaultProps;\n\n    if (element.type && element.type.defaultProps) {\n      defaultProps = element.type.defaultProps;\n    }\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        if (config[propName] === undefined && defaultProps !== undefined) {\n          // Resolve default props\n          props[propName] = defaultProps[propName];\n        } else {\n          props[propName] = config[propName];\n        }\n      }\n    }\n  } // Children can be more than one argument, and those are transferred onto\n  // the newly allocated props object.\n\n\n  var childrenLength = arguments.length - 2;\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = Array(childrenLength);\n\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 2];\n    }\n\n    props.children = childArray;\n  }\n\n  return ReactElement(element.type, key, ref, self, source, owner, props);\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\n\nvar SEPARATOR = '.';\nvar SUBSEPARATOR = ':';\n/**\n * Escape and wrap key so it is safe to use as a reactid\n *\n * @param {string} key to be escaped.\n * @return {string} the escaped key.\n */\n\nfunction escape(key) {\n  var escapeRegex = /[=:]/g;\n  var escaperLookup = {\n    '=': '=0',\n    ':': '=2'\n  };\n  var escapedString = key.replace(escapeRegex, function (match) {\n    return escaperLookup[match];\n  });\n  return '$' + escapedString;\n}\n/**\n * TODO: Test that a single child and an array with one item have the same key\n * pattern.\n */\n\n\nvar didWarnAboutMaps = false;\nvar userProvidedKeyEscapeRegex = /\\/+/g;\n\nfunction escapeUserProvidedKey(text) {\n  return text.replace(userProvidedKeyEscapeRegex, '$&/');\n}\n/**\n * Generate a key string that identifies a element within a set.\n *\n * @param {*} element A element that could contain a manual key.\n * @param {number} index Index that is used if a manual key is not provided.\n * @return {string}\n */\n\n\nfunction getElementKey(element, index) {\n  // Do some typechecking here since we call this blindly. We want to ensure\n  // that we don't block potential future ES APIs.\n  if (typeof element === 'object' && element !== null && element.key != null) {\n    // Explicit key\n    {\n      checkKeyStringCoercion(element.key);\n    }\n\n    return escape('' + element.key);\n  } // Implicit key determined by the index in the set\n\n\n  return index.toString(36);\n}\n\nfunction mapIntoArray(children, array, escapedPrefix, nameSoFar, callback) {\n  var type = typeof children;\n\n  if (type === 'undefined' || type === 'boolean') {\n    // All of the above are perceived as null.\n    children = null;\n  }\n\n  var invokeCallback = false;\n\n  if (children === null) {\n    invokeCallback = true;\n  } else {\n    switch (type) {\n      case 'string':\n      case 'number':\n        invokeCallback = true;\n        break;\n\n      case 'object':\n        switch (children.$$typeof) {\n          case REACT_ELEMENT_TYPE:\n          case REACT_PORTAL_TYPE:\n            invokeCallback = true;\n        }\n\n    }\n  }\n\n  if (invokeCallback) {\n    var _child = children;\n    var mappedChild = callback(_child); // If it's the only child, treat the name as if it was wrapped in an array\n    // so that it's consistent if the number of children grows:\n\n    var childKey = nameSoFar === '' ? SEPARATOR + getElementKey(_child, 0) : nameSoFar;\n\n    if (isArray(mappedChild)) {\n      var escapedChildKey = '';\n\n      if (childKey != null) {\n        escapedChildKey = escapeUserProvidedKey(childKey) + '/';\n      }\n\n      mapIntoArray(mappedChild, array, escapedChildKey, '', function (c) {\n        return c;\n      });\n    } else if (mappedChild != null) {\n      if (isValidElement(mappedChild)) {\n        {\n          // The `if` statement here prevents auto-disabling of the safe\n          // coercion ESLint rule, so we must manually disable it below.\n          // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n          if (mappedChild.key && (!_child || _child.key !== mappedChild.key)) {\n            checkKeyStringCoercion(mappedChild.key);\n          }\n        }\n\n        mappedChild = cloneAndReplaceKey(mappedChild, // Keep both the (mapped) and old keys if they differ, just as\n        // traverseAllChildren used to do for objects as children\n        escapedPrefix + ( // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n        mappedChild.key && (!_child || _child.key !== mappedChild.key) ? // $FlowFixMe Flow incorrectly thinks existing element's key can be a number\n        // eslint-disable-next-line react-internal/safe-string-coercion\n        escapeUserProvidedKey('' + mappedChild.key) + '/' : '') + childKey);\n      }\n\n      array.push(mappedChild);\n    }\n\n    return 1;\n  }\n\n  var child;\n  var nextName;\n  var subtreeCount = 0; // Count of children found in the current subtree.\n\n  var nextNamePrefix = nameSoFar === '' ? SEPARATOR : nameSoFar + SUBSEPARATOR;\n\n  if (isArray(children)) {\n    for (var i = 0; i < children.length; i++) {\n      child = children[i];\n      nextName = nextNamePrefix + getElementKey(child, i);\n      subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n    }\n  } else {\n    var iteratorFn = getIteratorFn(children);\n\n    if (typeof iteratorFn === 'function') {\n      var iterableChildren = children;\n\n      {\n        // Warn about using Maps as children\n        if (iteratorFn === iterableChildren.entries) {\n          if (!didWarnAboutMaps) {\n            warn('Using Maps as children is not supported. ' + 'Use an array of keyed ReactElements instead.');\n          }\n\n          didWarnAboutMaps = true;\n        }\n      }\n\n      var iterator = iteratorFn.call(iterableChildren);\n      var step;\n      var ii = 0;\n\n      while (!(step = iterator.next()).done) {\n        child = step.value;\n        nextName = nextNamePrefix + getElementKey(child, ii++);\n        subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n      }\n    } else if (type === 'object') {\n      // eslint-disable-next-line react-internal/safe-string-coercion\n      var childrenString = String(children);\n      throw new Error(\"Objects are not valid as a React child (found: \" + (childrenString === '[object Object]' ? 'object with keys {' + Object.keys(children).join(', ') + '}' : childrenString) + \"). \" + 'If you meant to render a collection of children, use an array ' + 'instead.');\n    }\n  }\n\n  return subtreeCount;\n}\n\n/**\n * Maps children that are typically specified as `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenmap\n *\n * The provided mapFunction(child, index) will be called for each\n * leaf child.\n *\n * @param {?*} children Children tree container.\n * @param {function(*, int)} func The map function.\n * @param {*} context Context for mapFunction.\n * @return {object} Object containing the ordered map of results.\n */\nfunction mapChildren(children, func, context) {\n  if (children == null) {\n    return children;\n  }\n\n  var result = [];\n  var count = 0;\n  mapIntoArray(children, result, '', '', function (child) {\n    return func.call(context, child, count++);\n  });\n  return result;\n}\n/**\n * Count the number of children that are typically specified as\n * `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrencount\n *\n * @param {?*} children Children tree container.\n * @return {number} The number of children.\n */\n\n\nfunction countChildren(children) {\n  var n = 0;\n  mapChildren(children, function () {\n    n++; // Don't return anything\n  });\n  return n;\n}\n\n/**\n * Iterates through children that are typically specified as `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenforeach\n *\n * The provided forEachFunc(child, index) will be called for each\n * leaf child.\n *\n * @param {?*} children Children tree container.\n * @param {function(*, int)} forEachFunc\n * @param {*} forEachContext Context for forEachContext.\n */\nfunction forEachChildren(children, forEachFunc, forEachContext) {\n  mapChildren(children, function () {\n    forEachFunc.apply(this, arguments); // Don't return anything.\n  }, forEachContext);\n}\n/**\n * Flatten a children object (typically specified as `props.children`) and\n * return an array with appropriately re-keyed children.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrentoarray\n */\n\n\nfunction toArray(children) {\n  return mapChildren(children, function (child) {\n    return child;\n  }) || [];\n}\n/**\n * Returns the first child in a collection of children and verifies that there\n * is only one child in the collection.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenonly\n *\n * The current implementation of this function assumes that a single child gets\n * passed without a wrapper, but the purpose of this helper function is to\n * abstract away the particular structure of children.\n *\n * @param {?object} children Child collection structure.\n * @return {ReactElement} The first and only `ReactElement` contained in the\n * structure.\n */\n\n\nfunction onlyChild(children) {\n  if (!isValidElement(children)) {\n    throw new Error('React.Children.only expected to receive a single React element child.');\n  }\n\n  return children;\n}\n\nfunction createContext(defaultValue) {\n  // TODO: Second argument used to be an optional `calculateChangedBits`\n  // function. Warn to reserve for future use?\n  var context = {\n    $$typeof: REACT_CONTEXT_TYPE,\n    // As a workaround to support multiple concurrent renderers, we categorize\n    // some renderers as primary and others as secondary. We only expect\n    // there to be two concurrent renderers at most: React Native (primary) and\n    // Fabric (secondary); React DOM (primary) and React ART (secondary).\n    // Secondary renderers store their context values on separate fields.\n    _currentValue: defaultValue,\n    _currentValue2: defaultValue,\n    // Used to track how many concurrent renderers this context currently\n    // supports within in a single renderer. Such as parallel server rendering.\n    _threadCount: 0,\n    // These are circular\n    Provider: null,\n    Consumer: null,\n    // Add these to use same hidden class in VM as ServerContext\n    _defaultValue: null,\n    _globalName: null\n  };\n  context.Provider = {\n    $$typeof: REACT_PROVIDER_TYPE,\n    _context: context\n  };\n  var hasWarnedAboutUsingNestedContextConsumers = false;\n  var hasWarnedAboutUsingConsumerProvider = false;\n  var hasWarnedAboutDisplayNameOnConsumer = false;\n\n  {\n    // A separate object, but proxies back to the original context object for\n    // backwards compatibility. It has a different $$typeof, so we can properly\n    // warn for the incorrect usage of Context as a Consumer.\n    var Consumer = {\n      $$typeof: REACT_CONTEXT_TYPE,\n      _context: context\n    }; // $FlowFixMe: Flow complains about not setting a value, which is intentional here\n\n    Object.defineProperties(Consumer, {\n      Provider: {\n        get: function () {\n          if (!hasWarnedAboutUsingConsumerProvider) {\n            hasWarnedAboutUsingConsumerProvider = true;\n\n            error('Rendering <Context.Consumer.Provider> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Provider> instead?');\n          }\n\n          return context.Provider;\n        },\n        set: function (_Provider) {\n          context.Provider = _Provider;\n        }\n      },\n      _currentValue: {\n        get: function () {\n          return context._currentValue;\n        },\n        set: function (_currentValue) {\n          context._currentValue = _currentValue;\n        }\n      },\n      _currentValue2: {\n        get: function () {\n          return context._currentValue2;\n        },\n        set: function (_currentValue2) {\n          context._currentValue2 = _currentValue2;\n        }\n      },\n      _threadCount: {\n        get: function () {\n          return context._threadCount;\n        },\n        set: function (_threadCount) {\n          context._threadCount = _threadCount;\n        }\n      },\n      Consumer: {\n        get: function () {\n          if (!hasWarnedAboutUsingNestedContextConsumers) {\n            hasWarnedAboutUsingNestedContextConsumers = true;\n\n            error('Rendering <Context.Consumer.Consumer> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Consumer> instead?');\n          }\n\n          return context.Consumer;\n        }\n      },\n      displayName: {\n        get: function () {\n          return context.displayName;\n        },\n        set: function (displayName) {\n          if (!hasWarnedAboutDisplayNameOnConsumer) {\n            warn('Setting `displayName` on Context.Consumer has no effect. ' + \"You should set it directly on the context with Context.displayName = '%s'.\", displayName);\n\n            hasWarnedAboutDisplayNameOnConsumer = true;\n          }\n        }\n      }\n    }); // $FlowFixMe: Flow complains about missing properties because it doesn't understand defineProperty\n\n    context.Consumer = Consumer;\n  }\n\n  {\n    context._currentRenderer = null;\n    context._currentRenderer2 = null;\n  }\n\n  return context;\n}\n\nvar Uninitialized = -1;\nvar Pending = 0;\nvar Resolved = 1;\nvar Rejected = 2;\n\nfunction lazyInitializer(payload) {\n  if (payload._status === Uninitialized) {\n    var ctor = payload._result;\n    var thenable = ctor(); // Transition to the next state.\n    // This might throw either because it's missing or throws. If so, we treat it\n    // as still uninitialized and try again next time. Which is the same as what\n    // happens if the ctor or any wrappers processing the ctor throws. This might\n    // end up fixing it if the resolution was a concurrency bug.\n\n    thenable.then(function (moduleObject) {\n      if (payload._status === Pending || payload._status === Uninitialized) {\n        // Transition to the next state.\n        var resolved = payload;\n        resolved._status = Resolved;\n        resolved._result = moduleObject;\n      }\n    }, function (error) {\n      if (payload._status === Pending || payload._status === Uninitialized) {\n        // Transition to the next state.\n        var rejected = payload;\n        rejected._status = Rejected;\n        rejected._result = error;\n      }\n    });\n\n    if (payload._status === Uninitialized) {\n      // In case, we're still uninitialized, then we're waiting for the thenable\n      // to resolve. Set it as pending in the meantime.\n      var pending = payload;\n      pending._status = Pending;\n      pending._result = thenable;\n    }\n  }\n\n  if (payload._status === Resolved) {\n    var moduleObject = payload._result;\n\n    {\n      if (moduleObject === undefined) {\n        error('lazy: Expected the result of a dynamic imp' + 'ort() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' + // Break up imports to avoid accidentally parsing them as dependencies.\n        'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\\n\\n\" + 'Did you accidentally put curly braces around the import?', moduleObject);\n      }\n    }\n\n    {\n      if (!('default' in moduleObject)) {\n        error('lazy: Expected the result of a dynamic imp' + 'ort() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' + // Break up imports to avoid accidentally parsing them as dependencies.\n        'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\", moduleObject);\n      }\n    }\n\n    return moduleObject.default;\n  } else {\n    throw payload._result;\n  }\n}\n\nfunction lazy(ctor) {\n  var payload = {\n    // We use these fields to store the result.\n    _status: Uninitialized,\n    _result: ctor\n  };\n  var lazyType = {\n    $$typeof: REACT_LAZY_TYPE,\n    _payload: payload,\n    _init: lazyInitializer\n  };\n\n  {\n    // In production, this would just set it on the object.\n    var defaultProps;\n    var propTypes; // $FlowFixMe\n\n    Object.defineProperties(lazyType, {\n      defaultProps: {\n        configurable: true,\n        get: function () {\n          return defaultProps;\n        },\n        set: function (newDefaultProps) {\n          error('React.lazy(...): It is not supported to assign `defaultProps` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n\n          defaultProps = newDefaultProps; // Match production behavior more closely:\n          // $FlowFixMe\n\n          Object.defineProperty(lazyType, 'defaultProps', {\n            enumerable: true\n          });\n        }\n      },\n      propTypes: {\n        configurable: true,\n        get: function () {\n          return propTypes;\n        },\n        set: function (newPropTypes) {\n          error('React.lazy(...): It is not supported to assign `propTypes` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n\n          propTypes = newPropTypes; // Match production behavior more closely:\n          // $FlowFixMe\n\n          Object.defineProperty(lazyType, 'propTypes', {\n            enumerable: true\n          });\n        }\n      }\n    });\n  }\n\n  return lazyType;\n}\n\nfunction forwardRef(render) {\n  {\n    if (render != null && render.$$typeof === REACT_MEMO_TYPE) {\n      error('forwardRef requires a render function but received a `memo` ' + 'component. Instead of forwardRef(memo(...)), use ' + 'memo(forwardRef(...)).');\n    } else if (typeof render !== 'function') {\n      error('forwardRef requires a render function but was given %s.', render === null ? 'null' : typeof render);\n    } else {\n      if (render.length !== 0 && render.length !== 2) {\n        error('forwardRef render functions accept exactly two parameters: props and ref. %s', render.length === 1 ? 'Did you forget to use the ref parameter?' : 'Any additional parameter will be undefined.');\n      }\n    }\n\n    if (render != null) {\n      if (render.defaultProps != null || render.propTypes != null) {\n        error('forwardRef render functions do not support propTypes or defaultProps. ' + 'Did you accidentally pass a React component?');\n      }\n    }\n  }\n\n  var elementType = {\n    $$typeof: REACT_FORWARD_REF_TYPE,\n    render: render\n  };\n\n  {\n    var ownName;\n    Object.defineProperty(elementType, 'displayName', {\n      enumerable: false,\n      configurable: true,\n      get: function () {\n        return ownName;\n      },\n      set: function (name) {\n        ownName = name; // The inner component shouldn't inherit this display name in most cases,\n        // because the component may be used elsewhere.\n        // But it's nice for anonymous functions to inherit the name,\n        // so that our component-stack generation logic will display their frames.\n        // An anonymous function generally suggests a pattern like:\n        //   React.forwardRef((props, ref) => {...});\n        // This kind of inner function is not used elsewhere so the side effect is okay.\n\n        if (!render.name && !render.displayName) {\n          render.displayName = name;\n        }\n      }\n    });\n  }\n\n  return elementType;\n}\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction memo(type, compare) {\n  {\n    if (!isValidElementType(type)) {\n      error('memo: The first argument must be a component. Instead ' + 'received: %s', type === null ? 'null' : typeof type);\n    }\n  }\n\n  var elementType = {\n    $$typeof: REACT_MEMO_TYPE,\n    type: type,\n    compare: compare === undefined ? null : compare\n  };\n\n  {\n    var ownName;\n    Object.defineProperty(elementType, 'displayName', {\n      enumerable: false,\n      configurable: true,\n      get: function () {\n        return ownName;\n      },\n      set: function (name) {\n        ownName = name; // The inner component shouldn't inherit this display name in most cases,\n        // because the component may be used elsewhere.\n        // But it's nice for anonymous functions to inherit the name,\n        // so that our component-stack generation logic will display their frames.\n        // An anonymous function generally suggests a pattern like:\n        //   React.memo((props) => {...});\n        // This kind of inner function is not used elsewhere so the side effect is okay.\n\n        if (!type.name && !type.displayName) {\n          type.displayName = name;\n        }\n      }\n    });\n  }\n\n  return elementType;\n}\n\nfunction resolveDispatcher() {\n  var dispatcher = ReactCurrentDispatcher.current;\n\n  {\n    if (dispatcher === null) {\n      error('Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for' + ' one of the following reasons:\\n' + '1. You might have mismatching versions of React and the renderer (such as React DOM)\\n' + '2. You might be breaking the Rules of Hooks\\n' + '3. You might have more than one copy of React in the same app\\n' + 'See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.');\n    }\n  } // Will result in a null access error if accessed outside render phase. We\n  // intentionally don't throw our own error because this is in a hot path.\n  // Also helps ensure this is inlined.\n\n\n  return dispatcher;\n}\nfunction useContext(Context) {\n  var dispatcher = resolveDispatcher();\n\n  {\n    // TODO: add a more generic warning for invalid values.\n    if (Context._context !== undefined) {\n      var realContext = Context._context; // Don't deduplicate because this legitimately causes bugs\n      // and nobody should be using this in existing code.\n\n      if (realContext.Consumer === Context) {\n        error('Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be ' + 'removed in a future major release. Did you mean to call useContext(Context) instead?');\n      } else if (realContext.Provider === Context) {\n        error('Calling useContext(Context.Provider) is not supported. ' + 'Did you mean to call useContext(Context) instead?');\n      }\n    }\n  }\n\n  return dispatcher.useContext(Context);\n}\nfunction useState(initialState) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useState(initialState);\n}\nfunction useReducer(reducer, initialArg, init) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useReducer(reducer, initialArg, init);\n}\nfunction useRef(initialValue) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useRef(initialValue);\n}\nfunction useEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useEffect(create, deps);\n}\nfunction useInsertionEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useInsertionEffect(create, deps);\n}\nfunction useLayoutEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useLayoutEffect(create, deps);\n}\nfunction useCallback(callback, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useCallback(callback, deps);\n}\nfunction useMemo(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useMemo(create, deps);\n}\nfunction useImperativeHandle(ref, create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useImperativeHandle(ref, create, deps);\n}\nfunction useDebugValue(value, formatterFn) {\n  {\n    var dispatcher = resolveDispatcher();\n    return dispatcher.useDebugValue(value, formatterFn);\n  }\n}\nfunction useTransition() {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useTransition();\n}\nfunction useDeferredValue(value) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useDeferredValue(value);\n}\nfunction useId() {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useId();\n}\nfunction useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher$1 = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher$1.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher$1.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher$1.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      setExtraStackFrame(stack);\n    } else {\n      setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n\nfunction getDeclarationErrorAddendum() {\n  if (ReactCurrentOwner.current) {\n    var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n    if (name) {\n      return '\\n\\nCheck the render method of `' + name + '`.';\n    }\n  }\n\n  return '';\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  if (source !== undefined) {\n    var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n    var lineNumber = source.lineNumber;\n    return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n  }\n\n  return '';\n}\n\nfunction getSourceInfoErrorAddendumForProps(elementProps) {\n  if (elementProps !== null && elementProps !== undefined) {\n    return getSourceInfoErrorAddendum(elementProps.__source);\n  }\n\n  return '';\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  var info = getDeclarationErrorAddendum();\n\n  if (!info) {\n    var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n    if (parentName) {\n      info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n    }\n  }\n\n  return info;\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  if (!element._store || element._store.validated || element.key != null) {\n    return;\n  }\n\n  element._store.validated = true;\n  var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n  if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n    return;\n  }\n\n  ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n  // property, it may be the creator of the child that's responsible for\n  // assigning it a key.\n\n  var childOwner = '';\n\n  if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n    // Give the component that originally created this child.\n    childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n  }\n\n  {\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  if (typeof node !== 'object') {\n    return;\n  }\n\n  if (isArray(node)) {\n    for (var i = 0; i < node.length; i++) {\n      var child = node[i];\n\n      if (isValidElement(child)) {\n        validateExplicitKey(child, parentType);\n      }\n    }\n  } else if (isValidElement(node)) {\n    // This element was passed in a valid location.\n    if (node._store) {\n      node._store.validated = true;\n    }\n  } else if (node) {\n    var iteratorFn = getIteratorFn(node);\n\n    if (typeof iteratorFn === 'function') {\n      // Entry iterators used to provide implicit keys,\n      // but now we print a separate warning for them later.\n      if (iteratorFn !== node.entries) {\n        var iterator = iteratorFn.call(node);\n        var step;\n\n        while (!(step = iterator.next()).done) {\n          if (isValidElement(step.value)) {\n            validateExplicitKey(step.value, parentType);\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\nfunction createElementWithValidation(type, props, children) {\n  var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n  // succeed and there will likely be errors in render.\n\n  if (!validType) {\n    var info = '';\n\n    if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n      info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n    }\n\n    var sourceInfo = getSourceInfoErrorAddendumForProps(props);\n\n    if (sourceInfo) {\n      info += sourceInfo;\n    } else {\n      info += getDeclarationErrorAddendum();\n    }\n\n    var typeString;\n\n    if (type === null) {\n      typeString = 'null';\n    } else if (isArray(type)) {\n      typeString = 'array';\n    } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n      typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n      info = ' Did you accidentally export a JSX literal instead of a component?';\n    } else {\n      typeString = typeof type;\n    }\n\n    {\n      error('React.createElement: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n  }\n\n  var element = createElement.apply(this, arguments); // The result can be nullish if a mock or a custom function is used.\n  // TODO: Drop this when these are no longer allowed as the type argument.\n\n  if (element == null) {\n    return element;\n  } // Skip key warning if the type isn't valid since our key validation logic\n  // doesn't expect a non-string/function type and can throw confusing errors.\n  // We don't want exception behavior to differ between dev and prod.\n  // (Rendering will throw with a helpful message and as soon as the type is\n  // fixed, the key warnings will appear.)\n\n\n  if (validType) {\n    for (var i = 2; i < arguments.length; i++) {\n      validateChildKeys(arguments[i], type);\n    }\n  }\n\n  if (type === REACT_FRAGMENT_TYPE) {\n    validateFragmentProps(element);\n  } else {\n    validatePropTypes(element);\n  }\n\n  return element;\n}\nvar didWarnAboutDeprecatedCreateFactory = false;\nfunction createFactoryWithValidation(type) {\n  var validatedFactory = createElementWithValidation.bind(null, type);\n  validatedFactory.type = type;\n\n  {\n    if (!didWarnAboutDeprecatedCreateFactory) {\n      didWarnAboutDeprecatedCreateFactory = true;\n\n      warn('React.createFactory() is deprecated and will be removed in ' + 'a future major release. Consider using JSX ' + 'or use React.createElement() directly instead.');\n    } // Legacy hook: remove it\n\n\n    Object.defineProperty(validatedFactory, 'type', {\n      enumerable: false,\n      get: function () {\n        warn('Factory.type is deprecated. Access the class directly ' + 'before passing it to createFactory.');\n\n        Object.defineProperty(this, 'type', {\n          value: type\n        });\n        return type;\n      }\n    });\n  }\n\n  return validatedFactory;\n}\nfunction cloneElementWithValidation(element, props, children) {\n  var newElement = cloneElement.apply(this, arguments);\n\n  for (var i = 2; i < arguments.length; i++) {\n    validateChildKeys(arguments[i], newElement.type);\n  }\n\n  validatePropTypes(newElement);\n  return newElement;\n}\n\nfunction startTransition(scope, options) {\n  var prevTransition = ReactCurrentBatchConfig.transition;\n  ReactCurrentBatchConfig.transition = {};\n  var currentTransition = ReactCurrentBatchConfig.transition;\n\n  {\n    ReactCurrentBatchConfig.transition._updatedFibers = new Set();\n  }\n\n  try {\n    scope();\n  } finally {\n    ReactCurrentBatchConfig.transition = prevTransition;\n\n    {\n      if (prevTransition === null && currentTransition._updatedFibers) {\n        var updatedFibersCount = currentTransition._updatedFibers.size;\n\n        if (updatedFibersCount > 10) {\n          warn('Detected a large number of updates inside startTransition. ' + 'If this is due to a subscription please re-write it to use React provided hooks. ' + 'Otherwise concurrent mode guarantees are off the table.');\n        }\n\n        currentTransition._updatedFibers.clear();\n      }\n    }\n  }\n}\n\nvar didWarnAboutMessageChannel = false;\nvar enqueueTaskImpl = null;\nfunction enqueueTask(task) {\n  if (enqueueTaskImpl === null) {\n    try {\n      // read require off the module object to get around the bundlers.\n      // we don't want them to detect a require and bundle a Node polyfill.\n      var requireString = ('require' + Math.random()).slice(0, 7);\n      var nodeRequire = module && module[requireString]; // assuming we're in node, let's try to get node's\n      // version of setImmediate, bypassing fake timers if any.\n\n      enqueueTaskImpl = nodeRequire.call(module, 'timers').setImmediate;\n    } catch (_err) {\n      // we're in a browser\n      // we can't use regular timers because they may still be faked\n      // so we try MessageChannel+postMessage instead\n      enqueueTaskImpl = function (callback) {\n        {\n          if (didWarnAboutMessageChannel === false) {\n            didWarnAboutMessageChannel = true;\n\n            if (typeof MessageChannel === 'undefined') {\n              error('This browser does not have a MessageChannel implementation, ' + 'so enqueuing tasks via await act(async () => ...) will fail. ' + 'Please file an issue at https://github.com/facebook/react/issues ' + 'if you encounter this warning.');\n            }\n          }\n        }\n\n        var channel = new MessageChannel();\n        channel.port1.onmessage = callback;\n        channel.port2.postMessage(undefined);\n      };\n    }\n  }\n\n  return enqueueTaskImpl(task);\n}\n\nvar actScopeDepth = 0;\nvar didWarnNoAwaitAct = false;\nfunction act(callback) {\n  {\n    // `act` calls can be nested, so we track the depth. This represents the\n    // number of `act` scopes on the stack.\n    var prevActScopeDepth = actScopeDepth;\n    actScopeDepth++;\n\n    if (ReactCurrentActQueue.current === null) {\n      // This is the outermost `act` scope. Initialize the queue. The reconciler\n      // will detect the queue and use it instead of Scheduler.\n      ReactCurrentActQueue.current = [];\n    }\n\n    var prevIsBatchingLegacy = ReactCurrentActQueue.isBatchingLegacy;\n    var result;\n\n    try {\n      // Used to reproduce behavior of `batchedUpdates` in legacy mode. Only\n      // set to `true` while the given callback is executed, not for updates\n      // triggered during an async event, because this is how the legacy\n      // implementation of `act` behaved.\n      ReactCurrentActQueue.isBatchingLegacy = true;\n      result = callback(); // Replicate behavior of original `act` implementation in legacy mode,\n      // which flushed updates immediately after the scope function exits, even\n      // if it's an async function.\n\n      if (!prevIsBatchingLegacy && ReactCurrentActQueue.didScheduleLegacyUpdate) {\n        var queue = ReactCurrentActQueue.current;\n\n        if (queue !== null) {\n          ReactCurrentActQueue.didScheduleLegacyUpdate = false;\n          flushActQueue(queue);\n        }\n      }\n    } catch (error) {\n      popActScope(prevActScopeDepth);\n      throw error;\n    } finally {\n      ReactCurrentActQueue.isBatchingLegacy = prevIsBatchingLegacy;\n    }\n\n    if (result !== null && typeof result === 'object' && typeof result.then === 'function') {\n      var thenableResult = result; // The callback is an async function (i.e. returned a promise). Wait\n      // for it to resolve before exiting the current scope.\n\n      var wasAwaited = false;\n      var thenable = {\n        then: function (resolve, reject) {\n          wasAwaited = true;\n          thenableResult.then(function (returnValue) {\n            popActScope(prevActScopeDepth);\n\n            if (actScopeDepth === 0) {\n              // We've exited the outermost act scope. Recursively flush the\n              // queue until there's no remaining work.\n              recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n            } else {\n              resolve(returnValue);\n            }\n          }, function (error) {\n            // The callback threw an error.\n            popActScope(prevActScopeDepth);\n            reject(error);\n          });\n        }\n      };\n\n      {\n        if (!didWarnNoAwaitAct && typeof Promise !== 'undefined') {\n          // eslint-disable-next-line no-undef\n          Promise.resolve().then(function () {}).then(function () {\n            if (!wasAwaited) {\n              didWarnNoAwaitAct = true;\n\n              error('You called act(async () => ...) without await. ' + 'This could lead to unexpected testing behaviour, ' + 'interleaving multiple act calls and mixing their ' + 'scopes. ' + 'You should - await act(async () => ...);');\n            }\n          });\n        }\n      }\n\n      return thenable;\n    } else {\n      var returnValue = result; // The callback is not an async function. Exit the current scope\n      // immediately, without awaiting.\n\n      popActScope(prevActScopeDepth);\n\n      if (actScopeDepth === 0) {\n        // Exiting the outermost act scope. Flush the queue.\n        var _queue = ReactCurrentActQueue.current;\n\n        if (_queue !== null) {\n          flushActQueue(_queue);\n          ReactCurrentActQueue.current = null;\n        } // Return a thenable. If the user awaits it, we'll flush again in\n        // case additional work was scheduled by a microtask.\n\n\n        var _thenable = {\n          then: function (resolve, reject) {\n            // Confirm we haven't re-entered another `act` scope, in case\n            // the user does something weird like await the thenable\n            // multiple times.\n            if (ReactCurrentActQueue.current === null) {\n              // Recursively flush the queue until there's no remaining work.\n              ReactCurrentActQueue.current = [];\n              recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n            } else {\n              resolve(returnValue);\n            }\n          }\n        };\n        return _thenable;\n      } else {\n        // Since we're inside a nested `act` scope, the returned thenable\n        // immediately resolves. The outer scope will flush the queue.\n        var _thenable2 = {\n          then: function (resolve, reject) {\n            resolve(returnValue);\n          }\n        };\n        return _thenable2;\n      }\n    }\n  }\n}\n\nfunction popActScope(prevActScopeDepth) {\n  {\n    if (prevActScopeDepth !== actScopeDepth - 1) {\n      error('You seem to have overlapping act() calls, this is not supported. ' + 'Be sure to await previous act() calls before making a new one. ');\n    }\n\n    actScopeDepth = prevActScopeDepth;\n  }\n}\n\nfunction recursivelyFlushAsyncActWork(returnValue, resolve, reject) {\n  {\n    var queue = ReactCurrentActQueue.current;\n\n    if (queue !== null) {\n      try {\n        flushActQueue(queue);\n        enqueueTask(function () {\n          if (queue.length === 0) {\n            // No additional work was scheduled. Finish.\n            ReactCurrentActQueue.current = null;\n            resolve(returnValue);\n          } else {\n            // Keep flushing work until there's none left.\n            recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n          }\n        });\n      } catch (error) {\n        reject(error);\n      }\n    } else {\n      resolve(returnValue);\n    }\n  }\n}\n\nvar isFlushing = false;\n\nfunction flushActQueue(queue) {\n  {\n    if (!isFlushing) {\n      // Prevent re-entrance.\n      isFlushing = true;\n      var i = 0;\n\n      try {\n        for (; i < queue.length; i++) {\n          var callback = queue[i];\n\n          do {\n            callback = callback(true);\n          } while (callback !== null);\n        }\n\n        queue.length = 0;\n      } catch (error) {\n        // If something throws, leave the remaining callbacks on the queue.\n        queue = queue.slice(i + 1);\n        throw error;\n      } finally {\n        isFlushing = false;\n      }\n    }\n  }\n}\n\nvar createElement$1 =  createElementWithValidation ;\nvar cloneElement$1 =  cloneElementWithValidation ;\nvar createFactory =  createFactoryWithValidation ;\nvar Children = {\n  map: mapChildren,\n  forEach: forEachChildren,\n  count: countChildren,\n  toArray: toArray,\n  only: onlyChild\n};\n\nexports.Children = Children;\nexports.Component = Component;\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.Profiler = REACT_PROFILER_TYPE;\nexports.PureComponent = PureComponent;\nexports.StrictMode = REACT_STRICT_MODE_TYPE;\nexports.Suspense = REACT_SUSPENSE_TYPE;\nexports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = ReactSharedInternals;\nexports.cloneElement = cloneElement$1;\nexports.createContext = createContext;\nexports.createElement = createElement$1;\nexports.createFactory = createFactory;\nexports.createRef = createRef;\nexports.forwardRef = forwardRef;\nexports.isValidElement = isValidElement;\nexports.lazy = lazy;\nexports.memo = memo;\nexports.startTransition = startTransition;\nexports.unstable_act = act;\nexports.useCallback = useCallback;\nexports.useContext = useContext;\nexports.useDebugValue = useDebugValue;\nexports.useDeferredValue = useDeferredValue;\nexports.useEffect = useEffect;\nexports.useId = useId;\nexports.useImperativeHandle = useImperativeHandle;\nexports.useInsertionEffect = useInsertionEffect;\nexports.useLayoutEffect = useLayoutEffect;\nexports.useMemo = useMemo;\nexports.useReducer = useReducer;\nexports.useRef = useRef;\nexports.useState = useState;\nexports.useSyncExternalStore = useSyncExternalStore;\nexports.useTransition = useTransition;\nexports.version = ReactVersion;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react.production.min.js');\n} else {\n  module.exports = require('./cjs/react.development.js');\n}\n", "import toPropertyKey from \"./toPropertyKey.js\";\nexport default function _defineProperty(obj, key, value) {\n  key = toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}", "import _typeof from \"./typeof.js\";\nexport default function _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}", "import _typeof from \"./typeof.js\";\nimport toPrimitive from \"./toPrimitive.js\";\nexport default function _toPropertyKey(arg) {\n  var key = toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}", "export default function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = function(module) {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "import './_acf-jsx-names.js';\nimport './_acf-blocks.js';\n"], "names": ["md5", "require", "$", "undefined", "BlockControls", "InspectorCont<PERSON><PERSON>", "InnerBlocks", "useBlockProps", "AlignmentToolbar", "BlockVerticalAlignmentToolbar", "wp", "blockEditor", "ToolbarGroup", "<PERSON><PERSON>barButton", "Placeholder", "Spinner", "components", "Fragment", "element", "Component", "React", "withSelect", "data", "createHigherOrderComponent", "compose", "BlockAlignmentMatrixToolbar", "__experimentalBlockAlignmentMatrixToolbar", "BlockAlignmentMatrixControl", "__experimentalBlockAlignmentMatrixControl", "BlockFullHeightAlignmentControl", "__experimentalBlockFullHeightAligmentControl", "__experimentalBlockFullHeightAlignmentControl", "useInnerBlocksProps", "__experimentalUseInnerBlocksProps", "blockTypes", "getBlockType", "name", "getBlockVersion", "blockType", "acf_block_version", "isBlockInQueryLoop", "clientId", "parents", "select", "getBlockParents", "parentsData", "getBlocksByClientId", "filter", "block", "length", "isSiteEditor", "pagenow", "isDesktopPreviewDeviceType", "editPostStore", "__experimentalGetPreviewDeviceType", "getPreviewDeviceType", "isEditingTemplate", "isiFramedMobileDevicePreview", "registerBlockType", "allowedTypes", "post_types", "push", "postType", "acf", "get", "includes", "icon", "substr", "iconHTML", "createElement", "Div", "category", "blocks", "getCategories", "slug", "pop", "parseArgs", "title", "api_version", "key", "attributes", "default", "supports", "anchor", "type", "ThisBlockEdit", "BlockEdit", "ThisBlockSave", "BlockSave", "alignText", "align_text", "addBackCompatAttribute", "withAlignTextComponent", "align<PERSON><PERSON><PERSON>", "align_content", "withAlignContentComponent", "fullHeight", "full_height", "withFullHeightComponent", "edit", "props", "_objectSpread", "save", "result", "selector", "dispatch", "getBlocks", "args", "recurseBlocks", "for<PERSON>ach", "k", "ajaxQueue", "fetchCache", "fetchBlock", "context", "query", "delay", "queueId", "JSON", "stringify", "timeout", "promise", "Deferred", "started", "clearTimeout", "setTimeout", "resolve", "apply", "ajax", "url", "dataType", "cache", "prepareForAjax", "action", "always", "done", "arguments", "fail", "reject", "compareObjects", "obj1", "obj2", "parseJSX", "html", "acfBlockVersion", "replace", "parseNode", "children", "node", "level", "nodeName", "parseNodeName", "toLowerCase", "nodeAttrs", "ref", "createRef", "arrayArgs", "map", "parseNodeAttr", "value", "ACFInnerBlocks", "childNodes", "child", "Text", "text", "textContent", "getJSXName", "replacement", "isget", "<PERSON><PERSON><PERSON>", "className", "innerBlockProps", "nodeAttr", "shortcut", "applyFilters", "css", "split", "s", "pos", "indexOf", "ruleName", "trim", "ruleValue", "char<PERSON>t", "strCamelCase", "c1", "parse", "withDefaultAttributes", "BlockListBlock", "WrappedBlockEdit", "constructor", "Object", "keys", "upgrades", "attribute", "render", "hooks", "addFilter", "Content", "setup", "restrictMode", "modes", "mode", "setAttributes", "forcePreview", "showToggle", "toggleText", "__", "toggleIcon", "toggleMode", "label", "onClick", "BlockForm", "BlockBody", "_BlockBody", "isSelected", "showForm", "additionalClasses", "BlockPreview", "ownProps", "rootClientId", "getBlockRootClientId", "index", "getBlockIndex", "dangerouslySetInnerHTML", "__html", "el", "setHTML", "componentDidUpdate", "componentDidMount", "store", "DynamicHTML", "setRef", "bind", "id", "subscribed", "renderMethod", "loadState", "fetch", "maybePreload", "blockId", "form", "state", "preloadedBlocks", "modeText", "replaceAll", "setState", "setHtml", "jsx", "console", "warn", "Array", "isArray", "refElement", "find", "isValidElement", "$el", "shouldComponentUpdate", "componentWillMove", "display", "$prevParent", "parent", "$thisParent", "clone", "componentDidAppend", "componentDidRemount", "prevProps", "prevState", "doAction", "componentWillUnmount", "hash", "createBlockAttributesHash", "preloaded", "serializeData", "silent", "serialize", "on", "contextPostId", "prevAttributes", "prevContext", "preview", "replaceHtml", "renderBlockPreviewEvent", "nextProps", "nextState", "nextAttributes", "thisAttributes", "blockElement", "current", "initialize", "editor", "addAction", "validateVerticalAlignment", "align", "ALIGNMENTS", "DEFAULT", "validateHorizontalAlignment", "validateMatrixAlignment", "y", "x", "OriginalBlockEdit", "AlignmentComponent", "validateAlignment", "onChangeAlignContent", "group", "onChange", "onChangeAlignText", "onToggleFullHeight", "isActive", "onToggle", "new_attribute", "sort", "reduce", "acc", "currValue", "j<PERSON><PERSON><PERSON>", "jsxNameReplacements", "accentheight", "acceptcharse<PERSON>", "accesskey", "alignmentbaseline", "allowedblocks", "allowfullscreen", "allowreorder", "arabicform", "attributename", "attributetype", "autocapitalize", "autocomplete", "autocorrect", "autofocus", "autoplay", "autoreverse", "autosave", "basefrequency", "baselineshift", "baseprofile", "calcmode", "capheight", "cellpadding", "cellspacing", "charset", "class", "classid", "classname", "clippath", "clippathunits", "<PERSON><PERSON><PERSON>", "colorinterpolation", "colorinterpolationfilters", "colorprofile", "colorrendering", "colspan", "contenteditable", "contentscripttype", "contentstyletype", "contextmenu", "controlslist", "crossorigin", "dangerouslysetinnerhtml", "datetime", "defaultchecked", "defaultvalue", "diffuseconstant", "disablepictureinpicture", "disableremoteplayback", "dominantbaseline", "edgemode", "enablebackground", "enctype", "enterkeyhint", "externalresourcesrequired", "fillopacity", "<PERSON><PERSON><PERSON>", "filterres", "filterunits", "floodcolor", "floodopacity", "fontfamily", "fontsize", "fontsizeadjust", "<PERSON><PERSON><PERSON><PERSON>", "fontstyle", "fontvariant", "fontweight", "for", "foreignobject", "formaction", "formenctype", "formmethod", "formnovalidate", "formtarget", "frameborder", "glyphname", "glyphorientationhorizontal", "glyphorientationvertical", "glyphref", "gradienttransform", "gradientunits", "horizadvx", "horizoriginx", "hreflang", "htmlfor", "httpequiv", "imagerendering", "innerhtml", "inputmode", "itemid", "itemprop", "itemref", "itemscope", "itemtype", "kernelmatrix", "kernelunitlength", "keyparams", "keypoints", "keysplines", "keytimes", "keytype", "lengthadjust", "letterspacing", "lightingcolor", "limitingconeangle", "marginheight", "marginwidth", "markerend", "markerheight", "markermid", "markerstart", "markerunits", "markerwidth", "maskcontentunits", "maskunits", "maxlength", "mediagroup", "minlength", "nomodule", "novalidate", "numoctaves", "overlineposition", "overlinethickness", "paintorder", "pathlength", "patterncontentunits", "patterntransform", "patternunits", "playsinline", "pointerevents", "pointsatx", "pointsaty", "pointsatz", "<PERSON><PERSON><PERSON>", "<PERSON>as<PERSON><PERSON>io", "primitiveunits", "radiogroup", "readonly", "referrerpolicy", "refx", "refy", "renderingintent", "repeatcount", "<PERSON>dur", "requiredextensions", "requiredfeatures", "rowspan", "shaperendering", "specularconstant", "specularexponent", "spellcheck", "spreadmethod", "srcdoc", "srclang", "srcset", "startoffset", "stddeviation", "stitchtiles", "stopcolor", "stopopacity", "strikethroughposition", "strikethroughthickness", "<PERSON><PERSON><PERSON><PERSON>", "strokedashoffset", "strokelinecap", "strokelinejoin", "strokemiterlimit", "strokeopacity", "strokewidth", "suppresscontenteditablewarning", "suppresshydrationwarning", "surfacescale", "systemlanguage", "tabindex", "tablevalues", "targetx", "targety", "templatelock", "textan<PERSON>", "textdecoration", "textlength", "textrendering", "underlineposition", "underlinethickness", "unicodebidi", "unicoderange", "unitsperem", "usemap", "valphabetic", "vectoreffect", "vertadvy", "vertoriginx", "vertoriginy", "vhanging", "videographic", "viewbox", "viewtarget", "vmathematical", "wordspacing", "writingmode", "xchannelselector", "xheight", "xlinkactuate", "xlinkarcrole", "xlinkhref", "xlinkrole", "xlinkshow", "xlinktitle", "xlinktype", "xmlbase", "xmllang", "xmlnsxlink", "xmlspace", "ychannelselector", "zoomandpan"], "sourceRoot": ""}