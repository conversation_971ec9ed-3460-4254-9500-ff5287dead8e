@keyframes wpforms-dot-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(223, 119, 57, 0.6);
  }
  100% {
    box-shadow: 0 0 0 10px rgba(223, 119, 57, 0);
  }
}

span.wpforms-admin-form-embed-wizard-dot {
  display: inline-block;
  width: 12px;
  height: 12px !important;
  padding: 0 !important;
  border: 0 !important;
  border-radius: 50%;
  background-color: #df7739;
  animation: wpforms-dot-pulse 1.5s infinite !important;
  margin: 3px 10px;
}

.wp-editor-tools span.wpforms-admin-form-embed-wizard-dot {
  margin-top: 9px;
}

.wpforms-admin-form-embed-wizard-tooltip {
  display: none;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip {
  z-index: 100100 !important;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip.tooltipster-top .tooltipster-box {
  margin-bottom: 18px;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip.tooltipster-top .tooltipster-arrow {
  bottom: 8px;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip.tooltipster-top .tooltipster-arrow-background {
  top: 0;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip.tooltipster-right .tooltipster-box {
  margin-left: 18px;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip.tooltipster-right .tooltipster-arrow {
  left: 8px;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip .tooltipster-box {
  max-width: 260px;
  background: white;
  border: none;
  border-radius: 4px;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);
  -webkit-box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip .tooltipster-box .tooltipster-content {
  color: #444444;
  padding: 16px 20px 18px;
  text-align: center;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip .tooltipster-box .tooltipster-content div *:first-child {
  margin-top: 0 !important;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip .tooltipster-box .tooltipster-content h3 {
  font-size: 16px;
  letter-spacing: 0px;
  line-height: 18px;
  margin: 0;
  color: #23282C;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip .tooltipster-box .tooltipster-content p {
  font-size: 14px;
  letter-spacing: 0px;
  line-height: 18px;
  margin: 10px 0 0;
  color: #444444;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip .tooltipster-box .tooltipster-content a {
  color: #1D7BAC;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip .tooltipster-box .wpforms-admin-form-embed-wizard-done-btn {
  border-radius: 3px;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  box-shadow: none;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0px;
  padding: 7px 18px;
  border: none;
  background-color: #df7739;
  color: #ffffff;
  display: block;
  margin: 15px auto 0;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip .tooltipster-box .wpforms-admin-form-embed-wizard-done-btn:hover {
  background-color: #b85a1b;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip .tooltipster-box .wpforms-admin-form-embed-wizard-done-btn:disabled {
  cursor: default;
  opacity: 0.5;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip .tooltipster-box .wpforms-admin-form-embed-wizard-done-btn:disabled:hover {
  background-color: #df7739;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip .tooltipster-arrow-border {
  border: none;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip.tooltipster-top .tooltipster-arrow-background {
  border-top-color: white;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip.tooltipster-right .tooltipster-arrow-background {
  border-right-color: white;
}

.wpforms-admin-form-embed-wizard.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow-background {
  border-bottom-color: white;
}

.block-editor-page .edit-post-layout .components-notice-list > div {
  padding-left: 50px;
}

.block-editor-page .wpforms-admin-form-embed-wizard-dot {
  position: absolute;
  top: 75px;
  left: 20px;
  z-index: 9999;
}

.block-editor-page .wpforms-admin-form-embed-wizard {
  width: 260px !important;
  z-index: 99980 !important;
  margin-top: 5px;
}

.block-editor-page .wpforms-admin-form-embed-wizard .tooltipster-box {
  margin-top: 10px;
}

.block-editor-page .wpforms-admin-form-embed-wizard .wpforms-admin-form-embed-wizard-tooltips-red-arrow {
  position: absolute;
  display: block;
  width: 15px;
  height: 42px;
  top: -65px;
  left: 145px;
  background-image: url(../images/challenge/red-arrow.svg);
  background-size: 15px 42px;
  background-repeat: no-repeat;
}

.block-editor-page.is-fullscreen-mode .edit-post-layout .components-notice-list > div {
  padding-left: 125px;
}

.block-editor-page.is-fullscreen-mode .wpforms-admin-form-embed-wizard-dot {
  left: 75px;
}

.block-editor-page.is-fullscreen-mode .wpforms-admin-form-embed-wizard .wpforms-admin-form-embed-wizard-tooltips-red-arrow {
  left: 105px;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
