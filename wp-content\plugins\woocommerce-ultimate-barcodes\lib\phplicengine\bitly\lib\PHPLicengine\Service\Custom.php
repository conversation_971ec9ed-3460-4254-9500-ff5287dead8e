<?php

// Custom.php
#################################################
##
## PHPLicengine
##
#################################################
## Copyright 2009-{current_year} PHPLicengine
## 
## Licensed under the Apache License, Version 2.0 (the "License");
## you may not use this file except in compliance with the License.
## You may obtain a copy of the License at
##
##    http://www.apache.org/licenses/LICENSE-2.0
##
## Unless required by applicable law or agreed to in writing, software
## distributed under the License is distributed on an "AS IS" BASIS,
## WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
## See the License for the specific language governing permissions and
## limitations under the License.
#################################################

namespace WPO\WC\Ultimate_Barcodes\Vendor\PHPLicengine\Service;
use WPO\WC\Ultimate_Barcodes\Vendor\PHPLicengine\Exception\ResponseException;
use WPO\WC\Ultimate_Barcodes\Vendor\PHPLicengine\Exception\CurlException;
use WPO\WC\Ultimate_Barcodes\Vendor\PHPLicengine\Api\ApiInterface;

class Custom {
 
       private $url;
       private $api;      
      
       public function __construct(ApiInterface $api)
       {
              $this->api = $api;
              $this->url = 'https://api-ssl.bitly.com/v4/custom_bitlinks';       
       }
 
       /*
      Update Custom Bitlink
      https://dev.bitly.com/api-reference#updateCustomBitlink
      *
      @license Apache-2.0
      Modified using {@see https://github.com/BrianHenryIE/strauss}.
*/
       public function updateCustomBitlink(string $custom_bitlink, array $params)
       {
              return $this->api->patch($this->url.'/'.$custom_bitlink, $params);
       }

       /*
      Retrieve Custom Bitlink
      https://dev.bitly.com/api-reference#getCustomBitlink
      */
       public function getCustomBitlink(string $custom_bitlink)
       {
              return $this->api->get($this->url.'/'.$custom_bitlink);
       }
      
       /*
      Add Custom Bitlink
      https://dev.bitly.com/api-reference#addCustomBitlink
      */
       public function addCustomBitlink(array $params)
       {
              return $this->api->post($this->url, $params);
       }
      
       /*
      Get Metrics for a Custom Bitlink by destination
      https://dev.bitly.com/api-reference#getCustomBitlinkMetricsByDestination
      */
       public function getCustomBitlinkMetricsByDestination(string $custom_bitlink, array $params = array())
       {
              return $this->api->get($this->url.'/'.$custom_bitlink.'/clicks_by_destination', $params);
       }
      
       /*
      Get Clicks for a Custom Bitlink's Entire History
      https://dev.bitly.com/api-reference#getClicksForCustomBitlink
      */
       public function getClicksForCustomBitlink(string $custom_bitlink, array $params = array())
       {
              return $this->api->get($this->url.'/'.$custom_bitlink."/clicks", $params);
       }
 
}
