<?php
/* THIS IS A GENERATED FILE. DO NOT EDIT DIRECTLY. */
$generated_i18n_strings = array(
	// Reference: src/plugins/setup-wizard-helper-plugin.js:106
	__( 'Error Message:', 'wp-mail-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:123
	__( 'OK', 'wp-mail-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:128
	__( 'Heads up!', 'wp-mail-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:129
	__( 'Please fill out all the required fields to continue.', 'wp-mail-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:31
	__( 'Settings Updated', 'wp-mail-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:61
	__( 'Could Not Save Changes', 'wp-mail-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:85
	__( 'Return to Mailer Settings', 'wp-mail-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:90
	__( 'Whoops, we found an issue!', 'wp-mail-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:91
	__( 'It looks like something went wrong...', 'wp-mail-smtp' ),

	// Reference: src/plugins/compatibility-plugin.js:16
	/* Translators: Current PHP version and recommended PHP version. */
	__( 'WP Mail SMTP has detected that your site is running an outdated, insecure version of PHP (%1$s), which could be putting your site at risk for being hacked. WordPress stopped supporting your PHP version in April, 2019. Updating to the recommended version (PHP %2$s) only takes a few minutes and will make your website significantly faster and more secure.', 'wp-mail-smtp' ),

	// Reference: src/plugins/compatibility-plugin.js:18
	__( 'Yikes! PHP Update Required', 'wp-mail-smtp' ),

	// Reference: src/plugins/compatibility-plugin.js:20
	/* Translators: Current PHP version and recommended PHP version. */
	__( 'WP Mail SMTP has detected that your site is running an outdated, insecure version of PHP (%1$s). Some mailers require at least PHP version 5.6. Updating to the recommended version (PHP %2$s) only takes a few minutes and will make your website significantly faster and more secure.', 'wp-mail-smtp' ),

	// Reference: src/plugins/compatibility-plugin.js:22
	__( 'Yikes! WordPress Update Required', 'wp-mail-smtp' ),

	// Reference: src/plugins/compatibility-plugin.js:24
	/* Translators: Current WordPress version. */
	__( 'WP Mail SMTP has detected that your site is running an outdated version of WordPress (%s). WP Mail SMTP requires at least WordPress version 4.9.', 'wp-mail-smtp' ),

	// Reference: src/plugins/compatibility-plugin.js:34
	__( 'Return to Plugin Settings', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/store/actions.js:102
	__( 'It looks like we can\'t load oAuth redirect.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/store/actions.js:11
	__( 'It looks like we can\'t load existing settings.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:265
	// Reference: src/modules/settings/store/actions.js:111
	__( 'It looks like we can\'t load oAuth connected data.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/store/actions.js:132
	__( 'It looks like we can\'t remove oAuth connection.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/store/actions.js:31
	__( 'It looks like we can\'t retrieve the Amazon SES Identities.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:84
	// Reference: src/modules/settings/store/actions.js:40
	__( 'It looks like we can\'t register the Amazon SES Identity.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/api/index.js:16
	__( 'It looks like we can\'t perform the mailer configuration check.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/api/index.js:37
	__( 'It looks like we can\'t send the feedback.', 'wp-mail-smtp' ),

	// Reference: src/modules/plugins/api/index.js:69
	// Reference: src/modules/settings/api/index.js:267
	// Reference: src/modules/setup-wizard/api/index.js:39
	/* Translators: Error status and error text. */
	__( '%1$s, %2$s', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:271
	// Reference: src/modules/setup-wizard/api/index.js:43
	__( 'You appear to be offline.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:113
	__( 'It looks like we can\'t save the settings.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:119
	__( 'Network error encountered. Settings not saved.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:139
	__( 'It looks like we can\'t import the plugin settings.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:145
	__( 'Network error encountered. SMTP plugin import failed!', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:172
	__( 'It looks like we can\'t load authentication details.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:235
	__( 'It looks like we can\'t remove OAuth connection.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:21
	__( 'It looks like we can\'t load the settings.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:51
	__( 'It looks like we can\'t retrieve Amazon SES Identities.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:57
	__( 'Can\'t retrieve Amazon SES Identities.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/api/index.js:90
	__( 'Can\'t register the Amazon SES Identity', 'wp-mail-smtp' ),

	// Reference: src/modules/plugins/api/index.js:25
	__( 'It looks like the plugin installation failed!', 'wp-mail-smtp' ),

	// Reference: src/modules/plugins/api/index.js:35
	__( 'It looks like we can\'t install the plugin.', 'wp-mail-smtp' ),

	// Reference: src/modules/plugins/api/index.js:41
	__( 'You appear to be offline. Plugin not installed.', 'wp-mail-smtp' ),

	// Reference: src/modules/plugins/api/index.js:58
	__( 'Can\'t fetch plugins information.', 'wp-mail-smtp' ),

	// Reference: src/modules/plugins/api/index.js:67
	__( 'It looks like we can\'t fetch plugins information.', 'wp-mail-smtp' ),

	// Reference: src/modules/plugins/api/index.js:73
	__( 'You appear to be offline. Plugin information not retrieved.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/Welcome.vue:12
	__( 'Welcome to the WP Mail SMTP Setup Wizard!', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/Welcome.vue:13
	__( 'We’ll guide you through each step needed to get WP Mail SMTP fully set up on your site.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/Welcome.vue:14
	__( 'Let\'s Get Started', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/Welcome.vue:15
	__( 'Go back to the Dashboard', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:14
	__( 'Which email features do you want to enable?', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:15
	__( 'Make sure you\'re getting the most out of WP Mail SMTP. Just check all of the features you\'d like to use, and we\'ll go ahead and enable those for you.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureMailer.vue:15
	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:16
	__( 'Save and Continue', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureMailer.vue:16
	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:17
	__( 'Previous Step', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:214
	__( 'The following plugin will be installed for free: WPForms', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:18
	__( 'Improved Email Deliverability', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:19
	__( 'Ensure your emails are sent successfully and reliably.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:20
	__( 'Email Error Tracking', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:21
	__( 'Easily spot errors causing delivery issues.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:22
	__( 'Smart Contact Form', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:23
	__( 'Create beautiful contact forms with just a few clicks.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:24
	__( 'Detailed Email Logs', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:25
	__( 'Keep records of every email that\'s sent out from your website.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:26
	__( 'Instant Email Alerts', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:27
	__( 'Get notifications via email, SMS, Slack, or webhook when emails fail to send.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:28
	__( 'Complete Email Reports', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:29
	__( 'See the delivery status, track opens and clicks, and create deliverability graphs.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:30
	__( 'Weekly Email Summary', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:31
	__( 'Get statistics about emails you\'ve sent.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:32
	__( 'Manage Default Notifications', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:33
	__( 'Control which email notifications your WordPress site sends.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:34
	__( 'Multisite Network Settings', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:35
	__( 'Save time with powerful WordPress Multisite controls.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:14
	__( 'Private API Key', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:15
	__( 'Domain Name', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SparkPost.vue:15
	__( 'Region', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:18
	/* Translators: Link to the Mailgun API settings. */
	__( '%1$sFollow this link%2$s to get a Private API Key from Mailgun.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:20
	/* Translators: Link to the Mailgun Domain settings. */
	__( '%1$sFollow this link%2$s to get a Domain Name from Mailgun.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:22
	/* Translators: Link to the Mailgun documentation. */
	__( 'Define which endpoint you want to use for sending messages. If you are operating under EU laws, you may be required to use EU region. %1$sMore information%2$s on Mailgun.com.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:16
	__( 'From Name', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:17
	__( 'Force From Name', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:23
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:18
	__( 'From Email', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:25
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:19
	__( 'Force From Email', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:20
	__( 'If enabled, the From Name setting above will be used for all emails, ignoring values set by other plugins.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:26
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:21
	__( 'If enabled, the From Email setting above will be used for all emails, ignoring values set by other plugins.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:22
	__( 'The name that emails are sent from.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:24
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:23
	__( 'The email address that emails are sent from.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:31
	__( 'Read how to set up Mailgun', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SparkPost.vue:31
	__( 'US', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SparkPost.vue:35
	__( 'EU', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/Steps.vue:12
	__( 'Close and exit the Setup Wizard', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:13
	__( 'API Key', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendgrid.vue:14
	__( 'Sending Domain', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendgrid.vue:16
	/* Translators: Link to the Sendgrid API settings. */
	__( '%1$sFollow this link%2$s to get an API Key for Sendgrid.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendgrid.vue:19
	/* Translators: italic styling. */
	__( 'To send emails you will need only a %1$sMail Send%2$s access level for this API key.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendgrid.vue:21
	/* Translators: Link to the Sendgrid doc page on wpmailsmtp.com. */
	__( 'Please input the sending domain/subdomain you configured in your SendGrid dashboard. More information can be found in our %1$sSendGrid documentation%2$s', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendgrid.vue:30
	__( 'Read how to set up Sendgrid', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/TheWizardHeader.vue:8
	__( 'WP Mail SMTP logo', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/TheWizardStepCounter.vue:11
	/* Translators: %1$s - the number of current step, %2$s - number of all steps. */
	__( 'Step %1$s of %2$s', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureMailer.vue:13
	__( 'Configure Mailer Settings', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureMailer.vue:14
	__( 'Below, we\'ll show you all of the settings required to set up this mailer.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputLongCheckbox.vue:20
	// Reference: src/modules/settings/components/input/SettingsInputNumber.vue:64
	// Reference: src/modules/settings/components/input/SettingsInputText.vue:69
	__( 'This setting is already configured with the WP Mail SMTP constant. To change it, please edit or remove the <code></code> constant in your <code>wp-config.php</code> file.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputText.vue:36
	__( 'Copy input value', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputText.vue:37
	__( 'Copied!', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputText.vue:63
	__( 'The value entered does not match the required format', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepCheckConfiguration.vue:10
	__( 'Checking Mailer Configuration', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepCheckConfiguration.vue:11
	__( 'We\'re running some tests in the background to make sure everything is set up properly.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepCheckConfiguration.vue:12
	__( 'Checking mailer configuration image', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationFailure.vue:11
	__( 'Whoops, looks like things aren’t configured properly.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationFailure.vue:12
	__( 'We just tried to send a test email, but something prevented that from working. To see more details about the issue we detected, as well as our suggestions to fix it, please start troubleshooting.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationFailure.vue:13
	__( 'Start Troubleshooting', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:23
	__( 'Send us Feedback', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:24
	__( 'Finish Setup', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepImport.vue:12
	__( 'Import data from your current plugins', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepImport.vue:13
	__( 'We have detected other SMTP plugins installed on your website. Select which plugin\'s data you would like to import to WP Mail SMTP.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepImport.vue:14
	__( 'Import Data and Continue', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:17
	__( 'Skip this Step', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepImport.vue:20
	__( 'Easy WP SMTP', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepImport.vue:27
	__( 'FluentSMTP', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepImport.vue:34
	__( 'Post SMTP Mailer', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepImport.vue:41
	__( 'SMTP Mailer', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepImport.vue:48
	__( 'WP SMTP', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:14
	__( 'Help Improve WP Mail SMTP + Smart Recommendations', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:15
	__( 'Get helpful suggestions from WP Mail SMTP on how to optimize your email deliverability and grow your business.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:19
	__( 'Your Email Address', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:20
	__( 'Your email is needed, so you can receive recommendations.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:21
	__( 'Help make WP Mail SMTP better for everyone', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:22
	__( 'Yes, count me in', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:23
	__( 'By allowing us to track usage data we can better help you because we know with which WordPress configurations, themes and plugins we should test.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Postmark.vue:13
	__( 'Server API Token', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Postmark.vue:14
	__( 'Message Stream ID', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Postmark.vue:16
	/* Translators: Link to the Postmark API settings. */
	__( '%1$sFollow this link%2$s to get a Server API Token for Postmark.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Postmark.vue:18
	/* Translators: Link to the Postmark Message Stream ID settings. */
	__( 'Message Stream ID is <strong>optional</strong>. By default <strong>outbound</strong> (Default Transactional Stream) will be used. More information can be found in our %1$sPostmark documentation%2$s.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Postmark.vue:27
	__( 'Read how to set up Postmark', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:13
	__( 'Configure Email Logs', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:14
	__( 'Enable these powerful logging features for more control of your WordPress emails.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:17
	__( 'Store the content for all sent emails', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:18
	__( 'This option must be enabled if you\'d like to be able to resend emails. Please be aware that all email content will be stored in your WordPress database. This may include sensitive data, passwords, and personal details.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:19
	__( 'Save file attachments sent from WordPress', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:20
	__( 'All file attachments sent from your site will be saved to the WordPress Uploads folder. Please note that this may reduce available disk space on your server.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:21
	__( 'Track when an email is opened', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:22
	__( 'See which emails were opened by the recipients. Email open tracking works with emails that are sent in HTML format.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:23
	__( 'Track when a link in an email is clicked', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:24
	__( 'See which links were clicked in emails sent from your WordPress site. Click tracking works with emails that are sent in HTML format.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Zoho.vue:17
	__( 'The data center location used by your Zoho account.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:17
	__( 'Client ID', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:18
	__( 'Client Secret', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:17
	__( 'Redirect URI', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:18
	__( 'Authorization', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Zoho.vue:26
	__( 'Read how to set up Zoho Mail', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendlayer.vue:15
	/* Translators: Link to the SendLayer API settings. */
	__( '%1$sFollow this link%2$s to get an API Key for SendLayer.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendlayer.vue:24
	__( 'Get Started with SendLayer', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendlayer.vue:25
	__( 'Read how to set up SendLayer', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:14
	__( 'Sender Name', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:16
	/* Translators: Link to the SMTP.com API settings. */
	__( '%1$sFollow this link%2$s to get an API Key for SMTP.com.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:18
	/* Translators: Link to the SMTP.com Senders/Channel settings. */
	__( '%1$sFollow this link%2$s to get a Sender Name for SMTP.com.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:27
	__( 'Get Started with SMTP.com', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:28
	__( 'Read how to set up SMTP.com', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:29
	__( 'Transparency and Disclosure', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:31
	__( 'We believe in full transparency. The SMTP.com links above are tracking links as part of our partnership with SMTP (j2 Global). We can recommend just about any SMTP service, but we only recommend products that we believe will add value to our users.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:100
	__( 'How was your WP Mail SMTP setup experience?', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:101
	__( 'Our goal is to make your SMTP setup as simple and straightforward as possible. We\'d love to know how this process went for you!', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:13
	__( 'Congrats, you’ve successfully set up WP Mail SMTP!', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:14
	__( 'Here’s what to do next:', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:15
	__( 'Check out our other free WordPress plugins:', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:16
	__( 'Upgrade to Unlock Powerful SMTP Features', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:17
	__( 'Upgrade to WP Mail SMTP Pro to unlock more awesome features and experience why WP Mail SMTP is used by over 4,000,000 websites.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:21
	__( 'Upgrade to Pro Today', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:22
	__( 'Send a Test Email', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:186
	/* Translators: Different bold styles and discount value (%5$s). */
	__( '%1$sBonus:%2$s You can upgrade to the Pro plan and %3$ssave %5$s today%4$s, automatically applied at checkout.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:27
	__( 'Star icon', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:42
	__( 'Thanks for the feedback!', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:45
	/* Translators: %1$s and %2$s are HTML bold tags; %3$s is a new line HTML tag; %4$s are 5 golden star icons in HTML. */
	__( 'Help us spread the word %1$sby giving WP Mail SMTP a 5-star rating %3$s(%4$s) on WordPress.org%2$s. Thanks for your support and we look forward to bringing you more awesome features.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:50
	__( 'Rate on WordPress.org', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:63
	__( 'What could we do to improve?', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:64
	__( 'We\'re sorry things didn\'t go smoothly for you, and want to keep improving. Please let us know any specific parts of this process that you think could be better. We really appreciate any details you\'re willing to share!', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:68
	__( 'Yes, I give WP Mail SMTP permission to contact me for any follow up questions.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:76
	__( 'Submit Feedback', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:16
	/* Translators: Link to the Sendinblue API settings. */
	__( '%1$sFollow this link%2$s to get an API Key for Sendinblue.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:18
	/* Translators: Link to the Sendinblue doc page on wpmailsmtp.com. */
	__( 'Please input the sending domain/subdomain you configured in your Sendinblue dashboard. More information can be found in our %1$sSendinblue documentation%2$s', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:27
	__( 'Get Started with Sendinblue', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:28
	__( 'Read how to set up Sendinblue', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:31
	__( 'We believe in full transparency. The Sendinblue links above are tracking links as part of our partnership with Sendinblue. We can recommend just about any SMTP service, but we only recommend products that we believe will add value to our users.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:15
	__( 'SMTP Host', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:16
	__( 'Encryption', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:17
	__( 'SMTP Port', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:18
	__( 'Auto TLS', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:19
	__( 'Enable Auto TLS', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:20
	__( 'By default, TLS encryption is automatically used if the server supports it (recommended). In some cases, due to server misconfigurations, this can cause issues and may need to be disabled.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:21
	__( 'Authentication', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:22
	__( 'Enable Authentication', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:23
	__( 'SMTP Username', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:24
	__( 'SMTP Password', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:31
	__( 'For most servers TLS is the recommended option. If your SMTP provider offers both SSL and TLS options, we recommend using TLS.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:37
	__( 'None', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:42
	__( 'SSL', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:47
	__( 'TLS', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:16
	__( 'Access Key ID', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:17
	__( 'Secret Access Key', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:19
	__( 'SES Identities', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:20
	__( 'Please select the Amazon SES API region which is the closest to where your website is hosted. This can help to decrease network latency between your site and Amazon SES, which will speed up email sending.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:29
	__( 'Read how to set up Amazon SES', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:30
	__( 'Amazon SES requires an SSL certificate, and so is not currently compatible with your site. Please contact your host to request a SSL certificate, or check out ', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:21
	__( 'WPBeginner\'s tutorial on how to set up SSL', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:22
	__( 'If you\'d prefer not to set up SSL, or need an SMTP solution in the meantime, please go back and select a different mailer option.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:13
	__( 'Choose Your SMTP Mailer', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:154
	__( 'Mailer', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:15
	/* Translators: Link to the SMTP Mailer docs page. */
	__( 'Which mailer would you like to use to send emails? Not sure which mailer to choose? Check out our %1$scomplete mailer guide%2$s for details on each option.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:164
	__( 'I Understand, Continue', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:165
	__( 'Choose a Different Mailer', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:18
	__( 'Recommended Mailers', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:19
	__( 'Your mailer is already configured in a WP Mail SMTP constant, so the options below have been disabled. To change your mailer, please edit or remove the <code>WPMS_MAILER</code> constant in your <code>wp-config.php</code> file.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:209
	__( 'is a PRO Feature', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:78
	__( 'Microsoft 365 / Outlook', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:105
	__( 'Successful Upgrade!', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:105
	__( 'Upgrade Failed!', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:13
	__( 'Enter your WP Mail SMTP License Key', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:15
	/* Translators: %1$s and %2$s are bold tags. */
	__( 'You\'re currently using %1$sWP Mail SMTP Lite%2$s - no license needed. Enjoy!', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:16
	__( 'Continue', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:181
	__( 'Would you like to purchase the following features now?', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:182
	__( 'These features are available as part of WP Mail SMTP Pro plan.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:197
	__( 'Purchase Now', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:198
	__( 'I\'ll do it later', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:20
	/* Translators: Link to the WPMailSMTP.com pricing page. */
	__( 'To unlock selected features, %1$sUpgrade to Pro%2$s and enter your license key below.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:24
	__( 'Enhanced Weekly Email Summary', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:26
	/* Translators: bold HTML tags. */
	__( 'Already purchased? Enter your license key below to connect with %1$sWP Mail SMTP Pro%2$s!', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:27
	__( 'Enter your license key below to unlock plugin updates!', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:28
	__( 'Verify License Key', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:28
	__( 'Connect', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:29
	__( 'The License Key format is incorrect. Please enter a valid key and try again.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:30
	__( 'Your license was successfully verified! You are ready for the next step.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:38
	__( 'Pro badge', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:39
	__( 'License key input', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:40
	__( 'Paste your license key here', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:76
	__( 'Successful Verification!', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:86
	__( 'Verification Error!', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:19
	__( 'Authorized Redirect URI', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:29
	__( 'Select which email address you would like to send your emails from.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:29
	__( 'Read how to set up the Gmail mailer', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SparkPost.vue:17
	/* Translators: Link to the SparkPost documentation. */
	__( 'Select your SparkPost account region. %1$sMore information%2$s on SparkPost.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SparkPost.vue:26
	__( 'Read how to set up SparkPost', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SparkPost.vue:55
	/* Translators: Link to the SparkPost Account API section. */
	__( '%1$sFollow this link%2$s to get an API Key for SparkPost.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:15
	__( 'Application ID', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:16
	__( 'Application Password', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:19
	__( 'Read how to set up Microsoft Outlook / 365', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:20
	__( 'Outlook / 365 requires an SSL certificate, and so is not currently compatible with your site. Please contact your host to request a SSL certificate, or check out ', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputNumber.vue:38
	/* Translators: Minimum and maximum number that can be used. */
	__( 'Please enter a value between %1$s and %2$s', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputNumber.vue:39
	__( 'Value has to be a round number', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:114
	__( 'There was an error while processing the authentication request. Please try again.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:120
	__( 'There was an error while processing the authentication request. Please recheck your Client ID and Client Secret and try again.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:128
	__( 'There was an error while processing the authentication request. The nonce is invalid. Please try again.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:132
	__( 'There was an error while processing the authentication request. The authorization code is missing. Please try again.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:135
	__( 'There was an error while processing the authentication request. Please recheck your Region, Client ID and Client Secret and try again.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:138
	__( 'You have successfully linked the current site with your Google API project. Now you can start sending emails through Gmail.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:144
	__( 'You have successfully linked the current site with your Microsoft API project. Now you can start sending emails through Outlook.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:147
	__( 'You have successfully linked the current site with your Zoho Mail API project. Now you can start sending emails through Zoho Mail.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:153
	__( 'Successful Authorization', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:153
	__( 'Authorization Error!', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:23
	/* Translators: name of the oAuth provider (Google, Microsoft, ...). */
	__( 'Connect to %s', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:25
	__( 'Before continuing, you\'ll need to allow this plugin to send emails using your %s account.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:26
	__( 'Remove OAuth Connection', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:28
	/* Translators: link to the Google documentation page. */
	__( 'If you want to use a different From Email address you can setup a Google email alias. %1$sFollow these instructions%2$s, then select the alias in the From Email section below.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:30
	/* Translators: name of the oAuth provider (Google, Microsoft, ...). */
	__( 'Removing this OAuth connection will give you the ability to redo the OAuth connection or connect to different %s account.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:31
	__( 'Connected as', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:109
	__( 'There was an error while processing the authentication request. The state key is invalid. Please try again.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:23
	__( 'Please enter a domain', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:24
	__( 'Please enter a valid email address', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:28
	__( 'Enter the domain name to verify it on Amazon SES and generate the required DNS CNAME records.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:29
	__( 'Enter a valid email address. A verification email will be sent to the email address you entered.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:33
	/* Translators: Email address. */
	__( 'Please check the inbox of <b>%s</b> for a confirmation email.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:36
	__( 'Verify Email', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:41
	__( 'No registered domains or emails.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:42
	__( 'You will not be able to send emails until you verify at least one domain or email address for the selected Amazon SES Region.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:43
	__( 'View DNS', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:44
	__( 'Resend', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:45
	__( 'Here are the domains and email addresses that have been verified and can be used as the From Email.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:46
	__( 'Verify SES Identity', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:47
	__( 'Add New SES Identity', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:48
	__( 'Name', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:49
	__( 'Value', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:51
	/* Translators: Link to Amazon SES documentation. */
	__( 'Please add these CNAME records to your domain\'s DNS settings. For information on how to add CNAME DNS records, please refer to the %1$sAmazon SES documentation%2$s.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:56
	__( 'Verify Domain', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:60
	__( 'Verify Email Address', 'wp-mail-smtp' ),

	// Reference: src/modules/plugins/components/PluginItem.vue:23
	__( 'Install', 'wp-mail-smtp' ),

	// Reference: src/modules/plugins/components/PluginItem.vue:26
	__( 'Installed', 'wp-mail-smtp' ),

	// Reference: src/modules/plugins/components/PluginItem.vue:30
	__( 'Activated', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/misc/SpinLoader.vue:19
	__( 'Loading', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:24
	__( 'WordPress SEO Toolkit', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:25
	__( 'Improve your website\'s SEO ranking with our toolkit.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:187
	__( 'The following plugin will be installed for free:', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendgrid.vue:30
	__( 'Read how to set up SendGrid', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:16
	/* Translators: Link to the Sendinblue API settings. */
	__( '%1$sFollow this link%2$s to get an API Key for Brevo.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:18
	/* Translators: Link to the Sendinblue doc page on wpmailsmtp.com. */
	__( 'Please input the sending domain/subdomain you configured in your Brevo dashboard. More information can be found in our %1$sBrevo documentation%2$s', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:27
	__( 'Get Started with Brevo', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:28
	__( 'Read how to set up Brevo', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:31
	__( 'We believe in full transparency. The Brevo links above are tracking links as part of our partnership with Brevo. We can recommend just about any SMTP service, but we only recommend products that we believe will add value to our users.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:14
	__( 'Mailgun API Key', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:18
	/* Translators: Link to the Mailgun API settings. */
	__( '%1$sFollow this link%2$s to get a Mailgun API Key. Generate a key in the "Mailgun API Keys" section.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:25
	__( 'Improve your SEO rankings with the All in One SEO plugin.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:15
	__( 'One-Click Setup', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:16
	__( 'Provides a quick and easy way to connect to Google that doesn\'t require creating your own app.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:30
	__( 'Enabled', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:31
	__( 'Disabled', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:24
	__( 'Sign in with Google', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:123
	__( 'There was an error while processing the authentication request.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:142
	__( 'Now you can continue mailer configuration.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:40
	__( 'Gmail mailer requires a valid Easy WP SMTP Pro license. Please activate your license key.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:40
	__( 'One-Click Setup for Google Mailer requires an active license. Emails are currently not being sent.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:40
	__( 'One-Click Setup for Google Mailer requires an active license. Verify your license to proceed with this one-click setup.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:40
	__( 'One-Click Setup for Google Mailer requires an active license. Verify your license to proceed with this one-click setup, please.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:41
	__( 'One-Click Setup for Google Mailer requires an active license. Verify your license to proceed with this One-Click Setup, please.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:147
	__( 'You have successfully connected your site with your Gmail account. This site will now send emails via your Gmail account.', 'wp-mail-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:141
	__( 'You have successfully connected your site with your Gmail account. Now you can start sending emails through Gmail.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:28
	__( 'The email address that emails are sent from. You can use only connected email address or ', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:28
	__( 'The email address that emails are sent from. You can use only connected email address or his alias.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:28
	__( 'The email address that emails are sent from. The email address that emails are sent from. You can use only the connected email address or its alias.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:28
	__( 'The email address that emails are sent from. You can use only the connected email address or its alias.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:32
	__( 'One-Click Setup for Google Mailer <br> is a Pro Feature', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:33
	__( 'We\'re sorry, One-Click Setup for Google Mailer is not available on your plan. Please upgrade to the Pro plan to unlock all these awesome features.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:23
	__( 'Install the WPForms plugin and create beautiful contact forms with just a few clicks.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailjet.vue:16
	__( 'Secret Key', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailjet.vue:17
	__( 'Follow this link to get an API key and Secret key from Mailjet: %1$sAPI Key Management%2$s.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailjet.vue:26
	__( 'Read how to set up Mailjet', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:15
	/* Translators: Link to the SMTP2GO API settings. */
	__( 'Generate an API key on the Sending → API Keys page in your %1$scontrol panel%2$s.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:24
	__( 'Read how to set up SMTP2GO', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailjet.vue:15
	/* Translators: Link to the SMTP2GO API settings. */
	__( 'Follow this link to get the API key from Mailjet: %1$sAPI Key Management%2$s.', 'wp-mail-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailjet.vue:17
	__( 'Follow this link to get the Secret key from Mailjet: %1$sAPI Key Management%2$s.', 'wp-mail-smtp' )
);
/* THIS IS THE END OF THE GENERATED FILE */
