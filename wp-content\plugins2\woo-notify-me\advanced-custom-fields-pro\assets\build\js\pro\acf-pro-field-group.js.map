{"version": 3, "file": "acf-pro-field-group.js", "mappings": ";;;;;;;;;AAAA,CAAE,UAAWA,CAAC,EAAG;EAChB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIC,wBAAwB,GAAGC,GAAG,CAACC,YAAY,CAACC,MAAM,CAAE;IACvDC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAIC,OAAO,GAAG,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC,CAAC;;MAE9B;MACA,IAAI,CAACC,YAAY,CAACC,IAAI,CAAE,cAAc,EAAEJ,OAAQ,CAAC;IAClD;EACD,CAAE,CAAC;EAEHN,GAAG,CAACW,oBAAoB,CAAEZ,wBAAyB,CAAC;;EAEpD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIa,4BAA4B,GAAGZ,GAAG,CAACC,YAAY,CAACC,MAAM,CAAE;IAC3DC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAIQ,MAAM,GAAG,EAAE;;MAEf;MACA,IAAK,IAAI,CAACN,KAAK,CAACC,GAAG,CAAC,CAAC,EAAG;QACvBK,MAAM,GAAG,IAAI,CAACC,WAAW,CAACC,IAAI,CAAE,OAAQ,CAAC,GAAG,GAAG;MAChD;;MAEA;MACA,IAAI,CAACjB,CAAC,CAAE,MAAO,CAAC,CAACkB,IAAI,CAAEH,MAAM,GAAG,eAAgB,CAAC;IAClD;EACD,CAAE,CAAC;EAEHb,GAAG,CAACW,oBAAoB,CAAEC,4BAA6B,CAAC;;EAExD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIK,2BAA2B,GAAGjB,GAAG,CAACC,YAAY,CAACC,MAAM,CAAE;IAC1DC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,aAAa;IACnBC,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAIQ,MAAM,GAAG,EAAE;;MAEf;MACA,IAAK,IAAI,CAACN,KAAK,CAACC,GAAG,CAAC,CAAC,EAAG;QACvBK,MAAM,GAAG,IAAI,CAACC,WAAW,CAACC,IAAI,CAAE,MAAO,CAAC,GAAG,GAAG;MAC/C;;MAEA;MACA,IAAI,CAACjB,CAAC,CAAE,MAAO,CAAC,CAACkB,IAAI,CAAEH,MAAM,GAAG,cAAe,CAAC;IACjD;EACD,CAAE,CAAC;EAEHb,GAAG,CAACW,oBAAoB,CAAEM,2BAA4B,CAAC;;EAEvD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIC,sBAAsB,GAAG,IAAIlB,GAAG,CAACmB,KAAK,CAAE;IAC3CC,OAAO,EAAE;MACRC,YAAY,EAAE;IACf,CAAC;IAEDC,WAAW,EAAE,SAAAA,CAAWC,OAAO,EAAEC,OAAO,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAG;MAC/D;MACA,IAAKF,IAAI,CAACG,UAAU,IAAI,wBAAwB,EAAG;QAClD;QACAL,OAAO,CAACM,aAAa,GAAG,KAAK;;QAE7B;QACAF,QAAQ,CAACF,IAAI,CAACK,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACvC;;MAEA;MACA,OAAOP,OAAO;IACf,CAAC;IAEDO,QAAQ,EAAE,SAAAA,CAAWL,IAAI,EAAG;MAC3B;MACAA,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC;;MAEhB;MACA/B,GAAG,CAACgC,eAAe,CAAC,CAAC,CAACC,GAAG,CAAE,UAAWnB,WAAW,EAAG;QACnD;QACAW,IAAI,CAACM,MAAM,CAAEjB,WAAW,CAACC,IAAI,CAAE,KAAM,CAAC,CAAE,GAAG;UAC1CmB,GAAG,EAAEpB,WAAW,CAACC,IAAI,CAAE,KAAM,CAAC;UAC9BZ,IAAI,EAAEW,WAAW,CAACC,IAAI,CAAE,MAAO,CAAC;UAChCoB,KAAK,EAAErB,WAAW,CAACC,IAAI,CAAE,OAAQ,CAAC;UAClCqB,SAAS,EAAEtB,WAAW,CAACuB,UAAU,CAAC,CAAC,CAACC;QACrC,CAAC;MACF,CAAE,CAAC;;MAEH;MACAb,IAAI,CAACc,KAAK,GAAGzC,CAAC,CAAE,QAAS,CAAC,CAACU,GAAG,CAAC,CAAC;;MAEhC;MACA,OAAOiB,IAAI;IACZ;EACD,CAAE,CAAC;AACJ,CAAC,EAAIe,MAAO,CAAC;;;;;;;;;;AC9Ib,CAAE,UAAW1C,CAAC,EAAG;EAChB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAI2C,iCAAiC,GAAGzC,GAAG,CAACC,YAAY,CAACC,MAAM,CAAE;IAChEC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,WAAW;IAEjBsC,MAAM,EAAE;MACP,oBAAoB,EAAE,eAAe;MACrC,mBAAmB,EAAE,YAAY;MACjC,mCAAmC,EAAE,aAAa;MAClD,uCAAuC,EAAE,kBAAkB;MAC3D,oCAAoC,EAAE,eAAe;MACrD,qBAAqB,EAAE;IACxB,CAAC;IAEDC,MAAM,EAAE,SAAAA,CAAWvC,IAAI,EAAG;MACzB,OAAON,CAAC,CAAE,GAAG,GAAG,IAAI,CAAC8C,UAAU,CAAC,CAAC,GAAG,GAAG,GAAGxC,IAAK,CAAC;IACjD,CAAC;IAEDyC,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,OAAO,IAAI,CAAC/C,CAAC,CAAE,uBAAwB,CAAC;IACzC,CAAC;IAED8C,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,OACC,IAAI,CAAC9B,WAAW,CAAC8B,UAAU,CAAC,CAAC,GAC7B,WAAW,GACX,IAAI,CAACrC,KAAK,CAACuC,GAAG,CAAE,IAAK,CAAC;IAExB,CAAC;IAED;IACAC,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAO/C,GAAG,CAACgC,eAAe,CAAE;QAAEgB,MAAM,EAAE,IAAI,CAACtB;MAAI,CAAE,CAAC;IACnD,CAAC;IAED;IACAuB,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB,OAAOjD,GAAG,CAACgC,eAAe,CAAE;QAAEkB,IAAI,EAAE,IAAI,CAACL,KAAK,CAAC;MAAE,CAAE,CAAC;IACrD,CAAC;IAEDM,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB;MACA,IAAIC,MAAM,GAAG,IAAI,CAAC1B,GAAG,CAACsB,MAAM,CAAC,CAAC;MAC9B,IAAK,CAAEI,MAAM,CAACC,QAAQ,CAAE,aAAc,CAAC,EAAG;QACzCD,MAAM,CAACE,QAAQ,CAAE;UAChBC,KAAK,EAAE,gCAAgC;UACvCC,MAAM,EAAE,mBAAmB;UAC3BC,eAAe,EAAE,IAAI;UACrBC,oBAAoB,EAAE,IAAI;UAC1BC,MAAM,EAAE,IAAI;UACZC,IAAI,EAAE,IAAI,CAACC,KAAK,CAAE,UAAWC,KAAK,EAAEC,EAAE,EAAG;YACxC,IAAI,CAACjD,WAAW,CAACkD,IAAI,CAAC,CAAC;UACxB,CAAE;QACH,CAAE,CAAC;MACJ;;MAEA;MACA,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC1B,CAAC;IAEDD,kBAAkB,EAAE,SAAAA,CAAA,EAAY;MAC/B,IAAI,CAAChB,WAAW,CAAC,CAAC,CAAChB,GAAG,CAAE,IAAI,CAACkC,iBAAiB,EAAE,IAAK,CAAC;IACvD,CAAC;IAEDA,iBAAiB,EAAE,SAAAA,CAAW5D,KAAK,EAAG;MACrCA,KAAK,CAACQ,IAAI,CAAE,eAAe,EAAE,IAAI,CAAC+B,GAAG,CAAE,IAAK,CAAE,CAAC;IAChD,CAAC;IAEDoB,kBAAkB,EAAE,SAAAA,CAAA,EAAY;MAC/B,MAAM/B,KAAK,GAAG,IAAI,CAACW,GAAG,CAAE,aAAc,CAAC;MAEvC,MAAMsB,WAAW,GAAG,IAAI,CAAC1C,GAAG,CAAC2C,IAAI,CAChC,kCACD,CAAC;MAED,IAAKlC,KAAK,EAAG;QACZiC,WAAW,CAACpD,IAAI,CAAEmB,KAAM,CAAC;MAC1B;IACD,CAAC;IAEDmC,WAAW,EAAE,SAAAA,CAAWC,CAAC,EAAG;MAC3B,MAAMC,OAAO,GAAG1E,CAAC,CAAEyE,CAAC,CAACE,MAAO,CAAC;MAE7B,IACCD,OAAO,CAACnB,QAAQ,CAAE,SAAU,CAAC,IAC7BmB,OAAO,CAACxB,MAAM,CAAC,CAAC,CAACK,QAAQ,CAAE,SAAU,CAAC,EACrC;QACD;MACD;MAEA,IAAI,CAACqB,MAAM,CAAC,CAAC,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC,GAAG,IAAI,CAACC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAEDF,MAAM,EAAE,SAAAA,CAAWH,CAAC,EAAG;MACtB,MAAMM,SAAS,GAAG,IAAI,CAACnD,GAAG,CAACoD,QAAQ,CAAE,4BAA6B,CAAC;MACnE,OAAOD,SAAS,CAACxB,QAAQ,CAAE,MAAO,CAAC;IACpC,CAAC;IAEDuB,IAAI,EAAE,SAAAA,CAAWG,OAAO,EAAEC,cAAc,EAAG;MAC1C,MAAMH,SAAS,GAAGE,OAAO,GACtBA,OAAO,CAACD,QAAQ,CAAE,4BAA6B,CAAC,GAChD,IAAI,CAACpD,GAAG,CAACoD,QAAQ,CAAE,4BAA6B,CAAC;MACpD,MAAMG,MAAM,GAAGF,OAAO,GACnBA,OAAO,CAACV,IAAI,CAAE,mBAAoB,CAAC,CAACa,KAAK,CAAC,CAAC,GAC3C,IAAI,CAACxD,GAAG,CAAC2C,IAAI,CAAE,mBAAoB,CAAC,CAACa,KAAK,CAAC,CAAC;;MAE/C;MACAlF,GAAG,CAACmF,QAAQ,CAAE,MAAM,EAAEN,SAAU,CAAC;;MAEjC;MACA,IAAKG,cAAc,EAAG;QACrBH,SAAS,CAACO,SAAS,CAAE;UACpBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;YACrBR,SAAS,CAACR,IAAI,CAAE,eAAgB,CAAC,CAACiB,OAAO,CAAE,OAAQ,CAAC;UACrD;QACD,CAAE,CAAC;MACJ,CAAC,MAAM;QACNT,SAAS,CAACO,SAAS,CAAC,CAAC;MACtB;MACAH,MAAM,CAACM,QAAQ,CAAE,MAAO,CAAC;MACzB,IAAKN,MAAM,CAAC5B,QAAQ,CAAE,QAAS,CAAC,EAAG;QAClC4B,MAAM,CAACO,WAAW,CAAE,QAAS,CAAC;MAC/B;MACAX,SAAS,CAACU,QAAQ,CAAE,MAAO,CAAC;IAC7B,CAAC;IAEDZ,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,MAAME,SAAS,GAAG,IAAI,CAACnD,GAAG,CAACoD,QAAQ,CAAE,4BAA6B,CAAC;MACnE,MAAMG,MAAM,GAAG,IAAI,CAACvD,GAAG,CAAC2C,IAAI,CAAE,mBAAoB,CAAC,CAACa,KAAK,CAAC,CAAC;;MAE3D;MACAL,SAAS,CAACY,OAAO,CAAC,CAAC;MACnBZ,SAAS,CAACW,WAAW,CAAE,MAAO,CAAC;MAC/BP,MAAM,CAACO,WAAW,CAAE,MAAO,CAAC;MAC5B,IAAK,CAAEP,MAAM,CAAC5B,QAAQ,CAAE,QAAS,CAAC,EAAG;QACpC4B,MAAM,CAACM,QAAQ,CAAE,QAAS,CAAC;MAC5B;;MAEA;MACAvF,GAAG,CAACmF,QAAQ,CAAE,MAAM,EAAEN,SAAU,CAAC;IAClC,CAAC;IAEDa,aAAa,EAAE,SAAAA,CAAWnB,CAAC,EAAE7C,GAAG,EAAG;MAClC,IAAIS,KAAK,GAAGT,GAAG,CAAClB,GAAG,CAAC,CAAC;MAErB,IAAI,CAACmF,GAAG,CAAE,aAAa,EAAExD,KAAM,CAAC;MAChC,IAAI,CAACT,GAAG,CAAChB,IAAI,CAAE,mBAAmB,EAAEyB,KAAM,CAAC;MAE3C,IAAIyD,KAAK,GAAG,IAAI,CAACjD,MAAM,CAAE,MAAO,CAAC;;MAEjC;MACA,IAAKiD,KAAK,CAACpF,GAAG,CAAC,CAAC,IAAI,EAAE,EAAG;QACxBR,GAAG,CAACQ,GAAG,CAAEoF,KAAK,EAAE5F,GAAG,CAAC6F,WAAW,CAAE1D,KAAM,CAAE,CAAC;MAC3C;IACD,CAAC;IAED2D,UAAU,EAAE,SAAAA,CAAWvB,CAAC,EAAE7C,GAAG,EAAG;MAC/B6C,CAAC,CAACwB,cAAc,CAAC,CAAC;MAClB,IAAIC,OAAO,GAAG,IAAI,CAAClD,GAAG,CAAE,IAAK,CAAC;MAC9B,IAAImD,MAAM,GAAGjG,GAAG,CAACkG,MAAM,CAAE,SAAU,CAAC;;MAEpC;MACAC,OAAO,GAAGnG,GAAG,CAACoG,SAAS,CAAE;QACxB1E,GAAG,EAAE,IAAI,CAACA,GAAG;QACb2E,MAAM,EAAEL,OAAO;QACfM,OAAO,EAAEL,MAAM;QACfM,KAAK,EAAE,SAAAA,CAAW7E,GAAG,EAAE8E,IAAI,EAAG;UAC7B,IAAI3D,KAAK,GAAG2D,IAAI,CAACnC,IAAI,CAAE,uBAAwB,CAAC;;UAEhD;UACAxB,KAAK,CAACiC,QAAQ,CAAE,mBAAoB,CAAC,CAAC2B,MAAM,CAAC,CAAC;;UAE9C;UACA5D,KAAK,CAAC0C,QAAQ,CAAE,QAAS,CAAC;;UAE1B;UACAiB,IAAI,CAAC9F,IAAI,CAAE,mBAAmB,EAAE,EAAG,CAAC;UACpC8F,IAAI,CAACnC,IAAI,CAAE,oBAAqB,CAAC,CAAC7D,GAAG,CAAE,EAAG,CAAC;UAC3CgG,IAAI,CAACnC,IAAI,CAAE,qBAAsB,CAAC,CAACrD,IAAI,CACtChB,GAAG,CAAC0G,EAAE,CAAE,QAAS,CAClB,CAAC;QACF;MACD,CAAE,CAAC;;MAEH;MACA,IAAIC,MAAM,GAAG3G,GAAG,CAAC4G,eAAe,CAAET,OAAQ,CAAC;;MAE3C;MACAQ,MAAM,CAAChE,MAAM,CAAE,KAAM,CAAC,CAACnC,GAAG,CAAEyF,MAAO,CAAC;MACpC,CAAE,IAAI,CAACvB,MAAM,CAAC,CAAC,GACZ,IAAI,CAACE,IAAI,CAAE+B,MAAM,CAACjF,GAAG,EAAE,IAAK,CAAC,GAC7BiF,MAAM,CAACjF,GAAG,CAAC2C,IAAI,CAAE,eAAgB,CAAC,CAACiB,OAAO,CAAE,OAAQ,CAAC;;MAExD;MACA,IAAI,CAACxE,WAAW,CAACkD,IAAI,CAAC,CAAC;IACxB,CAAC;IAED6C,gBAAgB,EAAE,SAAAA,CAAWtC,CAAC,EAAE7C,GAAG,EAAG;MACrC6C,CAAC,CAACwB,cAAc,CAAC,CAAC;MAClB,IAAIC,OAAO,GAAG,IAAI,CAAClD,GAAG,CAAE,IAAK,CAAC;MAC9B,IAAImD,MAAM,GAAGjG,GAAG,CAACkG,MAAM,CAAE,SAAU,CAAC;;MAEpC;MACAC,OAAO,GAAGnG,GAAG,CAACoG,SAAS,CAAE;QACxB1E,GAAG,EAAE,IAAI,CAACA,GAAG;QACb2E,MAAM,EAAEL,OAAO;QACfM,OAAO,EAAEL;MACV,CAAE,CAAC;;MAEH;MACA;MACA;MACA,IAAInB,QAAQ,GAAG9E,GAAG,CAACgC,eAAe,CAAE;QAAEgB,MAAM,EAAEmD;MAAQ,CAAE,CAAC;MACzD,IAAKrB,QAAQ,CAACxC,MAAM,EAAG;QACtB;QACAwC,QAAQ,CAAC7C,GAAG,CAAE,UAAW6E,KAAK,EAAG;UAChC;UACAA,KAAK,CAACC,IAAI,CAAC,CAAC;;UAEZ;UACA,IAAKD,KAAK,CAACpC,MAAM,CAAC,CAAC,EAAG;YACrBoC,KAAK,CAAClC,IAAI,CAAC,CAAC;UACb;;UAEA;UACAkC,KAAK,CAACE,YAAY,CAAC,CAAC;QACrB,CAAE,CAAC;;QAEH;QACAhH,GAAG,CAACmF,QAAQ,CACX,yBAAyB,EACzBL,QAAQ,EACR,IAAI,CAAChE,WAAW,EAChB,IAAI,CAACA,WACN,CAAC;MACF;;MAEA;MACA,IAAI6F,MAAM,GAAG3G,GAAG,CAAC4G,eAAe,CAAET,OAAQ,CAAC;;MAE3C;MACAQ,MAAM,CAAChE,MAAM,CAAE,KAAM,CAAC,CAACnC,GAAG,CAAEyF,MAAO,CAAC;MACpC,CAAE,IAAI,CAACvB,MAAM,CAAC,CAAC,GACZ,IAAI,CAACE,IAAI,CAAE+B,MAAM,CAACjF,GAAG,EAAE,IAAK,CAAC,GAC7BiF,MAAM,CAACjF,GAAG,CAAC2C,IAAI,CAAE,eAAgB,CAAC,CAACiB,OAAO,CAAE,OAAQ,CAAC;MACxD;MACA,IAAI,CAACxE,WAAW,CAACkD,IAAI,CAAC,CAAC;IACxB,CAAC;IAEDiD,aAAa,EAAE,SAAAA,CAAW1C,CAAC,EAAE7C,GAAG,EAAG;MAClC6C,CAAC,CAACwB,cAAc,CAAC,CAAC;MAClB;MACA,IAAKxB,CAAC,CAAC2C,QAAQ,EAAG;QACjB,OAAO,IAAI,CAACC,MAAM,CAAC,CAAC;MACrB;;MAEA;MACA,IAAI,CAACzF,GAAG,CAAC6D,QAAQ,CAAE,QAAS,CAAC;;MAE7B;MACA,IAAI6B,OAAO,GAAGpH,GAAG,CAACqH,UAAU,CAAE;QAC7BC,aAAa,EAAE,IAAI;QACnB7C,MAAM,EAAE/C,GAAG;QACX6F,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,SAAAA,CAAA,EAAY;UACpB,IAAI,CAACL,MAAM,CAAC,CAAC;QACd,CAAC;QACDM,MAAM,EAAE,SAAAA,CAAA,EAAY;UACnB,IAAI,CAAC/F,GAAG,CAAC8D,WAAW,CAAE,QAAS,CAAC;QACjC;MACD,CAAE,CAAC;IACJ,CAAC;IAED2B,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAIO,SAAS,GAAG,IAAI,CAAChG,GAAG,CAACiG,QAAQ,CAAE,8BAA+B,CAAC;;MAEnE;MACA,IAAK,CAAED,SAAS,CAACpF,MAAM,EAAG;QACzBsF,KAAK,CACJ5H,GAAG,CAAC0G,EAAE,CAAE,6CAA8C,CACvD,CAAC;QACD,OAAO,KAAK;MACb;;MAEA;MACA,IAAI,CAAC3D,SAAS,CAAC,CAAC,CAACd,GAAG,CAAE,UAAW6E,KAAK,EAAG;QACxCA,KAAK,CAACK,MAAM,CAAE;UACbU,OAAO,EAAE;QACV,CAAE,CAAC;MACJ,CAAE,CAAC;;MAEH;MACA7H,GAAG,CAACyG,MAAM,CAAE,IAAI,CAAC/E,GAAI,CAAC;;MAEtB;MACA,IAAI,CAACZ,WAAW,CAACkD,IAAI,CAAC,CAAC;IACxB;EACD,CAAE,CAAC;EAEHhE,GAAG,CAACW,oBAAoB,CAAE8B,iCAAkC,CAAC;;EAE7D;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIqF,qBAAqB,GAAG,IAAI9H,GAAG,CAACmB,KAAK,CAAE;IAC1C4G,OAAO,EAAE;MACRC,qBAAqB,EAAE,oBAAoB;MAC3CC,0BAA0B,EAAE;IAC7B,CAAC;IAEDC,kBAAkB,EAAE,SAAAA,CAAWpH,WAAW,EAAG;MAC5C,IAAIkC,MAAM,GAAGlC,WAAW,CAACqH,SAAS,CAAC,CAAC;;MAEpC;MACA,IAAK,CAAEnF,MAAM,IAAIA,MAAM,CAACjC,IAAI,CAAE,MAAO,CAAC,KAAK,kBAAkB,EAAG;QAC/DD,WAAW,CAACC,IAAI,CAAE,eAAe,EAAE,IAAK,CAAC;QACzC;MACD;;MAEA;MACA,IAAIoF,OAAO,GAAGrF,WAAW,CAACY,GAAG,CAAC0G,OAAO,CACpC,8BACD,CAAC;MACD,IAAIzB,MAAM,GAAG3G,GAAG,CAAC4G,eAAe,CAAET,OAAQ,CAAC;;MAE3C;MACA;MACA,IAAK,CAAErF,WAAW,CAACuH,GAAG,CAAE,eAAgB,CAAC,EAAG;QAC3CvH,WAAW,CAACC,IAAI,CAAE,eAAe,EAAE,CAAE,CAAC;MACvC;;MAEA;MACAD,WAAW,CAACC,IAAI,CAAE,eAAe,EAAE4F,MAAM,CAAC7D,GAAG,CAAE,IAAK,CAAE,CAAC;IACxD;EACD,CAAE,CAAC;AACJ,CAAC,EAAIN,MAAO,CAAC;;;;;;;;;;ACrWb,CAAE,UAAW1C,CAAC,EAAG;EAChB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIwI,6BAA6B,GAAGtI,GAAG,CAACC,YAAY,CAACC,MAAM,CAAE;IAC5DC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,WAAW;IACjBsC,MAAM,EAAE;MACP,cAAc,EAAE;IACjB,CAAC;IACD6F,OAAO,EAAE,SAAAA,CAAWhE,CAAC,EAAE7C,GAAG,EAAG;MAC5B;MACA,IAAIF,OAAO,GAAGE,GAAG;;MAEjB;MACA,IAAI8G,OAAO,GAAG,EAAE;;MAEhB;MACAA,OAAO,CAACC,IAAI,CAAE;QACbtG,KAAK,EAAEX,OAAO,CAAC6C,IAAI,CAAE,kBAAmB,CAAC,CAACqE,IAAI,CAAC,CAAC;QAChDC,KAAK,EAAE;MACR,CAAE,CAAC;;MAEH;MACA,IAAI9F,KAAK,GAAG,IAAI,CAAC/B,WAAW,CAAChB,CAAC,CAAE,uBAAwB,CAAC;MACzD,IAAIiC,MAAM,GAAG/B,GAAG,CAACgC,eAAe,CAAE;QACjCkB,IAAI,EAAEL;MACP,CAAE,CAAC;;MAEH;MACAd,MAAM,CAACE,GAAG,CAAE,UAAW1B,KAAK,EAAG;QAC9BiI,OAAO,CAACC,IAAI,CAAE;UACbtG,KAAK,EAAE5B,KAAK,CAACQ,IAAI,CAAE,OAAQ,CAAC;UAC5B4H,KAAK,EAAEpI,KAAK,CAACQ,IAAI,CAAE,KAAM;QAC1B,CAAE,CAAC;MACJ,CAAE,CAAC;;MAEH;MACAf,GAAG,CAAC4I,YAAY,CAAEpH,OAAO,EAAEgH,OAAQ,CAAC;IACrC;EACD,CAAE,CAAC;EAEHxI,GAAG,CAACW,oBAAoB,CAAE2H,6BAA8B,CAAC;AAC1D,CAAC,EAAI9F,MAAO,CAAC;;;;;;UCrDb;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA,eAAe,4BAA4B;WAC3C,eAAe;WACf,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA,8CAA8C;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;ACNoC;AACQ", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-setting-clone.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-setting-flexible-content.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-setting-repeater.js", "webpack://advanced-custom-fields-pro/webpack/bootstrap", "webpack://advanced-custom-fields-pro/webpack/runtime/compat get default export", "webpack://advanced-custom-fields-pro/webpack/runtime/define property getters", "webpack://advanced-custom-fields-pro/webpack/runtime/hasOwnProperty shorthand", "webpack://advanced-custom-fields-pro/webpack/runtime/make namespace object", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/acf-pro-field-group.js"], "sourcesContent": ["( function ( $ ) {\n\t/**\n\t *  CloneDisplayFieldSetting\n\t *\n\t *  Extra logic for this field setting\n\t *\n\t *  @date\t18/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar CloneDisplayFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: 'clone',\n\t\tname: 'display',\n\t\trender: function () {\n\t\t\t// vars\n\t\t\tvar display = this.field.val();\n\n\t\t\t// set data attribute used by CSS to hide/show\n\t\t\tthis.$fieldObject.attr( 'data-display', display );\n\t\t},\n\t} );\n\n\tacf.registerFieldSetting( CloneDisplayFieldSetting );\n\n\t/**\n\t *  ClonePrefixLabelFieldSetting\n\t *\n\t *  Extra logic for this field setting\n\t *\n\t *  @date\t18/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar ClonePrefixLabelFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: 'clone',\n\t\tname: 'prefix_label',\n\t\trender: function () {\n\t\t\t// vars\n\t\t\tvar prefix = '';\n\n\t\t\t// if checked\n\t\t\tif ( this.field.val() ) {\n\t\t\t\tprefix = this.fieldObject.prop( 'label' ) + ' ';\n\t\t\t}\n\n\t\t\t// update HTML\n\t\t\tthis.$( 'code' ).html( prefix + '%field_label%' );\n\t\t},\n\t} );\n\n\tacf.registerFieldSetting( ClonePrefixLabelFieldSetting );\n\n\t/**\n\t *  ClonePrefixNameFieldSetting\n\t *\n\t *  Extra logic for this field setting\n\t *\n\t *  @date\t18/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar ClonePrefixNameFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: 'clone',\n\t\tname: 'prefix_name',\n\t\trender: function () {\n\t\t\t// vars\n\t\t\tvar prefix = '';\n\n\t\t\t// if checked\n\t\t\tif ( this.field.val() ) {\n\t\t\t\tprefix = this.fieldObject.prop( 'name' ) + '_';\n\t\t\t}\n\n\t\t\t// update HTML\n\t\t\tthis.$( 'code' ).html( prefix + '%field_name%' );\n\t\t},\n\t} );\n\n\tacf.registerFieldSetting( ClonePrefixNameFieldSetting );\n\n\t/**\n\t *  cloneFieldSelectHelper\n\t *\n\t *  Customizes the clone field setting Select2 isntance\n\t *\n\t *  @date\t18/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar cloneFieldSelectHelper = new acf.Model( {\n\t\tfilters: {\n\t\t\tselect2_args: 'select2Args',\n\t\t},\n\n\t\tselect2Args: function ( options, $select, data, $el, instance ) {\n\t\t\t// check\n\t\t\tif ( data.ajaxAction == 'acf/fields/clone/query' ) {\n\t\t\t\t// remain open on select\n\t\t\t\toptions.closeOnSelect = false;\n\n\t\t\t\t// customize ajaxData function\n\t\t\t\tinstance.data.ajaxData = this.ajaxData;\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn options;\n\t\t},\n\n\t\tajaxData: function ( data ) {\n\t\t\t// find current fields\n\t\t\tdata.fields = {};\n\n\t\t\t// loop\n\t\t\tacf.getFieldObjects().map( function ( fieldObject ) {\n\t\t\t\t// append\n\t\t\t\tdata.fields[ fieldObject.prop( 'key' ) ] = {\n\t\t\t\t\tkey: fieldObject.prop( 'key' ),\n\t\t\t\t\ttype: fieldObject.prop( 'type' ),\n\t\t\t\t\tlabel: fieldObject.prop( 'label' ),\n\t\t\t\t\tancestors: fieldObject.getParents().length,\n\t\t\t\t};\n\t\t\t} );\n\n\t\t\t// append title\n\t\t\tdata.title = $( '#title' ).val();\n\n\t\t\t// return\n\t\t\treturn data;\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $ ) {\n\t/**\n\t *  CloneDisplayFieldSetting\n\t *\n\t *  Extra logic for this field setting\n\t *\n\t *  @date\t18/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar FlexibleContentLayoutFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: 'flexible_content',\n\t\tname: 'fc_layout',\n\n\t\tevents: {\n\t\t\t'blur .layout-label': 'onChangeLabel',\n\t\t\t'click .add-layout': 'onClickAdd',\n\t\t\t'click .acf-field-settings-fc_head': 'onClickEdit',\n\t\t\t'click .acf-field-setting-fc-duplicate': 'onClickDuplicate',\n\t\t\t'click .acf-field-setting-fc-delete': 'onClickDelete',\n\t\t\t'changed:layoutLabel': 'updateLayoutTitles',\n\t\t},\n\n\t\t$input: function ( name ) {\n\t\t\treturn $( '#' + this.getInputId() + '-' + name );\n\t\t},\n\n\t\t$list: function () {\n\t\t\treturn this.$( '.acf-field-list:first' );\n\t\t},\n\n\t\tgetInputId: function () {\n\t\t\treturn (\n\t\t\t\tthis.fieldObject.getInputId() +\n\t\t\t\t'-layouts-' +\n\t\t\t\tthis.field.get( 'id' )\n\t\t\t);\n\t\t},\n\n\t\t// get all sub fields\n\t\tgetFields: function () {\n\t\t\treturn acf.getFieldObjects( { parent: this.$el } );\n\t\t},\n\n\t\t// get imediate children\n\t\tgetChildren: function () {\n\t\t\treturn acf.getFieldObjects( { list: this.$list() } );\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\t// add sortable\n\t\t\tvar $tbody = this.$el.parent();\n\t\t\tif ( ! $tbody.hasClass( 'ui-sortable' ) ) {\n\t\t\t\t$tbody.sortable( {\n\t\t\t\t\titems: '> .acf-field-setting-fc_layout',\n\t\t\t\t\thandle: '.acf-fc_draggable',\n\t\t\t\t\tforceHelperSize: true,\n\t\t\t\t\tforcePlaceholderSize: true,\n\t\t\t\t\tscroll: true,\n\t\t\t\t\tstop: this.proxy( function ( event, ui ) {\n\t\t\t\t\t\tthis.fieldObject.save();\n\t\t\t\t\t} ),\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\t// add meta to sub fields\n\t\t\tthis.updateFieldLayouts();\n\t\t\tthis.updateLayoutTitles();\n\t\t},\n\n\t\tupdateFieldLayouts: function () {\n\t\t\tthis.getChildren().map( this.updateFieldLayout, this );\n\t\t},\n\n\t\tupdateFieldLayout: function ( field ) {\n\t\t\tfield.prop( 'parent_layout', this.get( 'id' ) );\n\t\t},\n\n\t\tupdateLayoutTitles: function () {\n\t\t\tconst label = this.get( 'layoutLabel' );\n\n\t\t\tconst parentLabel = this.$el.find(\n\t\t\t\t'> .acf-label .acf-fc-layout-name'\n\t\t\t);\n\n\t\t\tif ( label ) {\n\t\t\t\tparentLabel.html( label );\n\t\t\t}\n\t\t},\n\n\t\tonClickEdit: function ( e ) {\n\t\t\tconst $target = $( e.target );\n\n\t\t\tif (\n\t\t\t\t$target.hasClass( 'acf-btn' ) ||\n\t\t\t\t$target.parent().hasClass( 'acf-btn' )\n\t\t\t) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.isOpen() ? this.close() : this.open();\n\t\t},\n\n\t\tisOpen: function ( e ) {\n\t\t\tconst $settings = this.$el.children( '.acf-field-layout-settings' );\n\t\t\treturn $settings.hasClass( 'open' );\n\t\t},\n\n\t\topen: function ( element, isAddingLayout ) {\n\t\t\tconst $settings = element\n\t\t\t\t? element.children( '.acf-field-layout-settings' )\n\t\t\t\t: this.$el.children( '.acf-field-layout-settings' );\n\t\t\tconst toggle = element\n\t\t\t\t? element.find( '.toggle-indicator' ).first()\n\t\t\t\t: this.$el.find( '.toggle-indicator' ).first();\n\n\t\t\t// action (show)\n\t\t\tacf.doAction( 'show', $settings );\n\n\t\t\t// open\n\t\t\tif ( isAddingLayout ) {\n\t\t\t\t$settings.slideDown( {\n\t\t\t\t\tcomplete: function () {\n\t\t\t\t\t\t$settings.find( '.layout-label' ).trigger( 'focus' );\n\t\t\t\t\t},\n\t\t\t\t} );\n\t\t\t} else {\n\t\t\t\t$settings.slideDown();\n\t\t\t}\n\t\t\ttoggle.addClass( 'open' );\n\t\t\tif ( toggle.hasClass( 'closed' ) ) {\n\t\t\t\ttoggle.removeClass( 'closed' );\n\t\t\t}\n\t\t\t$settings.addClass( 'open' );\n\t\t},\n\n\t\tclose: function () {\n\t\t\tconst $settings = this.$el.children( '.acf-field-layout-settings' );\n\t\t\tconst toggle = this.$el.find( '.toggle-indicator' ).first();\n\n\t\t\t// close\n\t\t\t$settings.slideUp();\n\t\t\t$settings.removeClass( 'open' );\n\t\t\ttoggle.removeClass( 'open' );\n\t\t\tif ( ! toggle.hasClass( 'closed' ) ) {\n\t\t\t\ttoggle.addClass( 'closed' );\n\t\t\t}\n\n\t\t\t// action (hide)\n\t\t\tacf.doAction( 'hide', $settings );\n\t\t},\n\n\t\tonChangeLabel: function ( e, $el ) {\n\t\t\tvar label = $el.val();\n\n\t\t\tthis.set( 'layoutLabel', label );\n\t\t\tthis.$el.attr( 'data-layout-label', label );\n\n\t\t\tvar $name = this.$input( 'name' );\n\n\t\t\t// render name\n\t\t\tif ( $name.val() == '' ) {\n\t\t\t\tacf.val( $name, acf.strSanitize( label ) );\n\t\t\t}\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\te.preventDefault();\n\t\t\tvar prevKey = this.get( 'id' );\n\t\t\tvar newKey = acf.uniqid( 'layout_' );\n\n\t\t\t// duplicate\n\t\t\t$layout = acf.duplicate( {\n\t\t\t\t$el: this.$el,\n\t\t\t\tsearch: prevKey,\n\t\t\t\treplace: newKey,\n\t\t\t\tafter: function ( $el, $el2 ) {\n\t\t\t\t\tvar $list = $el2.find( '.acf-field-list:first' );\n\n\t\t\t\t\t// remove sub fields\n\t\t\t\t\t$list.children( '.acf-field-object' ).remove();\n\n\t\t\t\t\t// show empty\n\t\t\t\t\t$list.addClass( '-empty' );\n\n\t\t\t\t\t// reset layout meta values\n\t\t\t\t\t$el2.attr( 'data-layout-label', '' );\n\t\t\t\t\t$el2.find( '.acf-fc-meta input' ).val( '' );\n\t\t\t\t\t$el2.find( '.acf-fc-layout-name' ).html(\n\t\t\t\t\t\tacf.__( 'Layout' )\n\t\t\t\t\t);\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// get layout\n\t\t\tvar layout = acf.getFieldSetting( $layout );\n\n\t\t\t// update hidden input\n\t\t\tlayout.$input( 'key' ).val( newKey );\n\t\t\t! this.isOpen()\n\t\t\t\t? this.open( layout.$el, true )\n\t\t\t\t: layout.$el.find( '.layout-label' ).trigger( 'focus' );\n\n\t\t\t// save\n\t\t\tthis.fieldObject.save();\n\t\t},\n\n\t\tonClickDuplicate: function ( e, $el ) {\n\t\t\te.preventDefault();\n\t\t\tvar prevKey = this.get( 'id' );\n\t\t\tvar newKey = acf.uniqid( 'layout_' );\n\n\t\t\t// duplicate\n\t\t\t$layout = acf.duplicate( {\n\t\t\t\t$el: this.$el,\n\t\t\t\tsearch: prevKey,\n\t\t\t\treplace: newKey,\n\t\t\t} );\n\n\t\t\t// get all fields in new layout similar to fieldManager.onDuplicateField().\n\t\t\t// important to run field.wipe() before making any changes to the \"parent_layout\" prop\n\t\t\t// to ensure the correct input is modified.\n\t\t\tvar children = acf.getFieldObjects( { parent: $layout } );\n\t\t\tif ( children.length ) {\n\t\t\t\t// loop\n\t\t\t\tchildren.map( function ( child ) {\n\t\t\t\t\t// wipe field\n\t\t\t\t\tchild.wipe();\n\n\t\t\t\t\t// if the child is open, re-fire the open method to ensure it's initialised correctly.\n\t\t\t\t\tif ( child.isOpen() ) {\n\t\t\t\t\t\tchild.open();\n\t\t\t\t\t}\n\n\t\t\t\t\t// update parent\n\t\t\t\t\tchild.updateParent();\n\t\t\t\t} );\n\n\t\t\t\t// action\n\t\t\t\tacf.doAction(\n\t\t\t\t\t'duplicate_field_objects',\n\t\t\t\t\tchildren,\n\t\t\t\t\tthis.fieldObject,\n\t\t\t\t\tthis.fieldObject\n\t\t\t\t);\n\t\t\t}\n\n\t\t\t// get layout\n\t\t\tvar layout = acf.getFieldSetting( $layout );\n\n\t\t\t// update hidden input\n\t\t\tlayout.$input( 'key' ).val( newKey );\n\t\t\t! this.isOpen()\n\t\t\t\t? this.open( layout.$el, true )\n\t\t\t\t: layout.$el.find( '.layout-label' ).trigger( 'focus' );\n\t\t\t// save\n\t\t\tthis.fieldObject.save();\n\t\t},\n\n\t\tonClickDelete: function ( e, $el ) {\n\t\t\te.preventDefault();\n\t\t\t// Bypass confirmation when holding down \"shift\" key.\n\t\t\tif ( e.shiftKey ) {\n\t\t\t\treturn this.delete();\n\t\t\t}\n\n\t\t\t// add class\n\t\t\tthis.$el.addClass( '-hover' );\n\n\t\t\t// add tooltip\n\t\t\tvar tooltip = acf.newTooltip( {\n\t\t\t\tconfirmRemove: true,\n\t\t\t\ttarget: $el,\n\t\t\t\tcontext: this,\n\t\t\t\tconfirm: function () {\n\t\t\t\t\tthis.delete();\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\tthis.$el.removeClass( '-hover' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tdelete: function () {\n\t\t\tvar $siblings = this.$el.siblings( '.acf-field-setting-fc_layout' );\n\n\t\t\t// validate\n\t\t\tif ( ! $siblings.length ) {\n\t\t\t\talert(\n\t\t\t\t\tacf.__( 'Flexible Content requires at least 1 layout' )\n\t\t\t\t);\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// delete sub fields\n\t\t\tthis.getFields().map( function ( child ) {\n\t\t\t\tchild.delete( {\n\t\t\t\t\tanimate: false,\n\t\t\t\t} );\n\t\t\t} );\n\n\t\t\t// remove tr\n\t\t\tacf.remove( this.$el );\n\n\t\t\t// save\n\t\t\tthis.fieldObject.save();\n\t\t},\n\t} );\n\n\tacf.registerFieldSetting( FlexibleContentLayoutFieldSetting );\n\n\t/**\n\t *  flexibleContentHelper\n\t *\n\t *  description\n\t *\n\t *  @date\t19/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar flexibleContentHelper = new acf.Model( {\n\t\tactions: {\n\t\t\tsortstop_field_object: 'updateParentLayout',\n\t\t\tchange_field_object_parent: 'updateParentLayout',\n\t\t},\n\n\t\tupdateParentLayout: function ( fieldObject ) {\n\t\t\tvar parent = fieldObject.getParent();\n\n\t\t\t// delete meta\n\t\t\tif ( ! parent || parent.prop( 'type' ) !== 'flexible_content' ) {\n\t\t\t\tfieldObject.prop( 'parent_layout', null );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// get layout\n\t\t\tvar $layout = fieldObject.$el.closest(\n\t\t\t\t'.acf-field-setting-fc_layout'\n\t\t\t);\n\t\t\tvar layout = acf.getFieldSetting( $layout );\n\n\t\t\t// check if previous prop exists\n\t\t\t// - if not, set prop to allow following code to trigger 'change' and save the field\n\t\t\tif ( ! fieldObject.has( 'parent_layout' ) ) {\n\t\t\t\tfieldObject.prop( 'parent_layout', 0 );\n\t\t\t}\n\n\t\t\t// update meta\n\t\t\tfieldObject.prop( 'parent_layout', layout.get( 'id' ) );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $ ) {\n\t/*\n\t *  Repeater\n\t *\n\t *  This field type requires some extra logic for its settings\n\t *\n\t *  @type\tfunction\n\t *  @date\t24/10/13\n\t *  @since\t5.0.0\n\t *\n\t *  @param\tn/a\n\t *  @return\tn/a\n\t */\n\n\tvar RepeaterCollapsedFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: 'repeater',\n\t\tname: 'collapsed',\n\t\tevents: {\n\t\t\t'focus select': 'onFocus',\n\t\t},\n\t\tonFocus: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar $select = $el;\n\n\t\t\t// collapsed\n\t\t\tvar choices = [];\n\n\t\t\t// keep 'null' choice\n\t\t\tchoices.push( {\n\t\t\t\tlabel: $select.find( 'option[value=\"\"]' ).text(),\n\t\t\t\tvalue: '',\n\t\t\t} );\n\n\t\t\t// find sub fields\n\t\t\tvar $list = this.fieldObject.$( '.acf-field-list:first' );\n\t\t\tvar fields = acf.getFieldObjects( {\n\t\t\t\tlist: $list,\n\t\t\t} );\n\n\t\t\t// loop\n\t\t\tfields.map( function ( field ) {\n\t\t\t\tchoices.push( {\n\t\t\t\t\tlabel: field.prop( 'label' ),\n\t\t\t\t\tvalue: field.prop( 'key' ),\n\t\t\t\t} );\n\t\t\t} );\n\n\t\t\t// render\n\t\t\tacf.renderSelect( $select, choices );\n\t\t},\n\t} );\n\n\tacf.registerFieldSetting( RepeaterCollapsedFieldSetting );\n} )( jQuery );\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import './_acf-setting-repeater.js';\nimport './_acf-setting-flexible-content.js';\nimport './_acf-setting-clone.js';\n"], "names": ["$", "CloneDisplayFieldSetting", "acf", "FieldSetting", "extend", "type", "name", "render", "display", "field", "val", "$fieldObject", "attr", "registerFieldSetting", "ClonePrefixLabelFieldSetting", "prefix", "fieldObject", "prop", "html", "ClonePrefixNameFieldSetting", "cloneFieldSelectHelper", "Model", "filters", "select2_args", "select2Args", "options", "$select", "data", "$el", "instance", "ajaxAction", "closeOnSelect", "ajaxData", "fields", "getFieldObjects", "map", "key", "label", "ancestors", "getParents", "length", "title", "j<PERSON><PERSON><PERSON>", "FlexibleContentLayoutFieldSetting", "events", "$input", "getInputId", "$list", "get", "getFields", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "list", "initialize", "$tbody", "hasClass", "sortable", "items", "handle", "forceHelperSize", "forcePlaceholderSize", "scroll", "stop", "proxy", "event", "ui", "save", "updateFieldLayouts", "updateLayoutTitles", "updateFieldLayout", "parentLabel", "find", "onClickEdit", "e", "$target", "target", "isOpen", "close", "open", "$settings", "children", "element", "isAddingLayout", "toggle", "first", "doAction", "slideDown", "complete", "trigger", "addClass", "removeClass", "slideUp", "onChangeLabel", "set", "$name", "strSanitize", "onClickAdd", "preventDefault", "prev<PERSON><PERSON>", "new<PERSON>ey", "uniqid", "$layout", "duplicate", "search", "replace", "after", "$el2", "remove", "__", "layout", "getFieldSetting", "onClickDuplicate", "child", "wipe", "updateParent", "onClickDelete", "shift<PERSON>ey", "delete", "tooltip", "newTooltip", "confirmRemove", "context", "confirm", "cancel", "$siblings", "siblings", "alert", "animate", "flexibleContentHelper", "actions", "sortstop_field_object", "change_field_object_parent", "updateParentLayout", "getParent", "closest", "has", "RepeaterCollapsedFieldSetting", "onFocus", "choices", "push", "text", "value", "renderSelect"], "sourceRoot": ""}