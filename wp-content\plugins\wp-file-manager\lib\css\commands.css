/******************************************************************/
/*                          COMMANDS STYLES                       */
/******************************************************************/

/********************** COMMAND "RESIZE" ****************************/
.elfinder-resize-container {
    margin-top: .3em;
}

.elfinder-resize-type {
    float: left;
    margin-bottom: .4em;
}

.elfinder-resize-control {
    float: left;
}

.elfinder-resize-control input[type=number] {
    border: 1px solid #aaa;
    text-align: right;
    width: 4.5em;
}

.elfinder-mobile .elfinder-resize-control input[type=number] {
    width: 3.5em;
}

.elfinder-resize-control input.elfinder-resize-bg {
    text-align: center;
    width: 5em;
    direction: ltr;
}

.elfinder-dialog-resize .elfinder-resize-control-panel {
    margin-top: 10px;
}

.elfinder-dialog-resize .elfinder-resize-imgrotate,
.elfinder-dialog-resize .elfinder-resize-pallet {
    cursor: pointer;
}

.elfinder-dialog-resize .elfinder-resize-picking {
    cursor: crosshair;
}

.elfinder-dialog-resize .elfinder-resize-grid8 + button {
    padding-top: 2px;
    padding-bottom: 2px;
}

.elfinder-resize-preview {
    width: 400px;
    height: 400px;
    padding: 10px;
    background: #fff;
    border: 1px solid #aaa;
    float: right;
    position: relative;
    overflow: hidden;
    text-align: left;
    direction: ltr;
}

.elfinder-resize-handle {
    position: relative;
}

.elfinder-resize-handle-hline,
.elfinder-resize-handle-vline {
    position: absolute;
    background-image: url("../img/crop.gif");
}

.elfinder-resize-handle-hline {
    width: 100%;
    height: 1px !important;
    background-repeat: repeat-x;
}

.elfinder-resize-handle-vline {
    width: 1px !important;
    height: 100%;
    background-repeat: repeat-y;
}

.elfinder-resize-handle-hline-top {
    top: 0;
    left: 0;
}

.elfinder-resize-handle-hline-bottom {
    bottom: 0;
    left: 0;
}

.elfinder-resize-handle-vline-left {
    top: 0;
    left: 0;
}

.elfinder-resize-handle-vline-right {
    top: 0;
    right: 0;
}

.elfinder-resize-handle-point {
    position: absolute;
    width: 8px;
    height: 8px;
    border: 1px solid #777;
    background: transparent;
}

.elfinder-resize-handle-point-n {
    top: 0;
    left: 50%;
    margin-top: -5px;
    margin-left: -5px;
}

.elfinder-resize-handle-point-ne {
    top: 0;
    right: 0;
    margin-top: -5px;
    margin-right: -5px;
}

.elfinder-resize-handle-point-e {
    top: 50%;
    right: 0;
    margin-top: -5px;
    margin-right: -5px;
}

.elfinder-resize-handle-point-se {
    bottom: 0;
    right: 0;
    margin-bottom: -5px;
    margin-right: -5px;
}

.elfinder-resize-handle-point-s {
    bottom: 0;
    left: 50%;
    margin-bottom: -5px;
    margin-left: -5px;
}

.elfinder-resize-handle-point-sw {
    bottom: 0;
    left: 0;
    margin-bottom: -5px;
    margin-left: -5px;
}

.elfinder-resize-handle-point-w {
    top: 50%;
    left: 0;
    margin-top: -5px;
    margin-left: -5px;
}

.elfinder-resize-handle-point-nw {
    top: 0;
    left: 0;
    margin-top: -5px;
    margin-left: -5px;
}

.elfinder-dialog.elfinder-dialog-resize .ui-resizable-e {
    width: 10px;
    height: 100%;
}

.elfinder-dialog.elfinder-dialog-resize .ui-resizable-s {
    width: 100%;
    height: 10px;
}

.elfinder-resize-loading {
    position: absolute;
    width: 200px;
    height: 30px;
    top: 50%;
    margin-top: -25px;
    left: 50%;
    margin-left: -100px;
    text-align: center;
    background: url(../img/progress.gif) center bottom repeat-x;
}

.elfinder-resize-row {
    margin-bottom: 9px;
    position: relative;
}

.elfinder-resize-label {
    float: left;
    width: 80px;
    padding-top: 3px;
}

.elfinder-resize-checkbox-label {
    border: 1px solid transparent;
}

.elfinder-dialog-resize .elfinder-resize-whctrls {
    margin: -20px 5px 0 5px;
}

.elfinder-ltr .elfinder-dialog-resize .elfinder-resize-whctrls {
    float: right;
}

.elfinder-rtl .elfinder-dialog-resize .elfinder-resize-whctrls {
    float: left;
}

.elfinder-dialog-resize .ui-resizable-e,
.elfinder-dialog-resize .ui-resizable-w {
    height: 100%;
    width: 10px;
}

.elfinder-dialog-resize .ui-resizable-s,
.elfinder-dialog-resize .ui-resizable-n {
    width: 100%;
    height: 10px;
}

.elfinder-dialog-resize .ui-resizable-e {
    margin-right: -7px;
}

.elfinder-dialog-resize .ui-resizable-w {
    margin-left: -7px;
}

.elfinder-dialog-resize .ui-resizable-s {
    margin-bottom: -7px;
}

.elfinder-dialog-resize .ui-resizable-n {
    margin-top: -7px;
}

.elfinder-dialog-resize .ui-resizable-se,
.elfinder-dialog-resize .ui-resizable-sw,
.elfinder-dialog-resize .ui-resizable-ne,
.elfinder-dialog-resize .ui-resizable-nw {
    width: 10px;
    height: 10px;
}

.elfinder-dialog-resize .ui-resizable-se {
    background: transparent;
    bottom: 0;
    right: 0;
    margin-right: -7px;
    margin-bottom: -7px;
}

.elfinder-dialog-resize .ui-resizable-sw {
    margin-left: -7px;
    margin-bottom: -7px;
}

.elfinder-dialog-resize .ui-resizable-ne {
    margin-right: -7px;
    margin-top: -7px;
}

.elfinder-dialog-resize .ui-resizable-nw {
    margin-left: -7px;
    margin-top: -7px;
}

.elfinder-touch .elfinder-dialog-resize .ui-resizable-s,
.elfinder-touch .elfinder-dialog-resize .ui-resizable-n {
    height: 20px;
}

.elfinder-touch .elfinder-dialog-resize .ui-resizable-e,
.elfinder-touch .elfinder-dialog-resize .ui-resizable-w {
    width: 20px;
}

.elfinder-touch .elfinder-dialog-resize .ui-resizable-se,
.elfinder-touch .elfinder-dialog-resize .ui-resizable-sw,
.elfinder-touch .elfinder-dialog-resize .ui-resizable-ne,
.elfinder-touch .elfinder-dialog-resize .ui-resizable-nw {
    width: 30px;
    height: 30px;
}

.elfinder-touch .elfinder-dialog-resize .elfinder-resize-preview .ui-resizable-se {
    width: 30px;
    height: 30px;
    margin: 0;
}

.elfinder-dialog-resize .ui-icon-grip-solid-vertical {
    position: absolute;
    top: 50%;
    right: 0;
    margin-top: -8px;
    margin-right: -11px;
}

.elfinder-dialog-resize .ui-icon-grip-solid-horizontal {
    position: absolute;
    left: 50%;
    bottom: 0;
    margin-left: -8px;
    margin-bottom: -11px;;
}

.elfinder-dialog-resize .elfinder-resize-row .ui-buttonset {
    float: right;
}

.elfinder-dialog-resize .elfinder-resize-degree input,
.elfinder-dialog-resize input.elfinder-resize-quality {
    width: 3.5em;
}

.elfinder-mobile .elfinder-dialog-resize .elfinder-resize-degree input,
.elfinder-mobile .elfinder-dialog-resize input.elfinder-resize-quality {
    width: 2.5em;
}

.elfinder-dialog-resize .elfinder-resize-degree button.ui-button {
    padding: 6px 8px;
}

.elfinder-dialog-resize button.ui-button span {
    padding: 0;
}

.elfinder-dialog-resize .elfinder-resize-jpgsize {
    font-size: 90%;
}

.ui-widget-content .elfinder-resize-container .elfinder-resize-rotate-slider {
    width: 195px;
    margin: 10px 7px;
    background-color: #fafafa;
}

.elfinder-dialog-resize .elfinder-resize-type span.ui-checkboxradio-icon {
    display: none;
}

.elfinder-resize-preset-container {
    box-sizing: border-box;
    border-radius: 5px;
}

/********************** COMMAND "EDIT" ****************************/
/* edit text file textarea */
.elfinder-file-edit {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
    box-sizing: border-box;
    resize: none;
}

.elfinder-touch .elfinder-file-edit {
    font-size: 16px;
}

/* edit area */
.elfinder-dialog-edit .ui-dialog-content.elfinder-edit-editor {
    background-color: #fff;
}

.elfinder-dialog-edit .ui-dialog-content.elfinder-edit-editor .elfinder-edit-imageeditor {
    width: 100%;
    height: 300px;
    max-height: 100%;
    text-align: center;
}

.elfinder-dialog-edit .ui-dialog-content.elfinder-edit-editor .elfinder-edit-imageeditor * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
    user-select: none;
}

.elfinder-edit-imageeditor .tui-image-editor-main-container .tui-image-editor-main {
    top: 0;
}

.elfinder-edit-imageeditor .tui-image-editor-main-container .tui-image-editor-header {
    display: none;
}

.elfinder-edit-imageeditor .tui-image-editor-main.tui-image-editor-menu-crop .tui-image-editor-wrap,
.elfinder-edit-imageeditor .tui-image-editor-main.tui-image-editor-menu-flip .tui-image-editor-wrap,
.elfinder-edit-imageeditor .tui-image-editor-main.tui-image-editor-menu-rotate .tui-image-editor-wrap,
.elfinder-edit-imageeditor .tui-image-editor-main.tui-image-editor-menu-draw .tui-image-editor-wrap,
.elfinder-edit-imageeditor .tui-image-editor-main.tui-image-editor-menu-shape .tui-image-editor-wrap,
.elfinder-edit-imageeditor .tui-image-editor-main.tui-image-editor-menu-icon .tui-image-editor-wrap,
.elfinder-edit-imageeditor .tui-image-editor-main.tui-image-editor-menu-text .tui-image-editor-wrap,
.elfinder-edit-imageeditor .tui-image-editor-main.tui-image-editor-menu-mask .tui-image-editor-wrap,
.elfinder-edit-imageeditor .tui-image-editor-main.tui-image-editor-menu-filter .tui-image-editor-wrap {
    height: calc(100% - 150px);
}

/* bottom margen for softkeyboard on fullscreen mode */
.elfinder-touch.elfinder-fullscreen-native textarea.elfinder-file-edit {
    padding-bottom: 20em;
    margin-bottom: -20em;
}

.elfinder-dialog-edit .ui-dialog-buttonpane .elfinder-dialog-confirm-encoding {
    font-size: 12px;
}

.ui-dialog-buttonpane .ui-dialog-buttonset.elfinder-edit-extras {
    margin: 0 1em 0 .2em;
    float: left;
}

.ui-dialog-buttonpane .ui-dialog-buttonset.elfinder-edit-extras-quality {
    padding-top: 6px;
}

.ui-dialog-buttonpane .ui-dialog-buttonset.elfinder-edit-extras select {
    font-size: 12px;
    margin-top: 1px;
    min-height: 28px;
}

.elfinder-dialog-edit .ui-dialog-buttonpane .ui-icon {
    cursor: pointer;
}

.elfinder-edit-spinner {
    position: absolute;
    top: 50%;
    text-align: center;
    width: 100%;
    font-size: 16pt;
}

.elfinder-dialog-edit .elfinder-edit-spinner .elfinder-spinner,
.elfinder-dialog-edit .elfinder-edit-spinner .elfinder-spinner-text {
    float: none;
}

.elfinder-dialog-edit .elfinder-toast > div {
    width: 280px;
}
 
.elfinder-edit-onlineconvert-button {
    display: inline-block;
    width: 180px;
    min-height: 30px;
    vertical-align: top;
}
.elfinder-edit-onlineconvert-button button,
.elfinder-edit-onlineconvert-bottom-btn button {
    cursor: pointer;
}
.elfinder-edit-onlineconvert-bottom-btn button.elfinder-button-ios-multiline {
    -webkit-appearance: none;
    border-radius: 16px;
    color: #000;
    text-align: center;
    padding: 8px;
    background-color: #eee;
    background-image: -webkit-linear-gradient(top, hsl(0,0%,98%) 0%,hsl(0,0%,77%) 100%);
    background-image: linear-gradient(to bottom, hsl(0,0%,98%) 0%,hsl(0,0%,77%) 100%);
}
.elfinder-edit-onlineconvert-button .elfinder-button-icon {
    margin: 0 10px;
    vertical-align: middle;
    cursor: pointer;
}
.elfinder-edit-onlineconvert-bottom-btn {
    text-align: center;
    margin: 10px 0 0;
}

.elfinder-edit-onlineconvert-link {
    margin-top: 1em;
    text-align: center;
}
.elfinder-edit-onlineconvert-link .elfinder-button-icon {
    background-image: url("../img/editor-icons.png");
    background-repeat: no-repeat;
    background-position: 0 -144px;
    margin-bottom: -3px;
}
.elfinder-edit-onlineconvert-link a {
    text-decoration: none;
}

/********************** COMMAND "SORT" ****************************/
/* for list table header sort triangle icon */
div.elfinder-cwd-wrapper-list tr.ui-state-default td {
    position: relative;
}

div.elfinder-cwd-wrapper-list tr.ui-state-default td span.ui-icon {
    position: absolute;
    top: 4px;
    left: 0;
    right: 0;
    margin: auto 0px auto auto;
}

.elfinder-touch div.elfinder-cwd-wrapper-list tr.ui-state-default td span.ui-icon {
    top: 7px;
}

.elfinder-rtl div.elfinder-cwd-wrapper-list tr.ui-state-default td span.ui-icon {
    margin-right: 5px;
}

/********************** COMMAND "HELP" ****************************/
/* help dialog */
.elfinder-help {
    margin-bottom: .5em;
    -webkit-overflow-scrolling: touch;
}

/* fix tabs */
.elfinder-help .ui-tabs-panel {
    padding: .5em;
    overflow: auto;
    padding: 10px;
}

.elfinder-dialog .ui-tabs .ui-tabs-nav li {
    overflow: hidden;
}

.elfinder-dialog .ui-tabs .ui-tabs-nav li a {
    padding: .2em .8em;
    display: inline-block;
}

.elfinder-touch .elfinder-dialog .ui-tabs .ui-tabs-nav li a {
    padding: .5em .5em;
}

.elfinder-dialog .ui-tabs-active a {
    background: inherit;
}

.elfinder-help-shortcuts {
    height: auto;
    padding: 10px;
    margin: 0;
    box-sizing: border-box;
}

.elfinder-help-shortcut {
    white-space: nowrap;
    clear: both;
}

.elfinder-help-shortcut-pattern {
    float: left;
    width: 160px;
}

.elfinder-help-logo {
    width: 100px;
    height: 96px;
    float: left;
    margin-right: 1em;
    background: url('../img/logo.png') center center no-repeat;
}

.elfinder-help h3 {
    font-size: 1.5em;
    margin: .2em 0 .3em 0;
}

.elfinder-help-separator {
    clear: both;
    padding: .5em;
}

.elfinder-help-link {
    display: inline-block;
    margin-right: 12px;
    padding: 2px 0;
    white-space: nowrap;
}

.elfinder-rtl .elfinder-help-link {
    margin-right: 0;
    margin-left: 12px;
}

.elfinder-help .ui-priority-secondary {
    font-size: .9em;
}

.elfinder-help .ui-priority-primary {
    margin-bottom: 7px;
}

.elfinder-help-team {
    clear: both;
    text-align: right;
    border-bottom: 1px solid #ccc;
    margin: .5em 0;
    font-size: .9em;
}

.elfinder-help-team div {
    float: left;
}

.elfinder-help-license {
    font-size: .9em;
}

.elfinder-help-disabled {
    font-weight: bold;
    text-align: center;
    margin: 90px 0;
}

.elfinder-help .elfinder-dont-panic {
    display: block;
    border: 1px solid transparent;
    width: 200px;
    height: 200px;
    margin: 30px auto;
    text-decoration: none;
    text-align: center;
    position: relative;
    background: #d90004;
    -moz-box-shadow: 5px 5px 9px #111;
    -webkit-box-shadow: 5px 5px 9px #111;
    box-shadow: 5px 5px 9px #111;
    background: -moz-radial-gradient(80px 80px, circle farthest-corner, #d90004 35%, #960004 100%);
    background: -webkit-gradient(radial, 80 80, 60, 80 80, 120, from(#d90004), to(#960004));
    -moz-border-radius: 100px;
    -webkit-border-radius: 100px;
    border-radius: 100px;
    outline: none;
}

.elfinder-help .elfinder-dont-panic span {
    font-size: 3em;
    font-weight: bold;
    text-align: center;
    color: #fff;
    position: absolute;
    left: 0;
    top: 45px;
}

ul.elfinder-help-integrations ul {
    margin-bottom: 1em;
    padding: 0;
    margin: 0 1em 1em;
}

ul.elfinder-help-integrations a {
    text-decoration: none;
}

ul.elfinder-help-integrations a:hover {
    text-decoration: underline;
}

.elfinder-help-debug {
    height: 100%;
    padding: 0;
    margin: 0;
    overflow: none;
    border: none;
}

.elfinder-help-debug .ui-tabs-panel {
    padding: 0;
    margin: 0;
    overflow: auto;
}

.elfinder-help-debug fieldset {
    margin-bottom: 10px;
    border-color: #778899;
    border-radius: 10px;
}

.elfinder-help-debug legend {
    font-size: 1.2em;
    font-weight: bold;
    color: #2e8b57;
}

.elfinder-help-debug dl {
    margin: 0;
}

.elfinder-help-debug dt {
    color: #778899;
}

.elfinder-help-debug dt:before {
    content: "[";
}

.elfinder-help-debug dt:after {
    content: "]";
}

.elfinder-help-debug dd {
    margin-left: 1em;
}

.elfinder-help-debug dd span {
    /*font-size: 1.2em;*/
}

/********************** COMMAND "PREFERENCE" ****************************/
.elfinder-dialog .elfinder-preference .ui-tabs-nav {
    margin-bottom: 1px;
    height: auto;
}

/* fix tabs */
.elfinder-preference .ui-tabs-panel {
    padding: 10px 10px 0;
    overflow: auto;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
}

.elfinder-preference a.ui-state-hover,
.elfinder-preference label.ui-state-hover {
    border: none;
}

.elfinder-preference dl {
    width: 100%;
    display: inline-block;
    margin: .5em 0;
}

.elfinder-preference dt {
    display: block;
    width: 200px;
    clear: left;
    float: left;
    max-width: 50%;
}

.elfinder-rtl .elfinder-preference dt {
    clear: right;
    float: right;
}

.elfinder-preference dd {
    margin-bottom: 1em;
}

.elfinder-preference dt label {
    cursor: pointer;
}

.elfinder-preference dd label,
.elfinder-preference dd input[type=checkbox] {
    white-space: nowrap;
    display: inline-block;
    cursor: pointer;
}

.elfinder-preference dt.elfinder-preference-checkboxes {
    width: 100%;
    max-width: none;
}

.elfinder-preference dd.elfinder-preference-checkboxes {
    padding-top: 3ex;
}

.elfinder-preference select {
    max-width: 100%;
}

.elfinder-preference dd.elfinder-preference-iconSize .ui-slider {
    width: 50%;
    max-width: 100px;
    display: inline-block;
    margin: 0 10px;
}

.elfinder-preference button {
    margin: 0 16px;
}

.elfinder-preference button + button {
    margin: 0 -10px;
}

.elfinder-preference .elfinder-preference-taball .elfinder-reference-hide-taball {
    display: none;
}

.elfinder-preference-theme fieldset {
    margin-bottom: 10px;
}

.elfinder-preference-theme legend a {
    font-size: 1.8em;
    text-decoration: none;
    cursor: pointer;
}

.elfinder-preference-theme dt {
    width: 20%;
    word-break: break-all;
}

.elfinder-preference-theme dt:after {
    content: " :";
}

.elfinder-preference-theme dd {
    margin-inline-start: 20%;
}

.elfinder-preference img.elfinder-preference-theme-image {
    display: block;
    margin-left: auto;
    margin-right: auto;
    max-width: 90%;
    max-height: 200px;
    cursor: pointer;
}

.elfinder-preference-theme-btn {
    text-align: center;
}

.elfinder-preference-theme button.elfinder-preference-theme-default {
    display: inline;
    margin: 0 10px;
    font-size: 8pt;
}

/********************** COMMAND "INFO" ****************************/
.elfinder-rtl .elfinder-info-title .elfinder-cwd-icon:before {
    right: 33px;
    left: auto;
}

.elfinder-info-title .elfinder-cwd-icon.elfinder-cwd-bgurl:after {
    content: none;
}

/********************** COMMAND "UPLOAD" ****************************/
.elfinder-upload-dialog-wrapper .elfinder-upload-dirselect {
    position: absolute;
    bottom: 2px;
    width: 16px;
    height: 16px;
    padding: 10px;
    border: none;
    overflow: hidden;
    cursor: pointer;
}

.elfinder-ltr .elfinder-upload-dialog-wrapper .elfinder-upload-dirselect {
    left: 2px;
}

.elfinder-rtl .elfinder-upload-dialog-wrapper .elfinder-upload-dirselect {
    right: 2px;
}

/********************** COMMAND "RM" ****************************/
.elfinder-ltr .elfinder-rm-title .elfinder-cwd-icon:before {
    left: 38px;
}

.elfinder-rtl .elfinder-rm-title .elfinder-cwd-icon:before {
    right: 86px;
    left: auto;
}

.elfinder-rm-title .elfinder-cwd-icon.elfinder-cwd-bgurl:after {
    content: none;
}

/********************** COMMAND "RENAME" ****************************/
.elfinder-rename-batch div {
    margin: 5px 8px;
}

.elfinder-rename-batch .elfinder-rename-batch-name input {
    width: 100%;
    font-size: 1.6em;
}

.elfinder-rename-batch-type {
    text-align: center;
}

.elfinder-rename-batch .elfinder-rename-batch-type label {
    margin: 2px;
    font-size: .9em;
}

.elfinder-rename-batch-preview {
    padding: 0 8px;
    font-size: 1.1em;
    min-height: 4ex;
}
.CodeMirror {
    background: inherit !important;
}

