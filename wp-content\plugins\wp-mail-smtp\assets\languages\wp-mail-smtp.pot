msgid ""
msgstr ""
"Project-Id-Version: WP Mail SMTP 4.2.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/wp-mail-smtp\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-11-04T19:09:31+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.7.1\n"
"X-Domain: wp-mail-smtp\n"

#. Plugin Name of the plugin
#. Author of the plugin
#: src/Admin/Area.php:307
#: src/Admin/Area.php:308
#: src/Admin/Area.php:365
#: src/Admin/Area.php:366
#: src/Admin/DashboardWidget.php:164
#: src/Admin/DashboardWidget.php:562
#: src/SiteHealth.php:43
msgid "WP Mail SMTP"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://wpmailsmtp.com/"
msgstr ""

#. Description of the plugin
msgid "Reconfigures the <code>wp_mail()</code> function to use Gmail/Mailgun/SendGrid/SMTP instead of the default <code>mail()</code> and creates an options page to manage the settings."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:5
msgid "Error Message:"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:8
#: src/Admin/Pages/DebugEventsTab.php:152
#: src/Connect.php:58
msgid "OK"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:11
#: src/Admin/Area.php:521
#: src/Admin/Pages/DebugEventsTab.php:153
msgid "Heads up!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:14
msgid "Please fill out all the required fields to continue."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:17
msgid "Settings Updated"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:20
msgid "Could Not Save Changes"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:23
msgid "Return to Mailer Settings"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:26
msgid "Whoops, we found an issue!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:29
msgid "It looks like something went wrong..."
msgstr ""

#. Translators: Current PHP version and recommended PHP version.
#: assets/languages/wp-mail-smtp-vue.php:33
msgid "WP Mail SMTP has detected that your site is running an outdated, insecure version of PHP (%1$s), which could be putting your site at risk for being hacked. WordPress stopped supporting your PHP version in April, 2019. Updating to the recommended version (PHP %2$s) only takes a few minutes and will make your website significantly faster and more secure."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:36
msgid "Yikes! PHP Update Required"
msgstr ""

#. Translators: Current PHP version and recommended PHP version.
#: assets/languages/wp-mail-smtp-vue.php:40
msgid "WP Mail SMTP has detected that your site is running an outdated, insecure version of PHP (%1$s). Some mailers require at least PHP version 5.6. Updating to the recommended version (PHP %2$s) only takes a few minutes and will make your website significantly faster and more secure."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:43
msgid "Yikes! WordPress Update Required"
msgstr ""

#. Translators: Current WordPress version.
#: assets/languages/wp-mail-smtp-vue.php:47
msgid "WP Mail SMTP has detected that your site is running an outdated version of WordPress (%s). WP Mail SMTP requires at least WordPress version 4.9."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:50
msgid "Return to Plugin Settings"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:53
msgid "It looks like we can't load oAuth redirect."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:56
msgid "It looks like we can't load existing settings."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:60
msgid "It looks like we can't load oAuth connected data."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:63
msgid "It looks like we can't remove oAuth connection."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:66
msgid "It looks like we can't retrieve the Amazon SES Identities."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:70
msgid "It looks like we can't register the Amazon SES Identity."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:73
msgid "It looks like we can't perform the mailer configuration check."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:76
msgid "It looks like we can't send the feedback."
msgstr ""

#. Translators: Error status and error text.
#: assets/languages/wp-mail-smtp-vue.php:82
msgid "%1$s, %2$s"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:86
msgid "You appear to be offline."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:89
msgid "It looks like we can't save the settings."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:92
msgid "Network error encountered. Settings not saved."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:95
msgid "It looks like we can't import the plugin settings."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:98
msgid "Network error encountered. SMTP plugin import failed!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:101
msgid "It looks like we can't load authentication details."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:104
msgid "It looks like we can't remove OAuth connection."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:107
msgid "It looks like we can't load the settings."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:110
msgid "It looks like we can't retrieve Amazon SES Identities."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:113
msgid "Can't retrieve Amazon SES Identities."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:116
msgid "Can't register the Amazon SES Identity"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:119
msgid "It looks like the plugin installation failed!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:122
msgid "It looks like we can't install the plugin."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:125
msgid "You appear to be offline. Plugin not installed."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:128
msgid "Can't fetch plugins information."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:131
msgid "It looks like we can't fetch plugins information."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:134
msgid "You appear to be offline. Plugin information not retrieved."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:137
msgid "Welcome to the WP Mail SMTP Setup Wizard!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:140
msgid "We’ll guide you through each step needed to get WP Mail SMTP fully set up on your site."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:143
msgid "Let's Get Started"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:146
#: src/Admin/SetupWizard.php:294
msgid "Go back to the Dashboard"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:149
msgid "Which email features do you want to enable?"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:152
msgid "Make sure you're getting the most out of WP Mail SMTP. Just check all of the features you'd like to use, and we'll go ahead and enable those for you."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:156
msgid "Save and Continue"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:160
msgid "Previous Step"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:163
msgid "The following plugin will be installed for free: WPForms"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:166
msgid "Improved Email Deliverability"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:169
msgid "Ensure your emails are sent successfully and reliably."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:172
msgid "Email Error Tracking"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:175
msgid "Easily spot errors causing delivery issues."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:178
msgid "Smart Contact Form"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:181
msgid "Create beautiful contact forms with just a few clicks."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:184
msgid "Detailed Email Logs"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:187
msgid "Keep records of every email that's sent out from your website."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:190
msgid "Instant Email Alerts"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:193
msgid "Get notifications via email, SMS, Slack, or webhook when emails fail to send."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:196
msgid "Complete Email Reports"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:199
msgid "See the delivery status, track opens and clicks, and create deliverability graphs."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:202
msgid "Weekly Email Summary"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:205
msgid "Get statistics about emails you've sent."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:208
msgid "Manage Default Notifications"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:211
msgid "Control which email notifications your WordPress site sends."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:214
msgid "Multisite Network Settings"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:217
msgid "Save time with powerful WordPress Multisite controls."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:220
msgid "Private API Key"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:223
#: src/Providers/Mailgun/Options.php:106
msgid "Domain Name"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:226
#: src/Providers/Mailgun/Options.php:131
#: src/Providers/SparkPost/Options.php:131
msgid "Region"
msgstr ""

#. Translators: Link to the Mailgun API settings.
#: assets/languages/wp-mail-smtp-vue.php:230
msgid "%1$sFollow this link%2$s to get a Private API Key from Mailgun."
msgstr ""

#. Translators: Link to the Mailgun Domain settings.
#: assets/languages/wp-mail-smtp-vue.php:234
msgid "%1$sFollow this link%2$s to get a Domain Name from Mailgun."
msgstr ""

#. Translators: Link to the Mailgun documentation.
#: assets/languages/wp-mail-smtp-vue.php:238
msgid "Define which endpoint you want to use for sending messages. If you are operating under EU laws, you may be required to use EU region. %1$sMore information%2$s on Mailgun.com."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:241
#: src/Admin/ConnectionSettings.php:127
#: src/Admin/Pages/ExportTab.php:111
msgid "From Name"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:244
#: src/Admin/ConnectionSettings.php:146
msgid "Force From Name"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:248
#: src/Admin/ConnectionSettings.php:70
#: src/Admin/Pages/SmartRoutingTab.php:180
#: src/Admin/Pages/SmartRoutingTab.php:210
msgid "From Email"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:252
#: src/Admin/ConnectionSettings.php:95
msgid "Force From Email"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:255
msgid "If enabled, the From Name setting above will be used for all emails, ignoring values set by other plugins."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:259
msgid "If enabled, the From Email setting above will be used for all emails, ignoring values set by other plugins."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:262
#: src/Admin/ConnectionSettings.php:139
msgid "The name that emails are sent from."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:266
#: src/Admin/ConnectionSettings.php:82
msgid "The email address that emails are sent from."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:269
msgid "Read how to set up Mailgun"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:272
#: src/Providers/Mailgun/Options.php:141
#: src/Providers/SparkPost/Options.php:141
msgid "US"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:275
#: src/Providers/Mailgun/Options.php:150
#: src/Providers/SparkPost/Options.php:150
msgid "EU"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:278
msgid "Close and exit the Setup Wizard"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:281
#: src/Providers/Mailjet/Options.php:93
#: src/Providers/PepipostAPI/Options.php:96
#: src/Providers/Sendgrid/Options.php:68
#: src/Providers/Sendinblue/Options.php:112
#: src/Providers/Sendlayer/Options.php:102
#: src/Providers/SMTP2GO/Options.php:94
#: src/Providers/SMTPcom/Options.php:107
#: src/Providers/SparkPost/Options.php:88
msgid "API Key"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:284
#: src/Providers/Sendgrid/Options.php:116
#: src/Providers/Sendinblue/Options.php:152
msgid "Sending Domain"
msgstr ""

#. Translators: Link to the Sendgrid API settings.
#: assets/languages/wp-mail-smtp-vue.php:288
msgid "%1$sFollow this link%2$s to get an API Key for Sendgrid."
msgstr ""

#. Translators: italic styling.
#: assets/languages/wp-mail-smtp-vue.php:292
msgid "To send emails you will need only a %1$sMail Send%2$s access level for this API key."
msgstr ""

#. Translators: Link to the Sendgrid doc page on wpmailsmtp.com.
#: assets/languages/wp-mail-smtp-vue.php:296
msgid "Please input the sending domain/subdomain you configured in your SendGrid dashboard. More information can be found in our %1$sSendGrid documentation%2$s"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:299
msgid "Read how to set up Sendgrid"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:302
#: src/Admin/SetupWizard.php:477
msgid "WP Mail SMTP logo"
msgstr ""

#. Translators: %1$s - the number of current step, %2$s - number of all steps.
#: assets/languages/wp-mail-smtp-vue.php:306
msgid "Step %1$s of %2$s"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:309
msgid "Configure Mailer Settings"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:312
msgid "Below, we'll show you all of the settings required to set up this mailer."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:317
msgid "This setting is already configured with the WP Mail SMTP constant. To change it, please edit or remove the <code></code> constant in your <code>wp-config.php</code> file."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:320
msgid "Copy input value"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:323
msgid "Copied!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:326
msgid "The value entered does not match the required format"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:329
msgid "Checking Mailer Configuration"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:332
msgid "We're running some tests in the background to make sure everything is set up properly."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:335
msgid "Checking mailer configuration image"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:338
msgid "Whoops, looks like things aren’t configured properly."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:341
msgid "We just tried to send a test email, but something prevented that from working. To see more details about the issue we detected, as well as our suggestions to fix it, please start troubleshooting."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:344
msgid "Start Troubleshooting"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:347
msgid "Send us Feedback"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:350
msgid "Finish Setup"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:353
msgid "Import data from your current plugins"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:356
msgid "We have detected other SMTP plugins installed on your website. Select which plugin's data you would like to import to WP Mail SMTP."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:359
msgid "Import Data and Continue"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:362
msgid "Skip this Step"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:365
msgid "Easy WP SMTP"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:368
msgid "FluentSMTP"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:371
msgid "Post SMTP Mailer"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:374
msgid "SMTP Mailer"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:377
msgid "WP SMTP"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:380
msgid "Help Improve WP Mail SMTP + Smart Recommendations"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:383
msgid "Get helpful suggestions from WP Mail SMTP on how to optimize your email deliverability and grow your business."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:386
msgid "Your Email Address"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:389
msgid "Your email is needed, so you can receive recommendations."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:392
msgid "Help make WP Mail SMTP better for everyone"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:395
msgid "Yes, count me in"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:398
#: src/Admin/Pages/MiscTab.php:254
msgid "By allowing us to track usage data we can better help you because we know with which WordPress configurations, themes and plugins we should test."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:401
#: src/Providers/Postmark/Options.php:90
msgid "Server API Token"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:404
#: src/Providers/Postmark/Options.php:128
msgid "Message Stream ID"
msgstr ""

#. Translators: Link to the Postmark API settings.
#: assets/languages/wp-mail-smtp-vue.php:408
msgid "%1$sFollow this link%2$s to get a Server API Token for Postmark."
msgstr ""

#. Translators: Link to the Postmark Message Stream ID settings.
#: assets/languages/wp-mail-smtp-vue.php:412
msgid "Message Stream ID is <strong>optional</strong>. By default <strong>outbound</strong> (Default Transactional Stream) will be used. More information can be found in our %1$sPostmark documentation%2$s."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:415
msgid "Read how to set up Postmark"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:418
msgid "Configure Email Logs"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:421
msgid "Enable these powerful logging features for more control of your WordPress emails."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:424
msgid "Store the content for all sent emails"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:427
msgid "This option must be enabled if you'd like to be able to resend emails. Please be aware that all email content will be stored in your WordPress database. This may include sensitive data, passwords, and personal details."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:430
msgid "Save file attachments sent from WordPress"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:433
msgid "All file attachments sent from your site will be saved to the WordPress Uploads folder. Please note that this may reduce available disk space on your server."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:436
msgid "Track when an email is opened"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:439
msgid "See which emails were opened by the recipients. Email open tracking works with emails that are sent in HTML format."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:442
msgid "Track when a link in an email is clicked"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:445
msgid "See which links were clicked in emails sent from your WordPress site. Click tracking works with emails that are sent in HTML format."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:448
msgid "The data center location used by your Zoho account."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:451
#: src/Providers/Gmail/Options.php:113
msgid "Client ID"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:454
#: src/Providers/Gmail/Options.php:128
msgid "Client Secret"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:457
msgid "Redirect URI"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:460
#: src/Providers/Gmail/Options.php:180
msgid "Authorization"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:463
msgid "Read how to set up Zoho Mail"
msgstr ""

#. Translators: Link to the SendLayer API settings.
#: assets/languages/wp-mail-smtp-vue.php:467
msgid "%1$sFollow this link%2$s to get an API Key for SendLayer."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:470
#: src/Providers/Sendlayer/Options.php:65
msgid "Get Started with SendLayer"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:473
msgid "Read how to set up SendLayer"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:476
#: src/Providers/SMTPcom/Options.php:146
msgid "Sender Name"
msgstr ""

#. Translators: Link to the SMTP.com API settings.
#: assets/languages/wp-mail-smtp-vue.php:480
msgid "%1$sFollow this link%2$s to get an API Key for SMTP.com."
msgstr ""

#. Translators: Link to the SMTP.com Senders/Channel settings.
#: assets/languages/wp-mail-smtp-vue.php:484
msgid "%1$sFollow this link%2$s to get a Sender Name for SMTP.com."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:487
#: src/Providers/SMTPcom/Options.php:69
msgid "Get Started with SMTP.com"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:490
msgid "Read how to set up SMTP.com"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:493
#: src/Providers/Sendinblue/Options.php:68
#: src/Providers/SMTPcom/Options.php:74
msgid "Transparency and Disclosure"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:496
#: src/Providers/SMTPcom/Options.php:76
msgid "We believe in full transparency. The SMTP.com links above are tracking links as part of our partnership with SMTP (j2 Global). We can recommend just about any SMTP service, but we only recommend products that we believe will add value to our users."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:499
msgid "How was your WP Mail SMTP setup experience?"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:502
msgid "Our goal is to make your SMTP setup as simple and straightforward as possible. We'd love to know how this process went for you!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:505
msgid "Congrats, you’ve successfully set up WP Mail SMTP!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:508
msgid "Here’s what to do next:"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:511
msgid "Check out our other free WordPress plugins:"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:514
msgid "Upgrade to Unlock Powerful SMTP Features"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:517
msgid "Upgrade to WP Mail SMTP Pro to unlock more awesome features and experience why WP Mail SMTP is used by over 4,000,000 websites."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:520
msgid "Upgrade to Pro Today"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:523
#: src/Admin/Pages/TestTab.php:253
#: src/SiteHealth.php:352
msgid "Send a Test Email"
msgstr ""

#. Translators: Different bold styles and discount value (%5$s).
#: assets/languages/wp-mail-smtp-vue.php:527
msgid "%1$sBonus:%2$s You can upgrade to the Pro plan and %3$ssave %5$s today%4$s, automatically applied at checkout."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:530
msgid "Star icon"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:533
msgid "Thanks for the feedback!"
msgstr ""

#. Translators: %1$s and %2$s are HTML bold tags; %3$s is a new line HTML tag; %4$s are 5 golden star icons in HTML.
#: assets/languages/wp-mail-smtp-vue.php:537
msgid "Help us spread the word %1$sby giving WP Mail SMTP a 5-star rating %3$s(%4$s) on WordPress.org%2$s. Thanks for your support and we look forward to bringing you more awesome features."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:540
msgid "Rate on WordPress.org"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:543
msgid "What could we do to improve?"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:546
msgid "We're sorry things didn't go smoothly for you, and want to keep improving. Please let us know any specific parts of this process that you think could be better. We really appreciate any details you're willing to share!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:549
msgid "Yes, I give WP Mail SMTP permission to contact me for any follow up questions."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:552
msgid "Submit Feedback"
msgstr ""

#. Translators: Link to the Sendinblue API settings.
#: assets/languages/wp-mail-smtp-vue.php:556
msgid "%1$sFollow this link%2$s to get an API Key for Sendinblue."
msgstr ""

#. Translators: Link to the Sendinblue doc page on wpmailsmtp.com.
#: assets/languages/wp-mail-smtp-vue.php:560
msgid "Please input the sending domain/subdomain you configured in your Sendinblue dashboard. More information can be found in our %1$sSendinblue documentation%2$s"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:563
msgid "Get Started with Sendinblue"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:566
msgid "Read how to set up Sendinblue"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:569
msgid "We believe in full transparency. The Sendinblue links above are tracking links as part of our partnership with Sendinblue. We can recommend just about any SMTP service, but we only recommend products that we believe will add value to our users."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:572
#: src/Providers/OptionsAbstract.php:227
msgid "SMTP Host"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:575
#: src/Providers/OptionsAbstract.php:241
msgid "Encryption"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:578
#: src/Providers/OptionsAbstract.php:281
msgid "SMTP Port"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:581
#: src/Providers/OptionsAbstract.php:295
msgid "Auto TLS"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:584
msgid "Enable Auto TLS"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:587
#: src/Providers/OptionsAbstract.php:309
msgid "By default, TLS encryption is automatically used if the server supports it (recommended). In some cases, due to server misconfigurations, this can cause issues and may need to be disabled."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:590
#: src/Providers/OptionsAbstract.php:317
msgid "Authentication"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:593
#: src/Admin/Pages/TestTab.php:860
msgid "Enable Authentication"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:596
#: src/Providers/OptionsAbstract.php:336
msgid "SMTP Username"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:599
#: src/Providers/OptionsAbstract.php:350
msgid "SMTP Password"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:602
#: src/Providers/OptionsAbstract.php:273
msgid "For most servers TLS is the recommended option. If your SMTP provider offers both SSL and TLS options, we recommend using TLS."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:605
#: src/Admin/Pages/SettingsTab.php:371
#: src/Providers/OptionsAbstract.php:251
msgid "None"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:608
#: src/Providers/OptionsAbstract.php:260
msgid "SSL"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:611
#: src/Providers/OptionsAbstract.php:269
msgid "TLS"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:614
msgid "Access Key ID"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:617
msgid "Secret Access Key"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:620
msgid "SES Identities"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:623
msgid "Please select the Amazon SES API region which is the closest to where your website is hosted. This can help to decrease network latency between your site and Amazon SES, which will speed up email sending."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:626
msgid "Read how to set up Amazon SES"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:629
msgid "Amazon SES requires an SSL certificate, and so is not currently compatible with your site. Please contact your host to request a SSL certificate, or check out "
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:632
msgid "WPBeginner's tutorial on how to set up SSL"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:635
msgid "If you'd prefer not to set up SSL, or need an SMTP solution in the meantime, please go back and select a different mailer option."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:638
msgid "Choose Your SMTP Mailer"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:641
#: src/Admin/ConnectionSettings.php:202
#: src/Admin/Pages/ExportTab.php:125
msgid "Mailer"
msgstr ""

#. Translators: Link to the SMTP Mailer docs page.
#: assets/languages/wp-mail-smtp-vue.php:645
msgid "Which mailer would you like to use to send emails? Not sure which mailer to choose? Check out our %1$scomplete mailer guide%2$s for details on each option."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:648
msgid "I Understand, Continue"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:651
msgid "Choose a Different Mailer"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:654
msgid "Recommended Mailers"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:657
msgid "Your mailer is already configured in a WP Mail SMTP constant, so the options below have been disabled. To change your mailer, please edit or remove the <code>WPMS_MAILER</code> constant in your <code>wp-config.php</code> file."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:660
msgid "is a PRO Feature"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:663
msgid "Microsoft 365 / Outlook"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:666
msgid "Successful Upgrade!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:669
msgid "Upgrade Failed!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:672
msgid "Enter your WP Mail SMTP License Key"
msgstr ""

#. Translators: %1$s and %2$s are bold tags.
#: assets/languages/wp-mail-smtp-vue.php:676
msgid "You're currently using %1$sWP Mail SMTP Lite%2$s - no license needed. Enjoy!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:679
msgid "Continue"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:682
msgid "Would you like to purchase the following features now?"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:685
msgid "These features are available as part of WP Mail SMTP Pro plan."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:688
msgid "Purchase Now"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:691
msgid "I'll do it later"
msgstr ""

#. Translators: Link to the WPMailSMTP.com pricing page.
#: assets/languages/wp-mail-smtp-vue.php:695
msgid "To unlock selected features, %1$sUpgrade to Pro%2$s and enter your license key below."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:698
msgid "Enhanced Weekly Email Summary"
msgstr ""

#. Translators: bold HTML tags.
#: assets/languages/wp-mail-smtp-vue.php:702
msgid "Already purchased? Enter your license key below to connect with %1$sWP Mail SMTP Pro%2$s!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:705
msgid "Enter your license key below to unlock plugin updates!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:708
msgid "Verify License Key"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:711
#: src/Admin/Pages/SettingsTab.php:187
msgid "Connect"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:714
msgid "The License Key format is incorrect. Please enter a valid key and try again."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:717
msgid "Your license was successfully verified! You are ready for the next step."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:720
msgid "Pro badge"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:723
msgid "License key input"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:726
msgid "Paste your license key here"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:729
msgid "Successful Verification!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:732
msgid "Verification Error!"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:735
msgid "Authorized Redirect URI"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:738
msgid "Select which email address you would like to send your emails from."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:741
msgid "Read how to set up the Gmail mailer"
msgstr ""

#. Translators: Link to the SparkPost documentation.
#: assets/languages/wp-mail-smtp-vue.php:745
msgid "Select your SparkPost account region. %1$sMore information%2$s on SparkPost."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:748
msgid "Read how to set up SparkPost"
msgstr ""

#. Translators: Link to the SparkPost Account API section.
#: assets/languages/wp-mail-smtp-vue.php:752
msgid "%1$sFollow this link%2$s to get an API Key for SparkPost."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:755
msgid "Application ID"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:758
msgid "Application Password"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:761
msgid "Read how to set up Microsoft Outlook / 365"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:764
msgid "Outlook / 365 requires an SSL certificate, and so is not currently compatible with your site. Please contact your host to request a SSL certificate, or check out "
msgstr ""

#. Translators: Minimum and maximum number that can be used.
#: assets/languages/wp-mail-smtp-vue.php:768
msgid "Please enter a value between %1$s and %2$s"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:771
msgid "Value has to be a round number"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:774
#: src/Admin/Area.php:167
msgid "There was an error while processing the authentication request. Please try again."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:777
msgid "There was an error while processing the authentication request. Please recheck your Client ID and Client Secret and try again."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:780
#: src/Admin/Area.php:153
msgid "There was an error while processing the authentication request. The nonce is invalid. Please try again."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:783
msgid "There was an error while processing the authentication request. The authorization code is missing. Please try again."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:786
msgid "There was an error while processing the authentication request. Please recheck your Region, Client ID and Client Secret and try again."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:789
#: src/Admin/Area.php:190
msgid "You have successfully linked the current site with your Google API project. Now you can start sending emails through Gmail."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:792
msgid "You have successfully linked the current site with your Microsoft API project. Now you can start sending emails through Outlook."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:795
msgid "You have successfully linked the current site with your Zoho Mail API project. Now you can start sending emails through Zoho Mail."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:798
msgid "Successful Authorization"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:801
msgid "Authorization Error!"
msgstr ""

#. Translators: name of the oAuth provider (Google, Microsoft, ...).
#: assets/languages/wp-mail-smtp-vue.php:805
msgid "Connect to %s"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:808
msgid "Before continuing, you'll need to allow this plugin to send emails using your %s account."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:811
#: src/Providers/Gmail/Options.php:217
msgid "Remove OAuth Connection"
msgstr ""

#. Translators: link to the Google documentation page.
#: assets/languages/wp-mail-smtp-vue.php:815
msgid "If you want to use a different From Email address you can setup a Google email alias. %1$sFollow these instructions%2$s, then select the alias in the From Email section below."
msgstr ""

#. Translators: name of the oAuth provider (Google, Microsoft, ...).
#: assets/languages/wp-mail-smtp-vue.php:819
msgid "Removing this OAuth connection will give you the ability to redo the OAuth connection or connect to different %s account."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:822
msgid "Connected as"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:825
#: src/Admin/Area.php:146
msgid "There was an error while processing the authentication request. The state key is invalid. Please try again."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:828
msgid "Please enter a domain"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:831
msgid "Please enter a valid email address"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:834
msgid "Enter the domain name to verify it on Amazon SES and generate the required DNS CNAME records."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:837
msgid "Enter a valid email address. A verification email will be sent to the email address you entered."
msgstr ""

#. Translators: Email address.
#: assets/languages/wp-mail-smtp-vue.php:841
msgid "Please check the inbox of <b>%s</b> for a confirmation email."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:844
msgid "Verify Email"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:847
msgid "No registered domains or emails."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:850
msgid "You will not be able to send emails until you verify at least one domain or email address for the selected Amazon SES Region."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:853
msgid "View DNS"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:856
msgid "Resend"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:859
msgid "Here are the domains and email addresses that have been verified and can be used as the From Email."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:862
msgid "Verify SES Identity"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:865
msgid "Add New SES Identity"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:868
msgid "Name"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:871
msgid "Value"
msgstr ""

#. Translators: Link to Amazon SES documentation.
#: assets/languages/wp-mail-smtp-vue.php:875
msgid "Please add these CNAME records to your domain's DNS settings. For information on how to add CNAME DNS records, please refer to the %1$sAmazon SES documentation%2$s."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:878
msgid "Verify Domain"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:881
msgid "Verify Email Address"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:884
msgid "Install"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:887
msgid "Installed"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:890
#: src/Admin/Area.php:646
#: src/Admin/Pages/AboutTab.php:225
msgid "Activated"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:893
#: src/Core.php:1104
msgid "Loading"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:896
msgid "WordPress SEO Toolkit"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:899
msgid "Improve your website's SEO ranking with our toolkit."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:902
msgid "The following plugin will be installed for free:"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:905
msgid "Read how to set up SendGrid"
msgstr ""

#. Translators: Link to the Sendinblue API settings.
#: assets/languages/wp-mail-smtp-vue.php:909
msgid "%1$sFollow this link%2$s to get an API Key for Brevo."
msgstr ""

#. Translators: Link to the Sendinblue doc page on wpmailsmtp.com.
#: assets/languages/wp-mail-smtp-vue.php:913
msgid "Please input the sending domain/subdomain you configured in your Brevo dashboard. More information can be found in our %1$sBrevo documentation%2$s"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:916
msgid "Get Started with Brevo"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:919
msgid "Read how to set up Brevo"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:922
msgid "We believe in full transparency. The Brevo links above are tracking links as part of our partnership with Brevo. We can recommend just about any SMTP service, but we only recommend products that we believe will add value to our users."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:925
#: src/Providers/Mailgun/Options.php:60
msgid "Mailgun API Key"
msgstr ""

#. Translators: Link to the Mailgun API settings.
#: assets/languages/wp-mail-smtp-vue.php:929
msgid "%1$sFollow this link%2$s to get a Mailgun API Key. Generate a key in the \"Mailgun API Keys\" section."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:932
msgid "Improve your SEO rankings with the All in One SEO plugin."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:935
#: src/Providers/Gmail/Options.php:91
msgid "One-Click Setup"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:938
#: src/Providers/Gmail/Options.php:103
msgid "Provides a quick and easy way to connect to Google that doesn't require creating your own app."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:941
msgid "Enabled"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:944
msgid "Disabled"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:947
msgid "Sign in with Google"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:950
#: src/Admin/Area.php:181
msgid "There was an error while processing the authentication request."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:953
msgid "Now you can continue mailer configuration."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:956
msgid "Gmail mailer requires a valid Easy WP SMTP Pro license. Please activate your license key."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:959
msgid "One-Click Setup for Google Mailer requires an active license. Emails are currently not being sent."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:962
msgid "One-Click Setup for Google Mailer requires an active license. Verify your license to proceed with this one-click setup."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:965
msgid "One-Click Setup for Google Mailer requires an active license. Verify your license to proceed with this one-click setup, please."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:968
msgid "One-Click Setup for Google Mailer requires an active license. Verify your license to proceed with this One-Click Setup, please."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:971
msgid "You have successfully connected your site with your Gmail account. This site will now send emails via your Gmail account."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:974
msgid "You have successfully connected your site with your Gmail account. Now you can start sending emails through Gmail."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:977
msgid "The email address that emails are sent from. You can use only connected email address or "
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:980
msgid "The email address that emails are sent from. You can use only connected email address or his alias."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:983
msgid "The email address that emails are sent from. The email address that emails are sent from. You can use only the connected email address or its alias."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:986
msgid "The email address that emails are sent from. You can use only the connected email address or its alias."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:989
#: src/Admin/Area.php:557
msgid "One-Click Setup for Google Mailer <br> is a Pro Feature"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:992
#: src/Admin/Area.php:558
msgid "We're sorry, One-Click Setup for Google Mailer is not available on your plan. Please upgrade to the Pro plan to unlock all these awesome features."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:995
msgid "Install the WPForms plugin and create beautiful contact forms with just a few clicks."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:998
#: src/Providers/Mailjet/Options.php:134
msgid "Secret Key"
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:1001
msgid "Follow this link to get an API key and Secret key from Mailjet: %1$sAPI Key Management%2$s."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:1004
msgid "Read how to set up Mailjet"
msgstr ""

#. Translators: Link to the SMTP2GO API settings.
#: assets/languages/wp-mail-smtp-vue.php:1008
msgid "Generate an API key on the Sending → API Keys page in your %1$scontrol panel%2$s."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:1011
msgid "Read how to set up SMTP2GO"
msgstr ""

#. Translators: Link to the SMTP2GO API settings.
#: assets/languages/wp-mail-smtp-vue.php:1015
msgid "Follow this link to get the API key from Mailjet: %1$sAPI Key Management%2$s."
msgstr ""

#: assets/languages/wp-mail-smtp-vue.php:1018
msgid "Follow this link to get the Secret key from Mailjet: %1$sAPI Key Management%2$s."
msgstr ""

#. translators: %s - error code, returned by Google API.
#: src/Admin/Area.php:160
msgid "There was an error while processing the authentication request: %s. Please try again."
msgstr ""

#: src/Admin/Area.php:174
msgid "There was an error while processing the authentication request. Please make sure that you have Client ID and Client Secret both valid and saved."
msgstr ""

#. translators: %s - Mailer anchor link.
#: src/Admin/Area.php:221
msgid "Thanks for using WP Mail SMTP! To complete the plugin setup and start sending emails, <strong>please select and configure your <a href=\"%s\">Mailer</a></strong>."
msgstr ""

#. translators: %s: Tools page URL.
#: src/Admin/Area.php:267
msgid "The Email Test tab was moved to <a href=\"%s\">WP Mail SMTP > Tools</a>."
msgstr ""

#: src/Admin/Area.php:318
#: src/Admin/Area.php:319
#: src/Admin/Area.php:1278
msgid "Settings"
msgstr ""

#: src/Admin/Area.php:327
#: src/Admin/Area.php:328
#: src/Admin/Pages/Logs.php:48
#: src/Admin/Pages/LogsTab.php:51
#: src/Admin/Pages/LogsTab.php:143
#: src/Admin/Pages/VersusTab.php:176
msgid "Email Log"
msgstr ""

#: src/Admin/Area.php:348
#: src/Admin/Area.php:349
#: src/Admin/Area.php:535
#: src/Admin/SetupWizard.php:239
#: src/Reports/Emails/Summary.php:313
msgid "Upgrade to Pro"
msgstr ""

#: src/Admin/Area.php:393
#: src/Admin/Area.php:399
#: src/Admin/Pages/SettingsTab.php:38
msgid "General"
msgstr ""

#: src/Admin/Area.php:407
msgid "Multisite"
msgstr ""

#: src/Admin/Area.php:408
msgid "Pro+ badge icon"
msgstr ""

#: src/Admin/Area.php:411
msgid "Simply enable network-wide settings and every site on your network will inherit the same SMTP settings. Save time and only configure your SMTP provider once."
msgstr ""

#: src/Admin/Area.php:418
msgid "Settings control"
msgstr ""

#: src/Admin/Area.php:424
msgid "Make the plugin settings global network-wide"
msgstr ""

#: src/Admin/Area.php:428
msgid "If disabled, each subsite of this multisite will have its own WP Mail SMTP settings page that has to be configured separately."
msgstr ""

#: src/Admin/Area.php:430
msgid "If enabled, these global settings will manage email sending for all subsites of this multisite."
msgstr ""

#: src/Admin/Area.php:437
#: src/Admin/Area.php:1270
#: src/Admin/DashboardWidget.php:274
#: src/Admin/FlyoutMenu.php:100
#: src/Admin/Pages/AdditionalConnectionsTab.php:115
#: src/Admin/Pages/AdditionalConnectionsTab.php:125
#: src/Admin/Pages/AlertsTab.php:90
#: src/Admin/Pages/AlertsTab.php:326
#: src/Admin/Pages/ControlTab.php:246
#: src/Admin/Pages/ControlTab.php:299
#: src/Admin/Pages/EmailReportsTab.php:137
#: src/Admin/Pages/EmailReportsTab.php:175
#: src/Admin/Pages/ExportTab.php:89
#: src/Admin/Pages/ExportTab.php:146
#: src/Admin/Pages/LogsTab.php:152
#: src/Admin/Pages/LogsTab.php:190
#: src/Admin/Pages/SmartRoutingTab.php:111
#: src/Admin/Pages/SmartRoutingTab.php:308
msgid "Upgrade to WP Mail SMTP Pro"
msgstr ""

#: src/Admin/Area.php:518
msgid "Are you sure you want to reset the current provider connection? You will need to immediately create a new one to be able to send emails."
msgstr ""

#: src/Admin/Area.php:519
msgid "Changes that you made to the settings are not saved!"
msgstr ""

#: src/Admin/Area.php:523
msgid "<p>The Default (PHP) mailer is currently selected, but is not recommended because in most cases it does not resolve email delivery issues.</p><p>Please consider selecting and configuring one of the other mailers.</p>"
msgstr ""

#: src/Admin/Area.php:526
#: src/Admin/PageAbstract.php:192
msgid "Save Settings"
msgstr ""

#: src/Admin/Area.php:527
#: src/Admin/Pages/DebugEventsTab.php:149
msgid "Cancel"
msgstr ""

#: src/Admin/Area.php:528
msgid "Warning icon"
msgstr ""

#: src/Admin/Area.php:533
msgid "%name% is a PRO Feature"
msgstr ""

#: src/Admin/Area.php:534
msgid "We're sorry, the %name% mailer is not available on your plan. Please upgrade to the PRO plan to unlock all these awesome features."
msgstr ""

#. Translators: %s - discount value $50.
#: src/Admin/Area.php:540
msgid "<strong>Bonus:</strong> WP Mail SMTP users get <span>%s off</span> regular price,<br>applied at checkout."
msgstr ""

#: src/Admin/Area.php:554
#: src/Admin/SetupWizard.php:258
msgid "Already purchased?"
msgstr ""

#: src/Admin/Area.php:561
msgid "Email Rate Limiting <br> is a Pro Feature"
msgstr ""

#: src/Admin/Area.php:562
msgid "We're sorry, Email Rate Limiting is not available on your plan. Please upgrade to the Pro plan to unlock all these awesome features."
msgstr ""

#: src/Admin/Area.php:645
#: src/Admin/Area.php:653
#: src/Admin/Pages/AboutTab.php:233
#: src/Connect.php:55
msgid "Activate"
msgstr ""

#: src/Admin/Area.php:647
#: src/Admin/Pages/AboutTab.php:222
msgid "Active"
msgstr ""

#: src/Admin/Area.php:648
#: src/Admin/Pages/AboutTab.php:230
msgid "Inactive"
msgstr ""

#: src/Admin/Area.php:649
msgid "Processing..."
msgstr ""

#: src/Admin/Area.php:650
#: src/Admin/Pages/AboutTab.php:251
msgid "Visit Site"
msgstr ""

#: src/Admin/Area.php:651
msgid "Could not install a plugin. Please download from WordPress.org and install manually."
msgstr ""

#: src/Admin/Area.php:652
msgid "Install and Activate"
msgstr ""

#: src/Admin/Area.php:654
msgid "Download"
msgstr ""

#. translators: %1$s - WP.org link; %2$s - same WP.org link.
#: src/Admin/Area.php:723
msgid "Please rate <strong>WP Mail SMTP</strong> <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">&#9733;&#9733;&#9733;&#9733;&#9733;</a> on <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">WordPress.org</a> to help us spread the word. Thank you from the WP Mail SMTP team!"
msgstr ""

#: src/Admin/Area.php:1175
msgid "WP Mail SMTP Pro related message was successfully dismissed."
msgstr ""

#: src/Admin/Area.php:1241
msgid "Educational notice for this mailer was successfully dismissed."
msgstr ""

#: src/Admin/Area.php:1271
msgid "Get WP Mail SMTP Pro"
msgstr ""

#: src/Admin/Area.php:1277
msgid "Go to WP Mail SMTP Settings page"
msgstr ""

#: src/Admin/Area.php:1285
msgid "Go to WPMailSMTP.com documentation page"
msgstr ""

#: src/Admin/Area.php:1286
#: src/Admin/Area.php:1470
msgid "Docs"
msgstr ""

#: src/Admin/Area.php:1448
msgid "Made with ♥ by the WP Mail SMTP team"
msgstr ""

#: src/Admin/Area.php:1459
msgid "Support"
msgstr ""

#: src/Admin/Area.php:1478
msgid "Free Plugins"
msgstr ""

#: src/Admin/ConnectionSettings.php:85
msgid "If you're using an email provider (Yahoo, Outlook.com, etc) this should be your email address for that account."
msgstr ""

#: src/Admin/ConnectionSettings.php:88
msgid "Please note that other plugins can change this, to prevent this use the setting below."
msgstr ""

#: src/Admin/ConnectionSettings.php:112
msgid "Current provider will automatically force From Email to be the email address that you use to set up the OAuth connection below."
msgstr ""

#: src/Admin/ConnectionSettings.php:116
msgid "If checked, the From Email setting above will be used for all emails, ignoring values set by other plugins."
msgstr ""

#: src/Admin/ConnectionSettings.php:163
msgid "Current provider doesn't support setting and forcing From Name. Emails will be sent on behalf of the account name used to setup the OAuth connection below."
msgstr ""

#: src/Admin/ConnectionSettings.php:167
msgid "If checked, the From Name setting above will be used for all emails, ignoring values set by other plugins."
msgstr ""

#: src/Admin/ConnectionSettings.php:177
msgid "Return Path"
msgstr ""

#: src/Admin/ConnectionSettings.php:193
msgid "Return Path indicates where non-delivery receipts - or bounce messages - are to be sent."
msgstr ""

#: src/Admin/ConnectionSettings.php:194
msgid "If unchecked, bounce messages may be lost."
msgstr ""

#: src/Admin/ConnectionSettings.php:246
msgid "Don't see what you're looking for?"
msgstr ""

#: src/Admin/ConnectionSettings.php:251
msgid "Suggest a Mailer"
msgstr ""

#: src/Admin/ConnectionSettings.php:279
msgid "Dismiss this notice"
msgstr ""

#: src/Admin/DashboardWidget.php:270
msgid "View Detailed Email Stats"
msgstr ""

#: src/Admin/DashboardWidget.php:271
msgid "Automatically keep track of every email sent from your WordPress site and view valuable statistics right here in your dashboard."
msgstr ""

#: src/Admin/DashboardWidget.php:351
#: src/Pro/Emails/Logs/Admin/SinglePage.php:263
msgid "Error icon"
msgstr ""

#. translators: %d - number of failed emails.
#: src/Admin/DashboardWidget.php:359
msgid "We detected %d failed email in the last 30 days."
msgid_plural "We detected %d failed emails in the last 30 days."
msgstr[0] ""
msgstr[1] ""

#. translators: %s - URL to WPMailSMTP.com.
#: src/Admin/DashboardWidget.php:370
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Upgrade to Pro</a> and get instant alert notifications when they fail."
msgstr ""

#: src/Admin/DashboardWidget.php:391
msgid "Dismiss email alert block"
msgstr ""

#: src/Admin/DashboardWidget.php:417
msgid "<b>NEW!</b> Enable Weekly Email Summaries"
msgstr ""

#: src/Admin/DashboardWidget.php:426
msgid "View Example"
msgstr ""

#: src/Admin/DashboardWidget.php:432
msgid "Weekly Email Summaries have been enabled"
msgstr ""

#. translators: %s - URL to WPMailSMTP.com.
#: src/Admin/DashboardWidget.php:455
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Upgrade to Pro</a> for detailed stats, email logs, and more!"
msgstr ""

#: src/Admin/DashboardWidget.php:481
msgid "Select timespan"
msgstr ""

#: src/Admin/DashboardWidget.php:483
msgid "All Time"
msgstr ""

#. translators: %d - Number of days.
#: src/Admin/DashboardWidget.php:488
msgid "Last %d day"
msgid_plural "Last %d days"
msgstr[0] ""
msgstr[1] ""

#: src/Admin/DashboardWidget.php:504
msgid "Confirmed Emails"
msgstr ""

#: src/Admin/DashboardWidget.php:505
msgid "Unconfirmed Emails"
msgstr ""

#: src/Admin/DashboardWidget.php:506
msgid "Failed Emails"
msgstr ""

#: src/Admin/DashboardWidget.php:511
msgid "Sent Emails"
msgstr ""

#: src/Admin/DashboardWidget.php:515
msgid "Select email type"
msgstr ""

#: src/Admin/DashboardWidget.php:517
msgid "All Emails"
msgstr ""

#: src/Admin/DashboardWidget.php:545
msgid "Graph Style"
msgstr ""

#: src/Admin/DashboardWidget.php:549
msgid "Bar"
msgstr ""

#: src/Admin/DashboardWidget.php:553
msgid "Line"
msgstr ""

#: src/Admin/DashboardWidget.php:558
msgid "Color Scheme"
msgstr ""

#: src/Admin/DashboardWidget.php:566
msgid "WordPress"
msgstr ""

#: src/Admin/DashboardWidget.php:570
msgid "Save Changes"
msgstr ""

#: src/Admin/DashboardWidget.php:615
msgid "Table cell icon"
msgstr ""

#. translators: %d number of total emails sent.
#: src/Admin/DashboardWidget.php:646
msgid "%d total"
msgstr ""

#. translators: %s fixed string of 'N/A'.
#: src/Admin/DashboardWidget.php:652
msgid "Confirmed %s"
msgstr ""

#. translators: %s fixed string of 'N/A'.
#: src/Admin/DashboardWidget.php:658
msgid "Unconfirmed %s"
msgstr ""

#. translators: %s fixed string of 'N/A'.
#: src/Admin/DashboardWidget.php:664
msgid "Failed %s"
msgstr ""

#. translators: %s fixed string of 'N/A'.
#: src/Admin/DashboardWidget.php:675
msgid "Sent %s"
msgstr ""

#: src/Admin/DebugEvents/DebugEvents.php:100
#: src/Admin/DebugEvents/DebugEvents.php:143
msgid "Access rejected."
msgstr ""

#: src/Admin/DebugEvents/DebugEvents.php:104
#: src/Admin/DebugEvents/DebugEvents.php:147
#: src/Admin/Pages/DebugEventsTab.php:365
msgid "You don't have the capability to perform this action."
msgstr ""

#: src/Admin/DebugEvents/DebugEvents.php:108
#: src/Admin/DebugEvents/DebugEvents.php:151
msgid "For some reason the database table was not installed correctly. Please contact plugin support team to diagnose and fix the issue."
msgstr ""

#: src/Admin/DebugEvents/DebugEvents.php:121
msgid "All debug event entries were deleted successfully."
msgstr ""

#. translators: %s - WPDB error message.
#: src/Admin/DebugEvents/DebugEvents.php:126
msgid "There was an issue while trying to delete all debug event entries. Error message: %s"
msgstr ""

#: src/Admin/DebugEvents/DebugEvents.php:157
msgid "No Debug Event ID provided!"
msgstr ""

#: src/Admin/DebugEvents/DebugEvents.php:343
msgid "Number of events per page:"
msgstr ""

#. translators: %d the event ID.
#: src/Admin/DebugEvents/Event.php:147
msgid "Event #%d"
msgstr ""

#: src/Admin/DebugEvents/Event.php:184
#: src/Providers/PepipostAPI/Mailer.php:342
msgid "Error"
msgstr ""

#: src/Admin/DebugEvents/Event.php:185
#: src/SiteHealth.php:141
msgid "Debug"
msgstr ""

#: src/Admin/DebugEvents/Event.php:244
#: src/WP.php:571
msgid "N/A"
msgstr ""

#: src/Admin/DebugEvents/Event.php:357
msgid "Debug Event Details"
msgstr ""

#: src/Admin/DebugEvents/Event.php:360
#: src/Admin/DebugEvents/Table.php:170
msgid "Type"
msgstr ""

#: src/Admin/DebugEvents/Event.php:364
#: src/Admin/DebugEvents/Table.php:173
msgid "Date"
msgstr ""

#: src/Admin/DebugEvents/Event.php:368
#: src/Admin/DebugEvents/Table.php:171
msgid "Content"
msgstr ""

#: src/Admin/DebugEvents/Event.php:375
#: src/Admin/DebugEvents/Table.php:172
#: src/Admin/Pages/ExportTab.php:128
msgid "Source"
msgstr ""

#. Translators: %1$s the path of a file, %2$s the line number in the file.
#: src/Admin/DebugEvents/Event.php:381
msgid "%1$s (line: %2$s)"
msgstr ""

#: src/Admin/DebugEvents/Event.php:389
msgid "Backtrace:"
msgstr ""

#. translators: %1$d - index number; %2$s - function name; %3$s - file path; %4$s - line number.
#: src/Admin/DebugEvents/Event.php:395
msgid "[%1$d] %2$s called at [%3$s:%4$s]"
msgstr ""

#. Translators: %s - Email initiator/source name.
#: src/Admin/DebugEvents/Event.php:429
msgid "Email Source: %s"
msgstr ""

#: src/Admin/DebugEvents/Table.php:115
msgid "All"
msgstr ""

#: src/Admin/DebugEvents/Table.php:169
msgid "Event"
msgstr ""

#: src/Admin/DebugEvents/Table.php:489
msgid "No events found."
msgstr ""

#: src/Admin/DebugEvents/Table.php:491
msgid "No events have been logged for now."
msgstr ""

#: src/Admin/DebugEvents/Table.php:552
#: src/Admin/Pages/ExportTab.php:133
msgid "Select a date range"
msgstr ""

#: src/Admin/DebugEvents/Table.php:556
msgid "Filter"
msgstr ""

#: src/Admin/DebugEvents/Table.php:565
msgid "Delete All Events"
msgstr ""

#: src/Admin/DomainChecker.php:70
msgid "Something went wrong. Please try again later."
msgstr ""

#: src/Admin/DomainChecker.php:178
msgid "Domain Check Results"
msgstr ""

#. translators: %s - item state name.
#: src/Admin/DomainChecker.php:190
msgid "%s icon"
msgstr ""

#. translators: %s - WPMailSMTP.com Upgrade page URL.
#: src/Admin/Education.php:73
msgid "You’re using WP Mail SMTP Lite. To unlock more features, consider <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">upgrading to Pro</a>."
msgstr ""

#: src/Admin/Education.php:84
msgid "Dismiss this message."
msgstr ""

#: src/Admin/FlyoutMenu.php:52
msgid "See Quick Links"
msgstr ""

#: src/Admin/FlyoutMenu.php:107
msgid "Support & Docs"
msgstr ""

#: src/Admin/FlyoutMenu.php:113
msgid "Follow on Facebook"
msgstr ""

#: src/Admin/FlyoutMenu.php:118
msgid "Suggest a Feature"
msgstr ""

#: src/Admin/Notifications.php:477
msgid "Notifications"
msgstr ""

#: src/Admin/Notifications.php:481
msgid "Dismiss this message"
msgstr ""

#: src/Admin/Notifications.php:486
msgid "Previous message"
msgstr ""

#: src/Admin/Notifications.php:490
msgid "Next message"
msgstr ""

#: src/Admin/Pages/About.php:50
#: src/Admin/Pages/AboutTab.php:44
msgid "About Us"
msgstr ""

#: src/Admin/Pages/AboutTab.php:71
msgid "Hello and welcome to WP Mail SMTP, the easiest and most popular WordPress SMTP plugin. We build software that helps your site reliably deliver emails every time."
msgstr ""

#: src/Admin/Pages/AboutTab.php:75
msgid "Email deliverability has been a well-documented problem for all WordPress websites. However as WPForms grew, we became more aware of this painful issue that affects our users and the larger WordPress community. So we decided to solve this problem and make a solution that's beginner friendly."
msgstr ""

#: src/Admin/Pages/AboutTab.php:78
msgid "Our goal is to make reliable email deliverability easy for WordPress."
msgstr ""

#. translators: %1$s - WPForms URL, %2$s - WPBeginner URL, %3$s - OptinMonster URL, %4$s - MonsterInsights URL, %5$s - Awesome Motive URL
#: src/Admin/Pages/AboutTab.php:85
msgid "WP Mail SMTP is brought to you by the same team that's behind the most user friendly WordPress forms, <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">WPForms</a>, the largest WordPress resource site, <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">WPBeginner</a>, the most popular lead-generation software, <a href=\"%3$s\" target=\"_blank\" rel=\"noopener noreferrer\">OptinMonster</a>, the best WordPress analytics plugin, <a href=\"%4$s\" target=\"_blank\" rel=\"noopener noreferrer\">MonsterInsights</a>, and <a href=\"%5$s\" target=\"_blank\" rel=\"noopener noreferrer\">more</a>."
msgstr ""

#: src/Admin/Pages/AboutTab.php:104
msgid "Yup, we know a thing or two about building awesome products that customers love."
msgstr ""

#: src/Admin/Pages/AboutTab.php:110
msgid "The WPForms Team photo"
msgstr ""

#: src/Admin/Pages/AboutTab.php:112
msgid "The WPForms Team"
msgstr ""

#: src/Admin/Pages/AboutTab.php:161
msgid "Plugin icon"
msgstr ""

#. translators: %s - status HTML text.
#: src/Admin/Pages/AboutTab.php:175
msgid "Status: %s"
msgstr ""

#: src/Admin/Pages/AboutTab.php:241
msgid "Not Installed"
msgstr ""

#: src/Admin/Pages/AboutTab.php:244
msgid "Install Plugin"
msgstr ""

#: src/Admin/Pages/AboutTab.php:272
msgid "OptinMonster"
msgstr ""

#: src/Admin/Pages/AboutTab.php:273
msgid "Instantly get more subscribers, leads, and sales with the #1 conversion optimization toolkit. Create high converting popups, announcement bars, spin a wheel, and more with smart targeting and personalization."
msgstr ""

#: src/Admin/Pages/AboutTab.php:279
#: src/Admin/Pages/SmartRoutingTab.php:278
msgid "WPForms"
msgstr ""

#: src/Admin/Pages/AboutTab.php:280
#: src/Admin/Pages/AboutTab.php:286
msgid "The best drag & drop WordPress form builder. Easily create beautiful contact forms, surveys, payment forms, and more with our 600+ form templates. Trusted by over 5 million websites as the best forms plugin."
msgstr ""

#: src/Admin/Pages/AboutTab.php:285
msgid "WPForms Pro"
msgstr ""

#: src/Admin/Pages/AboutTab.php:293
msgid "MonsterInsights"
msgstr ""

#: src/Admin/Pages/AboutTab.php:294
#: src/Admin/Pages/AboutTab.php:300
msgid "The leading WordPress analytics plugin that shows you how people find and use your website, so you can make data driven decisions to grow your business. Properly set up Google Analytics without writing code."
msgstr ""

#: src/Admin/Pages/AboutTab.php:299
msgid "MonsterInsights Pro"
msgstr ""

#: src/Admin/Pages/AboutTab.php:307
#: src/Admin/Pages/AboutTab.php:313
msgid "AIOSEO"
msgstr ""

#: src/Admin/Pages/AboutTab.php:308
#: src/Admin/Pages/AboutTab.php:314
msgid "The original WordPress SEO plugin and toolkit that improves your website’s search rankings. Comes with all the SEO features like Local SEO, WooCommerce SEO, sitemaps, SEO optimizer, schema, and more."
msgstr ""

#: src/Admin/Pages/AboutTab.php:321
#: src/Admin/Pages/AboutTab.php:327
msgid "SeedProd"
msgstr ""

#: src/Admin/Pages/AboutTab.php:322
#: src/Admin/Pages/AboutTab.php:328
msgid "The fastest drag & drop landing page builder for WordPress. Create custom landing pages without writing code, connect them with your CRM, collect subscribers, and grow your audience. Trusted by 1 million sites."
msgstr ""

#: src/Admin/Pages/AboutTab.php:335
msgid "RafflePress"
msgstr ""

#: src/Admin/Pages/AboutTab.php:336
#: src/Admin/Pages/AboutTab.php:342
msgid "Turn your website visitors into brand ambassadors! Easily grow your email list, website traffic, and social media followers with the most powerful giveaways & contests plugin for WordPress."
msgstr ""

#: src/Admin/Pages/AboutTab.php:341
msgid "RafflePress Pro"
msgstr ""

#: src/Admin/Pages/AboutTab.php:349
msgid "PushEngage"
msgstr ""

#: src/Admin/Pages/AboutTab.php:350
msgid "Connect with your visitors after they leave your website with the leading web push notification software. Over 10,000+ businesses worldwide use PushEngage to send 15 billion notifications each month."
msgstr ""

#: src/Admin/Pages/AboutTab.php:356
#: src/Admin/Pages/AboutTab.php:362
msgid "Smash Balloon Instagram Feeds"
msgstr ""

#: src/Admin/Pages/AboutTab.php:357
#: src/Admin/Pages/AboutTab.php:363
msgid "Easily display Instagram content on your WordPress site without writing any code. Comes with multiple templates, ability to show content from multiple accounts, hashtags, and more. Trusted by 1 million websites."
msgstr ""

#: src/Admin/Pages/AboutTab.php:370
#: src/Admin/Pages/AboutTab.php:376
msgid "Smash Balloon Facebook Feeds"
msgstr ""

#: src/Admin/Pages/AboutTab.php:371
#: src/Admin/Pages/AboutTab.php:377
msgid "Easily display Facebook content on your WordPress site without writing any code. Comes with multiple templates, ability to embed albums, group content, reviews, live videos, comments, and reactions."
msgstr ""

#: src/Admin/Pages/AboutTab.php:384
#: src/Admin/Pages/AboutTab.php:390
msgid "Smash Balloon YouTube Feeds"
msgstr ""

#: src/Admin/Pages/AboutTab.php:385
#: src/Admin/Pages/AboutTab.php:391
msgid "Easily display YouTube videos on your WordPress site without writing any code. Comes with multiple layouts, ability to embed live streams, video filtering, ability to combine multiple channel videos, and more."
msgstr ""

#: src/Admin/Pages/AboutTab.php:398
#: src/Admin/Pages/AboutTab.php:404
msgid "Smash Balloon Twitter Feeds"
msgstr ""

#: src/Admin/Pages/AboutTab.php:399
#: src/Admin/Pages/AboutTab.php:405
msgid "Easily display Twitter content in WordPress without writing any code. Comes with multiple layouts, ability to combine multiple Twitter feeds, Twitter card support, tweet moderation, and more."
msgstr ""

#: src/Admin/Pages/AboutTab.php:412
msgid "TrustPulse"
msgstr ""

#: src/Admin/Pages/AboutTab.php:413
msgid "Boost your sales and conversions by up to 15% with real-time social proof notifications. TrustPulse helps you show live user activity and purchases to help convince other users to purchase."
msgstr ""

#: src/Admin/Pages/AboutTab.php:419
#: src/Admin/Pages/AboutTab.php:425
msgid "SearchWP"
msgstr ""

#: src/Admin/Pages/AboutTab.php:420
#: src/Admin/Pages/AboutTab.php:426
msgid "The most advanced WordPress search plugin. Customize your WordPress search algorithm, reorder search results, track search metrics, and everything you need to leverage search to grow your business."
msgstr ""

#: src/Admin/Pages/AboutTab.php:433
#: src/Admin/Pages/AboutTab.php:439
msgid "AffiliateWP"
msgstr ""

#: src/Admin/Pages/AboutTab.php:434
#: src/Admin/Pages/AboutTab.php:440
msgid "The #1 affiliate management plugin for WordPress. Easily create an affiliate program for your eCommerce store or membership site within minutes and start growing your sales with the power of referral marketing."
msgstr ""

#: src/Admin/Pages/AboutTab.php:447
msgid "WP Simple Pay"
msgstr ""

#: src/Admin/Pages/AboutTab.php:448
#: src/Admin/Pages/AboutTab.php:454
msgid "The #1 Stripe payments plugin for WordPress. Start accepting one-time and recurring payments on your WordPress site without setting up a shopping cart. No code required."
msgstr ""

#: src/Admin/Pages/AboutTab.php:453
msgid "WP Simple Pay Pro"
msgstr ""

#: src/Admin/Pages/AboutTab.php:461
msgid "Easy Digital Downloads"
msgstr ""

#: src/Admin/Pages/AboutTab.php:462
msgid "The best WordPress eCommerce plugin for selling digital downloads. Start selling eBooks, software, music, digital art, and more within minutes. Accept payments, manage subscriptions, advanced access control, and more."
msgstr ""

#: src/Admin/Pages/AboutTab.php:468
msgid "Sugar Calendar Lite"
msgstr ""

#: src/Admin/Pages/AboutTab.php:469
#: src/Admin/Pages/AboutTab.php:475
msgid "A simple & powerful event calendar plugin for WordPress that comes with all the event management features including payments, scheduling, timezones, ticketing, recurring events, and more."
msgstr ""

#: src/Admin/Pages/AboutTab.php:474
msgid "Sugar Calendar"
msgstr ""

#: src/Admin/Pages/AboutTab.php:482
msgid "Charitable"
msgstr ""

#: src/Admin/Pages/AboutTab.php:483
msgid "Top-rated WordPress donation and fundraising plugin. Over 10,000+ non-profit organizations and website owners use Charitable to create fundraising campaigns and raise more money online."
msgstr ""

#: src/Admin/Pages/AboutTab.php:489
msgid "WPCode Lite"
msgstr ""

#: src/Admin/Pages/AboutTab.php:490
#: src/Admin/Pages/AboutTab.php:496
msgid "Future proof your WordPress customizations with the most popular code snippet management plugin for WordPress. Trusted by over 1,500,000+ websites for easily adding code to WordPress right from the admin area."
msgstr ""

#: src/Admin/Pages/AboutTab.php:495
msgid "WPCode Pro"
msgstr ""

#: src/Admin/Pages/AboutTab.php:503
msgid "Duplicator"
msgstr ""

#: src/Admin/Pages/AboutTab.php:504
#: src/Admin/Pages/AboutTab.php:510
msgid "Leading WordPress backup & site migration plugin. Over 1,500,000+ smart website owners use Duplicator to make reliable and secure WordPress backups to protect their websites. It also makes website migration really easy."
msgstr ""

#: src/Admin/Pages/AboutTab.php:509
msgid "Duplicator Pro"
msgstr ""

#: src/Admin/Pages/AboutTab.php:529
msgid "Could not activate the plugin. Please activate it from the Plugins page."
msgstr ""

#: src/Admin/Pages/AboutTab.php:555
msgid "Could not activate the plugin. Plugin is not whitelisted."
msgstr ""

#: src/Admin/Pages/AboutTab.php:561
msgid "Plugin activated."
msgstr ""

#: src/Admin/Pages/AboutTab.php:577
msgid "Could not install the plugin."
msgstr ""

#: src/Admin/Pages/AboutTab.php:591
#: src/Admin/SetupWizard.php:872
msgid "Could not install the plugin. Plugin is not whitelisted."
msgstr ""

#: src/Admin/Pages/AboutTab.php:658
#: src/Connect.php:210
#: src/Connect.php:218
#: src/Connect.php:297
msgid "Plugin installed & activated."
msgstr ""

#: src/Admin/Pages/AboutTab.php:666
msgid "Plugin installed."
msgstr ""

#: src/Admin/Pages/ActionSchedulerTab.php:41
#: src/Admin/Pages/ActionSchedulerTab.php:101
msgid "Scheduled Actions"
msgstr ""

#. translators: %s - Action Scheduler website URL.
#: src/Admin/Pages/ActionSchedulerTab.php:107
msgid "WP Mail SMTP is using the <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Action Scheduler</a> library, which allows it to queue and process bigger tasks in the background without making your site slower for your visitors. Below you can see the list of all tasks and their status. This table can be very useful when debugging certain issues."
msgstr ""

#: src/Admin/Pages/ActionSchedulerTab.php:122
msgid "Action Scheduler library is also used by other plugins, like WPForms and WooCommerce, so you might see tasks that are not related to our plugin in the table below."
msgstr ""

#. translators: %s - search term.
#: src/Admin/Pages/ActionSchedulerTab.php:130
msgid "Search results for <strong>%s</strong>"
msgstr ""

#: src/Admin/Pages/AdditionalConnectionsTab.php:49
#: src/Admin/Pages/AdditionalConnectionsTab.php:106
msgid "Additional Connections"
msgstr ""

#: src/Admin/Pages/AdditionalConnectionsTab.php:110
msgid "Create additional connections to set a backup for your Primary Connection or to configure Smart Routing."
msgstr ""

#: src/Admin/Pages/AdditionalConnectionsTab.php:143
#: src/Admin/Pages/SettingsTab.php:340
#: src/Admin/Pages/SettingsTab.php:364
msgid "Backup Connection"
msgstr ""

#: src/Admin/Pages/AdditionalConnectionsTab.php:148
#: src/Admin/Pages/SmartRoutingTab.php:51
#: src/Admin/Pages/SmartRoutingTab.php:102
msgid "Smart Routing"
msgstr ""

#: src/Admin/Pages/AdditionalConnectionsTab.php:177
msgid "With additional connections you can..."
msgstr ""

#: src/Admin/Pages/AdditionalConnectionsTab.php:180
msgid "Set a Backup Connection"
msgstr ""

#: src/Admin/Pages/AdditionalConnectionsTab.php:183
msgid "Use mailers for different purposes"
msgstr ""

#: src/Admin/Pages/AdditionalConnectionsTab.php:186
msgid "Create advanced routing rules"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:43
#: src/Admin/Pages/AlertsTab.php:81
msgid "Alerts"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:85
msgid "Configure at least one of these integrations to receive notifications when email fails to send from your site. Alert notifications will contain the following important data: email subject, email Send To address, the error message, and helpful links to help you fix the issue."
msgstr ""

#: src/Admin/Pages/AlertsTab.php:98
msgid "Notify when"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:105
msgid "The initial email sending request fails"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:112
msgid "This option is always enabled and will notify you about instant email sending failures."
msgstr ""

#: src/Admin/Pages/AlertsTab.php:119
msgid "The deliverability verification process detects a hard bounce"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:125
msgid "Get notified about emails that were successfully sent, but have hard bounced on delivery attempt. A hard bounce is an email that has failed to deliver for permanent reasons, such as the recipient's email address being invalid."
msgstr ""

#: src/Admin/Pages/AlertsTab.php:135
msgid "Email"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:136
msgid "Enter the email addresses (3 max) you’d like to use to receive alerts when email sending fails. Read our documentation on setting up email alerts."
msgstr ""

#: src/Admin/Pages/AlertsTab.php:141
msgid "Email Alerts"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:153
#: src/Admin/Pages/TestTab.php:158
msgid "Send To"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:164
msgid "Slack"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:165
msgid "Paste in the Slack webhook URL you’d like to use to receive alerts when email sending fails. Read our documentation on setting up Slack alerts."
msgstr ""

#: src/Admin/Pages/AlertsTab.php:170
msgid "Slack Alerts"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:182
#: src/Admin/Pages/AlertsTab.php:211
#: src/Admin/Pages/AlertsTab.php:240
#: src/Admin/Pages/AlertsTab.php:316
msgid "Webhook URL"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:193
msgid "Discord"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:194
msgid "Paste in the Discord webhook URL you’d like to use to receive alerts when email sending fails. Read our documentation on setting up Discord alerts."
msgstr ""

#: src/Admin/Pages/AlertsTab.php:199
msgid "Discord Alerts"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:222
msgid "Microsoft Teams"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:223
msgid "Paste in the Microsoft Teams webhook URL you’d like to use to receive alerts when email sending fails. Read our documentation on setting up Microsoft Teams alerts."
msgstr ""

#: src/Admin/Pages/AlertsTab.php:228
msgid "Microsoft Teams Alerts"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:251
msgid "SMS via Twilio"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:252
msgid "To receive SMS alerts, you’ll need a Twilio account. Read our documentation to learn how to set up Twilio SMS, then enter your connection details below."
msgstr ""

#: src/Admin/Pages/AlertsTab.php:257
msgid "SMS via Twilio Alerts"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:269
msgid "Twilio Account ID"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:275
msgid "Twilio Auth Token"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:281
msgid "From Phone Number"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:287
msgid "To Phone Number"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:298
msgid "Webhook"
msgstr ""

#: src/Admin/Pages/AlertsTab.php:299
msgid "Paste in the webhook URL you’d like to use to receive alerts when email sending fails. Read our documentation on setting up webhook alerts."
msgstr ""

#: src/Admin/Pages/AlertsTab.php:304
msgid "Webhook Alerts"
msgstr ""

#: src/Admin/Pages/ControlTab.php:35
#: src/Admin/Pages/ControlTab.php:237
#: src/Admin/Pages/VersusTab.php:177
msgid "Email Controls"
msgstr ""

#: src/Admin/Pages/ControlTab.php:63
msgid "Comments"
msgstr ""

#: src/Admin/Pages/ControlTab.php:66
msgid "Awaiting Moderation"
msgstr ""

#: src/Admin/Pages/ControlTab.php:67
msgid "Comment is awaiting moderation. Sent to the site admin and post author if they can edit comments."
msgstr ""

#: src/Admin/Pages/ControlTab.php:70
msgid "Published"
msgstr ""

#: src/Admin/Pages/ControlTab.php:71
msgid "Comment has been published. Sent to the post author."
msgstr ""

#: src/Admin/Pages/ControlTab.php:76
msgid "Change of Admin Email"
msgstr ""

#: src/Admin/Pages/ControlTab.php:79
msgid "Site Admin Email Change Attempt"
msgstr ""

#: src/Admin/Pages/ControlTab.php:80
msgid "Change of site admin email address was attempted. Sent to the proposed new email address."
msgstr ""

#: src/Admin/Pages/ControlTab.php:83
msgid "Site Admin Email Changed"
msgstr ""

#: src/Admin/Pages/ControlTab.php:84
msgid "Site admin email address was changed. Sent to the old site admin email address."
msgstr ""

#: src/Admin/Pages/ControlTab.php:87
msgid "Network Admin Email Change Attempt"
msgstr ""

#: src/Admin/Pages/ControlTab.php:88
msgid "Change of network admin email address was attempted. Sent to the proposed new email address."
msgstr ""

#: src/Admin/Pages/ControlTab.php:91
msgid "Network Admin Email Changed"
msgstr ""

#: src/Admin/Pages/ControlTab.php:92
msgid "Network admin email address was changed. Sent to the old network admin email address."
msgstr ""

#: src/Admin/Pages/ControlTab.php:97
msgid "Change of User Email or Password"
msgstr ""

#: src/Admin/Pages/ControlTab.php:100
msgid "Reset Password Request"
msgstr ""

#: src/Admin/Pages/ControlTab.php:101
msgid "User requested a password reset via \"Lost your password?\". Sent to the user."
msgstr ""

#: src/Admin/Pages/ControlTab.php:104
msgid "Password Reset Successfully"
msgstr ""

#: src/Admin/Pages/ControlTab.php:105
msgid "User reset their password from the password reset link. Sent to the site admin."
msgstr ""

#: src/Admin/Pages/ControlTab.php:108
msgid "Password Changed"
msgstr ""

#: src/Admin/Pages/ControlTab.php:109
msgid "User changed their password. Sent to the user."
msgstr ""

#: src/Admin/Pages/ControlTab.php:112
msgid "Email Change Attempt"
msgstr ""

#: src/Admin/Pages/ControlTab.php:113
msgid "User attempted to change their email address. Sent to the proposed new email address."
msgstr ""

#: src/Admin/Pages/ControlTab.php:116
msgid "Email Changed"
msgstr ""

#: src/Admin/Pages/ControlTab.php:117
msgid "User changed their email address. Sent to the user."
msgstr ""

#: src/Admin/Pages/ControlTab.php:122
msgid "Personal Data Requests"
msgstr ""

#: src/Admin/Pages/ControlTab.php:125
msgid "User Confirmed Export / Erasure Request"
msgstr ""

#: src/Admin/Pages/ControlTab.php:126
msgid "User clicked a confirmation link in personal data export or erasure request email. Sent to the site or network admin."
msgstr ""

#: src/Admin/Pages/ControlTab.php:129
msgid "Admin Erased Data"
msgstr ""

#: src/Admin/Pages/ControlTab.php:130
msgid "Site admin clicked \"Erase Personal Data\" button next to a confirmed data erasure request. Sent to the requester email address."
msgstr ""

#: src/Admin/Pages/ControlTab.php:133
msgid "Admin Sent Link to Export Data"
msgstr ""

#: src/Admin/Pages/ControlTab.php:134
msgid "Site admin clicked \"Email Data\" button next to a confirmed data export request. Sent to the requester email address."
msgstr ""

#: src/Admin/Pages/ControlTab.php:135
msgid "Disabling this option will block users from being able to export their personal data, as they will not receive an email with a link."
msgstr ""

#: src/Admin/Pages/ControlTab.php:140
msgid "Automatic Updates"
msgstr ""

#: src/Admin/Pages/ControlTab.php:143
msgid "Plugin Status"
msgstr ""

#: src/Admin/Pages/ControlTab.php:144
msgid "Completion or failure of a background automatic plugin update. Sent to the site or network admin."
msgstr ""

#: src/Admin/Pages/ControlTab.php:147
msgid "Theme Status"
msgstr ""

#: src/Admin/Pages/ControlTab.php:148
msgid "Completion or failure of a background automatic theme update. Sent to the site or network admin."
msgstr ""

#: src/Admin/Pages/ControlTab.php:151
msgid "WP Core Status"
msgstr ""

#: src/Admin/Pages/ControlTab.php:152
msgid "Completion or failure of a background automatic core update. Sent to the site or network admin."
msgstr ""

#: src/Admin/Pages/ControlTab.php:155
msgid "Full Log"
msgstr ""

#: src/Admin/Pages/ControlTab.php:156
msgid "Full log of background update results which includes information about WordPress core, plugins, themes, and translations updates. Only sent when you are using a development version of WordPress. Sent to the site or network admin."
msgstr ""

#: src/Admin/Pages/ControlTab.php:161
msgid "New User"
msgstr ""

#: src/Admin/Pages/ControlTab.php:164
msgid "Created (Admin)"
msgstr ""

#: src/Admin/Pages/ControlTab.php:165
msgid "A new user was created. Sent to the site admin."
msgstr ""

#: src/Admin/Pages/ControlTab.php:168
msgid "Created (User)"
msgstr ""

#: src/Admin/Pages/ControlTab.php:169
msgid "A new user was created. Sent to the new user."
msgstr ""

#: src/Admin/Pages/ControlTab.php:172
msgid "Invited To Site"
msgstr ""

#: src/Admin/Pages/ControlTab.php:173
msgid "A new user was invited to a site from Users -> Add New -> Add New User. Sent to the invited user."
msgstr ""

#: src/Admin/Pages/ControlTab.php:176
msgid "Created On Site"
msgstr ""

#: src/Admin/Pages/ControlTab.php:177
msgid "A new user account was created. Sent to Network Admin."
msgstr ""

#: src/Admin/Pages/ControlTab.php:180
msgid "Added / Activated on Site"
msgstr ""

#: src/Admin/Pages/ControlTab.php:181
msgid "A user has been added, or their account activation has been successful. Sent to the user, that has been added/activated."
msgstr ""

#: src/Admin/Pages/ControlTab.php:186
msgid "New Site"
msgstr ""

#: src/Admin/Pages/ControlTab.php:189
msgid "User Created Site"
msgstr ""

#: src/Admin/Pages/ControlTab.php:190
msgid "User registered for a new site. Sent to the site admin."
msgstr ""

#: src/Admin/Pages/ControlTab.php:193
msgid "Network Admin: User Activated / Added Site"
msgstr ""

#: src/Admin/Pages/ControlTab.php:194
msgid "User activated their new site, or site was added from Network Admin -> Sites -> Add New. Sent to Network Admin."
msgstr ""

#: src/Admin/Pages/ControlTab.php:197
msgid "Site Admin: Activated / Added Site"
msgstr ""

#: src/Admin/Pages/ControlTab.php:198
msgid "User activated their new site, or site was added from Network Admin -> Sites -> Add New. Sent to Site Admin."
msgstr ""

#: src/Admin/Pages/ControlTab.php:241
msgid "Email controls allow you to manage the automatic notifications you receive from your WordPress website. With the flick of a switch, you can reduce inbox clutter and focus on the alerts that matter the most. It's easy to disable emails about comments, email or password changes, WordPress updates, user registrations, and personal data requests."
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:84
#: src/Admin/Pages/DebugEventsTab.php:188
#: src/Admin/Pages/DebugEventsTab.php:201
msgid "Debug Events"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:148
msgid "Are you sure you want to permanently delete all debug events?"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:150
msgid "Close"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:151
#: src/Admin/Review.php:139
msgid "Yes"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:154
msgid "An error occurred!"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:191
msgid "On this page, you can view different plugin debugging events and email sending errors."
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:204
msgid "On this page, you can view and configure different plugin debugging events. View email sending errors and enable debugging events, allowing you to detect email sending issues."
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:212
msgid "Event Types"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:228
msgid "Email Sending Errors"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:231
msgid "This debug event is always enabled and will record any email sending errors in the table below."
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:245
msgid "Debug Email Sending"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:248
msgid "Check this if you would like to debug the email sending process. Once enabled, all debug events will be logged in the table below. This setting should only be enabled for shorter debugging periods and disabled afterwards."
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:256
msgid "Events Retention Period"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:262
msgid "Forever"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:271
msgid "Debug events older than the selected period will be permanently deleted from the database."
msgstr ""

#. translators: %1$s - number of debug events found; %2$s - filtered type.
#: src/Admin/Pages/DebugEventsTab.php:312
msgid "Found <strong>%1$s %2$s event</strong>"
msgid_plural "Found <strong>%1$s %2$s events</strong>"
msgstr[0] ""
msgstr[1] ""

#: src/Admin/Pages/DebugEventsTab.php:341
msgid "Search Events"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:377
#: src/Admin/Pages/MiscTab.php:502
#: src/Admin/Pages/SettingsTab.php:440
msgid "Settings were successfully saved."
msgstr ""

#. translators: %s The searched term.
#: src/Admin/Pages/DebugEventsTab.php:416
msgid "where event contains \"%s\""
msgstr ""

#. translators: %s - Date.
#: src/Admin/Pages/DebugEventsTab.php:449
msgid "on %s"
msgstr ""

#. translators: %1$s - Date. %2$s - Date.
#: src/Admin/Pages/DebugEventsTab.php:455
msgid "between %1$s and %2$s"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:494
msgid "Debug Events are Not Installed Correctly"
msgstr ""

#. translators: %1$s - create missing tables link; %2$s - contact support link.
#: src/Admin/Pages/DebugEventsTab.php:501
msgid "WP Mail SMTP is using custom database tables for some of its features. In order to work properly, the custom tables should be created, and it seems they are missing. Please try to <a href=\"%1$s\">create the missing DB tables by clicking on this link</a>. If this issue persists, please <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">contact our support</a> and provide the error message below:"
msgstr ""

#. translators: %1$s - create missing tables link; %2$s - contact support link.
#: src/Admin/Pages/DebugEventsTab.php:518
msgid "WP Mail SMTP is using custom database tables for some of its features. In order to work properly, the custom tables should be created, and it seems they are missing. Please try to <a href=\"%1$s\">create the missing DB tables by clicking on this link</a>. If this issue persists, please <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">contact our support</a>."
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:567
msgid "1 Week"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:568
msgid "1 Month"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:569
msgid "3 Months"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:570
msgid "6 Months"
msgstr ""

#: src/Admin/Pages/DebugEventsTab.php:571
msgid "1 Year"
msgstr ""

#. translators: %d - days count.
#: src/Admin/Pages/DebugEventsTab.php:585
msgid "%d Day"
msgid_plural "%d Days"
msgstr[0] ""
msgstr[1] ""

#: src/Admin/Pages/EmailReports.php:41
#: src/Admin/Pages/EmailReportsTab.php:42
msgid "Email Reports"
msgstr ""

#: src/Admin/Pages/EmailReportsTab.php:114
msgid "Stats at a Glance"
msgstr ""

#: src/Admin/Pages/EmailReportsTab.php:119
msgid "Detailed Stats by Subject Line"
msgstr ""

#: src/Admin/Pages/EmailReportsTab.php:124
msgid "Weekly Email Report"
msgstr ""

#: src/Admin/Pages/EmailReportsTab.php:132
msgid "Email reports make it easy to track deliverability and engagement at-a-glance. Your open and click-through rates are grouped by subject line, making it easy to review the performance of campaigns or notifications. The report also displays Sent and Failed emails each week so you spot any issues quickly. When you upgrade, we'll also add an email report chart right in your WordPress dashboard."
msgstr ""

#: src/Admin/Pages/EmailReportsTab.php:156
msgid "Unlock these awesome reporting features:"
msgstr ""

#: src/Admin/Pages/EmailReportsTab.php:159
msgid "Get weekly deliverability reports"
msgstr ""

#: src/Admin/Pages/EmailReportsTab.php:160
msgid "View stats grouped by subject line"
msgstr ""

#: src/Admin/Pages/EmailReportsTab.php:163
msgid "Track total emails sent each week"
msgstr ""

#: src/Admin/Pages/EmailReportsTab.php:164
msgid "Measure open rate and click through rates"
msgstr ""

#: src/Admin/Pages/EmailReportsTab.php:167
msgid "Spot failed emails quickly"
msgstr ""

#: src/Admin/Pages/EmailReportsTab.php:168
msgid "See email report graphs in WordPress"
msgstr ""

#: src/Admin/Pages/ExportTab.php:42
msgid "Export"
msgstr ""

#: src/Admin/Pages/ExportTab.php:80
msgid "Export Email Logs"
msgstr ""

#: src/Admin/Pages/ExportTab.php:84
msgid "Easily export your logs to CSV or Excel. Filter the logs before you export and only download the data you need. This feature lets you easily create your own deliverability reports. You can also use the data in 3rd party dashboards to track deliverability along with your other website statistics."
msgstr ""

#: src/Admin/Pages/ExportTab.php:95
msgid "Export Type"
msgstr ""

#: src/Admin/Pages/ExportTab.php:97
msgid "Export in CSV (.csv)"
msgstr ""

#: src/Admin/Pages/ExportTab.php:100
msgid "Export in Microsoft Excel (.xlsx)"
msgstr ""

#: src/Admin/Pages/ExportTab.php:103
msgid "Export in EML (.eml)"
msgstr ""

#: src/Admin/Pages/ExportTab.php:108
msgid "Common Information"
msgstr ""

#: src/Admin/Pages/ExportTab.php:109
msgid "To Address"
msgstr ""

#: src/Admin/Pages/ExportTab.php:110
msgid "From Address"
msgstr ""

#: src/Admin/Pages/ExportTab.php:112
#: src/Admin/Pages/SmartRoutingTab.php:157
msgid "Subject"
msgstr ""

#: src/Admin/Pages/ExportTab.php:113
msgid "Body"
msgstr ""

#: src/Admin/Pages/ExportTab.php:114
msgid "Created Date"
msgstr ""

#: src/Admin/Pages/ExportTab.php:115
msgid "Number of Attachments"
msgstr ""

#: src/Admin/Pages/ExportTab.php:116
msgid "Attachments"
msgstr ""

#: src/Admin/Pages/ExportTab.php:120
msgid "Additional Information"
msgstr ""

#: src/Admin/Pages/ExportTab.php:121
msgid "Status"
msgstr ""

#: src/Admin/Pages/ExportTab.php:122
msgid "Carbon Copy (CC)"
msgstr ""

#: src/Admin/Pages/ExportTab.php:123
msgid "Blind Carbon Copy (BCC)"
msgstr ""

#: src/Admin/Pages/ExportTab.php:124
msgid "Headers"
msgstr ""

#: src/Admin/Pages/ExportTab.php:126
msgid "Error Details"
msgstr ""

#: src/Admin/Pages/ExportTab.php:127
msgid "Email log ID"
msgstr ""

#: src/Admin/Pages/ExportTab.php:132
msgid "Custom Date Range"
msgstr ""

#: src/Admin/Pages/ExportTab.php:137
msgid "Search"
msgstr ""

#: src/Admin/Pages/ExportTab.php:139
msgid "Email Addresses"
msgstr ""

#: src/Admin/Pages/LogsTab.php:130
msgid "Email Log Index"
msgstr ""

#: src/Admin/Pages/LogsTab.php:135
msgid "Individual Email Log"
msgstr ""

#: src/Admin/Pages/LogsTab.php:147
msgid "Email logging makes it easy to save details about all of the emails sent from your WordPress site. You can search and filter the email log to find specific messages and check the color-coded delivery status. Email logging also allows you to resend emails, save attachments, and export your logs in different formats."
msgstr ""

#: src/Admin/Pages/LogsTab.php:171
msgid "Unlock these awesome logging features:"
msgstr ""

#: src/Admin/Pages/LogsTab.php:174
msgid "Save detailed email headers"
msgstr ""

#: src/Admin/Pages/LogsTab.php:175
msgid "See sent and failed emails"
msgstr ""

#: src/Admin/Pages/LogsTab.php:178
msgid "Resend emails and attachments"
msgstr ""

#: src/Admin/Pages/LogsTab.php:179
msgid "Track email opens and clicks"
msgstr ""

#: src/Admin/Pages/LogsTab.php:182
msgid "Print email logs or save as PDF"
msgstr ""

#: src/Admin/Pages/LogsTab.php:183
msgid "Export logs to CSV, XLSX, or EML"
msgstr ""

#: src/Admin/Pages/MiscTab.php:39
msgid "Misc"
msgstr ""

#: src/Admin/Pages/MiscTab.php:51
msgid "Miscellaneous"
msgstr ""

#: src/Admin/Pages/MiscTab.php:78
msgid "Do Not Send"
msgstr ""

#: src/Admin/Pages/MiscTab.php:94
msgid "Stop sending all emails"
msgstr ""

#: src/Admin/Pages/MiscTab.php:100
msgid "Some plugins, like BuddyPress and Events Manager, are using their own email delivery solutions. By default, this option does not block their emails, as those plugins do not use default <code>wp_mail()</code> function to send emails."
msgstr ""

#: src/Admin/Pages/MiscTab.php:108
msgid "You will need to consult with their documentation to switch them to use default WordPress email delivery."
msgstr ""

#: src/Admin/Pages/MiscTab.php:110
msgid "Test emails are allowed to be sent, regardless of this option."
msgstr ""

#. translators: %s - The URL to the constants support article.
#: src/Admin/Pages/MiscTab.php:118
msgid "Please read this <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">support article</a> if you want to enable this option using constants."
msgstr ""

#: src/Admin/Pages/MiscTab.php:140
msgid "Hide Announcements"
msgstr ""

#: src/Admin/Pages/MiscTab.php:155
msgid "Hide plugin announcements and update details."
msgstr ""

#: src/Admin/Pages/MiscTab.php:165
msgid "Hide Email Delivery Errors"
msgstr ""

#: src/Admin/Pages/MiscTab.php:184
msgid "Hide warnings alerting of email delivery errors."
msgstr ""

#. translators: %s - filter that was used to disabled.
#: src/Admin/Pages/MiscTab.php:190
msgid "Email Delivery Errors were disabled using a %s filter."
msgstr ""

#: src/Admin/Pages/MiscTab.php:199
msgid "<strong>This is not recommended</strong> and should only be done for staging or development sites."
msgstr ""

#: src/Admin/Pages/MiscTab.php:214
msgid "Hide Dashboard Widget"
msgstr ""

#: src/Admin/Pages/MiscTab.php:229
msgid "Hide the WP Mail SMTP Dashboard Widget."
msgstr ""

#: src/Admin/Pages/MiscTab.php:239
msgid "Allow Usage Tracking"
msgstr ""

#: src/Admin/Pages/MiscTab.php:264
msgid "Disable Email Summaries"
msgstr ""

#: src/Admin/Pages/MiscTab.php:283
msgid "Disable Email Summaries weekly delivery."
msgstr ""

#. translators: %s - Email Log settings url.
#: src/Admin/Pages/MiscTab.php:288
msgid "Please enable <a href=\"%s\">Email Logging</a> first, before this setting can be configured."
msgstr ""

#: src/Admin/Pages/MiscTab.php:301
msgid "View Email Summary Example"
msgstr ""

#: src/Admin/Pages/MiscTab.php:317
msgid "Optimize Email Sending"
msgstr ""

#. translators: %1$s - Documentation URL.
#: src/Admin/Pages/MiscTab.php:335
msgid "Send emails asynchronously, which will make pages with email requests load faster, but may delay email delivery by a minute or two. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More</a>"
msgstr ""

#: src/Admin/Pages/MiscTab.php:366
msgid "Uninstall WP Mail SMTP"
msgstr ""

#: src/Admin/Pages/MiscTab.php:381
msgid "Remove ALL WP Mail SMTP data upon plugin deletion."
msgstr ""

#: src/Admin/Pages/MiscTab.php:384
msgid "All settings will be unrecoverable."
msgstr ""

#: src/Admin/Pages/MiscTab.php:406
msgid "Email Rate Limiting"
msgstr ""

#. translators: %1$s - Documentation URL.
#: src/Admin/Pages/MiscTab.php:421
msgid "Limit the number of emails this site will send in each time interval (per minute, hour, day, week and month). Emails that will cross those set limits will be queued and sent as soon as your limits allow. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More</a>"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:64
msgid "License"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:67
msgid "Your license key provides access to updates and support."
msgstr ""

#: src/Admin/Pages/SettingsTab.php:75
msgid "License Key"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:85
msgid "Primary Connection"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:93
msgid "Setup Wizard"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:97
msgid "Launch Setup Wizard"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:101
msgid "We'll guide you through each step needed to get WP Mail SMTP fully set up on your site."
msgstr ""

#: src/Admin/Pages/SettingsTab.php:141
msgid "You're using WP Mail SMTP Lite - no license needed. Enjoy!"
msgstr ""

#. translators: %s - WPMailSMTP.com upgrade URL.
#: src/Admin/Pages/SettingsTab.php:147
msgid "To unlock more features, consider <strong><a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wp-mail-smtp-upgrade-modal\">upgrading to PRO</a></strong>."
msgstr ""

#. Translators: %s - discount value $50
#: src/Admin/Pages/SettingsTab.php:167
msgid "As a valued WP Mail SMTP Lite user you receive <strong>%s off</strong>, automatically applied at checkout!"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:181
msgid "Already purchased? Simply enter your license key below to connect with WP Mail SMTP Pro!"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:185
msgid "Paste license key here"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:243
msgid "Get WP Mail SMTP Pro and Unlock all the Powerful Features"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:247
msgid "Thanks for being a loyal WP Mail SMTP user. Upgrade to WP Mail SMTP Pro to unlock more awesome features and experience why WP Mail SMTP is the most popular SMTP plugin."
msgstr ""

#: src/Admin/Pages/SettingsTab.php:251
msgid "We know that you will truly love WP Mail SMTP. It's used by over 4,000,000 websites."
msgstr ""

#: src/Admin/Pages/SettingsTab.php:254
msgid "Pro Features:"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:258
msgid "Email Logging - keep track of every email sent from your site"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:259
msgid "Alerts - get notified when your emails fail (via email, slack or SMS)"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:260
msgid "Backup Connection - send emails even if your primary connection fails"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:261
msgid "Smart Routing - define conditions for your email sending"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:262
msgid "Amazon SES - harness the power of AWS"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:263
msgid "Outlook - send emails using your Outlook or Microsoft 365 account"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:264
msgid "Zoho Mail - use your Zoho Mail account to send emails"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:265
msgid "Multisite Support - network settings for easy management"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:266
msgid "Manage Notifications - control which emails your site sends"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:267
msgid "Access to our world class support team"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:270
msgid "White Glove Setup - sit back and relax while we handle everything for you"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:271
msgid "Install & Setup WP Mail SMTP Pro plugin"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:272
msgid "Configure SendLayer, SMTP.com or Brevo service"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:273
msgid "Set up domain name verification (DNS)"
msgstr ""

#: src/Admin/Pages/SettingsTab.php:274
msgid "Test and verify email delivery"
msgstr ""

#. translators: %s - WPMailSMTP.com URL.
#: src/Admin/Pages/SettingsTab.php:282
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Get WP Mail SMTP Pro Today and Unlock all the Powerful Features &raquo;</a>"
msgstr ""

#. Translators: %s - discount value $50.
#: src/Admin/Pages/SettingsTab.php:301
msgid "<strong>Bonus:</strong> WP Mail SMTP users get <span class=\"price-off\">%s off regular price</span>, automatically applied at checkout."
msgstr ""

#. translators: %s - WPMailSMTP.com Upgrade page URL.
#: src/Admin/Pages/SettingsTab.php:346
msgid "Don’t worry about losing emails. Add an additional connection, then set it as your Backup Connection. Emails that fail to send with the Primary Connection will be sent via the selected Backup Connection. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Upgrade to WP Mail SMTP Pro!</a>"
msgstr ""

#. translators: %s - Smart routing settings page url.
#: src/Admin/Pages/SettingsTab.php:378
msgid "Once you add an <a href=\"%s\">additional connection</a>, you can select it here."
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:106
msgid "Send emails from different additional connections based on your configured conditions. Emails that do not match any of the conditions below will be sent via your Primary Connection."
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:119
msgid "Enable Smart Routing"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:130
#: src/Admin/Pages/SmartRoutingTab.php:242
msgid "Send with"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:132
msgid "WooCommerce Emails (SendLayer)"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:134
#: src/Admin/Pages/SmartRoutingTab.php:246
msgid "if the following conditions are met..."
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:138
#: src/Admin/Pages/SmartRoutingTab.php:250
msgid "Arrow Up"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:141
#: src/Admin/Pages/SmartRoutingTab.php:253
msgid "Arrow Down"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:162
msgid "Contains"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:166
msgid "Order"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:170
#: src/Admin/Pages/SmartRoutingTab.php:193
#: src/Admin/Pages/SmartRoutingTab.php:223
#: src/Admin/Pages/SmartRoutingTab.php:282
msgid "And"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:185
#: src/Admin/Pages/SmartRoutingTab.php:215
#: src/Admin/Pages/SmartRoutingTab.php:274
msgid "Is"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:202
#: src/Admin/Pages/SmartRoutingTab.php:232
#: src/Admin/Pages/SmartRoutingTab.php:291
msgid "or"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:235
#: src/Admin/Pages/SmartRoutingTab.php:294
msgid "Add New Group"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:244
msgid "Contact Emails (SMTP.com)"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:269
msgid "Initiator"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:301
msgid "Light bulb icon"
msgstr ""

#: src/Admin/Pages/SmartRoutingTab.php:302
msgid "Friendly reminder, your Primary Connection will be used for all emails that do not match the conditions above."
msgstr ""

#: src/Admin/Pages/TestTab.php:113
msgid "Email Test"
msgstr ""

#: src/Admin/Pages/TestTab.php:164
msgid "Enter email address where test email will be sent."
msgstr ""

#: src/Admin/Pages/TestTab.php:181
msgid "HTML"
msgstr ""

#: src/Admin/Pages/TestTab.php:194
msgid "Send this email in HTML or in plain text format."
msgstr ""

#: src/Admin/Pages/TestTab.php:214
msgid "You cannot send an email. Mailer is not properly configured. Please check your settings."
msgstr ""

#: src/Admin/Pages/TestTab.php:218
msgid "Send Email"
msgstr ""

#: src/Admin/Pages/TestTab.php:275
msgid "Success!"
msgstr ""

#: src/Admin/Pages/TestTab.php:278
msgid "Test HTML email was sent successfully! Please check your inbox to make sure it was delivered."
msgstr ""

#: src/Admin/Pages/TestTab.php:280
msgid "Test plain text email was sent successfully! Please check your inbox to make sure it was delivered."
msgstr ""

#: src/Admin/Pages/TestTab.php:320
msgid "Test failed. Please use a valid email address and try to resend the test email."
msgstr ""

#. translators: %s - email address a test email will be sent to.
#: src/Admin/Pages/TestTab.php:332
#: src/Admin/Pages/TestTab.php:339
msgid "Test email to %s"
msgstr ""

#: src/Admin/Pages/TestTab.php:724
msgid "SSL certificate issue."
msgstr ""

#: src/Admin/Pages/TestTab.php:726
msgid "This means your web server cannot reliably make secure connections (make requests to HTTPS sites)."
msgstr ""

#: src/Admin/Pages/TestTab.php:727
#: src/Admin/Pages/TestTab.php:792
msgid "Typically this error is returned when web server is not configured properly."
msgstr ""

#: src/Admin/Pages/TestTab.php:730
msgid "Contact your web hosting provider and inform them your site has an issue with SSL certificates."
msgstr ""

#: src/Admin/Pages/TestTab.php:731
#: src/Admin/Pages/TestTab.php:796
msgid "The exact error you can provide them is in the Error log, available at the bottom of this page."
msgstr ""

#: src/Admin/Pages/TestTab.php:732
#: src/Admin/Pages/TestTab.php:797
msgid "Ask them to resolve the issue then try again."
msgstr ""

#: src/Admin/Pages/TestTab.php:742
msgid "Could not connect to host."
msgstr ""

#. translators: %s - SMTP host address.
#: src/Admin/Pages/TestTab.php:746
#: src/Admin/Pages/TestTab.php:788
#: src/Admin/Pages/TestTab.php:904
msgid "This means your web server was unable to connect to %s."
msgstr ""

#: src/Admin/Pages/TestTab.php:749
#: src/Admin/Pages/TestTab.php:791
#: src/Admin/Pages/TestTab.php:907
msgid "This means your web server was unable to connect to the host server."
msgstr ""

#: src/Admin/Pages/TestTab.php:750
msgid "Typically this error is returned your web server is blocking the connections or the SMTP host denying the request."
msgstr ""

#. translators: %s - SMTP host address.
#: src/Admin/Pages/TestTab.php:754
msgid "Contact your web hosting provider and ask them to verify your server can connect to %s. Additionally, ask them if a firewall or security policy may be preventing the connection."
msgstr ""

#: src/Admin/Pages/TestTab.php:757
msgid "If using \"Other SMTP\" Mailer, triple check your SMTP settings including host address, email, and password."
msgstr ""

#: src/Admin/Pages/TestTab.php:758
msgid "If using \"Other SMTP\" Mailer, contact your SMTP host to confirm they are accepting outside connections with the settings you have configured (address, username, port, security, etc)."
msgstr ""

#: src/Admin/Pages/TestTab.php:767
msgid "Invalid SendGrid API key"
msgstr ""

#: src/Admin/Pages/TestTab.php:769
msgid "It looks like your SendGrid API Key is invalid."
msgstr ""

#: src/Admin/Pages/TestTab.php:772
#: src/Admin/Pages/TestTab.php:859
msgid "Go to WP Mail SMTP plugin Settings page."
msgstr ""

#: src/Admin/Pages/TestTab.php:773
msgid "Make sure your API Key in the SendGrid mailer settings is correct and valid."
msgstr ""

#: src/Admin/Pages/TestTab.php:774
msgid "Save the plugin settings."
msgstr ""

#: src/Admin/Pages/TestTab.php:775
msgid "If updating the API Key doesn't resolve this issue, please contact our support."
msgstr ""

#: src/Admin/Pages/TestTab.php:784
msgid "Could not connect to your host."
msgstr ""

#: src/Admin/Pages/TestTab.php:795
msgid "Contact your web hosting provider and inform them you are having issues making outbound connections."
msgstr ""

#: src/Admin/Pages/TestTab.php:806
msgid "Could not authenticate your SMTP account."
msgstr ""

#: src/Admin/Pages/TestTab.php:808
msgid "This means we were able to connect to your SMTP host, but were not able to proceed using the email/password in the settings."
msgstr ""

#: src/Admin/Pages/TestTab.php:809
msgid "Typically this error is returned when the email or password is not correct or is not what the SMTP host is expecting."
msgstr ""

#: src/Admin/Pages/TestTab.php:812
msgid "Triple check your SMTP settings including host address, email, and password. If you have recently reset your password you will need to update the settings."
msgstr ""

#: src/Admin/Pages/TestTab.php:813
#: src/Admin/Pages/TestTab.php:940
msgid "Contact your SMTP host to confirm you are using the correct username and password."
msgstr ""

#: src/Admin/Pages/TestTab.php:814
#: src/Admin/Pages/TestTab.php:941
msgid "Verify with your SMTP host that your account has permissions to send emails using outside connections."
msgstr ""

#. translators: %s - URL to the wpmailsmtp.com doc page.
#: src/Admin/Pages/TestTab.php:817
msgid "Visit <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">our documentation</a> for additional tips on how to resolve this error."
msgstr ""

#: src/Admin/Pages/TestTab.php:837
msgid "Error due to unsolicited and/or bulk e-mail."
msgstr ""

#: src/Admin/Pages/TestTab.php:839
msgid "This means the connection to your SMTP host was made successfully, but the host rejected the email."
msgstr ""

#: src/Admin/Pages/TestTab.php:840
msgid "Typically this error is returned when you are sending too many e-mails or e-mails that have been identified as spam."
msgstr ""

#: src/Admin/Pages/TestTab.php:843
msgid "Make sure you are not sending emails with too many recipients. Example: single email should not have 10+ recipients. You can install any WordPress e-mail logging plugin to check your recipients (TO, CC and BCC)."
msgstr ""

#: src/Admin/Pages/TestTab.php:844
msgid "Contact your SMTP host to ask about sending/rate limits."
msgstr ""

#: src/Admin/Pages/TestTab.php:845
msgid "Verify with them your SMTP account is in good standing and your account has not been flagged."
msgstr ""

#: src/Admin/Pages/TestTab.php:854
msgid "Unauthenticated senders are not allowed."
msgstr ""

#: src/Admin/Pages/TestTab.php:856
msgid "This means the connection to your SMTP host was made successfully, but you should enable Authentication and provide correct Username and Password."
msgstr ""

#: src/Admin/Pages/TestTab.php:861
msgid "Enter correct SMTP Username (usually this is an email address) and Password in the appropriate fields."
msgstr ""

#: src/Admin/Pages/TestTab.php:872
msgid "Misconfigured server certificate."
msgstr ""

#: src/Admin/Pages/TestTab.php:874
msgid "This means OpenSSL on your server isn't able to verify the host certificate."
msgstr ""

#: src/Admin/Pages/TestTab.php:875
msgid "There are a few reasons why this is happening. It could be that the host certificate is misconfigured, or this server's OpenSSL is using an outdated CA bundle."
msgstr ""

#: src/Admin/Pages/TestTab.php:878
msgid "Verify that the host's SSL certificate is valid."
msgstr ""

#. translators: %s - URL to the PHP openssl manual
#: src/Admin/Pages/TestTab.php:881
msgid "Contact your hosting support, show them the \"full Error Log for debugging\" below and share this <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">link</a> with them."
msgstr ""

#: src/Admin/Pages/TestTab.php:900
msgid "Could not connect to the SMTP host."
msgstr ""

#: src/Admin/Pages/TestTab.php:908
#: src/Admin/Pages/TestTab.php:1372
msgid "Typically this error is returned for one of the following reasons:"
msgstr ""

#: src/Admin/Pages/TestTab.php:911
msgid "SMTP settings are incorrect (wrong port, security setting, incorrect host)."
msgstr ""

#: src/Admin/Pages/TestTab.php:914
#: src/Admin/Pages/TestTab.php:1378
msgid "Your web server is blocking the connection."
msgstr ""

#: src/Admin/Pages/TestTab.php:917
msgid "Your SMTP host is rejecting the connection."
msgstr ""

#: src/Admin/Pages/TestTab.php:922
msgid "Triple check your SMTP settings including host address, email, and password, port, and security."
msgstr ""

#. translators: %1$s - SMTP host address, %2$s - SMTP port, %3$s - SMTP encryption.
#: src/Admin/Pages/TestTab.php:925
msgid "Contact your web hosting provider and ask them to verify your server can connect to %1$s on port %2$s using %3$s encryption. Additionally, ask them if a firewall or security policy may be preventing the connection - many shared hosts block certain ports.<br><strong>Note: this is the most common cause of this issue.</strong>"
msgstr ""

#: src/Admin/Pages/TestTab.php:938
msgid "no"
msgstr ""

#: src/Admin/Pages/TestTab.php:950
#: src/Admin/Pages/TestTab.php:965
#: src/Admin/Pages/TestTab.php:1005
msgid "Mailgun failed."
msgstr ""

#: src/Admin/Pages/TestTab.php:952
msgid "It seems that you forgot to activate your Mailgun account."
msgstr ""

#: src/Admin/Pages/TestTab.php:955
msgid "Check your inbox you used to create a Mailgun account. Click the activation link in an email from Mailgun."
msgstr ""

#: src/Admin/Pages/TestTab.php:956
msgid "If you do not see activation email, go to your Mailgun control panel and resend the activation email."
msgstr ""

#: src/Admin/Pages/TestTab.php:967
msgid "Typically this error occurs because there is an issue with your Mailgun settings, in many cases Mailgun API Key, Domain Name, or Region is incorrect."
msgstr ""

#. translators: %1$s - Mailgun API Key area URL.
#: src/Admin/Pages/TestTab.php:972
msgid "Go to your Mailgun account and verify that your <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Mailgun API Key</a> is correct."
msgstr ""

#. translators: %1$s - Mailgun domains area URL.
#: src/Admin/Pages/TestTab.php:985
msgid "Verify your <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Domain Name</a> is correct."
msgstr ""

#: src/Admin/Pages/TestTab.php:996
msgid "Verify your domain Region is correct."
msgstr ""

#: src/Admin/Pages/TestTab.php:1007
msgid "Your Mailgun account does not have access to send emails."
msgstr ""

#: src/Admin/Pages/TestTab.php:1008
msgid "Typically this error occurs because you have not set up and/or complete domain name verification for your Mailgun account."
msgstr ""

#. translators: %s - Mailgun documentation URL.
#: src/Admin/Pages/TestTab.php:1013
msgid "Go to our how-to guide for setting up <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Mailgun with WP Mail SMTP</a>."
msgstr ""

#: src/Admin/Pages/TestTab.php:1025
msgid "Complete the steps in section \"2. Verify Your Domain\"."
msgstr ""

#: src/Admin/Pages/TestTab.php:1034
#: src/Admin/Pages/TestTab.php:1083
#: src/Admin/Pages/TestTab.php:1103
#: src/Admin/Pages/TestTab.php:1131
#: src/Admin/Pages/TestTab.php:1147
#: src/Admin/Pages/TestTab.php:1199
#: src/Admin/Pages/TestTab.php:1225
msgid "Google API Error."
msgstr ""

#: src/Admin/Pages/TestTab.php:1036
msgid "You have not properly configured Gmail mailer."
msgstr ""

#: src/Admin/Pages/TestTab.php:1037
msgid "Make sure that you have clicked the \"Allow plugin to send emails using your Google account\" button under Gmail settings."
msgstr ""

#: src/Admin/Pages/TestTab.php:1040
msgid "Go to plugin Settings page and click the \"Allow plugin to send emails using your Google account\" button."
msgstr ""

#: src/Admin/Pages/TestTab.php:1041
msgid "After the click you should be redirected to a Gmail authorization screen, where you will be asked a permission to send emails on your behalf."
msgstr ""

#: src/Admin/Pages/TestTab.php:1042
msgid "Please click \"Agree\", if you see that button. If not - you will need to enable less secure apps first:"
msgstr ""

#. translators: %s - Google support article URL.
#: src/Admin/Pages/TestTab.php:1047
msgid "if you are using regular Gmail account, please <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">read this article</a> to proceed."
msgstr ""

#. translators: %s - Google support article URL.
#: src/Admin/Pages/TestTab.php:1062
msgid "if you are using Google Workspace, please <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">read this article</a> to proceed."
msgstr ""

#: src/Admin/Pages/TestTab.php:1085
msgid "Typically this error occurs because the address to which the email was sent to is invalid or was empty."
msgstr ""

#: src/Admin/Pages/TestTab.php:1088
msgid "Check the \"Send To\" email address used and confirm it is a valid email and was not empty."
msgstr ""

#. translators: 1 - correct email address example. 2 - incorrect email address example.
#: src/Admin/Pages/TestTab.php:1090
msgid "It should be something like this: %1$s. These are incorrect values: %2$s."
msgstr ""

#: src/Admin/Pages/TestTab.php:1094
msgid "Make sure that the generated email has a TO header, useful when you are responsible for email creation."
msgstr ""

#: src/Admin/Pages/TestTab.php:1105
msgid "Unfortunately, this error can be due to many different reasons."
msgstr ""

#. translators: %s - Blog article URL.
#: src/Admin/Pages/TestTab.php:1108
msgid "Please <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">read this article</a> to learn more about what can cause this error and follow the steps below."
msgstr ""

#: src/Admin/Pages/TestTab.php:1121
#: src/Providers/Gmail/Mailer.php:249
msgid "Go to WP Mail SMTP plugin settings page. Click the “Remove OAuth Connection” button."
msgstr ""

#: src/Admin/Pages/TestTab.php:1122
#: src/Providers/Gmail/Mailer.php:250
msgid "Then click the “Allow plugin to send emails using your Google account” button and re-enable access."
msgstr ""

#: src/Admin/Pages/TestTab.php:1133
msgid "Authentication code that Google returned to you has already been used on your previous auth attempt."
msgstr ""

#: src/Admin/Pages/TestTab.php:1136
msgid "Make sure that you are not trying to manually clean up the plugin options to retry the \"Allow...\" step."
msgstr ""

#: src/Admin/Pages/TestTab.php:1137
msgid "Reinstall the plugin with clean plugin data turned on on Misc page. This will remove all the plugin options and you will be safe to retry."
msgstr ""

#: src/Admin/Pages/TestTab.php:1138
msgid "Make sure there is no aggressive caching on site admin area pages or try to clean cache between attempts."
msgstr ""

#: src/Admin/Pages/TestTab.php:1149
msgid "There are various reasons for that, please review the steps below."
msgstr ""

#. translators: %s - Google Workspace Admin area URL.
#: src/Admin/Pages/TestTab.php:1154
msgid "Make sure that your Google Workspace trial period has not expired. You can check the status <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">here</a>."
msgstr ""

#. translators: %s - Google Workspace Admin area URL.
#: src/Admin/Pages/TestTab.php:1167
msgid "Make sure that Gmail app in your Google Workspace is actually enabled. You can check that in Apps list in <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Google Workspace Admin</a> area."
msgstr ""

#. translators: %s - Google Developers Console URL.
#: src/Admin/Pages/TestTab.php:1180
msgid "Make sure that you have Gmail API enabled, and you can do that <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">here</a>."
msgstr ""

#: src/Admin/Pages/TestTab.php:1202
#: src/Admin/Pages/TestTab.php:1230
msgid "Make sure that the used Client ID/Secret correspond to a proper project that has Gmail API enabled."
msgstr ""

#. translators: %s - Gmail documentation URL.
#: src/Admin/Pages/TestTab.php:1205
msgid "Please follow our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Gmail tutorial</a> to be sure that all the correct project and data is applied."
msgstr ""

#: src/Admin/Pages/TestTab.php:1227
msgid "You may have added a new API to a project"
msgstr ""

#: src/Admin/Pages/TestTab.php:1231
msgid "Try to use a separate project for your emails, so the project has only 1 Gmail API in it enabled. You will need to remove the old project and create a new one from scratch."
msgstr ""

#: src/Admin/Pages/TestTab.php:1240
msgid "SMTP.com API Error."
msgstr ""

#: src/Admin/Pages/TestTab.php:1242
msgid "Your Sender Name option is incorrect."
msgstr ""

#: src/Admin/Pages/TestTab.php:1245
msgid "Please make sure you entered an accurate Sender Name in WP Mail SMTP plugin settings."
msgstr ""

#: src/Admin/Pages/TestTab.php:1254
msgid "GuzzleHttp requirements."
msgstr ""

#: src/Admin/Pages/TestTab.php:1256
msgid "GuzzleHttp requires cURL, the allow_url_fopen ini setting, or a custom HTTP handler."
msgstr ""

#: src/Admin/Pages/TestTab.php:1259
msgid "Edit your php.ini file on your hosting server."
msgstr ""

#: src/Admin/Pages/TestTab.php:1260
msgid "(Recommended) Enable PHP extension: cURL, by adding \"extension=curl\" to the php.ini file (without the quotation marks) OR"
msgstr ""

#: src/Admin/Pages/TestTab.php:1261
msgid "(If cURL can't be enabled on your hosting server) Enable PHP setting: allow_url_fopen, by adding \"allow_url_fopen = On\" to the php.ini file (without the quotation marks)"
msgstr ""

#: src/Admin/Pages/TestTab.php:1262
msgid "If you don't know how to do the above we strongly suggest contacting your hosting support and provide them the \"full Error Log for debugging\" below and these steps. They should be able to fix this issue for you."
msgstr ""

#: src/Admin/Pages/TestTab.php:1271
#: src/Admin/Pages/TestTab.php:1300
msgid "SparkPost API failed."
msgstr ""

#: src/Admin/Pages/TestTab.php:1273
msgid "Typically this error occurs because there is an issue with your SparkPost settings, in many cases an incorrect API key."
msgstr ""

#. translators: %1$s - SparkPost API Keys area URL, %1$s - SparkPost EU API Keys area URL.
#: src/Admin/Pages/TestTab.php:1278
msgid "Go to your <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">SparkPost account</a> or <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">SparkPost EU account</a> and verify that your API key is correct."
msgstr ""

#: src/Admin/Pages/TestTab.php:1291
msgid "Verify that your API key has \"Transmissions: Read/Write\" permission."
msgstr ""

#: src/Admin/Pages/TestTab.php:1302
msgid "Typically this error occurs because there is an issue with your SparkPost settings, in many cases an incorrect region."
msgstr ""

#: src/Admin/Pages/TestTab.php:1305
msgid "Verify that your SparkPost account region is selected in WP Mail SMTP settings."
msgstr ""

#: src/Admin/Pages/TestTab.php:1326
msgid "PCRE library issue"
msgstr ""

#: src/Admin/Pages/TestTab.php:1328
msgid "It looks like your server is running PHP version 7.4.x with an outdated PCRE library (libpcre2) that has a known issue with email address validation."
msgstr ""

#: src/Admin/Pages/TestTab.php:1329
msgid "There is a known issue with PHP version 7.4.x, when using libpcre2 library version lower than 10.33."
msgstr ""

#: src/Admin/Pages/TestTab.php:1332
msgid "Contact your web hosting provider and inform them you are having issues with libpcre2 library on PHP 7.4."
msgstr ""

#: src/Admin/Pages/TestTab.php:1333
msgid "They should be able to resolve this issue for you."
msgstr ""

#: src/Admin/Pages/TestTab.php:1334
msgid "For a quick fix, until your web hosting resolves this, you can downgrade to PHP version 7.3 on your server."
msgstr ""

#: src/Admin/Pages/TestTab.php:1369
msgid "An issue was detected."
msgstr ""

#: src/Admin/Pages/TestTab.php:1371
msgid "This means your test email was unable to be sent."
msgstr ""

#: src/Admin/Pages/TestTab.php:1375
msgid "Plugin settings are incorrect (wrong SMTP settings, invalid Mailer configuration, etc)."
msgstr ""

#: src/Admin/Pages/TestTab.php:1381
msgid "Your host is rejecting the connection."
msgstr ""

#: src/Admin/Pages/TestTab.php:1386
msgid "Triple-check the plugin settings and consider reconfiguring to make sure everything is correct. Maybe there was an issue with copy&pasting."
msgstr ""

#: src/Admin/Pages/TestTab.php:1388
msgid "Contact your web hosting provider and ask them to verify your server can make outside connections. Additionally, ask them if a firewall or security policy may be preventing the connection - many shared hosts block certain ports.<br><strong>Note: this is the most common cause of this issue.</strong>"
msgstr ""

#: src/Admin/Pages/TestTab.php:1394
msgid "Try using a different mailer."
msgstr ""

#: src/Admin/Pages/TestTab.php:1432
msgid "There was a problem while sending the test email."
msgstr ""

#: src/Admin/Pages/TestTab.php:1449
msgid "Recommended next steps:"
msgstr ""

#: src/Admin/Pages/TestTab.php:1457
msgid "Need support?"
msgstr ""

#. translators: %s - WPMailSMTP.com account area link.
#: src/Admin/Pages/TestTab.php:1465
msgid "As a WP Mail SMTP Pro user you have access to WP Mail SMTP priority support. Please log in to your WPMailSMTP.com account and <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">submit a support ticket</a>."
msgstr ""

#: src/Admin/Pages/TestTab.php:1483
msgid "WP Mail SMTP is a free plugin, and the team behind WPForms maintains it to give back to the WordPress community."
msgstr ""

#. translators: %s - WPMailSMTP.com URL.
#: src/Admin/Pages/TestTab.php:1490
msgid "To access our world class support, please <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">upgrade to WP Mail SMTP Pro</a>. Along with getting expert support, you will also get Notification controls, Email Logging, and integrations for Amazon SES, Office 365, and Outlook.com."
msgstr ""

#: src/Admin/Pages/TestTab.php:1505
msgid "Additionally, you can take advantage of our White Glove Setup. Sit back and relax while we handle everything for you! If you simply don't have time or maybe you feel a bit in over your head - we got you covered."
msgstr ""

#. Translators: %s - discount value $50
#: src/Admin/Pages/TestTab.php:1512
msgid "As a valued WP Mail SMTP user, you will get <span class=\"price-off\">%s off regular pricing</span>, automatically applied at checkout!"
msgstr ""

#. translators: %1$s - WP Mail SMTP support policy URL, %2$s - WP Mail SMTP support forum URL, %3$s - WPMailSMTP.com URL.
#: src/Admin/Pages/TestTab.php:1528
msgid "Alternatively, we also offer <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">limited support</a> on the WordPress.org support forums. You can <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">create a support thread</a> there, but please understand that free support is not guaranteed and is limited to simple issues. If you have an urgent or complex issue, then please consider <a href=\"%3$s\" target=\"_blank\" rel=\"noopener noreferrer\">upgrading to WP Mail SMTP Pro</a> to access our priority support ticket system."
msgstr ""

#: src/Admin/Pages/TestTab.php:1547
msgid "Please copy the error log message below into the support ticket."
msgstr ""

#: src/Admin/Pages/TestTab.php:1552
msgid "View Full Error Log"
msgstr ""

#: src/Admin/Pages/TestTab.php:1556
msgid "Copy Error Log"
msgstr ""

#: src/Admin/Pages/TestTab.php:1559
msgid "Copied"
msgstr ""

#: src/Admin/Pages/TestTab.php:1570
#: src/Admin/Pages/TestTab.php:1601
msgid "Send Another Test Email"
msgstr ""

#: src/Admin/Pages/TestTab.php:1593
msgid "The test email might have sent, but its deliverability should be improved."
msgstr ""

#: src/Admin/Pages/Tools.php:41
msgid "Tools"
msgstr ""

#: src/Admin/Pages/VersusTab.php:41
msgid "Lite vs Pro"
msgstr ""

#. translators: %s - plugin current license type.
#: src/Admin/Pages/VersusTab.php:72
msgid "%s vs Pro"
msgstr ""

#: src/Admin/Pages/VersusTab.php:80
msgid "Get the most out of WP Mail SMTP by upgrading to Pro and unlocking all of the powerful features."
msgstr ""

#: src/Admin/Pages/VersusTab.php:89
msgid "Feature"
msgstr ""

#: src/Admin/Pages/VersusTab.php:99
msgid "Pro"
msgstr ""

#: src/Admin/Pages/VersusTab.php:140
msgid "Get WP Mail SMTP Pro Today and Unlock all of these Powerful Features"
msgstr ""

#. Translators: %s - discount value $50.
#: src/Admin/Pages/VersusTab.php:148
msgid "Bonus: WP Mail SMTP Lite users get <span class=\"price-off\">%s off regular price</span>, automatically applied at checkout."
msgstr ""

#: src/Admin/Pages/VersusTab.php:178
msgid "Mailer Options"
msgstr ""

#: src/Admin/Pages/VersusTab.php:179
msgid "WordPress Multisite"
msgstr ""

#: src/Admin/Pages/VersusTab.php:180
msgid "Customer Support"
msgstr ""

#: src/Admin/Pages/VersusTab.php:201
msgid "Emails are not logged"
msgstr ""

#: src/Admin/Pages/VersusTab.php:207
msgid "Access to all Email Logging options right inside WordPress"
msgstr ""

#: src/Admin/Pages/VersusTab.php:215
msgid "No controls over whether default WordPress emails are sent"
msgstr ""

#: src/Admin/Pages/VersusTab.php:221
msgid "Complete Email Controls management for most default WordPress emails"
msgstr ""

#: src/Admin/Pages/VersusTab.php:229
msgid "Limited Mailers"
msgstr ""

#: src/Admin/Pages/VersusTab.php:229
msgid "Access is limited to standard mailer options only"
msgstr ""

#: src/Admin/Pages/VersusTab.php:235
msgid "Additional Mailer Options"
msgstr ""

#: src/Admin/Pages/VersusTab.php:235
msgid "Microsoft Outlook (with Office365 support), Amazon SES and Zoho Mail"
msgstr ""

#: src/Admin/Pages/VersusTab.php:243
msgid "No Global Network Settings"
msgstr ""

#: src/Admin/Pages/VersusTab.php:249
msgid "All Global Network Settings"
msgstr ""

#: src/Admin/Pages/VersusTab.php:249
msgid "Optionally configure settings at the network level or manage separately for each subsite"
msgstr ""

#: src/Admin/Pages/VersusTab.php:257
msgid "Limited Support"
msgstr ""

#: src/Admin/Pages/VersusTab.php:263
msgid "Priority Support"
msgstr ""

#: src/Admin/Review.php:137
msgid "Are you enjoying WP Mail SMTP?"
msgstr ""

#: src/Admin/Review.php:140
msgid "No"
msgstr ""

#: src/Admin/Review.php:144
msgid "We're sorry to hear you aren't enjoying WP Mail SMTP. We would love a chance to improve. Could you take a minute and let us know what we can do better?"
msgstr ""

#: src/Admin/Review.php:151
msgid "Provide Feedback"
msgstr ""

#: src/Admin/Review.php:156
msgid "No thanks"
msgstr ""

#: src/Admin/Review.php:161
msgid "That's fantastic! Would you consider giving it a 5-star rating on WordPress.org? It will help other users with email issues and it will mean the world to us!"
msgstr ""

#: src/Admin/Review.php:164
msgid "Yes, I'll rate it with 5-stars"
msgstr ""

#: src/Admin/Review.php:166
msgid "No, maybe later"
msgstr ""

#: src/Admin/Review.php:167
msgid "I already did"
msgstr ""

#: src/Admin/SetupWizard.php:238
msgid "We're sorry, the %mailer% mailer is not available on your plan. Please upgrade to the PRO plan to unlock all these awesome features."
msgstr ""

#. Translators: %s - discount value $50
#: src/Admin/SetupWizard.php:243
msgid "<strong>Bonus:</strong> WP Mail SMTP users get <span class=\"highlight\">%s off</span> regular price,<br>applied at checkout."
msgstr ""

#: src/Admin/SetupWizard.php:277
msgid "WP Mail SMTP &rsaquo; Setup Wizard"
msgstr ""

#: src/Admin/SetupWizard.php:485
msgid "Whoops, something's not working."
msgstr ""

#: src/Admin/SetupWizard.php:486
msgid "It looks like something is preventing JavaScript from loading on your website. WP Mail SMTP requires JavaScript in order to give you the best possible experience."
msgstr ""

#: src/Admin/SetupWizard.php:488
msgid "In order to fix this issue, please check each of the items below:"
msgstr ""

#: src/Admin/SetupWizard.php:491
msgid "If you are using an ad blocker, please disable it or whitelist the current page."
msgstr ""

#: src/Admin/SetupWizard.php:492
msgid "If you aren't already using Chrome, Firefox, Safari, or Edge, then please try switching to one of these popular browsers."
msgstr ""

#: src/Admin/SetupWizard.php:493
msgid "Confirm that your browser is updated to the latest version."
msgstr ""

#: src/Admin/SetupWizard.php:496
msgid "If you've checked each of these details and are still running into issues, then please get in touch with our support team. We’d be happy to help!"
msgstr ""

#: src/Admin/SetupWizard.php:502
msgid "Copy the error message above and paste it in a message to the WP Mail SMTP support team."
msgstr ""

#: src/Admin/SetupWizard.php:505
msgid "Contact Us"
msgstr ""

#: src/Admin/SetupWizard.php:552
#: src/Admin/SetupWizard.php:570
#: src/Admin/SetupWizard.php:634
msgid "You don't have permission to change options for this WP site!"
msgstr ""

#: src/Admin/SetupWizard.php:858
msgid "Could not install the plugin. You don't have permission to install plugins."
msgstr ""

#: src/Admin/SetupWizard.php:862
msgid "Could not install the plugin. You don't have permission to activate plugins."
msgstr ""

#: src/Admin/SetupWizard.php:868
msgid "Could not install the plugin. Plugin slug is missing."
msgstr ""

#: src/Admin/SetupWizard.php:888
#: src/Admin/SetupWizard.php:892
msgid "Could not install the plugin. Don't have file permission."
msgstr ""

#: src/Admin/SetupWizard.php:906
msgid "Could not install the plugin. WP Plugin installer initialization failed."
msgstr ""

#: src/Admin/SetupWizard.php:992
msgid "Could not install the plugin. WP Plugin installer could not retrieve plugin information."
msgstr ""

#: src/Admin/SetupWizard.php:1053
msgid "Contact Forms by WPForms"
msgstr ""

#: src/Admin/SetupWizard.php:1059
msgid "All in One SEO"
msgstr ""

#: src/Admin/SetupWizard.php:1065
msgid "Google Analytics by MonsterInsights"
msgstr ""

#: src/Admin/SetupWizard.php:1071
msgid "Code Snippets by WPCode"
msgstr ""

#: src/Admin/SetupWizard.php:1077
msgid "Giveaways by RafflePress"
msgstr ""

#: src/Admin/SetupWizard.php:1083
msgid "Smash Balloon Social Photo Feed"
msgstr ""

#: src/Admin/SetupWizard.php:1089
msgid "SeedProd Landing Page Builder"
msgstr ""

#: src/Admin/SetupWizard.php:1095
msgid "WP Call Button"
msgstr ""

#: src/Admin/SetupWizard.php:1170
msgid "You are already using the WP Mail SMTP PRO version. Please refresh this page and verify your license key."
msgstr ""

#: src/Admin/SetupWizard.php:1174
msgid "You don't have the permission to perform this action."
msgstr ""

#: src/Admin/SetupWizard.php:1180
msgid "Please enter a valid license key!"
msgstr ""

#: src/Admin/SetupWizard.php:1190
msgid "Upgrade functionality not available!"
msgstr ""

#: src/Conflicts.php:310
msgid "Or disable the Sendinblue email sending setting in WooCommerce > Settings > Sendinblue (tab) > Email Options (tab) > Enable Sendinblue to send WooCommerce emails."
msgstr ""

#: src/Conflicts.php:342
msgid "Or enable \"Do not change email sender by default\" setting in Settings > Email template > Sender (tab)."
msgstr ""

#: src/Conflicts.php:355
msgid "Or deactivate \"SMTP\" module in Branda > Emails > SMTP."
msgstr ""

#. translators: %1$s - Plugin name causing conflict.
#: src/Conflicts.php:492
msgid "Heads up! WP Mail SMTP has detected %1$s is activated. Please deactivate %1$s to prevent conflicts."
msgstr ""

#: src/Connect.php:56
msgid "Almost Done"
msgstr ""

#: src/Connect.php:57
msgid "Oops!"
msgstr ""

#: src/Connect.php:59
msgid "Unfortunately there was a server connection error."
msgstr ""

#: src/Connect.php:119
msgid "You are not allowed to install plugins."
msgstr ""

#: src/Connect.php:129
msgid "Please enter your license key to connect."
msgstr ""

#: src/Connect.php:137
msgid "Only the Lite version can be upgraded."
msgstr ""

#: src/Connect.php:152
msgid "WP Mail SMTP Pro was already installed, but was not active. We activated it for you."
msgstr ""

#: src/Connect.php:163
msgid "There was an error while generating an upgrade URL. Please try again."
msgstr ""

#: src/Connect.php:178
msgid "There was an error while installing an upgrade. Please download the plugin from wpmailsmtp.com and install it manually."
msgstr ""

#: src/Connect.php:231
msgid "There was an error while installing an upgrade. Please check file system permissions and try again. Also, you can download the plugin from wpmailsmtp.com and install it manually."
msgstr ""

#: src/Connect.php:263
msgid "There was an error while installing an upgrade. Please try again."
msgstr ""

#: src/Connect.php:301
msgid "Pro version installed but needs to be activated on the Plugins page."
msgstr ""

#: src/Connection.php:52
msgid "Primary"
msgstr ""

#. translators: %s - plugin name and its version.
#: src/Core.php:474
msgid "<strong>EMAILING DISABLED:</strong> The %s is currently blocking all emails from being sent."
msgstr ""

#. translators: %1$s - constant name; %2$s - constant value.
#: src/Core.php:481
msgid "To send emails, change the value of the %1$s constant to %2$s."
msgstr ""

#. translators: %s - plugin Misc settings page URL.
#: src/Core.php:488
msgid "To send emails, go to plugin <a href=\"%s\">Misc settings</a> and disable the \"Do Not Send\" option."
msgstr ""

#: src/Core.php:500
msgid "If you create a test email on this page, it will still be sent."
msgstr ""

#: src/Core.php:539
msgid "<strong>Heads up!</strong> The last email your site attempted to send was unsuccessful."
msgstr ""

#: src/Core.php:1351
msgid "WP Mail SMTP has detected incorrect \"wp_mail\" function location. Usually, this means that emails will not be sent successfully!"
msgstr ""

#. translators: %s - plugin name.
#: src/Core.php:1356
msgid "It looks like the \"%s\" plugin is overwriting the \"wp_mail\" function. Please reach out to the plugin developer on how to disable or remove the \"wp_mail\" function overwrite to prevent conflicts with WP Mail SMTP."
msgstr ""

#. translators: %s - must-use plugin name.
#: src/Core.php:1362
msgid "It looks like the \"%s\" must-use plugin is overwriting the \"wp_mail\" function. Please reach out to your hosting provider on how to disable or remove the \"wp_mail\" function overwrite to prevent conflicts with WP Mail SMTP."
msgstr ""

#: src/Core.php:1366
msgid "It looks like it's overwritten in the \"wp-config.php\" file. Please reach out to your hosting provider on how to disable or remove the \"wp_mail\" function overwrite to prevent conflicts with WP Mail SMTP."
msgstr ""

#. translators: %s - path.
#: src/Core.php:1371
msgid "Current function path: %s"
msgstr ""

#: src/DBRepair.php:101
msgid "Unknown."
msgstr ""

#. translators: %1$s - missing table name; %2$s - error message.
#: src/DBRepair.php:159
msgid "<strong>Table:</strong> %1$s. <strong>Reason:</strong> %2$s"
msgstr ""

#: src/DBRepair.php:189
msgid "Missing DB tables were created successfully."
msgstr ""

#: src/DBRepair.php:207
msgid "The following DB table is still missing."
msgid_plural "The following DB tables are still missing."
msgstr[0] ""
msgstr[1] ""

#: src/DBRepair.php:215
msgid "Some DB Tables are still missing."
msgstr ""

#: src/Helpers/UI.php:37
msgid "On"
msgstr ""

#: src/Helpers/UI.php:38
msgid "Off"
msgstr ""

#: src/Helpers/UI.php:87
msgid "Remove"
msgstr ""

#: src/MailCatcherTrait.php:218
#: src/Providers/MailerAbstract.php:308
msgid "An email request was sent."
msgstr ""

#: src/MailCatcherTrait.php:236
msgid "Debug Output:"
msgstr ""

#: src/MailCatcherTrait.php:306
msgid "The selected mailer not found."
msgstr ""

#: src/MailCatcherTrait.php:310
msgid "The selected mailer is not compatible with your PHP version."
msgstr ""

#. translators: %1$s - WP Mail SMTP, %2$s - error message.
#: src/Migration.php:135
msgid "There was an error while upgrading the database. Please contact %1$s support with this information: %2$s."
msgstr ""

#. translators: %1$s - the DB option name, %2$s - WP Mail SMTP, %3$s - error message.
#: src/MigrationAbstract.php:151
msgid "There was an error while upgrading the %1$s database. Please contact %2$s support with this information: %3$s."
msgstr ""

#. translators: %1$s - constant that was used; %2$s - file where it was used.
#: src/Options.php:1593
msgid "The value of this field was set using a constant %1$s most likely inside %2$s of your WordPress installation."
msgstr ""

#: src/Pro/SiteHealth.php:60
msgid "Is wpmailsmtp.com reachable?"
msgstr ""

#: src/Providers/AmazonSES/Options.php:25
msgid "Amazon SES"
msgstr ""

#: src/Providers/AmazonSES/Options.php:39
msgid "We're sorry, the Amazon SES mailer is not available on your plan. Please upgrade to the PRO plan to unlock all these awesome features."
msgstr ""

#: src/Providers/Gmail/Auth.php:144
msgid "Please make sure your Google Client ID and Secret in the plugin settings are valid. Save the settings and try the Authorization again."
msgstr ""

#: src/Providers/Gmail/Auth.php:257
msgid "There was an error while processing the Google authentication request. Please make sure that you have Client ID and Client Secret both valid and saved."
msgstr ""

#: src/Providers/Gmail/Mailer.php:98
msgid "An email request was sent to the Gmail API."
msgstr ""

#: src/Providers/Gmail/Mailer.php:120
msgid "The response object is invalid (missing getId method)."
msgstr ""

#: src/Providers/Gmail/Mailer.php:125
msgid "The email message ID is missing."
msgstr ""

#: src/Providers/Gmail/Mailer.php:248
msgid "Please re-grant Google app permissions!"
msgstr ""

#: src/Providers/Gmail/Options.php:38
msgid "Google / Gmail"
msgstr ""

#. translators: %s - URL to our Gmail doc.
#: src/Providers/Gmail/Options.php:41
msgid "Our Gmail mailer works with any Gmail or Google Workspace account via the Google API. You can send WordPress emails from your main email address or a Gmail alias, and it's more secure than connecting to Gmail using SMTP credentials. We now have a One-Click Setup, which simply asks you to authorize your Google account to use our app and takes care of everything for you. Alternatively, you can connect manually, which involves several steps that are more technical than other mailer options, so we created a detailed guide to walk you through the process.<br><br>To get started, read our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Gmail documentation</a>."
msgstr ""

#: src/Providers/Gmail/Options.php:55
msgid "The Gmail mailer works well for sites that send low numbers of emails. However, Gmail's API has rate limitations and a number of additional restrictions that can lead to challenges during setup.<br><br>If you expect to send a high volume of emails, or if you find that your web host is not compatible with the Gmail API restrictions, then we recommend considering a different mailer option."
msgstr ""

#: src/Providers/Gmail/Options.php:146
msgid "Remove Client Secret"
msgstr ""

#: src/Providers/Gmail/Options.php:158
msgid "Authorized redirect URI"
msgstr ""

#: src/Providers/Gmail/Options.php:166
msgid "Copy URL to clipboard"
msgstr ""

#: src/Providers/Gmail/Options.php:171
msgid "Please copy this URL into the \"Authorized redirect URIs\" field of your Google web application."
msgstr ""

#: src/Providers/Gmail/Options.php:208
msgid "Allow plugin to send emails using your Google account"
msgstr ""

#: src/Providers/Gmail/Options.php:211
msgid "Click the button above to confirm authorization."
msgstr ""

#. translators: %s - email address, as received from Google API.
#: src/Providers/Gmail/Options.php:226
msgid "Connected as %s"
msgstr ""

#. translators: %s - URL to Google Gmail alias documentation page.
#: src/Providers/Gmail/Options.php:236
msgid "If you want to use a different From Email address you can set up a Google email alias. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Follow these instructions</a> and then select the From Email at the top of this page."
msgstr ""

#: src/Providers/Gmail/Options.php:250
msgid "You can also send emails with different From Email addresses, by disabling the Force From Email setting and using registered aliases throughout your WordPress site as the From Email addresses."
msgstr ""

#: src/Providers/Gmail/Options.php:253
msgid "Removing the OAuth connection will give you an ability to redo the OAuth connection or link to another Google account."
msgstr ""

#: src/Providers/Gmail/Options.php:261
msgid "You need to save settings with Client ID and Client Secret before you can proceed."
msgstr ""

#: src/Providers/Mail/Options.php:26
msgid "Default (none)"
msgstr ""

#. translators: %1$s - URL to all mailer doc page. %2$s - URL to the setup wizard.
#: src/Providers/Mail/Options.php:41
msgid "You currently have the <strong>Default (none)</strong> mailer selected, which won't improve email deliverability. Please select <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">any other email provider</a> and use the easy <a href=\"%2$s\">Setup Wizard</a> to configure it."
msgstr ""

#: src/Providers/Mailgun/Options.php:29
msgid "Mailgun"
msgstr ""

#. translators: %1$s - URL to mailgun.com; %2$s - URL to Mailgun documentation on wpmailsmtp.com
#: src/Providers/Mailgun/Options.php:33
msgid "<a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Mailgun</a> is a transactional email provider that offers a generous 3-month free trial. After that, it offers a 'Pay As You Grow' plan that allows you to pay for what you use without committing to a fixed monthly rate.<br><br>To get started, read our <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Mailgun documentation</a>."
msgstr ""

#: src/Providers/Mailgun/Options.php:78
#: src/Providers/Mailjet/Options.php:111
#: src/Providers/Sendgrid/Options.php:86
#: src/Providers/Sendinblue/Options.php:130
#: src/Providers/Sendlayer/Options.php:120
#: src/Providers/SMTP2GO/Options.php:112
#: src/Providers/SMTPcom/Options.php:125
#: src/Providers/SparkPost/Options.php:106
msgid "Remove API Key"
msgstr ""

#. translators: %s - API key URL.
#: src/Providers/Mailgun/Options.php:87
msgid "Follow this link to <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">get a Mailgun API Key</a>. Generate a key in the \"Mailgun API Keys\" section."
msgstr ""

#. translators: %s - Domain Name link.
#: src/Providers/Mailgun/Options.php:118
msgid "Follow this link to get a Domain Name from Mailgun: %s."
msgstr ""

#: src/Providers/Mailgun/Options.php:120
msgid "Get a Domain Name"
msgstr ""

#: src/Providers/Mailgun/Options.php:154
msgid "Define which endpoint you want to use for sending messages."
msgstr ""

#: src/Providers/Mailgun/Options.php:155
msgid "If you are operating under EU laws, you may be required to use EU region."
msgstr ""

#. translators: %s - URL to Mailgun.com page.
#: src/Providers/Mailgun/Options.php:160
msgid "<a href=\"%s\" rel=\"\" target=\"_blank\">More information</a> on Mailgun.com."
msgstr ""

#. translators: %1$s - URL to Mailjet.com site.
#: src/Providers/Mailjet/Options.php:37
msgid "<a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Mailjet</a> is a cloud-based email service platform that enables businesses to send marketing and transactional emails, offering features like email automation, real-time analytics, and responsive design templates. If you're just starting out, you can send up to 200 emails per day without a credit card."
msgstr ""

#. translators: %2$s - URL to wpmailsmtp.com doc.
#: src/Providers/Mailjet/Options.php:40
msgid "To get started, read our <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Mailjet documentation</a>."
msgstr ""

#: src/Providers/Mailjet/Options.php:59
msgid "Mailjet"
msgstr ""

#. translators: %s - link to get an API Key.
#: src/Providers/Mailjet/Options.php:120
msgid "Follow this link to get the API key from Mailjet: %s."
msgstr ""

#: src/Providers/Mailjet/Options.php:122
#: src/Providers/Mailjet/Options.php:163
msgid "API Key Management"
msgstr ""

#: src/Providers/Mailjet/Options.php:152
msgid "Remove Secret Key"
msgstr ""

#. translators: %s - link to get an API Key.
#: src/Providers/Mailjet/Options.php:161
msgid "Follow this link to get the Secret key from Mailjet: %s."
msgstr ""

#. translators: %s - constant name: WPMS_SMTP_PASS.
#: src/Providers/OptionsAbstract.php:362
msgid "To change the password you need to change the value of the constant there: %s"
msgstr ""

#. translators: %1$s - wp-config.php file, %2$s - WPMS_ON constant name.
#: src/Providers/OptionsAbstract.php:370
msgid "If you want to disable the use of constants, find in %1$s file the constant %2$s and turn if off:"
msgstr ""

#: src/Providers/OptionsAbstract.php:380
msgid "All the defined constants will stop working and you will be able to change all the values on this page."
msgstr ""

#: src/Providers/OptionsAbstract.php:393
msgid "Remove Password"
msgstr ""

#: src/Providers/OptionsAbstract.php:399
msgid "The password is encrypted in the database, but for improved security we recommend using your site's WordPress configuration file to set your password."
msgstr ""

#: src/Providers/OptionsAbstract.php:405
msgid "Learn More"
msgstr ""

#. translators: %1$s - Provider name; %2$s - PHP version required by Provider; %3$s - current PHP version.
#: src/Providers/OptionsAbstract.php:466
msgid "%1$s requires PHP %2$s to work and does not support your current PHP version %3$s. Please contact your host and request a PHP upgrade to the latest one."
msgstr ""

#: src/Providers/OptionsAbstract.php:473
msgid "Meanwhile you can switch to some other mailers."
msgstr ""

#. translators: %s - Provider name
#: src/Providers/OptionsAbstract.php:492
msgid "%s requires an SSL certificate, and so is not currently compatible with your site. Please contact your host to request a SSL certificate, or check out <a href=\"https://www.wpbeginner.com/wp-tutorials/how-to-add-ssl-and-https-in-wordpress/\" target=\"_blank\">WPBeginner's tutorial on how to set up SSL</a>."
msgstr ""

#: src/Providers/OptionsAbstract.php:505
msgid "If you'd prefer not to set up SSL, or need an SMTP solution in the meantime, please select a different mailer option."
msgstr ""

#: src/Providers/Outlook/Options.php:25
msgid "365 / Outlook"
msgstr ""

#: src/Providers/Outlook/Options.php:39
msgid "We're sorry, the Microsoft Outlook mailer is not available on your plan. Please upgrade to the PRO plan to unlock all these awesome features."
msgstr ""

#: src/Providers/Pepipost/Options.php:25
msgid "Pepipost SMTP"
msgstr ""

#: src/Providers/PepipostAPI/Mailer.php:341
msgid "General error"
msgstr ""

#. translators: %1$s - URL to pepipost.com site.
#: src/Providers/PepipostAPI/Options.php:32
msgid "<a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Pepipost</a> is a transactional email service. Every month Pepipost delivers over 8 billion emails from 20,000+ customers. Their mission is to reliably send emails in the most efficient way and at the most disruptive pricing ever. Pepipost provides users 30,000 free emails the first 30 days."
msgstr ""

#. translators: %1$s - URL to wpmailsmtp.com doc.
#: src/Providers/PepipostAPI/Options.php:35
msgid "Read our <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Pepipost documentation</a> to learn how to configure Pepipost and improve your email deliverability."
msgstr ""

#: src/Providers/PepipostAPI/Options.php:55
msgid "Get Started with Pepipost"
msgstr ""

#: src/Providers/PepipostAPI/Options.php:63
msgid "Pepipost"
msgstr ""

#. translators: %s - link to get an API Key.
#: src/Providers/PepipostAPI/Options.php:115
#: src/Providers/Sendinblue/Options.php:139
msgid "Follow this link to get an API Key: %s."
msgstr ""

#: src/Providers/PepipostAPI/Options.php:117
msgid "Get the API Key"
msgstr ""

#: src/Providers/Postmark/Mailer.php:414
msgid "Server API Token:"
msgstr ""

#: src/Providers/Postmark/Mailer.php:416
msgid "Message Stream ID:"
msgstr ""

#. translators: %1$s - URL to postmarkapp.com site.
#: src/Providers/Postmark/Options.php:34
msgid "<a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Postmark</a> is a transactional email provider that offers great deliverability and accessible pricing for any business. You can start out with the free trial that allows you to send 100 test emails each month via its secure API."
msgstr ""

#. translators: %2$s - URL to wpmailsmtp.com doc.
#: src/Providers/Postmark/Options.php:37
msgid "To get started, read our <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Postmark documentation</a>."
msgstr ""

#: src/Providers/Postmark/Options.php:56
msgid "Postmark"
msgstr ""

#: src/Providers/Postmark/Options.php:107
msgid "Remove Server API Token"
msgstr ""

#. translators: %s - Server API Token link.
#: src/Providers/Postmark/Options.php:115
msgid "Follow this link to get a Server API Token from Postmark: %s."
msgstr ""

#: src/Providers/Postmark/Options.php:117
msgid "Get Server API Token"
msgstr ""

#. translators: %s - URL to Postmark documentation on wpmailsmtp.com
#: src/Providers/Postmark/Options.php:145
msgid "Message Stream ID is <strong>optional</strong>. By default <strong>outbound</strong> (Default Transactional Stream) will be used. More information can be found in our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Postmark documentation</a>."
msgstr ""

#: src/Providers/Sendgrid/Options.php:30
msgid "SendGrid"
msgstr ""

#. translators: %1$s - URL to sendgrid.com; %2$s - URL to Sendgrid documentation on wpmailsmtp.com
#: src/Providers/Sendgrid/Options.php:34
msgid "<a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">SendGrid</a> is a popular transactional email provider that sends more than 35 billion emails every month. If you're just starting out, the free plan allows you to send up to 100 emails each day without entering your credit card details.<br><br>To get started, read our <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">SendGrid documentation</a>."
msgstr ""

#. translators: %s - API key link.
#: src/Providers/Sendgrid/Options.php:95
msgid "Follow this link to get an API Key from SendGrid: %s."
msgstr ""

#: src/Providers/Sendgrid/Options.php:97
msgid "Create API Key"
msgstr ""

#. translators: %s - SendGrid access level.
#: src/Providers/Sendgrid/Options.php:105
msgid "To send emails you will need only a %s access level for this API key."
msgstr ""

#. translators: %s - URL to SendGrid documentation on wpmailsmtp.com
#: src/Providers/Sendgrid/Options.php:129
msgid "Please input the sending domain/subdomain you configured in your SendGrid dashboard. More information can be found in our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">SendGrid documentation</a>."
msgstr ""

#. translators: %1$s - URL to brevo.com site.
#: src/Providers/Sendinblue/Options.php:39
msgid "<strong><a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">Brevo</a> (formerly Sendinblue) is one of our recommended mailers.</strong> It's a transactional email provider with scalable price plans, so it's suitable for any size of business.<br><br>If you're just starting out, you can use Brevo's free plan to send up to 300 emails a day. You don't need to use a credit card to try it out. When you're ready, you can upgrade to a higher plan to increase your sending limits."
msgstr ""

#. translators: %2$s - URL to wpmailsmtp.com doc.
#: src/Providers/Sendinblue/Options.php:42
msgid "To get started, read our <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Brevo documentation</a>."
msgstr ""

#: src/Providers/Sendinblue/Options.php:63
msgid "Get Brevo Now (Free)"
msgstr ""

#: src/Providers/Sendinblue/Options.php:70
msgid "We believe in full transparency. The Brevo (formerly Sendinblue) links above are tracking links as part of our partnership with Brevo. We can recommend just about any SMTP service, but we only recommend products that we believe will add value to our users."
msgstr ""

#: src/Providers/Sendinblue/Options.php:77
msgid "Brevo"
msgstr ""

#: src/Providers/Sendinblue/Options.php:141
msgid "Get v3 API Key"
msgstr ""

#. translators: %s - URL to Sendinblue documentation on wpmailsmtp.com
#: src/Providers/Sendinblue/Options.php:165
msgid "Please input the sending domain/subdomain you configured in your Brevo (formerly Sendinblue) dashboard. More information can be found in our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Brevo documentation</a>."
msgstr ""

#: src/Providers/Sendlayer/Mailer.php:404
#: src/Providers/SparkPost/Mailer.php:484
msgid "API Key:"
msgstr ""

#. translators: %1$s - URL to sendlayer.com; %2$s - URL to SendLayer documentation on wpmailsmtp.com.
#: src/Providers/Sendlayer/Options.php:41
msgid "<strong><a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">SendLayer</a> is our #1 recommended mailer.</strong> Its affordable pricing and simple setup make it the perfect choice for WordPress sites. SendLayer will authenticate your outgoing emails to make sure they always hit customers’ inboxes, and it has detailed documentation to help you authorize your domain.<br><br>You can send hundreds of emails for free when you sign up for a trial.<br><br>To get started, read our <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">SendLayer documentation</a>."
msgstr ""

#: src/Providers/Sendlayer/Options.php:73
msgid "SendLayer"
msgstr ""

#. translators: %s - API key link.
#: src/Providers/Sendlayer/Options.php:129
msgid "Follow this link to get an API Key from SendLayer: %s."
msgstr ""

#: src/Providers/Sendlayer/Options.php:131
#: src/Providers/SMTPcom/Options.php:135
#: src/Providers/SparkPost/Options.php:120
msgid "Get API Key"
msgstr ""

#: src/Providers/SMTP/Options.php:28
msgid "Other SMTP"
msgstr ""

#. translators: %s - URL to SMTP documentation.
#: src/Providers/SMTP/Options.php:32
msgid "The Other SMTP option lets you send emails through an SMTP server instead of using a provider's API. This is easy and convenient, but it's less secure than the other mailers. Please note that your provider may not allow you to send a large number of emails. In that case, please use a different mailer.<br><br>To get started, read our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Other SMTP documentation</a>."
msgstr ""

#. translators: %1$s - URL to SMTP2GO.com site.
#: src/Providers/SMTP2GO/Options.php:38
msgid "<a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">SMTP2GO</a> provides a robust and reliable email delivery service with global infrastructure, real-time analytics, and advanced security features. If you're just starting out, you can use SMTP2GO's free plan to send up to 1000 emails per month."
msgstr ""

#. translators: %2$s - URL to wpmailsmtp.com doc.
#: src/Providers/SMTP2GO/Options.php:41
msgid "To get started, read our <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">SMTP2GO documentation</a>."
msgstr ""

#: src/Providers/SMTP2GO/Options.php:60
msgid "SMTP2GO"
msgstr ""

#. translators: %s - link to get an API Key.
#: src/Providers/SMTP2GO/Options.php:121
msgid "Generate an API key on the Sending &rarr; API Keys page in your %s."
msgstr ""

#: src/Providers/SMTP2GO/Options.php:123
msgid "control panel"
msgstr ""

#: src/Providers/SMTPcom/Mailer.php:451
msgid "Api Key:"
msgstr ""

#: src/Providers/SMTPcom/Mailer.php:453
msgid "Channel:"
msgstr ""

#. translators: %s - URL to smtp.com site.
#: src/Providers/SMTPcom/Options.php:49
msgid "<strong><a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">SMTP.com</a> is one of our recommended mailers.</strong> It's a transactional email provider that's currently used by 100,000+ businesses. SMTP.com is an established brand that's been offering email services for more than 20 years.<br><br>SMTP.com offers a free 30-day trial that allows you to send up to 50,000 emails."
msgstr ""

#. translators: %s - URL to wpmailsmtp.com doc page for stmp.com.
#: src/Providers/SMTPcom/Options.php:57
msgid "To get started, read our <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">SMTP.com documentation</a>."
msgstr ""

#: src/Providers/SMTPcom/Options.php:83
msgid "SMTP.com"
msgstr ""

#. translators: %s - API key link.
#: src/Providers/SMTPcom/Options.php:133
msgid "Follow this link to get an API Key from SMTP.com: %s."
msgstr ""

#. translators: %s - Channel/Sender Name link for smtp.com documentation.
#: src/Providers/SMTPcom/Options.php:162
msgid "Follow this link to get a Sender Name from SMTP.com: %s."
msgstr ""

#: src/Providers/SMTPcom/Options.php:164
msgid "Get Sender Name"
msgstr ""

#: src/Providers/SparkPost/Mailer.php:486
msgid "Region:"
msgstr ""

#. translators: %1$s - URL to SparkPost website.
#: src/Providers/SparkPost/Options.php:34
msgid "<a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">SparkPost</a> is a transactional email provider that's trusted by big brands and small businesses. It sends more than 4 trillion emails each year and reports 99.9%% uptime. You can get started with the free test account that lets you send up to 500 emails per month."
msgstr ""

#. translators: %2$s - URL to wpmailsmtp.com doc.
#: src/Providers/SparkPost/Options.php:37
msgid "To get started, read our <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">SparkPost documentation</a>."
msgstr ""

#: src/Providers/SparkPost/Options.php:55
msgid "SparkPost"
msgstr ""

#. translators: %s - API Key link.
#: src/Providers/SparkPost/Options.php:118
msgid "Follow this link to get an API Key from SparkPost: %s."
msgstr ""

#: src/Providers/SparkPost/Options.php:159
msgid "Select your SparkPost account region."
msgstr ""

#. translators: %s - URL to Mailgun.com page.
#: src/Providers/SparkPost/Options.php:164
msgid "<a href=\"%s\" rel=\"\" target=\"_blank\">More information</a> on SparkPost."
msgstr ""

#: src/Providers/Zoho/Options.php:25
msgid "Zoho Mail"
msgstr ""

#: src/Providers/Zoho/Options.php:41
msgid "We're sorry, the Zoho Mail mailer is not available on your plan. Please upgrade to the PRO plan to unlock all these awesome features."
msgstr ""

#: src/Queue/Email.php:150
msgid "Record not found in DB"
msgstr ""

#: src/Queue/Email.php:162
msgid "Invalid record format"
msgstr ""

#. translators: %1$s - JSON error message.
#: src/Queue/Email.php:171
msgid "Data JSON decoding error: %1$s"
msgstr ""

#. translators: %1$s - JSON error message.
#: src/Queue/Email.php:545
msgid "Data JSON encoding error: %1$s"
msgstr ""

#. translators: %1$s - Database error message.
#: src/Queue/Email.php:600
msgid "Insert/update SQL query error: %1$s"
msgstr ""

#. translators: %1$s - exception message.
#: src/Queue/Queue.php:117
msgid "[Emails Queue] Skipped enqueueing email. %1$s."
msgstr ""

#. translators: %1$d - email ID.
#: src/Queue/Queue.php:143
msgid "[Emails Queue] Skipped email sending from the queue. Queue::send_email method was called directly. Email ID: %1$d."
msgstr ""

#. translators: %1$s - exception message; %2$s - email ID.
#: src/Queue/Queue.php:159
msgid "[Emails Queue] Skipped email sending from the queue. %1$s. Email ID:  %2$s"
msgstr ""

#. translators: %1$d - email ID; %2$s - email status.
#: src/Queue/Queue.php:173
msgid "[Emails Queue] Skipped email sending from the queue. Wrong email status. Email ID: %1$d, email status: %2$s."
msgstr ""

#. translators: %1$s - exception message; %2$d - email ID.
#: src/Queue/Queue.php:231
msgid "[Emails Queue] Failed to update queue record after sending email from the queue. %1$s. Email ID: %2$d"
msgstr ""

#. translators: %1$s - exception message.
#: src/Queue/Queue.php:404
#: src/Queue/Queue.php:698
msgid "[Emails Queue] Skipped processing enqueued email. %1$s. Email ID: %2$d"
msgstr ""

#. translators: %s - site domain.
#: src/Reports/Emails/Summary.php:88
msgid "Your Weekly WP Mail SMTP Summary for %s"
msgstr ""

#: src/Reports/Emails/Summary.php:154
msgid "WP Mail SMTP Weekly Email Summary"
msgstr ""

#: src/Reports/Emails/Summary.php:169
#: src/Reports/Emails/Summary.php:173
msgid "WP Mail SMTP Logo"
msgstr ""

#. translators: %1$s - link to a site; %2$s - link to the settings page.
#: src/Reports/Emails/Summary.php:209
msgid "This email was auto-generated and sent from %1$s. Learn %2$s."
msgstr ""

#: src/Reports/Emails/Summary.php:211
msgid "how to disable it"
msgstr ""

#: src/Reports/Emails/Summary.php:254
msgid "Hi there,"
msgstr ""

#: src/Reports/Emails/Summary.php:257
msgid "Let’s see how many emails you’ve sent with WP Mail SMTP."
msgstr ""

#: src/Reports/Emails/Summary.php:265
msgid "Total Emails"
msgstr ""

#: src/Reports/Emails/Summary.php:276
msgid "Last week"
msgstr ""

#: src/Reports/Emails/Summary.php:292
msgid "Reports"
msgstr ""

#: src/Reports/Emails/Summary.php:293
msgid "Want More Stats?"
msgstr ""

#: src/Reports/Emails/Summary.php:297
msgid "Upgrade to <b>WP Mail SMTP Pro</b> and unlock <u>Email Log</u> and advanced <u>Email Reports</u>. Start measuring the success of your emails today!"
msgstr ""

#: src/SiteHealth.php:97
msgid "Is WP Mail SMTP mailer setup complete?"
msgstr ""

#: src/SiteHealth.php:102
msgid "Do WP Mail SMTP DB tables exist?"
msgstr ""

#: src/SiteHealth.php:107
msgid "Is email domain configured properly?"
msgstr ""

#: src/SiteHealth.php:133
msgid "Version"
msgstr ""

#: src/SiteHealth.php:137
msgid "License key type"
msgstr ""

#: src/SiteHealth.php:142
msgid "No debug notices found."
msgstr ""

#: src/SiteHealth.php:145
msgid "DB tables"
msgstr ""

#: src/SiteHealth.php:147
msgid "No DB tables found."
msgstr ""

#: src/SiteHealth.php:159
msgid "Lite install date"
msgstr ""

#: src/SiteHealth.php:176
msgid "None selected"
msgstr ""

#: src/SiteHealth.php:198
msgid "Current mailer"
msgstr ""

#: src/SiteHealth.php:203
msgid "WP Mail SMTP mailer setup is complete"
msgstr ""

#: src/SiteHealth.php:212
msgid "The WP Mail SMTP plugin mailer setup is complete. You can send a test email, to make sure it's working properly."
msgstr ""

#: src/SiteHealth.php:217
msgid "Test email sending"
msgstr ""

#: src/SiteHealth.php:225
msgid "You currently have the default mailer selected, which means that you haven’t set up SMTP yet."
msgstr ""

#: src/SiteHealth.php:230
msgid "WP Mail SMTP mailer setup is incomplete"
msgstr ""

#: src/SiteHealth.php:236
msgid "The WP Mail SMTP plugin mailer setup is incomplete. Please click on the link below to access plugin settings and configure the mailer."
msgstr ""

#: src/SiteHealth.php:241
#: src/SiteHealth.php:375
msgid "Configure mailer"
msgstr ""

#: src/SiteHealth.php:258
msgid "WP Mail SMTP DB tables are created"
msgstr ""

#: src/SiteHealth.php:264
msgid "WP Mail SMTP is using custom database tables for some of its features. In order to work properly, the custom tables should be created, and it looks like they exist in your database."
msgstr ""

#: src/SiteHealth.php:282
msgid "WP Mail SMTP DB tables check has failed"
msgstr ""

#. translators: %s - the list of missing tables separated by comma.
#: src/SiteHealth.php:288
msgid "Missing table: %s"
msgid_plural "Missing tables: %s"
msgstr[0] ""
msgstr[1] ""

#. translators: %1$s - Settings Page URL; %2$s - The aria label; %3$s - The text that will appear on the link.
#: src/SiteHealth.php:293
msgid "WP Mail SMTP is using custom database tables for some of its features. In order to work properly, the custom tables should be created, and it seems they are missing. Please try to <a href=\"%1$s\" target=\"_self\" aria-label=\"%2$s\" rel=\"noopener noreferrer\">%3$s</a>. If this issue persists, please contact our support."
msgstr ""

#: src/SiteHealth.php:295
msgid "Go to WP Mail SMTP settings page."
msgstr ""

#: src/SiteHealth.php:296
msgid "create the missing DB tables by clicking on this link"
msgstr ""

#: src/SiteHealth.php:333
msgid "Current from email domain"
msgstr ""

#: src/SiteHealth.php:338
msgid "Email domain is configured correctly"
msgstr ""

#: src/SiteHealth.php:347
msgid "All checks for your email domain were successful. It looks like everything is configured correctly."
msgstr ""

#: src/SiteHealth.php:365
msgid "Email domain issues detected"
msgstr ""

#. translators: %s: Directory path.
#: src/Uploads.php:58
msgid "Unable to create directory %s. Is its parent directory writable by the server?"
msgstr ""

#. translators: %s: Directory path.
#: src/Uploads.php:69
msgid "Unable to write in WPMailSMTP upload directory %s. Is it writable by the server?"
msgstr ""

#. translators: %1$s - date, \a\t - specially escaped "at", %2$s - time.
#: src/WP.php:205
msgid "%1$s \\a\\t %2$s"
msgstr ""

#. translators: %s - plugin name.
#: src/WP.php:552
msgid "WP Core (%s)"
msgstr ""

#: src/WP.php:693
msgid "WP Core"
msgstr ""

#: wp_mail_smtp.php:169
msgid "Please deactivate the free version of the WP Mail SMTP plugin before activating WP Mail SMTP Pro."
msgstr ""

#. translators: %1$s - WPBeginner URL for recommended WordPress hosting.
#: wp_mail_smtp.php:197
msgid "Your site is running an <strong>insecure version</strong> of PHP that is no longer supported. Please contact your web hosting provider to update your PHP version or switch to a <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">recommended WordPress hosting company</a>."
msgstr ""

#. translators: %s - WPMailSMTP.com docs URL with more details.
#: wp_mail_smtp.php:225
msgid "<strong>WP Mail SMTP plugin is disabled</strong> on your site until you fix the issue. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Read more for additional information.</a>"
msgstr ""

#. translators: %s The minimal WP version supported by WP Mail SMTP.
#: wp_mail_smtp.php:297
msgid "Your site is running an <strong>old version</strong> of WordPress that is no longer supported by WP Mail SMTP. Please update your WordPress site to at least version <strong>%s</strong>."
msgstr ""

#: wp_mail_smtp.php:308
msgid "<strong>WP Mail SMTP plugin is disabled</strong> on your site until WordPress is updated to the required version."
msgstr ""
