{"version": 3, "file": "acf-field-group.js", "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;AAEA,CAAE,UAAWA,CAAC,EAAEC,SAAS,EAAEC,GAAG,EAAG;EAChC,MAAMC,iBAAiB,GAAG;IACzBC,IAAI,EAAE;MACLC,QAAQ,EAAE,IAAI;MACdC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,CAClB,MAAM,EACN,UAAU,EACV,OAAO,EACP,KAAK,EACL,MAAM,EACN,SAAS,EACT,QAAQ,EACR,YAAY,EACZ,MAAM,EACN,aAAa,EACb,cAAc,EACd,UAAU,EACV,kBAAkB,EAClB,OAAO;IAET,CAAC;IAEDC,MAAM,EAAE;MACP,wBAAwB,EAAE,cAAc;MACxC,kCAAkC,EAAE,oBAAoB;MACxD,yBAAyB,EAAE,oBAAoB;MAC/C,uBAAuB,EAAE,kBAAkB;MAC3C,0BAA0B,EAAE,mBAAmB;MAC/C,+BAA+B,EAAE,oBAAoB;MACrD,kCAAkC,EAAE;IACrC,CAAC;IAEDC,KAAK,EAAE,SAAAA,CAAWC,KAAK,EAAG;MACzBV,CAAC,CAACW,MAAM,CAAE,IAAI,CAACP,IAAI,EAAEM,KAAM,CAAC;MAC5B,IAAI,CAACE,GAAG,GAAGZ,CAAC,CAAE,IAAI,CAACa,IAAI,CAAC,CAAE,CAAC;MAC3B,IAAI,CAACC,MAAM,CAAC,CAAC;IACd,CAAC;IAEDC,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,IAAI,CAACC,IAAI,CAAC,CAAC;MACX,IAAI,CAACC,gBAAgB,CAAE,IAAK,CAAC;MAC7B,IAAI,CAACL,GAAG,CAACM,IAAI,CAAE,kBAAmB,CAAC,CAACC,KAAK,CAAC,CAAC;MAC3CjB,GAAG,CAACkB,QAAQ,CAAE,MAAM,EAAE,IAAI,CAACR,GAAI,CAAC;IACjC,CAAC;IAEDC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACjB,OAAOb,CAAC,CAAE,+BAAgC,CAAC,CAACqB,IAAI,CAAC,CAAC;IACnD,CAAC;IAEDC,aAAa,EAAE,SAAAA,CAAWC,QAAQ,EAAEC,MAAM,EAAG;MAC5C,IAAIC,UAAU;MACd,IAAK,CAAEvB,GAAG,CAACwB,GAAG,CAAE,QAAS,CAAC,EAAG;QAC5B;QACAD,UAAU,GAAGE,MAAM,CAACC,MAAM,CAAAC,aAAA,CAAAA,aAAA,KACtB3B,GAAG,CAACwB,GAAG,CAAE,YAAa,CAAC,GACvBxB,GAAG,CAACwB,GAAG,CAAE,eAAgB,CAAC,CAC5B,CAAC;MACJ,CAAC,MAAM;QACND,UAAU,GAAGE,MAAM,CAACC,MAAM,CAAE1B,GAAG,CAACwB,GAAG,CAAE,YAAa,CAAE,CAAC;MACtD;MAEA,IAAKH,QAAQ,EAAG;QACf,IAAK,SAAS,KAAKA,QAAQ,EAAG;UAC7B,OAAOE,UAAU,CAACK,MAAM,CAAIC,SAAS,IACpC,IAAI,CAACL,GAAG,CAAE,mBAAoB,CAAC,CAACM,QAAQ,CACvCD,SAAS,CAACE,IACX,CACD,CAAC;QACF;QAEA,IAAK,KAAK,KAAKV,QAAQ,EAAG;UACzB,OAAOE,UAAU,CAACK,MAAM,CAAIC,SAAS,IAAMA,SAAS,CAACG,GAAI,CAAC;QAC3D;QAEAT,UAAU,GAAGA,UAAU,CAACK,MAAM,CAC3BC,SAAS,IAAMA,SAAS,CAACR,QAAQ,KAAKA,QACzC,CAAC;MACF;MAEA,IAAKC,MAAM,EAAG;QACbC,UAAU,GAAGA,UAAU,CAACK,MAAM,CAAIC,SAAS,IAAM;UAChD,MAAMI,KAAK,GAAGJ,SAAS,CAACI,KAAK,CAACC,WAAW,CAAC,CAAC;UAC3C,MAAMC,UAAU,GAAGF,KAAK,CAACG,KAAK,CAAE,GAAI,CAAC;UACrC,IAAIC,KAAK,GAAG,KAAK;UAEjB,IAAKJ,KAAK,CAACK,UAAU,CAAEhB,MAAM,CAACY,WAAW,CAAC,CAAE,CAAC,EAAG;YAC/CG,KAAK,GAAG,IAAI;UACb,CAAC,MAAM,IAAKF,UAAU,CAACI,MAAM,GAAG,CAAC,EAAG;YACnCJ,UAAU,CAACK,OAAO,CAAIC,IAAI,IAAM;cAC/B,IAAKA,IAAI,CAACH,UAAU,CAAEhB,MAAM,CAACY,WAAW,CAAC,CAAE,CAAC,EAAG;gBAC9CG,KAAK,GAAG,IAAI;cACb;YACD,CAAE,CAAC;UACJ;UAEA,OAAOA,KAAK;QACb,CAAE,CAAC;MACJ;MAEA,OAAOd,UAAU;IAClB,CAAC;IAEDX,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnBZ,GAAG,CAACkB,QAAQ,CAAE,QAAQ,EAAE,IAAI,CAACR,GAAI,CAAC;MAElC,MAAMgC,KAAK,GAAG,IAAI,CAAChC,GAAG,CAACM,IAAI,CAAE,sBAAuB,CAAC;MACrD,MAAM2B,IAAI,GAAG,IAAI;MAEjBD,KAAK,CAACE,IAAI,CAAE,YAAY;QACvB,MAAMvB,QAAQ,GAAGvB,CAAC,CAAE,IAAK,CAAC,CAACI,IAAI,CAAE,UAAW,CAAC;QAC7C,MAAMqB,UAAU,GAAGoB,IAAI,CAACvB,aAAa,CAAEC,QAAS,CAAC;QACjDE,UAAU,CAACiB,OAAO,CAAIX,SAAS,IAAM;UACpC/B,CAAC,CAAE,IAAK,CAAC,CAAC+C,MAAM,CAAEF,IAAI,CAACG,gBAAgB,CAAEjB,SAAU,CAAE,CAAC;QACvD,CAAE,CAAC;MACJ,CAAE,CAAC;MAEH,IAAI,CAACkB,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACzB,CAAC;IAEDH,gBAAgB,EAAE,SAAAA,CAAWjB,SAAS,EAAG;MACxC,MAAMqB,QAAQ,GAAGrB,SAAS,CAACE,IAAI,CAACoB,UAAU,CAAE,GAAG,EAAE,GAAI,CAAC;MAEtD,OAAQ;AACX,yDAA0DtB,SAAS,CAACE,IAAM;AAC1E,MACKF,SAAS,CAACG,GAAG,IAAI,CAAEhC,GAAG,CAACwB,GAAG,CAAE,QAAS,CAAC,GACnC,wFAAwF,GACxFK,SAAS,CAACG,GAAG,GACb,kDAAkD,GAClD,EACH;AACL,gDAAiDkB,QAAU;AAC3D,qCAAsCrB,SAAS,CAACI,KAAO;AACvD;AACA,IAAI;IACF,CAAC;IAEDmB,kBAAkB,EAAE,SAAAA,CAAWC,GAAG,EAAG;MACpC,IAAK,OAAOA,GAAG,IAAI,QAAQ,EAAG,OAAOA,GAAG;MACxC,OAAOA,GAAG,CAACF,UAAU,CAAE,QAAQ,EAAE,GAAI,CAAC;IACvC,CAAC;IAEDG,mBAAmB,EAAE,SAAAA,CAAWzB,SAAS,EAAG;MAC3C,MAAM0B,aAAa,GAClB,IAAI,CAACnC,aAAa,CAAC,CAAC,CAACQ,MAAM,CACxB4B,eAAe,IAAMA,eAAe,CAACzB,IAAI,KAAKF,SACjD,CAAC,CAAE,CAAC,CAAE,IAAI,CAAC,CAAC;MAEb,MAAM4B,IAAI,GAAGzD,GAAG,CAAC0D,SAAS,CAAEH,aAAa,EAAE;QAC1CtB,KAAK,EAAE,EAAE;QACT0B,WAAW,EAAE,EAAE;QACfC,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE,KAAK;QACnBC,aAAa,EAAE,KAAK;QACpB9B,GAAG,EAAE;MACN,CAAE,CAAC;MAEH,IAAI,CAACtB,GAAG,CAACM,IAAI,CAAE,kBAAmB,CAAC,CAAC+C,IAAI,CAAEN,IAAI,CAACxB,KAAM,CAAC;MACtD,IAAI,CAACvB,GAAG,CAACM,IAAI,CAAE,kBAAmB,CAAC,CAAC+C,IAAI,CAAEN,IAAI,CAACE,WAAY,CAAC;MAE5D,IAAKF,IAAI,CAACG,OAAO,EAAG;QACnB,IAAI,CAAClD,GAAG,CACNM,IAAI,CAAE,iBAAkB,CAAC,CACzBgD,IAAI,CAAE,MAAM,EAAE,IAAI,CAACZ,kBAAkB,CAAEK,IAAI,CAACG,OAAQ,CAAE,CAAC,CACvDK,IAAI,CAAC,CAAC;MACT,CAAC,MAAM;QACN,IAAI,CAACvD,GAAG,CAACM,IAAI,CAAE,iBAAkB,CAAC,CAACkD,IAAI,CAAC,CAAC;MAC1C;MAEA,IAAKT,IAAI,CAACI,YAAY,EAAG;QACxB,IAAI,CAACnD,GAAG,CACNM,IAAI,CAAE,sBAAuB,CAAC,CAC9BgD,IAAI,CACJ,MAAM,EACN,IAAI,CAACZ,kBAAkB,CAAEK,IAAI,CAACI,YAAa,CAC5C,CAAC,CACAM,MAAM,CAAC,CAAC,CACRF,IAAI,CAAC,CAAC;MACT,CAAC,MAAM;QACN,IAAI,CAACvD,GAAG,CAACM,IAAI,CAAE,sBAAuB,CAAC,CAACmD,MAAM,CAAC,CAAC,CAACD,IAAI,CAAC,CAAC;MACxD;MAEA,IAAKT,IAAI,CAACK,aAAa,EAAG;QACzB,IAAI,CAACpD,GAAG,CACNM,IAAI,CAAE,mBAAoB,CAAC,CAC3BgD,IAAI,CAAE,KAAK,EAAEP,IAAI,CAACK,aAAc,CAAC,CACjCG,IAAI,CAAC,CAAC;MACT,CAAC,MAAM;QACN,IAAI,CAACvD,GAAG,CAACM,IAAI,CAAE,mBAAoB,CAAC,CAACkD,IAAI,CAAC,CAAC;MAC5C;MAEA,MAAME,KAAK,GAAGpE,GAAG,CAACwB,GAAG,CAAE,QAAS,CAAC;MACjC,MAAM6C,kBAAkB,GAAG,IAAI,CAAC3D,GAAG,CAACM,IAAI,CAAE,cAAe,CAAC;MAC1D,MAAMsD,sBAAsB,GAAG,IAAI,CAAC5D,GAAG,CAACM,IAAI,CAC3C,+BACD,CAAC;MAED,IAAKyC,IAAI,CAACzB,GAAG,IAAI,CAAEoC,KAAK,EAAG;QAC1BC,kBAAkB,CAACJ,IAAI,CAAC,CAAC;QACzBI,kBAAkB,CAACL,IAAI,CACtB,MAAM,EACNK,kBAAkB,CAACnE,IAAI,CAAE,SAAU,CAAC,GAAG2B,SACxC,CAAC;QAEDyC,sBAAsB,CAACL,IAAI,CAAC,CAAC;QAC7BK,sBAAsB,CAACN,IAAI,CAC1B,MAAM,EACNM,sBAAsB,CAACpE,IAAI,CAAE,SAAU,CAAC,GAAG2B,SAC5C,CAAC;QACD,IAAI,CAACnB,GAAG,CACNM,IAAI,CAAE,yBAA0B,CAAC,CACjCgD,IAAI,CAAE,UAAU,EAAE,IAAK,CAAC;QAC1B,IAAI,CAACtD,GAAG,CAACM,IAAI,CAAE,mBAAoB,CAAC,CAACkD,IAAI,CAAC,CAAC;MAC5C,CAAC,MAAM;QACNG,kBAAkB,CAACH,IAAI,CAAC,CAAC;QACzBI,sBAAsB,CAACJ,IAAI,CAAC,CAAC;QAC7B,IAAI,CAACxD,GAAG,CACNM,IAAI,CAAE,yBAA0B,CAAC,CACjCgD,IAAI,CAAE,UAAU,EAAE,KAAM,CAAC;QAC3B,IAAI,CAACtD,GAAG,CAACM,IAAI,CAAE,mBAAoB,CAAC,CAACiD,IAAI,CAAC,CAAC;MAC5C;IACD,CAAC;IAEDjB,mBAAmB,EAAE,SAAAA,CAAA,EAAY;MAAA,IAAAuB,iBAAA;MAChC,MAAMC,WAAW,GAAG,IAAI,CAAChD,GAAG,CAAE,UAAW,CAAC;MAC1C,MAAMK,SAAS,GAAG2C,WAAW,aAAXA,WAAW,gBAAAD,iBAAA,GAAXC,WAAW,CAAEtE,IAAI,cAAAqE,iBAAA,uBAAjBA,iBAAA,CAAmBE,IAAI;;MAEzC;MACA,IAAK5C,SAAS,EAAG;QAChB,IAAI,CAAC6C,GAAG,CAAE,kBAAkB,EAAE7C,SAAU,CAAC;MAC1C,CAAC,MAAM;QACN,IAAI,CAAC6C,GAAG,CAAE,kBAAkB,EAAE,MAAO,CAAC;MACvC;;MAEA;MACA;MACA;MACA,MAAMnD,UAAU,GAAG,IAAI,CAACH,aAAa,CAAC,CAAC;MACvC,MAAMuD,kBAAkB,GACvB,IAAI,CAACnD,GAAG,CAAE,mBAAoB,CAAC,CAACM,QAAQ,CAAED,SAAU,CAAC;MAEtD,IAAIR,QAAQ,GAAG,EAAE;MACjB,IAAKsD,kBAAkB,EAAG;QACzBtD,QAAQ,GAAG,SAAS;MACrB,CAAC,MAAM;QACN,MAAMuD,iBAAiB,GAAGrD,UAAU,CAACP,IAAI,CAAI6D,CAAC,IAAM;UACnD,OAAOA,CAAC,CAAC9C,IAAI,KAAKF,SAAS;QAC5B,CAAE,CAAC;QAEHR,QAAQ,GAAGuD,iBAAiB,CAACvD,QAAQ;MACtC;MAEA,MAAMyD,iBAAiB,GACtBzD,QAAQ,CAAE,CAAC,CAAE,CAAC0D,WAAW,CAAC,CAAC,GAAG1D,QAAQ,CAAC2D,KAAK,CAAE,CAAE,CAAC;MAClD,MAAMC,gBAAgB,GAAI,gDAAgDH,iBAAmB,IAAG;MAChGI,UAAU,CAAE,MAAM;QACjBpF,CAAC,CAAEmF,gBAAiB,CAAC,CAACE,KAAK,CAAC,CAAC;MAC9B,CAAC,EAAE,CAAE,CAAC;IACP,CAAC;IAEDpC,oBAAoB,EAAE,SAAAA,CAAA,EAAY;MACjC,MAAMyB,WAAW,GAAG,IAAI,CAAChD,GAAG,CAAE,UAAW,CAAC;MAC1C,MAAM4D,SAAS,GAAGZ,WAAW,CAACa,WAAW,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC;MACjD,MAAMD,WAAW,GAAG,IAAI,CAAC3E,GAAG,CAACM,IAAI,CAAE,yBAA0B,CAAC;MAC9D,IAAKoE,SAAS,EAAG;QAChBC,WAAW,CAACC,GAAG,CAAEF,SAAU,CAAC;MAC7B,CAAC,MAAM;QACNC,WAAW,CAACC,GAAG,CAAE,EAAG,CAAC;MACtB;IACD,CAAC;IAEDC,2BAA2B,EAAE,SAAAA,CAAA,EAAY;MACxC,MAAMtD,KAAK,GAAG,IAAI,CAACvB,GAAG,CAACM,IAAI,CAAE,yBAA0B,CAAC,CAACsE,GAAG,CAAC,CAAC;MAC9D,MAAMd,WAAW,GAAG,IAAI,CAAChD,GAAG,CAAE,UAAW,CAAC;MAC1CgD,WAAW,CAACa,WAAW,CAAC,CAAC,CAACC,GAAG,CAAErD,KAAM,CAAC;MACtCuC,WAAW,CAACa,WAAW,CAAC,CAAC,CAACG,OAAO,CAAE,MAAO,CAAC;IAC5C,CAAC;IAEDvC,iBAAiB,EAAE,SAAAA,CAAA,EAAY;MAC9B,MAAMpB,SAAS,GAAG,IAAI,CAACL,GAAG,CAAE,kBAAmB,CAAC;MAEhD,IAAI,CAACd,GAAG,CAACM,IAAI,CAAE,WAAY,CAAC,CAACyE,WAAW,CAAE,UAAW,CAAC;MACtD,IAAI,CAAC/E,GAAG,CACNM,IAAI,CAAE,mCAAmC,GAAGa,SAAS,GAAG,IAAK,CAAC,CAC9D6D,QAAQ,CAAE,UAAW,CAAC;MAExB,IAAI,CAACpC,mBAAmB,CAAEzB,SAAU,CAAC;IACtC,CAAC;IAED8D,kBAAkB,EAAE,SAAAA,CAAWC,CAAC,EAAG;MAClC,MAAMC,MAAM,GAAG,IAAI,CAACnF,GAAG,CAACM,IAAI,CAAE,0BAA2B,CAAC;MAC1D,MAAM8E,QAAQ,GAAG,IAAI,CAACpF,GAAG,CAACM,IAAI,CAAE,yBAA0B,CAAC,CAACsE,GAAG,CAAC,CAAC;MACjE,MAAM3C,IAAI,GAAG,IAAI;MACjB,IAAIoD,YAAY;QACfC,WAAW,GAAG,EAAE;MACjB,IAAIC,OAAO,GAAG,EAAE;MAEhB,IAAK,QAAQ,KAAK,OAAOH,QAAQ,EAAG;QACnCC,YAAY,GAAGD,QAAQ,CAACI,IAAI,CAAC,CAAC;QAC9BD,OAAO,GAAG,IAAI,CAAC7E,aAAa,CAAE,KAAK,EAAE2E,YAAa,CAAC;MACpD;MAEA,IAAKA,YAAY,CAACxD,MAAM,IAAI0D,OAAO,CAAC1D,MAAM,EAAG;QAC5CsD,MAAM,CAACH,QAAQ,CAAE,cAAe,CAAC;MAClC,CAAC,MAAM;QACNG,MAAM,CAACJ,WAAW,CAAE,cAAe,CAAC;MACrC;MAEA,IAAK,CAAEQ,OAAO,CAAC1D,MAAM,EAAG;QACvBsD,MAAM,CAACH,QAAQ,CAAE,kBAAmB,CAAC;QACrC,IAAI,CAAChF,GAAG,CACNM,IAAI,CAAE,0BAA2B,CAAC,CAClC+C,IAAI,CAAEgC,YAAa,CAAC;QACtB;MACD,CAAC,MAAM;QACNF,MAAM,CAACJ,WAAW,CAAE,kBAAmB,CAAC;MACzC;MAEAQ,OAAO,CAACzD,OAAO,CAAIX,SAAS,IAAM;QACjCmE,WAAW,GAAGA,WAAW,GAAGrD,IAAI,CAACG,gBAAgB,CAAEjB,SAAU,CAAC;MAC/D,CAAE,CAAC;MAEH/B,CAAC,CAAE,gCAAiC,CAAC,CAACqB,IAAI,CAAE6E,WAAY,CAAC;MAEzD,IAAI,CAACtB,GAAG,CAAE,kBAAkB,EAAEuB,OAAO,CAAE,CAAC,CAAE,CAAClE,IAAK,CAAC;MACjD,IAAI,CAACkB,iBAAiB,CAAC,CAAC;IACzB,CAAC;IAEDkD,oBAAoB,EAAE,SAAAA,CAAA,EAAY;MACjC,IAAI,CAACzF,GAAG,CACNM,IAAI,CAAE,yBAA0B,CAAC,CACjCsE,GAAG,CAAE,EAAG,CAAC,CACTE,OAAO,CAAE,OAAQ,CAAC;MACpB,IAAI,CAAC9E,GAAG,CAACM,IAAI,CAAE,iBAAkB,CAAC,CAACoF,KAAK,CAAC,CAAC,CAACZ,OAAO,CAAE,OAAQ,CAAC;IAC9D,CAAC;IAEDa,kBAAkB,EAAE,SAAAA,CAAWT,CAAC,EAAG;MAClC,MAAMpB,WAAW,GAAG,IAAI,CAAChD,GAAG,CAAE,UAAW,CAAC;MAE1CgD,WAAW,CACT8B,gBAAgB,CAAC,CAAC,CAClBhB,GAAG,CAAE,IAAI,CAAC9D,GAAG,CAAE,kBAAmB,CAAE,CAAC;MACvCgD,WAAW,CAAC8B,gBAAgB,CAAC,CAAC,CAACd,OAAO,CAAE,QAAS,CAAC;MAElD,IAAI,CAACD,2BAA2B,CAAC,CAAC;MAElC,IAAI,CAACgB,KAAK,CAAC,CAAC;IACb,CAAC;IAEDC,gBAAgB,EAAE,SAAAA,CAAWZ,CAAC,EAAG;MAChC,MAAMa,UAAU,GAAG3G,CAAC,CAAE8F,CAAC,CAACc,aAAc,CAAC;MACvC,IAAI,CAAChC,GAAG,CAAE,kBAAkB,EAAE+B,UAAU,CAACvG,IAAI,CAAE,YAAa,CAAE,CAAC;IAChE,CAAC;IAEDyG,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,IAAI,CAACJ,KAAK,CAAC,CAAC;IACb,CAAC;IAEDK,kBAAkB,EAAE,SAAAA,CAAWhB,CAAC,EAAG;MAClC,IAAKA,CAAC,CAACiB,GAAG,KAAK,QAAQ,EAAG;QACzB,IAAI,CAACN,KAAK,CAAC,CAAC;MACb;IACD,CAAC;IAEDA,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,IAAI,CAACxF,gBAAgB,CAAE,KAAM,CAAC;MAC9B,IAAI,CAAC+F,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACC,MAAM,CAAC,CAAC;IACd,CAAC;IAED9F,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,IAAI,CAACP,GAAG,CAACM,IAAI,CAAE,QAAS,CAAC,CAACoF,KAAK,CAAC,CAAC,CAACZ,OAAO,CAAE,OAAQ,CAAC;IACrD;EACD,CAAC;EAEDxF,GAAG,CAACgH,MAAM,CAAC/G,iBAAiB,GAAGD,GAAG,CAACgH,MAAM,CAACC,KAAK,CAACxG,MAAM,CAAER,iBAAkB,CAAC;EAC3ED,GAAG,CAACkH,oBAAoB,GAAK1G,KAAK,IACjC,IAAIR,GAAG,CAACgH,MAAM,CAAC/G,iBAAiB,CAAEO,KAAM,CAAC;AAC3C,CAAC,EAAI2G,MAAM,CAACC,MAAM,EAAErH,SAAS,EAAEoH,MAAM,CAACnH,GAAI,CAAC;;;;;;;;;;ACnY3C,CAAE,UAAWF,CAAC,EAAEC,SAAS,EAAG;EAC3B,IAAIsH,IAAI,GAAGrH,GAAG,CAACsH,gBAAgB,CAAEtH,GAAI,CAAC;;EAEtC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECqH,IAAI,CAACE,WAAW,GAAG;IAClBC,UAAU,EAAE,SAAAA,CAAWC,MAAM,EAAEhD,IAAI,EAAG;MACrCA,IAAI,GAAGA,IAAI,KAAK1E,SAAS,GAAG0E,IAAI,GAAG,UAAU;MAC7CzE,GAAG,CAAC0H,cAAc,CAAED,MAAO,CAAC,CAACE,IAAI,CAAElD,IAAK,CAAC;IAC1C,CAAC;IAEDmD,YAAY,EAAE,SAAAA,CAAWH,MAAM,EAAEI,OAAO,EAAG;MAC1CA,OAAO,GAAGA,OAAO,KAAK9H,SAAS,GAAG8H,OAAO,GAAG,IAAI;MAChD7H,GAAG,CAAC0H,cAAc,CAAED,MAAO,CAAC,CAACK,MAAM,CAAE;QACpCD,OAAO,EAAEA;MACV,CAAE,CAAC;IACJ,CAAC;IAEDE,iBAAiB,EAAE,SAAAA,CAAWN,MAAM,EAAE1F,IAAI,EAAEiG,KAAK,EAAG;MACnDhI,GAAG,CAAC0H,cAAc,CAAED,MAAO,CAAC,CAACQ,IAAI,CAAElG,IAAI,EAAEiG,KAAM,CAAC;IACjD,CAAC;IAEDE,iBAAiB,EAAE,SAAAA,CAAWT,MAAM,EAAE1F,IAAI,EAAG;MAC5C/B,GAAG,CAAC0H,cAAc,CAAED,MAAO,CAAC,CAACQ,IAAI,CAAElG,IAAI,EAAE,IAAK,CAAC;IAChD;EACD,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECsF,IAAI,CAACE,WAAW,CAACY,YAAY,GAAGnI,GAAG,CAACoI,KAAK,CAAC3H,MAAM,CAAE;IACjD;IACAgE,IAAI,EAAE,EAAE;IACR4D,CAAC,EAAE,CAAC,CAAC;IACLZ,MAAM,EAAE,IAAI;IACZa,SAAS,EAAE,IAAI;IAEfC,GAAG,EAAE,SAAAA,CAAWA,GAAG,EAAG;MACrB;MACA,IAAI9D,IAAI,GAAG,IAAI,CAACA,IAAI;;MAEpB;MACA;MACA;MACA,IAAI+D,IAAI,GAAGD,GAAG,CAACnG,KAAK,CAAE,GAAI,CAAC;MAC3BoG,IAAI,CAACC,MAAM,CAAE,CAAC,EAAE,CAAC,EAAE,OAAQ,CAAC;MAC5BF,GAAG,GAAGC,IAAI,CAACE,IAAI,CAAE,GAAI,CAAC;;MAEtB;MACA,IAAKjE,IAAI,EAAG;QACX8D,GAAG,IAAI,QAAQ,GAAG9D,IAAI;MACvB;;MAEA;MACA,OAAO8D,GAAG;IACX,CAAC;IAEDI,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB;MACA,IAAIA,QAAQ,GAAG,mBAAmB;MAClC,IAAIlE,IAAI,GAAG,IAAI,CAACA,IAAI;;MAEpB;MACA,IAAKA,IAAI,EAAG;QACXkE,QAAQ,IAAI,GAAG,GAAGlE,IAAI;QACtBkE,QAAQ,GAAG3I,GAAG,CAAC4I,WAAW,CAAE,GAAG,EAAE,GAAG,EAAED,QAAS,CAAC;MACjD;;MAEA;MACA,OAAOA,QAAQ;IAChB,CAAC;IAEDE,WAAW,EAAE,SAAAA,CAAW9G,IAAI,EAAE+G,QAAQ,EAAG;MACxC;MACA,IAAIV,KAAK,GAAG,IAAI;;MAEhB;MACApI,GAAG,CAAC+I,UAAU,CAAE,IAAI,CAACR,GAAG,CAAExG,IAAK,CAAC,EAAE,UAAW0F,MAAM,EAAG;QACrD;QACAW,KAAK,CAAC1D,GAAG,CAAE,QAAQ,EAAE+C,MAAO,CAAC;;QAE7B;QACAW,KAAK,CAAEU,QAAQ,CAAE,CAACE,KAAK,CAAEZ,KAAK,EAAEa,SAAU,CAAC;MAC5C,CAAE,CAAC;IACJ,CAAC;IAEDC,WAAW,EAAE,SAAAA,CAAWnH,IAAI,EAAE+G,QAAQ,EAAG;MACxC;MACA,IAAIV,KAAK,GAAG,IAAI;;MAEhB;MACApI,GAAG,CAACmJ,UAAU,CAAE,IAAI,CAACZ,GAAG,CAAExG,IAAK,CAAC,EAAE,UAAW0F,MAAM,EAAG;QACrD;QACAW,KAAK,CAAC1D,GAAG,CAAE,QAAQ,EAAE+C,MAAO,CAAC;;QAE7B;QACAW,KAAK,CAAEU,QAAQ,CAAE,CAACE,KAAK,CAAEZ,KAAK,EAAEa,SAAU,CAAC;MAC5C,CAAE,CAAC;IACJ,CAAC;IAEDG,UAAU,EAAE,SAAAA,CAAWrH,IAAI,EAAE+G,QAAQ,EAAG;MACvC;MACA,IAAIV,KAAK,GAAG,IAAI;MAChB,IAAIiB,KAAK,GAAGtH,IAAI,CAACuH,MAAM,CAAE,CAAC,EAAEvH,IAAI,CAACwH,OAAO,CAAE,GAAI,CAAE,CAAC;MACjD,IAAIZ,QAAQ,GAAG5G,IAAI,CAACuH,MAAM,CAAEvH,IAAI,CAACwH,OAAO,CAAE,GAAI,CAAC,GAAG,CAAE,CAAC;MACrD,IAAIC,OAAO,GAAG,IAAI,CAACb,QAAQ,CAAC,CAAC;;MAE7B;MACA7I,CAAC,CAAE2J,QAAS,CAAC,CAACC,EAAE,CAAEL,KAAK,EAAEG,OAAO,GAAG,GAAG,GAAGb,QAAQ,EAAE,UAAW/C,CAAC,EAAG;QACjE;QACAA,CAAC,CAAClF,GAAG,GAAGZ,CAAC,CAAE,IAAK,CAAC;QACjB8F,CAAC,CAAC6B,MAAM,GAAG7B,CAAC,CAAClF,GAAG,CAACiJ,OAAO,CAAE,mBAAoB,CAAC;;QAE/C;QACAvB,KAAK,CAAC1D,GAAG,CAAE,QAAQ,EAAEkB,CAAC,CAAC6B,MAAO,CAAC;;QAE/B;QACAW,KAAK,CAAEU,QAAQ,CAAE,CAACE,KAAK,CAAEZ,KAAK,EAAE,CAAExC,CAAC,CAAG,CAAC;MACxC,CAAE,CAAC;IACJ,CAAC;IAEDgE,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAI,CAACvB,CAAC,GAAG,IAAI,CAACZ,MAAM,CAACvH,IAAI,CAAC,CAAC;;MAE3B;MACA,IAAI,CAACoI,SAAS,GAAG,IAAI,CAACb,MAAM,CAACzG,IAAI,CAAE,6BAA8B,CAAC;;MAElE;MACA,IAAI,CAACC,KAAK,CAAC,CAAC;IACb,CAAC;IAEDA,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB;IAAA,CACA;IAED4I,OAAO,EAAE,SAAAA,CAAW9H,IAAI,EAAG;MAC1B,OAAO,IAAI,CAACuG,SAAS,CAACtH,IAAI,CAAE,uBAAuB,GAAGe,IAAK,CAAC;IAC7D;EACD,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAI+H,aAAa,GAAG,IAAI9J,GAAG,CAAC+J,KAAK,CAAE;IAClCC,OAAO,EAAE;MACRC,iBAAiB,EAAE,mBAAmB;MACtCC,kBAAkB,EAAE,oBAAoB;MACxCC,gBAAgB,EAAE,kBAAkB;MACpCC,sBAAsB,EAAE,wBAAwB;MAChDC,mBAAmB,EAAE,qBAAqB;MAC1CC,wBAAwB,EAAE,yBAAyB;MACnDC,yBAAyB,EAAE,0BAA0B;MACrDC,wBAAwB,EAAE,yBAAyB;MACnDC,0BAA0B,EAAE,2BAA2B;MACvDC,qBAAqB,EAAE;IACxB,CAAC;IAEDC,iBAAiB,EAAE,SAAAA,CAAWC,KAAK,EAAG;MACrC5K,GAAG,CAACkB,QAAQ,CAAE,YAAY,EAAE0J,KAAK,CAAClK,GAAI,CAAC;MACvCV,GAAG,CAACkB,QAAQ,CAAE,kBAAkB,GAAG0J,KAAK,CAACpJ,GAAG,CAAE,MAAO,CAAC,EAAEoJ,KAAK,CAAClK,GAAI,CAAC;MAEnEV,GAAG,CAACkB,QAAQ,CAAE,uBAAuB,EAAE0J,KAAK,CAAClK,GAAI,CAAC;MAClDV,GAAG,CAACkB,QAAQ,CACX,6BAA6B,GAAG0J,KAAK,CAACpJ,GAAG,CAAE,MAAO,CAAC,EACnDoJ,KAAK,CAAClK,GACP,CAAC;IACF,CAAC;IAEDmK,kBAAkB,EAAE,SAAAA,CAAWD,KAAK,EAAG;MACtC5K,GAAG,CAACkB,QAAQ,CAAE,aAAa,EAAE0J,KAAK,CAAClK,GAAI,CAAC;MACxCV,GAAG,CAACkB,QAAQ,CACX,mBAAmB,GAAG0J,KAAK,CAACpJ,GAAG,CAAE,MAAO,CAAC,EACzCoJ,KAAK,CAAClK,GACP,CAAC;IACF,CAAC;IAEDoK,gBAAgB,EAAE,SAAAA,CAAWF,KAAK,EAAG;MACpC5K,GAAG,CAACkB,QAAQ,CAAE,WAAW,EAAE0J,KAAK,CAAClK,GAAI,CAAC;MACtCV,GAAG,CAACkB,QAAQ,CAAE,iBAAiB,GAAG0J,KAAK,CAACpJ,GAAG,CAAE,MAAO,CAAC,EAAEoJ,KAAK,CAAClK,GAAI,CAAC;IACnE,CAAC;IAEDqK,sBAAsB,EAAE,SAAAA,CAAWH,KAAK,EAAG;MAC1C5K,GAAG,CAACkB,QAAQ,CAAE,iBAAiB,EAAE0J,KAAK,CAAClK,GAAI,CAAC;MAC5CV,GAAG,CAACkB,QAAQ,CACX,uBAAuB,GAAG0J,KAAK,CAACpJ,GAAG,CAAE,MAAO,CAAC,EAC7CoJ,KAAK,CAAClK,GACP,CAAC;IACF,CAAC;IAEDsK,mBAAmB,EAAE,SAAAA,CAAWJ,KAAK,EAAG;MACvC5K,GAAG,CAACkB,QAAQ,CAAE,cAAc,EAAE0J,KAAK,CAAClK,GAAI,CAAC;MACzCV,GAAG,CAACkB,QAAQ,CACX,oBAAoB,GAAG0J,KAAK,CAACpJ,GAAG,CAAE,MAAO,CAAC,EAC1CoJ,KAAK,CAAClK,GACP,CAAC;IACF,CAAC;IAEDuK,uBAAuB,EAAE,SAAAA,CAAWL,KAAK,EAAG;MAC3C5K,GAAG,CAACkB,QAAQ,CAAE,mBAAmB,EAAE0J,KAAK,CAAClK,GAAI,CAAC;MAC9CV,GAAG,CAACkB,QAAQ,CACX,yBAAyB,GAAG0J,KAAK,CAACpJ,GAAG,CAAE,MAAO,CAAC,EAC/CoJ,KAAK,CAAClK,GACP,CAAC;MAEDV,GAAG,CAACkB,QAAQ,CAAE,uBAAuB,EAAE0J,KAAK,CAAClK,GAAI,CAAC;MAClDV,GAAG,CAACkB,QAAQ,CACX,6BAA6B,GAAG0J,KAAK,CAACpJ,GAAG,CAAE,MAAO,CAAC,EACnDoJ,KAAK,CAAClK,GACP,CAAC;IACF,CAAC;IAEDwK,wBAAwB,EAAE,SAAAA,CAAWN,KAAK,EAAG;MAC5C5K,GAAG,CAACkB,QAAQ,CAAE,oBAAoB,EAAE0J,KAAK,CAAClK,GAAI,CAAC;MAC/CV,GAAG,CAACkB,QAAQ,CACX,0BAA0B,GAAG0J,KAAK,CAACpJ,GAAG,CAAE,MAAO,CAAC,EAChDoJ,KAAK,CAAClK,GACP,CAAC;IACF,CAAC;IAEDyK,uBAAuB,EAAE,SAAAA,CAAWP,KAAK,EAAG;MAC3C5K,GAAG,CAACkB,QAAQ,CAAE,mBAAmB,EAAE0J,KAAK,CAAClK,GAAI,CAAC;MAC9CV,GAAG,CAACkB,QAAQ,CACX,yBAAyB,GAAG0J,KAAK,CAACpJ,GAAG,CAAE,MAAO,CAAC,EAC/CoJ,KAAK,CAAClK,GACP,CAAC;IACF,CAAC;IAED0K,yBAAyB,EAAE,SAAAA,CAAWR,KAAK,EAAG;MAC7C5K,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAE0J,KAAK,CAAClK,GAAI,CAAC;IACjD;EACD,CAAE,CAAC;AACJ,CAAC,EAAI0G,MAAO,CAAC;;;;;;;;;;ACrQb,CAAE,UAAWtH,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIsL,4BAA4B,GAAGrL,GAAG,CAACsL,YAAY,CAAC7K,MAAM,CAAE;IAC3DgE,IAAI,EAAE,EAAE;IACR1C,IAAI,EAAE,mBAAmB;IACzBzB,MAAM,EAAE;MACP,2BAA2B,EAAE,gBAAgB;MAC7C,8BAA8B,EAAE,iBAAiB;MACjD,6BAA6B,EAAE,cAAc;MAC7C,8BAA8B,EAAE,eAAe;MAC/C,iCAAiC,EAAE,kBAAkB;MACrD,6BAA6B,EAAE,YAAY;MAC3C,gCAAgC,EAAE;IACnC,CAAC;IAEDiL,KAAK,EAAE,KAAK;IAEZC,KAAK,EAAE,SAAAA,CAAWD,KAAK,EAAG;MACzB,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,OAAO,IAAI;IACZ,CAAC;IAEDE,QAAQ,EAAE,SAAAA,CAAW1J,IAAI,EAAEiG,KAAK,EAAG;MAClC,OAAO,IAAI,CAACuD,KAAK,CAACrL,IAAI,CAAC8I,KAAK,CAAE,IAAI,CAACuC,KAAK,EAAEtC,SAAU,CAAC;IACtD,CAAC;IAEDyC,MAAM,EAAE,SAAAA,CAAW3J,IAAI,EAAG;MACzB,OAAO,IAAI,CAACwJ,KAAK,CAACvK,IAAI,CAAE,kBAAkB,GAAGe,IAAK,CAAC;IACpD,CAAC;IAED4J,GAAG,EAAE,SAAAA,CAAW5J,IAAI,EAAG;MACtB,OAAO,IAAI,CAACwJ,KAAK,CAACvK,IAAI,CAAE,KAAK,GAAGe,IAAK,CAAC;IACvC,CAAC;IAED6J,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAAC9L,CAAC,CAAE,oBAAqB,CAAC;IACtC,CAAC;IAED+L,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAAC/L,CAAC,CAAE,cAAe,CAAC;IAChC,CAAC;IAEDgM,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAAChM,CAAC,CAAE,aAAc,CAAC;IAC/B,CAAC;IAEDiM,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAACjM,CAAC,CAAE,OAAQ,CAAC;IACzB,CAAC;IAEDkM,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAO,IAAI,CAACxH,WAAW,CAAC9D,GAAG,CAACM,IAAI,CAAC,0BAA0B,CAAC;IAC7D,CAAC;IAEDF,IAAI,EAAE,SAAAA,CAAA,EAAY;MACjB,IAAImL,IAAI,GAAG,IAAI,CAACJ,QAAQ,CAAC,CAAC;MAC1BI,IAAI,CAAChI,IAAI,CAAC,CAAC;MACXjE,GAAG,CAACkM,MAAM,CAAED,IAAK,CAAC;IACnB,CAAC;IAED1F,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,IAAI0F,IAAI,GAAG,IAAI,CAACJ,QAAQ,CAAC,CAAC;MAC1BI,IAAI,CAAC/H,IAAI,CAAC,CAAC;MACXlE,GAAG,CAACmM,OAAO,CAAEF,IAAK,CAAC;IACpB,CAAC;IAEDrL,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAK,IAAI,CAACgL,OAAO,CAAC,CAAC,CAAC3D,IAAI,CAAE,SAAU,CAAC,EAAG;QACvC,IAAI,CAAC+D,SAAS,CAAC,CAAC,CAACtG,QAAQ,CAAC,YAAY,CAAC;QACvC,IAAI,CAAC0G,WAAW,CAAC,CAAC;QAClB,IAAI,CAACtL,IAAI,CAAC,CAAC;;QAEX;MACD,CAAC,MAAM;QACN,IAAI,CAACkL,SAAS,CAAC,CAAC,CAACvG,WAAW,CAAC,YAAY,CAAC;QAC1C,IAAI,CAACc,KAAK,CAAC,CAAC;MACb;IACD,CAAC;IAED6F,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAIzJ,IAAI,GAAG,IAAI;;MAEf;MACA,IAAI,CAACoJ,MAAM,CAAC,CAAC,CAACnJ,IAAI,CAAE,YAAY;QAC/BD,IAAI,CAAC0J,UAAU,CAAEvM,CAAC,CAAE,IAAK,CAAE,CAAC;MAC7B,CAAE,CAAC;IACJ,CAAC;IAEDuM,UAAU,EAAE,SAAAA,CAAWd,KAAK,EAAG;MAC9B,IAAI,CAACC,KAAK,CAAED,KAAM,CAAC;MACnB,IAAI,CAACe,WAAW,CAAC,CAAC;MAClB,IAAI,CAACC,cAAc,CAAC,CAAC;MACrB,IAAI,CAACC,WAAW,CAAC,CAAC;IACnB,CAAC;IAEDF,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAIG,OAAO,GAAG,EAAE;MAChB,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAIC,GAAG,GAAG,IAAI,CAACnI,WAAW,CAACmI,GAAG;MAC9B,IAAIC,OAAO,GAAG,IAAI,CAAClB,MAAM,CAAE,OAAQ,CAAC;;MAEpC;MACA1L,GAAG,CAAC6M,eAAe,CAAC,CAAC,CAACC,GAAG,CAAE,UAAWtI,WAAW,EAAG;QACnD;QACA,IAAIuI,MAAM,GAAG;UACZC,EAAE,EAAExI,WAAW,CAACyI,MAAM,CAAC,CAAC;UACxBlJ,IAAI,EAAES,WAAW,CAAC0I,QAAQ,CAAC;QAC5B,CAAC;;QAED;QACA,IAAK1I,WAAW,CAACmI,GAAG,KAAKA,GAAG,EAAG;UAC9BI,MAAM,CAAChJ,IAAI,IAAI,GAAG,GAAG/D,GAAG,CAACmN,EAAE,CAAE,cAAe,CAAC;UAC7CJ,MAAM,CAACK,QAAQ,GAAG,IAAI;QACvB;;QAEA;QACA,IAAIC,cAAc,GAAGrN,GAAG,CAACsN,iBAAiB,CAAE;UAC3CzL,SAAS,EAAE2C,WAAW,CAAC+I,OAAO,CAAC;QAChC,CAAE,CAAC;;QAEH;QACA,IAAK,CAAEF,cAAc,CAAC9K,MAAM,EAAG;UAC9BwK,MAAM,CAACK,QAAQ,GAAG,IAAI;QACvB;;QAEA;QACA,IAAII,OAAO,GAAGhJ,WAAW,CAACiJ,UAAU,CAAC,CAAC,CAAClL,MAAM;QAC7CwK,MAAM,CAAChJ,IAAI,GAAG,IAAI,CAAC2J,MAAM,CAAEF,OAAQ,CAAC,GAAGT,MAAM,CAAChJ,IAAI;;QAElD;QACA0I,OAAO,CAACkB,IAAI,CAAEZ,MAAO,CAAC;MACvB,CAAE,CAAC;;MAEH;MACA,IAAK,CAAEN,OAAO,CAAClK,MAAM,EAAG;QACvBkK,OAAO,CAACkB,IAAI,CAAE;UACbX,EAAE,EAAE,EAAE;UACNjJ,IAAI,EAAE/D,GAAG,CAACmN,EAAE,CAAE,4BAA6B;QAC5C,CAAE,CAAC;MACJ;;MAEA;MACAnN,GAAG,CAAC4N,YAAY,CAAEhB,OAAO,EAAEH,OAAQ,CAAC;;MAEpC;MACA,IAAI,CAAChB,QAAQ,CAAE,OAAO,EAAEmB,OAAO,CAACtH,GAAG,CAAC,CAAE,CAAC;IACxC,CAAC;IAEDiH,cAAc,EAAE,SAAAA,CAAA,EAAY;MAC3B;MACA,IAAK,CAAE,IAAI,CAACd,QAAQ,CAAE,OAAQ,CAAC,EAAG;QACjC;MACD;;MAEA;MACA,IAAImB,OAAO,GAAG,IAAI,CAAClB,MAAM,CAAE,UAAW,CAAC;MACvC,IAAIpG,GAAG,GAAGsH,OAAO,CAACtH,GAAG,CAAC,CAAC;MACvB,IAAImH,OAAO,GAAG,EAAE;;MAEhB;MACA;MACA,IAAKG,OAAO,CAACtH,GAAG,CAAC,CAAC,KAAK,IAAI,EAAG;QAC7BtF,GAAG,CAAC4N,YAAY,CAAEhB,OAAO,EAAE,CAC1B;UACCI,EAAE,EAAE,IAAI,CAACvB,QAAQ,CAAE,UAAW,CAAC;UAC/B1H,IAAI,EAAE;QACP,CAAC,CACA,CAAC;MACJ;;MAEA;MACA,IAAI0D,MAAM,GAAGzH,GAAG,CAAC6N,eAAe,CAAE,IAAI,CAACpC,QAAQ,CAAE,OAAQ,CAAE,CAAC;MAC5D,IAAIb,KAAK,GAAG5K,GAAG,CAAC0H,cAAc,CAAED,MAAO,CAAC;;MAExC;MACA,IAAI4F,cAAc,GAAGrN,GAAG,CAACsN,iBAAiB,CAAE;QAC3CzL,SAAS,EAAE+I,KAAK,CAAC2C,OAAO,CAAC;MAC1B,CAAE,CAAC;;MAEH;MACAF,cAAc,CAACP,GAAG,CAAE,UAAW1E,KAAK,EAAG;QACtCqE,OAAO,CAACkB,IAAI,CAAE;UACbX,EAAE,EAAE5E,KAAK,CAAC0F,SAAS,CAACC,QAAQ;UAC5BhK,IAAI,EAAEqE,KAAK,CAAC0F,SAAS,CAAC7L;QACvB,CAAE,CAAC;MACJ,CAAE,CAAC;;MAEH;MACAjC,GAAG,CAAC4N,YAAY,CAAEhB,OAAO,EAAEH,OAAQ,CAAC;;MAEpC;MACA,IAAI,CAAChB,QAAQ,CAAE,UAAU,EAAEmB,OAAO,CAACtH,GAAG,CAAC,CAAE,CAAC;IAC3C,CAAC;IAEDkH,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAK,CAAE,IAAI,CAACf,QAAQ,CAAE,OAAQ,CAAC,IAAI,CAAE,IAAI,CAACA,QAAQ,CAAE,UAAW,CAAC,EAAG;QAClE;MACD;;MAEA;MACA,IAAImB,OAAO,GAAG,IAAI,CAAClB,MAAM,CAAE,OAAQ,CAAC;MACpC,IAAIC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAE,OAAQ,CAAC;MAC7B,IAAIrG,GAAG,GAAGsH,OAAO,CAACtH,GAAG,CAAC,CAAC;;MAEvB;MACA,IAAImC,MAAM,GAAGzH,GAAG,CAAC6N,eAAe,CAAE,IAAI,CAACpC,QAAQ,CAAE,OAAQ,CAAE,CAAC;MAC5D,IAAIb,KAAK,GAAG5K,GAAG,CAAC0H,cAAc,CAAED,MAAO,CAAC;;MAExC;MACA,IAAI4F,cAAc,GAAGrN,GAAG,CAACsN,iBAAiB,CAAE;QAC3CzL,SAAS,EAAE+I,KAAK,CAAC2C,OAAO,CAAC,CAAC;QAC1BQ,QAAQ,EAAE,IAAI,CAACtC,QAAQ,CAAE,UAAW;MACrC,CAAE,CAAC;;MAEH;MACA,IAAIuC,aAAa,GAAGX,cAAc,CAAE,CAAC,CAAE,CAACS,SAAS;MACjD,IAAIrB,OAAO,GAAGuB,aAAa,CAACvB,OAAO,CAAE7B,KAAM,CAAC;;MAE5C;MACA,IAAK6B,OAAO,YAAYwB,KAAK,EAAG;QAC/B,IAAIC,UAAU,GAAGpO,CAAC,CAAE,mBAAoB,CAAC;QACzCE,GAAG,CAAC4N,YAAY,CAAEM,UAAU,EAAEzB,OAAQ,CAAC;;QAEvC;MACD,CAAC,MAAM;QACN,IAAIyB,UAAU,GAAGpO,CAAC,CAAE2M,OAAQ,CAAC;MAC9B;;MAEA;MACAG,OAAO,CAACuB,MAAM,CAAC,CAAC;MAChBxC,GAAG,CAACxK,IAAI,CAAE+M,UAAW,CAAC;;MAEtB;MACA;MACAhJ,UAAU,CAAE,YAAY;QACvB,CAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAE,CAAC4H,GAAG,CAAE,UAAW9I,IAAI,EAAG;UAChDkK,UAAU,CAAClK,IAAI,CAAEA,IAAI,EAAE4I,OAAO,CAAC5I,IAAI,CAAEA,IAAK,CAAE,CAAC;QAC9C,CAAE,CAAC;MACJ,CAAC,EAAE,CAAE,CAAC;;MAEN;MACA,IAAK,CAAEkK,UAAU,CAACjG,IAAI,CAAE,UAAW,CAAC,EAAG;QACtCjI,GAAG,CAACsF,GAAG,CAAE4I,UAAU,EAAE5I,GAAG,EAAE,IAAK,CAAC;MACjC;;MAEA;MACA,IAAI,CAACmG,QAAQ,CAAE,OAAO,EAAEyC,UAAU,CAAC5I,GAAG,CAAC,CAAE,CAAC;IAC3C,CAAC;IAED8I,cAAc,EAAE,SAAAA,CAAA,EAAY;MAC3B,IAAI,CAACxN,MAAM,CAAC,CAAC;IACd,CAAC;IAEDyN,eAAe,EAAE,SAAAA,CAAWzI,CAAC,EAAElF,GAAG,EAAG;MACpC,IAAI,CAAC4N,QAAQ,CAAC,CAAC;IAChB,CAAC;IAEDA,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB;MACA,IAAIC,MAAM,GAAG,IAAI,CAACzO,CAAC,CAAE,kBAAmB,CAAC;;MAEzC;MACA,IAAI0O,OAAO,GAAGxO,GAAG,CAACyO,SAAS,CAAEF,MAAO,CAAC;;MAErC;MACAC,OAAO,CAACxN,IAAI,CAAE,IAAK,CAAC,CAAC+C,IAAI,CAAE/D,GAAG,CAACmN,EAAE,CAAE,IAAK,CAAE,CAAC;;MAE3C;MACAqB,OAAO,CAACxN,IAAI,CAAE,IAAK,CAAC,CAAC0N,GAAG,CAAE,QAAS,CAAC,CAAC3H,MAAM,CAAC,CAAC;;MAE7C;MACA,IAAI,CAACvC,WAAW,CAACmD,IAAI,CAAC,CAAC;IACxB,CAAC;IAEDgH,YAAY,EAAE,SAAAA,CAAW/I,CAAC,EAAElF,GAAG,EAAG;MACjC,IAAI,CAAC4L,WAAW,CAAC,CAAC;IACnB,CAAC;IAEDsC,aAAa,EAAE,SAAAA,CAAWhJ,CAAC,EAAElF,GAAG,EAAG;MAClC;MACA,IAAI,CAAC8K,KAAK,CAAE9K,GAAG,CAACiJ,OAAO,CAAE,OAAQ,CAAE,CAAC;;MAEpC;MACA,IAAI,CAAC8B,QAAQ,CAAE,OAAO,EAAE/K,GAAG,CAAC4E,GAAG,CAAC,CAAE,CAAC;;MAEnC;MACA,IAAI,CAACiH,cAAc,CAAC,CAAC;MACrB,IAAI,CAACC,WAAW,CAAC,CAAC;IACnB,CAAC;IAEDqC,gBAAgB,EAAE,SAAAA,CAAWjJ,CAAC,EAAElF,GAAG,EAAG;MACrC;MACA,IAAI,CAAC8K,KAAK,CAAE9K,GAAG,CAACiJ,OAAO,CAAE,OAAQ,CAAE,CAAC;;MAEpC;MACA,IAAI,CAAC8B,QAAQ,CAAE,UAAU,EAAE/K,GAAG,CAAC4E,GAAG,CAAC,CAAE,CAAC;;MAEtC;MACA,IAAI,CAACkH,WAAW,CAAC,CAAC;IACnB,CAAC;IAEDsC,UAAU,EAAE,SAAAA,CAAWlJ,CAAC,EAAElF,GAAG,EAAG;MAC/B;MACA,IAAI6K,KAAK,GAAGvL,GAAG,CAACyO,SAAS,CAAE/N,GAAG,CAACiJ,OAAO,CAAE,OAAQ,CAAE,CAAC;;MAEnD;MACA,IAAI,CAAC0C,UAAU,CAAEd,KAAM,CAAC;IACzB,CAAC;IAEDwD,aAAa,EAAE,SAAAA,CAAWnJ,CAAC,EAAElF,GAAG,EAAG;MAClC;MACA,IAAI6K,KAAK,GAAG7K,GAAG,CAACiJ,OAAO,CAAE,OAAQ,CAAC;;MAElC;MACA,IAAI,CAACnF,WAAW,CAACmD,IAAI,CAAC,CAAC;;MAEvB;MACA,IAAK4D,KAAK,CAACyD,QAAQ,CAAE,OAAQ,CAAC,CAACzM,MAAM,IAAI,CAAC,EAAG;QAC5CgJ,KAAK,CAAC5B,OAAO,CAAE,aAAc,CAAC,CAAC5C,MAAM,CAAC,CAAC;MACxC;;MAEA;MACAwE,KAAK,CAACxE,MAAM,CAAC,CAAC;IACf;EACD,CAAE,CAAC;EAEH/G,GAAG,CAACiP,oBAAoB,CAAE5D,4BAA6B,CAAC;;EAExD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAI6D,sBAAsB,GAAG,IAAIlP,GAAG,CAAC+J,KAAK,CAAE;IAC3CC,OAAO,EAAE;MACRmF,uBAAuB,EAAE;IAC1B,CAAC;IAEDC,uBAAuB,EAAE,SAAAA,CAAWC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAG;MACnE;MACA,IAAIrP,IAAI,GAAG,CAAC,CAAC;MACb,IAAIsP,QAAQ,GAAG1P,CAAC,CAAC,CAAC;;MAElB;MACAuP,QAAQ,CAACvC,GAAG,CAAE,UAAW2C,KAAK,EAAG;QAChC;QACAvP,IAAI,CAAEuP,KAAK,CAACjO,GAAG,CAAE,SAAU,CAAC,CAAE,GAAGiO,KAAK,CAACjO,GAAG,CAAE,KAAM,CAAC;;QAEnD;QACAgO,QAAQ,GAAGA,QAAQ,CAACE,GAAG,CAAED,KAAK,CAAC3P,CAAC,CAAE,uBAAwB,CAAE,CAAC;MAC9D,CAAE,CAAC;;MAEH;MACA0P,QAAQ,CAAC5M,IAAI,CAAE,YAAY;QAC1B;QACA,IAAIgK,OAAO,GAAG9M,CAAC,CAAE,IAAK,CAAC;QACvB,IAAIwF,GAAG,GAAGsH,OAAO,CAACtH,GAAG,CAAC,CAAC;;QAEvB;QACA,IAAK,CAAEA,GAAG,IAAI,CAAEpF,IAAI,CAAEoF,GAAG,CAAE,EAAG;UAC7B;QACD;;QAEA;QACAsH,OAAO,CAAC5L,IAAI,CAAE,iBAAkB,CAAC,CAACgD,IAAI,CAAE,OAAO,EAAE9D,IAAI,CAAEoF,GAAG,CAAG,CAAC;;QAE9D;QACAsH,OAAO,CAACtH,GAAG,CAAEpF,IAAI,CAAEoF,GAAG,CAAG,CAAC;MAC3B,CAAE,CAAC;IACJ;EACD,CAAE,CAAC;AACJ,CAAC,EAAI8B,MAAO,CAAC;;;;;;;;;;ACzYb,CAAE,UAAWtH,CAAC,EAAEC,SAAS,EAAG;EAC3BC,GAAG,CAAC2P,WAAW,GAAG3P,GAAG,CAAC+J,KAAK,CAACtJ,MAAM,CAAE;IACnC;IACAmP,UAAU,EAAE,mBAAmB;IAE/B;IACAC,gBAAgB,EAAE,KAAK;IAEvB;IACAvP,MAAM,EAAE;MACP,iBAAiB,EAAE,aAAa;MAChC,eAAe,EAAE,aAAa;MAC9B,oBAAoB,EAAE,aAAa;MACnC,6CAA6C,EAC5C,qBAAqB;MACtB,qBAAqB,EAAE,eAAe;MACtC,wBAAwB,EAAE,WAAW;MACrC,mBAAmB,EAAE,MAAM;MAC3B,sBAAsB,EAAE,cAAc;MAEtC,mBAAmB,EAAE,aAAa;MAClC,kCAAkC,EAAE,YAAY;MAEhD,oBAAoB,EAAE,cAAc;MACpC,wBAAwB,EAAE,kBAAkB;MAC5C,mBAAmB,EAAE,eAAe;MACpC,kBAAkB,EAAE,cAAc;MAElCwP,MAAM,EAAE,UAAU;MAClBC,OAAO,EAAE;IACV,CAAC;IAED;IACA7P,IAAI,EAAE;MACL;MACA;MACA8M,EAAE,EAAE,CAAC;MAEL;MACAnG,GAAG,EAAE,EAAE;MAEP;MACApC,IAAI,EAAE;;MAEN;MACA;;MAEA;MACA;;MAEA;MACA;IACD,CAAC;;IAEDlE,KAAK,EAAE,SAAAA,CAAWkH,MAAM,EAAG;MAC1B;MACA,IAAI,CAAC/G,GAAG,GAAG+G,MAAM;;MAEjB;MACA,IAAI,CAACuI,OAAO,CAAEvI,MAAO,CAAC;;MAEtB;MACA;MACA,IAAI,CAACQ,IAAI,CAAE,IAAK,CAAC;MACjB,IAAI,CAACA,IAAI,CAAE,QAAS,CAAC;MACrB,IAAI,CAACA,IAAI,CAAE,YAAa,CAAC;IAC1B,CAAC;IAEDyD,MAAM,EAAE,SAAAA,CAAW3J,IAAI,EAAG;MACzB,OAAOjC,CAAC,CAAE,GAAG,GAAG,IAAI,CAACmQ,UAAU,CAAC,CAAC,GAAG,GAAG,GAAGlO,IAAK,CAAC;IACjD,CAAC;IAEDmO,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,OAAO,IAAI,CAACpQ,CAAC,CAAE,aAAc,CAAC;IAC/B,CAAC;IAEDqQ,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAACrQ,CAAC,CAAE,eAAgB,CAAC;IACjC,CAAC;IAEDwI,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAO,IAAI,CAACxI,CAAC,CAAE,iBAAkB,CAAC;IACnC,CAAC;IAEDsQ,QAAQ,EAAE,SAAAA,CAAWrO,IAAI,EAAG;MAC3B,OAAO,IAAI,CAACjC,CAAC,CACZ,+CAA+C,GAAGiC,IACnD,CAAC;IACF,CAAC;IAEDuE,gBAAgB,EAAE,SAAAA,CAAA,EAAY;MAC7B,OAAO,IAAI,CAACxG,CAAC,CAAE,aAAc,CAAC;IAC/B,CAAC;IAEDuF,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB,OAAO,IAAI,CAACvF,CAAC,CAAE,cAAe,CAAC;IAChC,CAAC;IAEDuQ,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAOrQ,GAAG,CAAC6M,eAAe,CAAE;QAAE4C,KAAK,EAAE,IAAI,CAAC/O,GAAG;QAAE4P,KAAK,EAAE;MAAE,CAAE,CAAC,CAACC,GAAG,CAAC,CAAC;IAClE,CAAC;IAED9C,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,OAAOzN,GAAG,CAAC6M,eAAe,CAAE;QAAE4C,KAAK,EAAE,IAAI,CAAC/O;MAAI,CAAE,CAAC;IAClD,CAAC;IAED8P,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAOxQ,GAAG,CAAC6M,eAAe,CAAE;QAAE1I,MAAM,EAAE,IAAI,CAACzD;MAAI,CAAE,CAAC;IACnD,CAAC;IAED+P,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,OAAO,aAAa,GAAG,IAAI,CAACjP,GAAG,CAAE,IAAK,CAAC,GAAG,GAAG;IAC9C,CAAC;IAEDyO,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,OAAO,aAAa,GAAG,IAAI,CAACzO,GAAG,CAAE,IAAK,CAAC;IACxC,CAAC;IAEDkP,QAAQ,EAAE,SAAAA,CAAW3O,IAAI,EAAEiG,KAAK,EAAG;MAClC;MACA,IAAI2I,OAAO,GAAG,IAAI,CAACV,UAAU,CAAC,CAAC;MAC/B,IAAIW,SAAS,GAAG,IAAI,CAACH,YAAY,CAAC,CAAC;;MAEnC;MACA,IAAK1O,IAAI,EAAG;QACX4O,OAAO,IAAI,GAAG,GAAG5O,IAAI;QACrB6O,SAAS,IAAI,GAAG,GAAG7O,IAAI,GAAG,GAAG;MAC9B;;MAEA;MACA,IAAI2J,MAAM,GAAG5L,CAAC,CAAE,WAAY,CAAC,CAACkE,IAAI,CAAE;QACnCgJ,EAAE,EAAE2D,OAAO;QACX5O,IAAI,EAAE6O,SAAS;QACf5I,KAAK,EAAEA;MACR,CAAE,CAAC;MACH,IAAI,CAAClI,CAAC,CAAE,SAAU,CAAC,CAAC+C,MAAM,CAAE6I,MAAO,CAAC;;MAEpC;MACA,OAAOA,MAAM;IACd,CAAC;IAEDmF,OAAO,EAAE,SAAAA,CAAW9O,IAAI,EAAG;MAC1B;MACA,IAAK,IAAI,CAAC+O,GAAG,CAAE/O,IAAK,CAAC,EAAG;QACvB,OAAO,IAAI,CAACP,GAAG,CAAEO,IAAK,CAAC;MACxB;;MAEA;MACA,IAAI2J,MAAM,GAAG,IAAI,CAACA,MAAM,CAAE3J,IAAK,CAAC;MAChC,IAAIiG,KAAK,GAAG0D,MAAM,CAACnJ,MAAM,GAAGmJ,MAAM,CAACpG,GAAG,CAAC,CAAC,GAAG,IAAI;;MAE/C;MACA,IAAI,CAACZ,GAAG,CAAE3C,IAAI,EAAEiG,KAAK,EAAE,IAAK,CAAC;;MAE7B;MACA,OAAOA,KAAK;IACb,CAAC;IAED+I,OAAO,EAAE,SAAAA,CAAWhP,IAAI,EAAEiG,KAAK,EAAG;MACjC;MACA,IAAI0D,MAAM,GAAG,IAAI,CAACA,MAAM,CAAE3J,IAAK,CAAC;MAChC,IAAIiP,OAAO,GAAGtF,MAAM,CAACpG,GAAG,CAAC,CAAC;;MAE1B;MACA,IAAK,CAAEoG,MAAM,CAACnJ,MAAM,EAAG;QACtBmJ,MAAM,GAAG,IAAI,CAACgF,QAAQ,CAAE3O,IAAI,EAAEiG,KAAM,CAAC;MACtC;;MAEA;MACA,IAAKA,KAAK,KAAK,IAAI,EAAG;QACrB0D,MAAM,CAAC3E,MAAM,CAAC,CAAC;;QAEf;MACD,CAAC,MAAM;QACN2E,MAAM,CAACpG,GAAG,CAAE0C,KAAM,CAAC;MACpB;;MAEA;;MAEA;MACA,IAAK,CAAE,IAAI,CAAC8I,GAAG,CAAE/O,IAAK,CAAC,EAAG;QACzB;QACA,IAAI,CAAC2C,GAAG,CAAE3C,IAAI,EAAEiG,KAAK,EAAE,IAAK,CAAC;;QAE7B;MACD,CAAC,MAAM;QACN;QACA,IAAI,CAACtD,GAAG,CAAE3C,IAAI,EAAEiG,KAAM,CAAC;MACxB;;MAEA;MACA,OAAO,IAAI;IACZ,CAAC;IAEDC,IAAI,EAAE,SAAAA,CAAWlG,IAAI,EAAEiG,KAAK,EAAG;MAC9B,IAAKA,KAAK,KAAKjI,SAAS,EAAG;QAC1B,OAAO,IAAI,CAACgR,OAAO,CAAEhP,IAAI,EAAEiG,KAAM,CAAC;MACnC,CAAC,MAAM;QACN,OAAO,IAAI,CAAC6I,OAAO,CAAE9O,IAAK,CAAC;MAC5B;IACD,CAAC;IAEDvB,KAAK,EAAE,SAAAA,CAAWA,KAAK,EAAG;MACzBiB,MAAM,CAACwP,IAAI,CAAEzQ,KAAM,CAAC,CAACsM,GAAG,CAAE,UAAWjG,GAAG,EAAG;QAC1C,IAAI,CAACkK,OAAO,CAAElK,GAAG,EAAErG,KAAK,CAAEqG,GAAG,CAAG,CAAC;MAClC,CAAC,EAAE,IAAK,CAAC;IACV,CAAC;IAEDqG,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB;MACA,IAAIjL,KAAK,GAAG,IAAI,CAACgG,IAAI,CAAE,OAAQ,CAAC;MAChC,IAAKhG,KAAK,KAAK,EAAE,EAAG;QACnBA,KAAK,GAAGjC,GAAG,CAACmN,EAAE,CAAE,YAAa,CAAC;MAC/B;;MAEA;MACA,OAAOlL,KAAK;IACb,CAAC;IAEDiP,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAACjJ,IAAI,CAAE,MAAO,CAAC;IAC3B,CAAC;IAEDsF,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAACtF,IAAI,CAAE,MAAO,CAAC;IAC3B,CAAC;IAEDkJ,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,IAAI1M,IAAI,GAAG,IAAI,CAACwD,IAAI,CAAE,MAAO,CAAC;MAC9B,IAAImJ,KAAK,GAAGpR,GAAG,CAACwB,GAAG,CAAE,YAAa,CAAC;MACnC,OAAO4P,KAAK,CAAE3M,IAAI,CAAE,GAAG2M,KAAK,CAAE3M,IAAI,CAAE,CAACxC,KAAK,GAAGwC,IAAI;IAClD,CAAC;IAEDwI,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAAChF,IAAI,CAAE,KAAM,CAAC;IAC1B,CAAC;IAEDpH,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,IAAI,CAACwQ,aAAa,CAAC,CAAC;IACrB,CAAC;IAEDC,YAAY,EAAE,SAAAA,CAAWvN,IAAI,EAAG;MAC/B,IAAK,CAAEwN,SAAS,CAACC,SAAS,EACzB,OACC,0CAA0C,GAC1CzN,IAAI,GACJ,SAAS;MAEX,OAAO,yBAAyB,GAAGA,IAAI,GAAG,SAAS;IACpD,CAAC;IAEDsN,aAAa,EAAE,SAAAA,CAAA,EAAY;MAC1B,IAAK,CAAEE,SAAS,CAACC,SAAS,EAAG;QAC5B,IAAI,CAAC9Q,GAAG,CAACM,IAAI,CAAE,WAAY,CAAC,CAAC0E,QAAQ,CAAE,kBAAmB,CAAC;MAC5D;IACD,CAAC;IAED+L,0BAA0B,EAAE,SAAAA,CAAA,EAAY;MACvC,IAAK,IAAI,CAAC5B,gBAAgB,EAAG;;MAE7B;MACA,IAAK,IAAI,CAACvJ,gBAAgB,CAAC,CAAC,CAACoL,QAAQ,CAAE,iBAAkB,CAAC,EAAG;;MAE7D;MACA,IAAI;QACH5R,CAAC,CAAC6R,EAAE,CAACC,OAAO,CAACC,GAAG,CAACC,OAAO,CAAE,4BAA6B,CAAC;MACzD,CAAC,CAAC,OAAQC,GAAG,EAAG;QACfC,OAAO,CAACC,IAAI,CACX,mLACD,CAAC;QACD;MACD;MAEA,IAAI,CAACpC,gBAAgB,GAAG7P,GAAG,CAACkS,UAAU,CAAE,IAAI,CAAC5L,gBAAgB,CAAC,CAAC,EAAE;QAChEsE,KAAK,EAAE,KAAK;QACZuH,IAAI,EAAE,KAAK;QACXC,QAAQ,EAAE,KAAK;QACfC,SAAS,EAAE,KAAK;QAChBC,eAAe,EAAE,IAAI;QACrBC,gBAAgB,EAAE,2BAA2B;QAC7CC,cAAc,EAAE,SAAAA,CAAWC,SAAS,EAAG;UACtC,IACCA,SAAS,CAACC,OAAO,IACfD,SAAS,CAACE,OAAO,IAClBF,SAAS,CAACE,OAAO,CAACC,QAAQ,KAAK,UAAY,EAC3C;YACD,IAAIC,UAAU,GAAG/S,CAAC,CACjB,qCACD,CAAC;YACD+S,UAAU,CAAC1R,IAAI,CAAEnB,GAAG,CAAC8S,OAAO,CAAEL,SAAS,CAAC1O,IAAK,CAAE,CAAC;UACjD,CAAC,MAAM;YACN,IAAI8O,UAAU,GAAG/S,CAAC,CACjB,4CAA4C,GAC3C2S,SAAS,CAACzF,EAAE,CAAC7J,UAAU,CAAE,GAAG,EAAE,GAAI,CAAC,GACnC,6CAA6C,GAC7CnD,GAAG,CAAC8S,OAAO,CAAEL,SAAS,CAAC1O,IAAK,CAAC,GAC7B,SACF,CAAC;UACF;UACA8O,UAAU,CAAC3S,IAAI,CAAE,SAAS,EAAEuS,SAAS,CAACE,OAAQ,CAAC;UAC/C,OAAOE,UAAU;QAClB,CAAC;QACDE,iBAAiB,EAAE,SAAAA,CAAWN,SAAS,EAAG;UACzC,IAAII,UAAU,GAAG/S,CAAC,CACjB,4CAA4C,GAC3C2S,SAAS,CAACzF,EAAE,CAAC7J,UAAU,CAAE,GAAG,EAAE,GAAI,CAAC,GACnC,6CAA6C,GAC7CnD,GAAG,CAAC8S,OAAO,CAAEL,SAAS,CAAC1O,IAAK,CAAC,GAC7B,SACF,CAAC;UACD8O,UAAU,CAAC3S,IAAI,CAAE,SAAS,EAAEuS,SAAS,CAACE,OAAQ,CAAC;UAC/C,OAAOE,UAAU;QAClB;MACD,CAAE,CAAC;MAEH,IAAI,CAAChD,gBAAgB,CAACnG,EAAE,CAAE,cAAc,EAAE,YAAY;QACrD5J,CAAC,CACA,wDACD,CAAC,CAACkE,IAAI,CAAE,aAAa,EAAEhE,GAAG,CAACmN,EAAE,CAAE,mBAAoB,CAAE,CAAC;MACvD,CAAE,CAAC;MAEH,IAAI,CAAC0C,gBAAgB,CAACnG,EAAE,CAAE,QAAQ,EAAE,UAAW9D,CAAC,EAAG;QAClD9F,CAAC,CAAE8F,CAAC,CAACoN,MAAO,CAAC,CACXC,OAAO,CAAE,UAAW,CAAC,CACrBjS,IAAI,CAAE,sBAAuB,CAAC,CAC9BiH,IAAI,CAAE,UAAU,EAAE,IAAK,CAAC;MAC3B,CAAE,CAAC;;MAEH;MACA,IAAI,CAAC4H,gBAAgB,CAACnP,GAAG,CACvByD,MAAM,CAAC,CAAC,CACRuF,EAAE,CACF,SAAS,EACT,8CAA8C,EAC9C,IAAI,CAACwJ,eACN,CAAC;IACH,CAAC;IACDC,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB;MACA,IAAKnT,GAAG,CAACwB,GAAG,CAAE,QAAS,CAAC,EAAG;QAC1B;MACD;;MAEA;MACA,IAAI8E,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC,CAAC;MAC9C,IAAKA,gBAAgB,CAACoL,QAAQ,CAAE,qBAAsB,CAAC,EAAG;;MAE1D;MACA,MAAM0B,aAAa,GAAGpT,GAAG,CAACwB,GAAG,CAAE,eAAgB,CAAC;MAChD,IAAK,OAAO4R,aAAa,KAAK,QAAQ,EAAG;MAEzC,MAAMC,YAAY,GAAG/M,gBAAgB,CACnCtF,IAAI,CAAE,gCAAiC,CAAC,CACxCmD,MAAM,CAAC,CAAC;MAEV,MAAMmP,aAAa,GAAGhN,gBAAgB,CACpCtF,IAAI,CAAE,gCAAiC,CAAC,CACxCmD,MAAM,CAAC,CAAC;MAEV,KAAM,MAAM,CAAEpC,IAAI,EAAE6I,KAAK,CAAE,IAAInJ,MAAM,CAAC8R,OAAO,CAAEH,aAAc,CAAC,EAAG;QAChE,MAAMI,SAAS,GACd5I,KAAK,CAACvJ,QAAQ,KAAK,SAAS,GAAGiS,aAAa,GAAGD,YAAY;QAC5DG,SAAS,CAAC3Q,MAAM,CACf,2CAA2C,GAC1C+H,KAAK,CAAC3I,KAAK,GACX,IAAI,GACJjC,GAAG,CAACmN,EAAE,CAAE,UAAW,CAAC,GACpB,YACF,CAAC;MACF;MAEA7G,gBAAgB,CAACZ,QAAQ,CAAE,qBAAsB,CAAC;IACnD,CAAC;IAED9E,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAIuP,OAAO,GAAG,IAAI,CAACrQ,CAAC,CAAE,eAAgB,CAAC;MACvC,IAAI2T,UAAU,GAAG,IAAI,CAACxL,IAAI,CAAE,YAAa,CAAC;MAC1C,IAAIhG,KAAK,GAAG,IAAI,CAACiL,QAAQ,CAAC,CAAC;MAC3B,IAAInL,IAAI,GAAG,IAAI,CAACkG,IAAI,CAAE,MAAO,CAAC;MAC9B,IAAIxD,IAAI,GAAG,IAAI,CAAC0M,YAAY,CAAC,CAAC;MAC9B,IAAItK,GAAG,GAAG,IAAI,CAACoB,IAAI,CAAE,KAAM,CAAC;MAC5B,IAAIyL,QAAQ,GAAG,IAAI,CAAChI,MAAM,CAAE,UAAW,CAAC,CAACzD,IAAI,CAAE,SAAU,CAAC;;MAE1D;MACAkI,OAAO,CAACnP,IAAI,CAAE,WAAY,CAAC,CAACG,IAAI,CAAEwS,QAAQ,CAAEF,UAAW,CAAC,GAAG,CAAE,CAAC;;MAE9D;MACA,IAAKC,QAAQ,EAAG;QACfzR,KAAK,IAAI,sCAAsC;MAChD;;MAEA;MACAkO,OAAO,CAACnP,IAAI,CAAE,0BAA2B,CAAC,CAACG,IAAI,CAAEc,KAAM,CAAC;;MAExD;MACAkO,OAAO,CAACnP,IAAI,CAAE,gBAAiB,CAAC,CAACG,IAAI,CAAE,IAAI,CAACmQ,YAAY,CAAEvP,IAAK,CAAE,CAAC;;MAElE;MACA,MAAMmB,QAAQ,GAAGlD,GAAG,CAAC4T,UAAU,CAAE,IAAI,CAACrG,OAAO,CAAC,CAAE,CAAC;MACjD4C,OAAO,CAACnP,IAAI,CAAE,mBAAoB,CAAC,CAAC+C,IAAI,CAAE,GAAG,GAAGU,IAAK,CAAC;MACtD0L,OAAO,CACLnP,IAAI,CAAE,kBAAmB,CAAC,CAC1ByE,WAAW,CAAC,CAAC,CACbC,QAAQ,CAAE,kCAAkC,GAAGxC,QAAS,CAAC;;MAE3D;MACAiN,OAAO,CAACnP,IAAI,CAAE,eAAgB,CAAC,CAACG,IAAI,CAAE,IAAI,CAACmQ,YAAY,CAAEzK,GAAI,CAAE,CAAC;;MAEhE;MACA7G,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAE,IAAK,CAAC;IAC5C,CAAC;IAED2S,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB7T,GAAG,CAACkB,QAAQ,CAAE,sBAAsB,EAAE,IAAK,CAAC;IAC7C,CAAC;IAED4S,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAACpT,GAAG,CAACgR,QAAQ,CAAE,MAAO,CAAC;IACnC,CAAC;IAEDqC,WAAW,EAAE,SAAAA,CAAWnO,CAAC,EAAG;MAC3BA,CAAC,CAACoO,eAAe,CAAC,CAAC;MACnB,IAAK,CAAEzC,SAAS,CAACC,SAAS,EAAG;MAC7BD,SAAS,CAACC,SAAS,CAACyC,SAAS,CAAEnU,CAAC,CAAE8F,CAAC,CAACoN,MAAO,CAAC,CAACjP,IAAI,CAAC,CAAE,CAAC,CAACmQ,IAAI,CAAE,MAAM;QACjEpU,CAAC,CAAE8F,CAAC,CAACoN,MAAO,CAAC,CAACtN,QAAQ,CAAE,QAAS,CAAC;QAClCR,UAAU,CAAE,YAAY;UACvBpF,CAAC,CAAE8F,CAAC,CAACoN,MAAO,CAAC,CAACvN,WAAW,CAAE,QAAS,CAAC;QACtC,CAAC,EAAE,IAAK,CAAC;MACV,CAAE,CAAC;IACJ,CAAC;IAED0O,WAAW,EAAE,SAAAA,CAAWvO,CAAC,EAAG;MAC3BwO,OAAO,GAAGtU,CAAC,CAAE8F,CAAC,CAACoN,MAAO,CAAC;MACvB,IACCoB,OAAO,CAACjQ,MAAM,CAAC,CAAC,CAACuN,QAAQ,CAAE,aAAc,CAAC,IAC1C,CAAE0C,OAAO,CAAC1C,QAAQ,CAAE,YAAa,CAAC,EAElC;MACD,IAAI,CAACoC,MAAM,CAAC,CAAC,GAAG,IAAI,CAACvN,KAAK,CAAC,CAAC,GAAG,IAAI,CAACzF,IAAI,CAAC,CAAC;IAC3C,CAAC;IAEDuT,mBAAmB,EAAE,SAAAA,CAAA,EAAY;MAChC,MAAM/L,SAAS,GAAG,IAAI,CAAC5H,GAAG,CAAC2O,QAAQ,CAAE,WAAY,CAAC;MAClDrP,GAAG,CAACkB,QAAQ,CAAE,MAAM,EAAEoH,SAAU,CAAC;IAClC,CAAC;IAED;AACF;AACA;IACEgM,WAAW,EAAE,SAAAA,CAAW1O,CAAC,EAAG;MAC3B,IAAI2O,WAAW,GAAGzU,CAAC,CAAE8F,CAAC,CAACoN,MAAO,CAAC,CAC7BrJ,OAAO,CAAE,IAAK,CAAC,CACf3I,IAAI,CAAE,cAAe,CAAC;MACxBuT,WAAW,CAAC7O,QAAQ,CAAE,QAAS,CAAC;IACjC,CAAC;IAED;AACF;AACA;IACE8O,UAAU,EAAE,SAAAA,CAAW5O,CAAC,EAAG;MAC1B,IAAI6O,sBAAsB,GAAG,EAAE;MAC/B,IAAIC,sBAAsB,GAAG5U,CAAC,CAAE8F,CAAC,CAACoN,MAAO,CAAC,CACxCrJ,OAAO,CAAE,IAAK,CAAC,CACf3I,IAAI,CAAE,cAAe,CAAC;;MAExB;MACAkE,UAAU,CAAE,YAAY;QACvB,IAAIyP,uBAAuB,GAAG7U,CAAC,CAAE2J,QAAQ,CAACmL,aAAc,CAAC,CACvDjL,OAAO,CAAE,IAAK,CAAC,CACf3I,IAAI,CAAE,cAAe,CAAC;QACxB,IAAK,CAAE0T,sBAAsB,CAACG,EAAE,CAAEF,uBAAwB,CAAC,EAAG;UAC7DD,sBAAsB,CAACjP,WAAW,CAAE,QAAS,CAAC;QAC/C;MACD,CAAC,EAAEgP,sBAAuB,CAAC;IAC5B,CAAC;IAED3T,IAAI,EAAE,SAAAA,CAAA,EAAY;MACjB;MACA,IAAIwH,SAAS,GAAG,IAAI,CAAC5H,GAAG,CAAC2O,QAAQ,CAAE,WAAY,CAAC;;MAEhD;MACA,IAAI,CAAC8D,YAAY,CAAC,CAAC;MACnB,IAAI,CAAC1B,0BAA0B,CAAC,CAAC;;MAEjC;MACAzR,GAAG,CAACkB,QAAQ,CAAE,mBAAmB,EAAE,IAAK,CAAC;MACzC,IAAI,CAACsE,OAAO,CAAE,iBAAkB,CAAC;;MAEjC;MACAxF,GAAG,CAACkB,QAAQ,CAAE,MAAM,EAAEoH,SAAU,CAAC;MAEjC,IAAI,CAACwM,aAAa,CAAC,CAAC;;MAEpB;MACAxM,SAAS,CAACyM,SAAS,CAAC,CAAC;MACrB,IAAI,CAACrU,GAAG,CAACgF,QAAQ,CAAE,MAAO,CAAC;IAC5B,CAAC;IAEDwN,eAAe,EAAE,SAAAA,CAAWtN,CAAC,EAAG;MAC/B;MACA,IACC,EACGA,CAAC,CAACoP,KAAK,IAAI,GAAG,IAAIpP,CAAC,CAACoP,KAAK,IAAI,GAAG;MAAM;MACxC,CACC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACpD,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAC5C,CAAClT,QAAQ,CAAE8D,CAAC,CAACoP,KAAM,CAAC;MAAI;MACvBpP,CAAC,CAACoP,KAAK,IAAI,GAAG,IAAIpP,CAAC,CAACoP,KAAK,IAAI,GAAK,CACpC,EACA;QACD;QACAlV,CAAC,CAAE,IAAK,CAAC,CACP6J,OAAO,CAAE,oBAAqB,CAAC,CAC/BqF,QAAQ,CAAE,gBAAiB,CAAC,CAC5B4C,OAAO,CAAE,MAAO,CAAC;QACnB;MACD;IACD,CAAC;IAEDrL,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB;MACA,IAAI+B,SAAS,GAAG,IAAI,CAAC5H,GAAG,CAAC2O,QAAQ,CAAE,WAAY,CAAC;;MAEhD;MACA/G,SAAS,CAAC2M,OAAO,CAAC,CAAC;MACnB,IAAI,CAACvU,GAAG,CAAC+E,WAAW,CAAE,MAAO,CAAC;;MAE9B;MACAzF,GAAG,CAACkB,QAAQ,CAAE,oBAAoB,EAAE,IAAK,CAAC;MAC1C,IAAI,CAACsE,OAAO,CAAE,kBAAmB,CAAC;;MAElC;MACAxF,GAAG,CAACkB,QAAQ,CAAE,MAAM,EAAEoH,SAAU,CAAC;IAClC,CAAC;IAED4M,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAOlV,GAAG,CAACkV,SAAS,CAAE,IAAI,CAACxU,GAAG,EAAE,IAAI,CAAC+P,YAAY,CAAC,CAAE,CAAC;IACtD,CAAC;IAED9I,IAAI,EAAE,SAAAA,CAAWlD,IAAI,EAAG;MACvB;MACAA,IAAI,GAAGA,IAAI,IAAI,UAAU,CAAC,CAAC;;MAE3B;MACA,IAAIkD,IAAI,GAAG,IAAI,CAACkJ,OAAO,CAAE,MAAO,CAAC;;MAEjC;MACA,IAAKlJ,IAAI,KAAK,UAAU,EAAG;QAC1B;MACD;;MAEA;MACA,IAAI,CAACoJ,OAAO,CAAE,MAAM,EAAEtM,IAAK,CAAC;;MAE5B;MACA,IAAI,CAAC/D,GAAG,CAACsD,IAAI,CAAE,WAAW,EAAES,IAAK,CAAC;;MAElC;MACAzE,GAAG,CAACkB,QAAQ,CAAE,mBAAmB,EAAE,IAAI,EAAEuD,IAAK,CAAC;IAChD,CAAC;IAED0Q,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAIvE,SAAS,GAAG,IAAI,CAACH,YAAY,CAAC,CAAC;MACnC,IAAI9I,IAAI,GAAG,IAAI,CAACnG,GAAG,CAAE,MAAO,CAAC;;MAE7B;MACA,IAAK,IAAI,CAACsS,MAAM,CAAC,CAAC,EAAG;QACpB,IAAI,CAACvN,KAAK,CAAC,CAAC;MACb;;MAEA;MACA,IAAKoB,IAAI,IAAI,UAAU,EAAG;QACzB;QACA;MAAA,CACA,MAAM,IAAKA,IAAI,IAAI,MAAM,EAAG;QAC5B,IAAI,CAAC7H,CAAC,CAAE,sBAAsB,GAAG8Q,SAAS,GAAG,IAAK,CAAC,CAAC7J,MAAM,CAAC,CAAC;;QAE5D;MACD,CAAC,MAAM;QACN,IAAI,CAACjH,CAAC,CAAE,UAAU,GAAG8Q,SAAS,GAAG,IAAK,CAAC,CAAC7J,MAAM,CAAC,CAAC;MACjD;;MAEA;MACA/G,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAE,IAAK,CAAC;IAC5C,CAAC;IAEDkU,QAAQ,EAAE,SAAAA,CAAWxP,CAAC,EAAElF,GAAG,EAAG;MAC7B;MACA,IAAI,CAACiH,IAAI,CAAC,CAAC;;MAEX;MACA3H,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAE,IAAK,CAAC;IAC5C,CAAC;IAEDmU,SAAS,EAAE,SAAAA,CAAWzP,CAAC,EAAElF,GAAG,EAAEqB,IAAI,EAAEiG,KAAK,EAAG;MAC3C,IAAK,IAAI,CAACuF,OAAO,CAAC,CAAC,KAAK7M,GAAG,CAACsD,IAAI,CAAE,WAAY,CAAC,EAAG;QACjDlE,CAAC,CAAE,8BAA+B,CAAC,CAACmI,IAAI,CAAE,UAAU,EAAE,KAAM,CAAC;MAC9D;;MAEA;MACA,IAAKlG,IAAI,IAAI,MAAM,EAAG;QACrB;MACD;;MAEA;MACA,IAAK,CAAE,YAAY,EAAE,QAAQ,CAAE,CAACwH,OAAO,CAAExH,IAAK,CAAC,GAAG,CAAC,CAAC,EAAG;QACtD,IAAI,CAAC4F,IAAI,CAAE,MAAO,CAAC;;QAEnB;MACD,CAAC,MAAM;QACN,IAAI,CAACA,IAAI,CAAC,CAAC;MACZ;;MAEA;MACA,IACC,CACC,YAAY,EACZ,OAAO,EACP,UAAU,EACV,MAAM,EACN,MAAM,EACN,KAAK,CACL,CAAC4B,OAAO,CAAExH,IAAK,CAAC,GAAG,CAAC,CAAC,EACrB;QACD,IAAI,CAACnB,MAAM,CAAC,CAAC;MACd;;MAEA;MACAZ,GAAG,CAACkB,QAAQ,CAAE,sBAAsB,GAAGa,IAAI,EAAE,IAAI,EAAEiG,KAAM,CAAC;IAC3D,CAAC;IAEDsN,aAAa,EAAE,SAAAA,CAAW1P,CAAC,EAAElF,GAAG,EAAG;MAClC;MACA,IAAIuB,KAAK,GAAGvB,GAAG,CAAC4E,GAAG,CAAC,CAAC;MACrB,IAAI,CAACZ,GAAG,CAAE,OAAO,EAAEzC,KAAM,CAAC;;MAE1B;MACA,IAAK,IAAI,CAACgG,IAAI,CAAE,MAAO,CAAC,IAAI,EAAE,EAAG;QAChC,IAAIlG,IAAI,GAAG/B,GAAG,CAACuV,YAAY,CAC1B,4BAA4B,EAC5BvV,GAAG,CAACwV,WAAW,CAAEvT,KAAM,CAAC,EACxB,IACD,CAAC;QACD,IAAI,CAACgG,IAAI,CAAE,MAAM,EAAElG,IAAK,CAAC;MAC1B;IACD,CAAC;IAED0T,YAAY,EAAE,SAAAA,CAAW7P,CAAC,EAAElF,GAAG,EAAG;MACjC;MACA,IAAIqB,IAAI,GAAGrB,GAAG,CAAC4E,GAAG,CAAC,CAAC;MACpB,IAAI,CAACZ,GAAG,CAAE,MAAM,EAAE3C,IAAK,CAAC;;MAExB;MACA,IAAKA,IAAI,CAACuH,MAAM,CAAE,CAAC,EAAE,CAAE,CAAC,KAAK,QAAQ,EAAG;QACvCoM,KAAK,CACJ1V,GAAG,CAACmN,EAAE,CACL,kEACD,CACD,CAAC;MACF;IACD,CAAC;IAEDwI,gBAAgB,EAAE,SAAAA,CAAW/P,CAAC,EAAElF,GAAG,EAAG;MACrC;MACA,IAAIgT,QAAQ,GAAGhT,GAAG,CAACuH,IAAI,CAAE,SAAU,CAAC,GAAG,CAAC,GAAG,CAAC;MAC5C,IAAI,CAACvD,GAAG,CAAE,UAAU,EAAEgP,QAAS,CAAC;IACjC,CAAC;IAED5L,MAAM,EAAE,SAAAA,CAAWrE,IAAI,EAAG;MACzB;MACAA,IAAI,GAAGzD,GAAG,CAAC0D,SAAS,CAAED,IAAI,EAAE;QAC3BoE,OAAO,EAAE;MACV,CAAE,CAAC;;MAEH;MACA,IAAImF,EAAE,GAAG,IAAI,CAAC/E,IAAI,CAAE,IAAK,CAAC;MAE1B,IAAK+E,EAAE,EAAG;QACT,IAAItB,MAAM,GAAG5L,CAAC,CAAE,qBAAsB,CAAC;QACvC,IAAI8V,MAAM,GAAGlK,MAAM,CAACpG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG0H,EAAE;QACpCtB,MAAM,CAACpG,GAAG,CAAEsQ,MAAO,CAAC;MACrB;;MAEA;MACA5V,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAE,IAAK,CAAC;;MAE3C;MACA,IAAKuC,IAAI,CAACoE,OAAO,EAAG;QACnB,IAAI,CAACgO,aAAa,CAAC,CAAC;MACrB,CAAC,MAAM;QACN,IAAI,CAAC9O,MAAM,CAAC,CAAC;MACd;IACD,CAAC;IAED+O,aAAa,EAAE,SAAAA,CAAWlQ,CAAC,EAAElF,GAAG,EAAG;MAClC;MACA,IAAKkF,CAAC,CAACmQ,QAAQ,EAAG;QACjB,OAAO,IAAI,CAACjO,MAAM,CAAC,CAAC;MACrB;;MAEA;MACA,IAAI,CAACpH,GAAG,CAACgF,QAAQ,CAAE,QAAS,CAAC;;MAE7B;MACA,IAAIsQ,OAAO,GAAGhW,GAAG,CAACiW,UAAU,CAAE;QAC7BC,aAAa,EAAE,IAAI;QACnBlD,MAAM,EAAEtS,GAAG;QACX8I,OAAO,EAAE,IAAI;QACb2M,OAAO,EAAE,SAAAA,CAAA,EAAY;UACpB,IAAI,CAACrO,MAAM,CAAC,CAAC;QACd,CAAC;QACDsO,MAAM,EAAE,SAAAA,CAAA,EAAY;UACnB,IAAI,CAAC1V,GAAG,CAAC+E,WAAW,CAAE,QAAS,CAAC;QACjC;MACD,CAAE,CAAC;IACJ,CAAC;IAEDoQ,aAAa,EAAE,SAAAA,CAAA,EAAY;MAC1B;MACA,IAAIjL,KAAK,GAAG,IAAI;MAChB,IAAIyL,KAAK,GAAG,IAAI,CAAC3V,GAAG,CAACyD,MAAM,CAAC,CAAC;MAC7B,IAAImS,OAAO,GAAGtW,GAAG,CAACuW,gBAAgB,CAAE;QACnCC,OAAO,EAAE,IAAI,CAAC9V;MACf,CAAE,CAAC;;MAEH;MACAV,GAAG,CAAC+G,MAAM,CAAE;QACXiM,MAAM,EAAE,IAAI,CAACtS,GAAG;QAChB+V,SAAS,EAAEH,OAAO,CAAC/T,MAAM,GAAG,CAAC,GAAG,EAAE;QAClCmU,QAAQ,EAAE,SAAAA,CAAA,EAAY;UACrB9L,KAAK,CAAC7D,MAAM,CAAC,CAAC;UACd/G,GAAG,CAACkB,QAAQ,CAAE,sBAAsB,EAAE0J,KAAK,EAAEyL,KAAM,CAAC;QACrD;MACD,CAAE,CAAC;;MAEH;MACArW,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAE0J,KAAK,EAAEyL,KAAM,CAAC;IACpD,CAAC;IAED5H,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB;MACA,IAAIkI,MAAM,GAAG3W,GAAG,CAAC4W,MAAM,CAAE,QAAS,CAAC;;MAEnC;MACA,IAAIC,SAAS,GAAG7W,GAAG,CAACyO,SAAS,CAAE;QAC9BuE,MAAM,EAAE,IAAI,CAACtS,GAAG;QAChBY,MAAM,EAAE,IAAI,CAACE,GAAG,CAAE,IAAK,CAAC;QACxBsV,OAAO,EAAEH;MACV,CAAE,CAAC;;MAEH;MACAE,SAAS,CAAC7S,IAAI,CAAE,UAAU,EAAE2S,MAAO,CAAC;;MAEpC;MACA,IAAIrH,QAAQ,GAAGtP,GAAG,CAAC0H,cAAc,CAAEmP,SAAU,CAAC;;MAE9C;MACA,IAAI5U,KAAK,GAAGqN,QAAQ,CAACrH,IAAI,CAAE,OAAQ,CAAC;MACpC,IAAIlG,IAAI,GAAGuN,QAAQ,CAACrH,IAAI,CAAE,MAAO,CAAC;MAClC,IAAI8O,GAAG,GAAGhV,IAAI,CAACK,KAAK,CAAE,GAAI,CAAC,CAACmO,GAAG,CAAC,CAAC;MACjC,IAAIyG,IAAI,GAAGhX,GAAG,CAACmN,EAAE,CAAE,MAAO,CAAC;;MAE3B;MACA,IAAKnN,GAAG,CAACiX,SAAS,CAAEF,GAAI,CAAC,EAAG;QAC3B,IAAIG,CAAC,GAAGH,GAAG,GAAG,CAAC,GAAG,CAAC;QACnB9U,KAAK,GAAGA,KAAK,CAAC6U,OAAO,CAAEC,GAAG,EAAEG,CAAE,CAAC;QAC/BnV,IAAI,GAAGA,IAAI,CAAC+U,OAAO,CAAEC,GAAG,EAAEG,CAAE,CAAC;;QAE7B;MACD,CAAC,MAAM,IAAKH,GAAG,CAACxN,OAAO,CAAEyN,IAAK,CAAC,KAAK,CAAC,EAAG;QACvC,IAAIE,CAAC,GAAGH,GAAG,CAACD,OAAO,CAAEE,IAAI,EAAE,EAAG,CAAC,GAAG,CAAC;QACnCE,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC;;QAEjB;QACAjV,KAAK,GAAGA,KAAK,CAAC6U,OAAO,CAAEC,GAAG,EAAEC,IAAI,GAAGE,CAAE,CAAC;QACtCnV,IAAI,GAAGA,IAAI,CAAC+U,OAAO,CAAEC,GAAG,EAAEC,IAAI,GAAGE,CAAE,CAAC;;QAEpC;MACD,CAAC,MAAM;QACNjV,KAAK,IAAI,IAAI,GAAG+U,IAAI,GAAG,GAAG;QAC1BjV,IAAI,IAAI,GAAG,GAAGiV,IAAI;MACnB;MAEA1H,QAAQ,CAACrH,IAAI,CAAE,IAAI,EAAE,CAAE,CAAC;MACxBqH,QAAQ,CAACrH,IAAI,CAAE,OAAO,EAAEhG,KAAM,CAAC;MAC/BqN,QAAQ,CAACrH,IAAI,CAAE,MAAM,EAAElG,IAAK,CAAC;MAC7BuN,QAAQ,CAACrH,IAAI,CAAE,KAAK,EAAE0O,MAAO,CAAC;;MAE9B;MACA,IAAK,IAAI,CAAC7C,MAAM,CAAC,CAAC,EAAG;QACpB,IAAI,CAACvN,KAAK,CAAC,CAAC;MACb;;MAEA;MACA+I,QAAQ,CAACxO,IAAI,CAAC,CAAC;;MAEf;MACA,IAAIqW,MAAM,GAAG7H,QAAQ,CAACc,QAAQ,CAAE,aAAc,CAAC;MAC/ClL,UAAU,CAAE,YAAY;QACvBiS,MAAM,CAAC3R,OAAO,CAAE,OAAQ,CAAC;MAC1B,CAAC,EAAE,GAAI,CAAC;;MAER;MACAxF,GAAG,CAACkB,QAAQ,CAAE,wBAAwB,EAAE,IAAI,EAAEoO,QAAS,CAAC;MACxDtP,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAEoO,QAAS,CAAC;IAChD,CAAC;IAED8H,IAAI,EAAE,SAAAA,CAAA,EAAY;MACjB;MACA,IAAIC,MAAM,GAAG,IAAI,CAAC7V,GAAG,CAAE,IAAK,CAAC;MAC7B,IAAI8V,OAAO,GAAG,IAAI,CAAC9V,GAAG,CAAE,KAAM,CAAC;MAC/B,IAAImV,MAAM,GAAG3W,GAAG,CAAC4W,MAAM,CAAE,QAAS,CAAC;;MAEnC;MACA5W,GAAG,CAACuX,MAAM,CAAE;QACXvE,MAAM,EAAE,IAAI,CAACtS,GAAG;QAChBY,MAAM,EAAE+V,MAAM;QACdP,OAAO,EAAEH;MACV,CAAE,CAAC;;MAEH;MACA,IAAI,CAACjS,GAAG,CAAE,IAAI,EAAEiS,MAAO,CAAC;MACxB,IAAI,CAACjS,GAAG,CAAE,QAAQ,EAAE2S,MAAO,CAAC;MAC5B,IAAI,CAAC3S,GAAG,CAAE,SAAS,EAAE4S,OAAQ,CAAC;;MAE9B;MACA,IAAI,CAACrP,IAAI,CAAE,KAAK,EAAE0O,MAAO,CAAC;MAC1B,IAAI,CAAC1O,IAAI,CAAE,IAAI,EAAE,CAAE,CAAC;;MAEpB;MACA,IAAI,CAACvH,GAAG,CAACsD,IAAI,CAAE,UAAU,EAAE2S,MAAO,CAAC;MACnC,IAAI,CAACjW,GAAG,CAACsD,IAAI,CAAE,SAAS,EAAE2S,MAAO,CAAC;;MAElC;MACA3W,GAAG,CAACkB,QAAQ,CAAE,mBAAmB,EAAE,IAAK,CAAC;IAC1C,CAAC;IAEDsW,IAAI,EAAE,SAAAA,CAAA,EAAY;MACjB;MACA,IAAIC,UAAU,GAAG,SAAAA,CAAW7M,KAAK,EAAG;QACnC,OAAOA,KAAK,CAACpJ,GAAG,CAAE,MAAO,CAAC,IAAI,UAAU;MACzC,CAAC;;MAED;MACA,IAAIuO,OAAO,GAAG0H,UAAU,CAAE,IAAK,CAAC;;MAEhC;MACA,IAAK,CAAE1H,OAAO,EAAG;QAChB/P,GAAG,CAAC6M,eAAe,CAAE;UACpB1I,MAAM,EAAE,IAAI,CAACzD;QACd,CAAE,CAAC,CAACoM,GAAG,CAAE,UAAWlC,KAAK,EAAG;UAC3BmF,OAAO,GAAG0H,UAAU,CAAE7M,KAAM,CAAC,IAAIA,KAAK,CAACmF,OAAO;QAC/C,CAAE,CAAC;MACJ;;MAEA;MACA,IAAKA,OAAO,EAAG;QACd2F,KAAK,CACJ1V,GAAG,CAACmN,EAAE,CACL,8DACD,CACD,CAAC;QACD;MACD;;MAEA;MACA,IAAIH,EAAE,GAAG,IAAI,CAAC/E,IAAI,CAAE,IAAK,CAAC;MAC1B,IAAI2C,KAAK,GAAG,IAAI;MAChB,IAAI8M,KAAK,GAAG,KAAK;MACjB,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAY;QACvB;QACAD,KAAK,GAAG1X,GAAG,CAAC4X,QAAQ,CAAE;UACrBC,KAAK,EAAE7X,GAAG,CAACmN,EAAE,CAAE,mBAAoB,CAAC;UACpCuF,OAAO,EAAE,IAAI;UACboF,KAAK,EAAE,OAAO;UACd3X,QAAQ,EAAEyK,KAAK,CAAClK,GAAG,CAACM,IAAI,CAAE,aAAc;QACzC,CAAE,CAAC;;QAEH;QACA,IAAI+W,QAAQ,GAAG;UACdC,MAAM,EAAE,4BAA4B;UACpCC,QAAQ,EAAEjL;QACX,CAAC;;QAED;QACAlN,CAAC,CAACqS,IAAI,CAAE;UACP9O,GAAG,EAAErD,GAAG,CAACwB,GAAG,CAAE,SAAU,CAAC;UACzBtB,IAAI,EAAEF,GAAG,CAACkY,cAAc,CAAEH,QAAS,CAAC;UACpCtT,IAAI,EAAE,MAAM;UACZ0T,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAEC;QACV,CAAE,CAAC;MACJ,CAAC;MAED,IAAIA,KAAK,GAAG,SAAAA,CAAWlX,IAAI,EAAG;QAC7B;QACAuW,KAAK,CAAChF,OAAO,CAAE,KAAM,CAAC;QACtBgF,KAAK,CAACY,OAAO,CAAEnX,IAAK,CAAC;;QAErB;QACAuW,KAAK,CAAChO,EAAE,CAAE,QAAQ,EAAE,MAAM,EAAE6O,KAAM,CAAC;MACpC,CAAC;MAED,IAAIA,KAAK,GAAG,SAAAA,CAAW3S,CAAC,EAAElF,GAAG,EAAG;QAC/B;QACAkF,CAAC,CAAC4S,cAAc,CAAC,CAAC;;QAElB;QACAxY,GAAG,CAACyY,kBAAkB,CAAEf,KAAK,CAAC5X,CAAC,CAAE,SAAU,CAAE,CAAC;;QAE9C;QACA,IAAIiY,QAAQ,GAAG;UACdC,MAAM,EAAE,4BAA4B;UACpCC,QAAQ,EAAEjL,EAAE;UACZ0L,cAAc,EAAEhB,KAAK,CAAC5X,CAAC,CAAE,QAAS,CAAC,CAACwF,GAAG,CAAC;QACzC,CAAC;;QAED;QACAxF,CAAC,CAACqS,IAAI,CAAE;UACP9O,GAAG,EAAErD,GAAG,CAACwB,GAAG,CAAE,SAAU,CAAC;UACzBtB,IAAI,EAAEF,GAAG,CAACkY,cAAc,CAAEH,QAAS,CAAC;UACpCtT,IAAI,EAAE,MAAM;UACZ0T,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAEO;QACV,CAAE,CAAC;MACJ,CAAC;MAED,IAAIA,KAAK,GAAG,SAAAA,CAAWxX,IAAI,EAAG;QAC7BuW,KAAK,CAACY,OAAO,CAAEnX,IAAK,CAAC;QAErB,IAAKyX,EAAE,CAACC,IAAI,IAAID,EAAE,CAACC,IAAI,CAACC,KAAK,IAAI9Y,GAAG,CAACmN,EAAE,EAAG;UACzCyL,EAAE,CAACC,IAAI,CAACC,KAAK,CACZ9Y,GAAG,CAACmN,EAAE,CAAE,4BAA6B,CAAC,EACtC,QACD,CAAC;QACF;QAEAuK,KAAK,CAAC5X,CAAC,CAAE,kBAAmB,CAAC,CAACmB,KAAK,CAAC,CAAC;QAErC2J,KAAK,CAACiL,aAAa,CAAC,CAAC;MACtB,CAAC;;MAED;MACA8B,KAAK,CAAC,CAAC;IACR,CAAC;IAEDoB,YAAY,EAAE,SAAAA,CAAWnT,CAAC,EAAElF,GAAG,EAAG;MACjCkF,CAAC,CAAC4S,cAAc,CAAC,CAAC;MAElB,MAAMQ,KAAK,GAAGhZ,GAAG,CAACkH,oBAAoB,CAAE;QACvC/G,QAAQ,EAAE;MACX,CAAE,CAAC;IACJ,CAAC;IAED8Y,YAAY,EAAE,SAAAA,CAAWrT,CAAC,EAAElF,GAAG,EAAG;MACjC;MACA,IAAK,IAAI,CAACwY,aAAa,EAAG;QACzBC,YAAY,CAAE,IAAI,CAACD,aAAc,CAAC;MACnC;;MAEA;MACA;MACA,IAAI,CAACA,aAAa,GAAG,IAAI,CAAChU,UAAU,CAAE,YAAY;QACjD,IAAI,CAACkU,UAAU,CAAE1Y,GAAG,CAAC4E,GAAG,CAAC,CAAE,CAAC;MAC7B,CAAC,EAAE,GAAI,CAAC;IACT,CAAC;IAED8T,UAAU,EAAE,SAAAA,CAAWC,OAAO,EAAG;MAChC,IAAIC,QAAQ,GAAG,IAAI,CAACrR,IAAI,CAAE,MAAO,CAAC;MAClC,IAAIsR,SAAS,GAAGvZ,GAAG,CAAC4T,UAAU,CAAE,mBAAmB,GAAG0F,QAAS,CAAC;MAChE,IAAIE,QAAQ,GAAGxZ,GAAG,CAAC4T,UAAU,CAAE,mBAAmB,GAAGyF,OAAQ,CAAC;;MAE9D;MACA,IAAI,CAAC3Y,GAAG,CAAC+E,WAAW,CAAE8T,SAAU,CAAC,CAAC7T,QAAQ,CAAE8T,QAAS,CAAC;MACtD,IAAI,CAAC9Y,GAAG,CAACsD,IAAI,CAAE,WAAW,EAAEqV,OAAQ,CAAC;MACrC,IAAI,CAAC3Y,GAAG,CAACR,IAAI,CAAE,MAAM,EAAEmZ,OAAQ,CAAC;;MAEhC;MACA,IAAK,IAAI,CAACvI,GAAG,CAAE,KAAM,CAAC,EAAG;QACxB,IAAI,CAACtP,GAAG,CAAE,KAAM,CAAC,CAACiY,KAAK,CAAC,CAAC;MAC1B;;MAEA;MACA,MAAMC,YAAY,GAAG,CAAC,CAAC;MAEvB,IAAI,CAAChZ,GAAG,CACNM,IAAI,CACJ,iFACD,CAAC,CACA4B,IAAI,CAAE,YAAY;QAClB,IAAI+W,GAAG,GAAG7Z,CAAC,CAAE,IAAK,CAAC,CAACI,IAAI,CAAE,YAAa,CAAC;QACxC,IAAI0Z,YAAY,GAAG9Z,CAAC,CAAE,IAAK,CAAC,CAACuP,QAAQ,CAAC,CAAC,CAACwK,UAAU,CAAC,CAAC;QAEpDH,YAAY,CAAEC,GAAG,CAAE,GAAGC,YAAY;QAElCA,YAAY,CAACzL,MAAM,CAAC,CAAC;MACtB,CAAE,CAAC;MAEJ,IAAI,CAACzJ,GAAG,CAAE,WAAW,GAAG4U,QAAQ,EAAEI,YAAa,CAAC;;MAEhD;MACA,IAAK,IAAI,CAAC5I,GAAG,CAAE,WAAW,GAAGuI,OAAQ,CAAC,EAAG;QACxC,IAAIS,YAAY,GAAG,IAAI,CAACtY,GAAG,CAAE,WAAW,GAAG6X,OAAQ,CAAC;QAEpD,IAAI,CAACU,qBAAqB,CAAED,YAAa,CAAC;QAC1C,IAAI,CAACpV,GAAG,CAAE,MAAM,EAAE2U,OAAQ,CAAC;QAC3B;MACD;;MAEA;MACA,MAAMW,QAAQ,GAAGla,CAAC,CACjB,2FACD,CAAC;MACD,IAAI,CAACY,GAAG,CACNM,IAAI,CACJ,2DACD,CAAC,CACAiZ,MAAM,CAAED,QAAS,CAAC;MAEpB,MAAMjC,QAAQ,GAAG;QAChBC,MAAM,EAAE,uCAAuC;QAC/CpN,KAAK,EAAE,IAAI,CAACsK,SAAS,CAAC,CAAC;QACvBgF,MAAM,EAAE,IAAI,CAACzJ,YAAY,CAAC;MAC3B,CAAC;;MAED;MACA,IAAI0J,GAAG,GAAGra,CAAC,CAACqS,IAAI,CAAE;QACjB9O,GAAG,EAAErD,GAAG,CAACwB,GAAG,CAAE,SAAU,CAAC;QACzBtB,IAAI,EAAEF,GAAG,CAACkY,cAAc,CAAEH,QAAS,CAAC;QACpCtT,IAAI,EAAE,MAAM;QACZ0T,QAAQ,EAAE,MAAM;QAChB3O,OAAO,EAAE,IAAI;QACb4O,OAAO,EAAE,SAAAA,CAAWgC,QAAQ,EAAG;UAC9B,IAAK,CAAEpa,GAAG,CAACqa,aAAa,CAAED,QAAS,CAAC,EAAG;YACtC;UACD;UAEA,IAAI,CAACL,qBAAqB,CAAEK,QAAQ,CAACla,IAAK,CAAC;QAC5C,CAAC;QACDwW,QAAQ,EAAE,SAAAA,CAAA,EAAY;UACrB;UACAsD,QAAQ,CAACjT,MAAM,CAAC,CAAC;UACjB,IAAI,CAACrC,GAAG,CAAE,MAAM,EAAE2U,OAAQ,CAAC;UAC3B;QACD;MACD,CAAE,CAAC;;MAEH;MACA,IAAI,CAAC3U,GAAG,CAAE,KAAK,EAAEyV,GAAI,CAAC;IACvB,CAAC;IAEDJ,qBAAqB,EAAE,SAAAA,CAAWO,QAAQ,EAAG;MAC5C,IAAK,QAAQ,KAAK,OAAOA,QAAQ,EAAG;QACnC;MACD;MAEA,MAAM3X,IAAI,GAAG,IAAI;MACjB,MAAM4X,IAAI,GAAG9Y,MAAM,CAACwP,IAAI,CAAEqJ,QAAS,CAAC;MAEpCC,IAAI,CAAC/X,OAAO,CAAImX,GAAG,IAAM;QACxB,MAAMa,IAAI,GAAG7X,IAAI,CAACjC,GAAG,CAACM,IAAI,CACzB,2BAA2B,GAC1B2Y,GAAG,CAAC7C,OAAO,CAAE,GAAG,EAAE,GAAI,CAAC,GACvB,2BACF,CAAC;QACD,IAAI2D,UAAU,GAAG,EAAE;QAEnB,IACC,CAAE,QAAQ,EAAE,QAAQ,CAAE,CAAC3Y,QAAQ,CAAE,OAAOwY,QAAQ,CAAEX,GAAG,CAAG,CAAC,EACxD;UACDc,UAAU,GAAGH,QAAQ,CAAEX,GAAG,CAAE;QAC7B;QAEAa,IAAI,CAACE,OAAO,CAAED,UAAW,CAAC;QAC1Bza,GAAG,CAACkB,QAAQ,CAAE,QAAQ,EAAEsZ,IAAK,CAAC;MAC/B,CAAE,CAAC;MAEH,IAAI,CAAC1F,aAAa,CAAC,CAAC;IACrB,CAAC;IAED6F,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB;MACA,IAAIC,EAAE,GAAG5a,GAAG,CAACwB,GAAG,CAAE,SAAU,CAAC;;MAE7B;MACA,IAAI2C,MAAM,GAAG,IAAI,CAACkM,SAAS,CAAC,CAAC;MAC7B,IAAKlM,MAAM,EAAG;QACbyW,EAAE,GAAGjH,QAAQ,CAAExP,MAAM,CAAC8D,IAAI,CAAE,IAAK,CAAE,CAAC,IAAI9D,MAAM,CAAC8D,IAAI,CAAE,KAAM,CAAC;MAC7D;;MAEA;MACA,IAAI,CAACA,IAAI,CAAE,QAAQ,EAAE2S,EAAG,CAAC;IAC1B,CAAC;IAED9F,aAAa,EAAE,SAAAA,CAAA,EAAW;MACzB,MAAMxM,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC;MAClC,MAAM5F,KAAK,GAAG4F,SAAS,CAACtH,IAAI,CAAE,sDAAuD,CAAC;MAEtF0B,KAAK,CAACE,IAAI,CAAE,YAAW;QACtB,MAAMiY,WAAW,GAAG/a,CAAC,CAAE,IAAK,CAAC;QAC7B,MAAMgb,OAAO,GAAGD,WAAW,CAAC7Z,IAAI,CAAE,gCAAiC,CAAC,CAACd,IAAI,CAAE,WAAY,CAAC;QACxF,MAAM6a,QAAQ,GAAGzS,SAAS,CAACtH,IAAI,CAAE,qBAAqB,GAAG8Z,OAAQ,CAAC,CAAC1U,KAAK,CAAC,CAAC;QAE1E,IAAKtG,CAAC,CAACoG,IAAI,CAAE2U,WAAW,CAAC9W,IAAI,CAAC,CAAE,CAAC,KAAK,EAAE,EAAG;UAC1CgX,QAAQ,CAAC7W,IAAI,CAAC,CAAC;QAChB,CAAC,MAAM,IAAK6W,QAAQ,CAAClG,EAAE,CAAE,SAAU,CAAC,EAAG;UACtCkG,QAAQ,CAAC9W,IAAI,CAAC,CAAC;QAChB;MACD,CAAE,CAAC;IACJ;EAED,CAAE,CAAC;AACJ,CAAC,EAAImD,MAAO,CAAC;;;;;;;;;;AC1lCb,CAAE,UAAWtH,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECC,GAAG,CAAC6N,eAAe,GAAG,UAAWhH,GAAG,EAAG;IACtC,OAAO7G,GAAG,CAACuW,gBAAgB,CAAE;MAC5B1P,GAAG,EAAEA,GAAG;MACRyJ,KAAK,EAAE;IACR,CAAE,CAAC;EACJ,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECtQ,GAAG,CAACuW,gBAAgB,GAAG,UAAW9S,IAAI,EAAG;IACxC;IACAA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB,IAAIkF,QAAQ,GAAG,mBAAmB;IAClC,IAAI2N,OAAO,GAAG,KAAK;;IAEnB;IACA7S,IAAI,GAAGzD,GAAG,CAAC0D,SAAS,CAAED,IAAI,EAAE;MAC3BuJ,EAAE,EAAE,EAAE;MACNnG,GAAG,EAAE,EAAE;MACPpC,IAAI,EAAE,EAAE;MACR6L,KAAK,EAAE,KAAK;MACZ0K,IAAI,EAAE,IAAI;MACV7W,MAAM,EAAE,KAAK;MACbqS,OAAO,EAAE,KAAK;MACd/G,KAAK,EAAE;IACR,CAAE,CAAC;;IAEH;IACA,IAAKhM,IAAI,CAACuJ,EAAE,EAAG;MACdrE,QAAQ,IAAI,YAAY,GAAGlF,IAAI,CAACuJ,EAAE,GAAG,IAAI;IAC1C;;IAEA;IACA,IAAKvJ,IAAI,CAACoD,GAAG,EAAG;MACf8B,QAAQ,IAAI,aAAa,GAAGlF,IAAI,CAACoD,GAAG,GAAG,IAAI;IAC5C;;IAEA;IACA,IAAKpD,IAAI,CAACgB,IAAI,EAAG;MAChBkE,QAAQ,IAAI,cAAc,GAAGlF,IAAI,CAACgB,IAAI,GAAG,IAAI;IAC9C;;IAEA;IACA,IAAKhB,IAAI,CAACuX,IAAI,EAAG;MAChB1E,OAAO,GAAG7S,IAAI,CAACuX,IAAI,CAAC3L,QAAQ,CAAE1G,QAAS,CAAC;IACzC,CAAC,MAAM,IAAKlF,IAAI,CAACU,MAAM,EAAG;MACzBmS,OAAO,GAAG7S,IAAI,CAACU,MAAM,CAACnD,IAAI,CAAE2H,QAAS,CAAC;IACvC,CAAC,MAAM,IAAKlF,IAAI,CAAC+S,OAAO,EAAG;MAC1BF,OAAO,GAAG7S,IAAI,CAAC+S,OAAO,CAACxH,QAAQ,CAAErG,QAAS,CAAC;IAC5C,CAAC,MAAM,IAAKlF,IAAI,CAACgM,KAAK,EAAG;MACxB6G,OAAO,GAAG7S,IAAI,CAACgM,KAAK,CAACwD,OAAO,CAAEtK,QAAS,CAAC;IACzC,CAAC,MAAM;MACN2N,OAAO,GAAGxW,CAAC,CAAE6I,QAAS,CAAC;IACxB;;IAEA;IACA,IAAKlF,IAAI,CAAC6M,KAAK,EAAG;MACjBgG,OAAO,GAAGA,OAAO,CAACtR,KAAK,CAAE,CAAC,EAAEvB,IAAI,CAAC6M,KAAM,CAAC;IACzC;;IAEA;IACA,OAAOgG,OAAO;EACf,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECtW,GAAG,CAAC0H,cAAc,GAAG,UAAWD,MAAM,EAAG;IACxC;IACA,IAAK,OAAOA,MAAM,KAAK,QAAQ,EAAG;MACjCA,MAAM,GAAGzH,GAAG,CAAC6N,eAAe,CAAEpG,MAAO,CAAC;IACvC;;IAEA;IACA,IAAImD,KAAK,GAAGnD,MAAM,CAACvH,IAAI,CAAE,KAAM,CAAC;IAChC,IAAK,CAAE0K,KAAK,EAAG;MACdA,KAAK,GAAG5K,GAAG,CAACib,cAAc,CAAExT,MAAO,CAAC;IACrC;;IAEA;IACA,OAAOmD,KAAK;EACb,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC5K,GAAG,CAAC6M,eAAe,GAAG,UAAWpJ,IAAI,EAAG;IACvC;IACA,IAAI6S,OAAO,GAAGtW,GAAG,CAACuW,gBAAgB,CAAE9S,IAAK,CAAC;;IAE1C;IACA,IAAIyX,MAAM,GAAG,EAAE;IACf5E,OAAO,CAAC1T,IAAI,CAAE,YAAY;MACzB,IAAIgI,KAAK,GAAG5K,GAAG,CAAC0H,cAAc,CAAE5H,CAAC,CAAE,IAAK,CAAE,CAAC;MAC3Cob,MAAM,CAACvN,IAAI,CAAE/C,KAAM,CAAC;IACrB,CAAE,CAAC;;IAEH;IACA,OAAOsQ,MAAM;EACd,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEClb,GAAG,CAACib,cAAc,GAAG,UAAWxT,MAAM,EAAG;IACxC;IACA,IAAImD,KAAK,GAAG,IAAI5K,GAAG,CAAC2P,WAAW,CAAElI,MAAO,CAAC;;IAEzC;IACAzH,GAAG,CAACkB,QAAQ,CAAE,kBAAkB,EAAE0J,KAAM,CAAC;;IAEzC;IACA,OAAOA,KAAK;EACb,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIuQ,YAAY,GAAG,IAAInb,GAAG,CAAC+J,KAAK,CAAE;IACjCqR,QAAQ,EAAE,CAAC;IAEXva,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB;MACA,IAAImJ,OAAO,GAAG,CAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAE;;MAExD;MACAA,OAAO,CAAC8C,GAAG,CAAE,UAAWkL,MAAM,EAAG;QAChC,IAAI,CAACqD,eAAe,CAAErD,MAAO,CAAC;MAC/B,CAAC,EAAE,IAAK,CAAC;IACV,CAAC;IAEDqD,eAAe,EAAE,SAAAA,CAAWrD,MAAM,EAAG;MACpC;MACA,IAAIsD,YAAY,GAAGtD,MAAM,GAAG,gBAAgB,CAAC,CAAC;MAC9C,IAAIuD,YAAY,GAAGvD,MAAM,GAAG,eAAe,CAAC,CAAC;MAC7C,IAAIwD,WAAW,GAAGxD,MAAM,GAAG,aAAa,CAAC,CAAC;;MAE1C;MACA,IAAIlP,QAAQ,GAAG,SAAAA,CAAWpI,GAAG,CAAC,uBAAwB;QACrD;QACA,IAAI+a,YAAY,GAAGzb,GAAG,CAAC6M,eAAe,CAAE;UAAE1I,MAAM,EAAEzD;QAAI,CAAE,CAAC;;QAEzD;QACA,IAAK+a,YAAY,CAAClZ,MAAM,EAAG;UAC1B;UACA,IAAIkB,IAAI,GAAGzD,GAAG,CAAC0b,SAAS,CAAEzS,SAAU,CAAC;;UAErC;UACAxF,IAAI,CAACgF,MAAM,CAAE,CAAC,EAAE,CAAC,EAAE6S,YAAY,EAAEG,YAAa,CAAC;UAC/Czb,GAAG,CAACkB,QAAQ,CAAC8H,KAAK,CAAE,IAAI,EAAEvF,IAAK,CAAC;QACjC;MACD,CAAC;;MAED;MACA,IAAIkY,cAAc,GAAG,SAAAA,CACpBF,YAAY,CAAC,uBACZ;QACD;QACA,IAAIhY,IAAI,GAAGzD,GAAG,CAAC0b,SAAS,CAAEzS,SAAU,CAAC;;QAErC;QACAxF,IAAI,CAACmY,OAAO,CAAEL,YAAa,CAAC;;QAE5B;QACAE,YAAY,CAAC3O,GAAG,CAAE,UAAWtI,WAAW,EAAG;UAC1C;UACAf,IAAI,CAAE,CAAC,CAAE,GAAGe,WAAW;UACvBxE,GAAG,CAACkB,QAAQ,CAAC8H,KAAK,CAAE,IAAI,EAAEvF,IAAK,CAAC;QACjC,CAAE,CAAC;MACJ,CAAC;;MAED;MACA,IAAIoY,cAAc,GAAG,SAAAA,CACpBrX,WAAW,CAAC,uBACX;QACD;QACA,IAAIf,IAAI,GAAGzD,GAAG,CAAC0b,SAAS,CAAEzS,SAAU,CAAC;;QAErC;QACAxF,IAAI,CAACmY,OAAO,CAAEL,YAAa,CAAC;;QAE5B;QACA,IAAIO,UAAU,GAAG,CAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAE;QAC1CA,UAAU,CAAChP,GAAG,CAAE,UAAWiP,SAAS,EAAG;UACtCtY,IAAI,CAAE,CAAC,CAAE,GACR8X,YAAY,GACZ,GAAG,GACHQ,SAAS,GACT,GAAG,GACHvX,WAAW,CAAChD,GAAG,CAAEua,SAAU,CAAC;UAC7B/b,GAAG,CAACkB,QAAQ,CAAC8H,KAAK,CAAE,IAAI,EAAEvF,IAAK,CAAC;QACjC,CAAE,CAAC;;QAEH;QACAA,IAAI,CAACgF,MAAM,CAAE,CAAC,EAAE,CAAE,CAAC;;QAEnB;QACAjE,WAAW,CAACgB,OAAO,CAAEgW,WAAW,EAAE/X,IAAK,CAAC;MACzC,CAAC;;MAED;MACAzD,GAAG,CAACgc,SAAS,CAAEhE,MAAM,EAAElP,QAAQ,EAAE,CAAE,CAAC;MACpC9I,GAAG,CAACgc,SAAS,CAAEV,YAAY,EAAEK,cAAc,EAAE,CAAE,CAAC;MAChD3b,GAAG,CAACgc,SAAS,CAAET,YAAY,EAAEM,cAAc,EAAE,CAAE,CAAC;IACjD;EACD,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAII,YAAY,GAAG,IAAIjc,GAAG,CAAC+J,KAAK,CAAE;IACjCiD,EAAE,EAAE,cAAc;IAElB1M,MAAM,EAAE;MACP,cAAc,EAAE,UAAU;MAC1B,4BAA4B,EAAE,iBAAiB;MAC/C,kBAAkB,EAAE;IACrB,CAAC;IAED0J,OAAO,EAAE;MACRkS,oBAAoB,EAAE,gBAAgB;MACtCxR,qBAAqB,EAAE,gBAAgB;MACvCL,mBAAmB,EAAE,eAAe;MACpCC,wBAAwB,EAAE,mBAAmB;MAC7CF,sBAAsB,EAAE;IACzB,CAAC;IAED+R,QAAQ,EAAE,SAAAA,CAAWvW,CAAC,EAAElF,GAAG,EAAG;MAC7B;MACA,IAAIwa,MAAM,GAAGlb,GAAG,CAAC6M,eAAe,CAAC,CAAC;;MAElC;MACAqO,MAAM,CAACpO,GAAG,CAAE,UAAWlC,KAAK,EAAG;QAC9BA,KAAK,CAACuK,MAAM,CAAC,CAAC;MACf,CAAE,CAAC;IACJ,CAAC;IAEDiH,iBAAiB,EAAE,SAAAA,CAAWxR,KAAK,EAAG;MACrC,IAAI,CAACyR,YAAY,CAAEzR,KAAK,CAAClK,GAAG,CAACyD,MAAM,CAAC,CAAE,CAAC;IACxC,CAAC;IAEDmY,eAAe,EAAE,SAAAA,CAAW1W,CAAC,EAAElF,GAAG,EAAG;MACpC;MACA,IAAKA,GAAG,CAACgR,QAAQ,CAAE,aAAc,CAAC,EAAG;;MAErC;MACAhR,GAAG,CAAC6b,QAAQ,CAAE;QACbC,MAAM,EAAE,SAAAA,CAAUnT,KAAK,EAAEsJ,OAAO,EAAG;UAClC;UACA,OAAOA,OAAO,CAAC8J,KAAK,CAAC,CAAC,CACpBzb,IAAI,CAAE,QAAS,CAAC,CACfgD,IAAI,CAAE,MAAM,EAAE,UAAUkT,CAAC,EAAEwF,WAAW,EAAG;YACxC,OAAO,OAAO,GAAG/I,QAAQ,CAAEgJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,MAAM,EAAE,EAAG,CAAC,CAACC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAGH,WAAW;UACxF,CAAE,CAAC,CACH3F,GAAG,CAAC,CAAC;QACR,CAAC;QACD+F,MAAM,EAAE,sBAAsB;QAC9BC,WAAW,EAAE,iBAAiB;QAC9BC,KAAK,EAAE,SAAAA,CAAWpX,CAAC,EAAEqX,EAAE,EAAG;UACzB,IAAIrS,KAAK,GAAG5K,GAAG,CAAC0H,cAAc,CAAEuV,EAAE,CAACC,IAAK,CAAC;UACzCD,EAAE,CAACE,WAAW,CAACC,MAAM,CAAEH,EAAE,CAACC,IAAI,CAACE,MAAM,CAAC,CAAE,CAAC;UACzCpd,GAAG,CAACkB,QAAQ,CAAE,wBAAwB,EAAE0J,KAAK,EAAElK,GAAI,CAAC;QACrD,CAAC;QACD2c,MAAM,EAAE,SAAAA,CAAWzX,CAAC,EAAEqX,EAAE,EAAG;UAC1B,IAAIrS,KAAK,GAAG5K,GAAG,CAAC0H,cAAc,CAAEuV,EAAE,CAACC,IAAK,CAAC;UACzCld,GAAG,CAACkB,QAAQ,CAAE,uBAAuB,EAAE0J,KAAK,EAAElK,GAAI,CAAC;QACpD;MACD,CAAE,CAAC;IACJ,CAAC;IAED4c,cAAc,EAAE,SAAAA,CAAW1S,KAAK,EAAEyL,KAAK,EAAG;MACzC,IAAI,CAACgG,YAAY,CAAEhG,KAAM,CAAC;IAC3B,CAAC;IAEDkH,cAAc,EAAE,SAAAA,CAAW3S,KAAK,EAAEyL,KAAK,EAAG;MACzCzL,KAAK,CAAC+P,YAAY,CAAC,CAAC;MACpB,IAAI,CAAC0B,YAAY,CAAEhG,KAAM,CAAC;IAC3B,CAAC;IAEDmH,aAAa,EAAE,SAAAA,CAAW5S,KAAK,EAAG;MACjC;MACAA,KAAK,CAAC4F,SAAS,CAAC,CAAC,CAAC1D,GAAG,CAAE,UAAW2C,KAAK,EAAG;QACzCA,KAAK,CAAC3H,MAAM,CAAE;UAAED,OAAO,EAAE;QAAM,CAAE,CAAC;MACnC,CAAE,CAAC;IACJ,CAAC;IAED5E,iBAAiB,EAAE,SAAAA,CAAW2H,KAAK,EAAG;MACrC;MACAA,KAAK,CAAClK,GAAG,CAACM,IAAI,CAAE,sBAAuB,CAAC,CAACiH,IAAI,CAAE,UAAU,EAAE,KAAM,CAAC;IACnE,CAAC;IAEDwV,gBAAgB,EAAE,SAAAA,CAAW7S,KAAK,EAAE0E,QAAQ,EAAG;MAC9C;MACA,IAAID,QAAQ,GAAGC,QAAQ,CAACkB,SAAS,CAAC,CAAC;MACnC,IAAKnB,QAAQ,CAAC9M,MAAM,EAAG;QACtB;QACA8M,QAAQ,CAACvC,GAAG,CAAE,UAAW2C,KAAK,EAAG;UAChC;UACAA,KAAK,CAAC2H,IAAI,CAAC,CAAC;;UAEZ;UACA,IAAK3H,KAAK,CAACqE,MAAM,CAAC,CAAC,EAAG;YACrBrE,KAAK,CAAC3O,IAAI,CAAC,CAAC;UACb;;UAEA;UACA2O,KAAK,CAACkL,YAAY,CAAC,CAAC;QACrB,CAAE,CAAC;;QAEH;QACA3a,GAAG,CAACkB,QAAQ,CACX,yBAAyB,EACzBmO,QAAQ,EACRC,QAAQ,EACR1E,KACD,CAAC;MACF;;MAEA;MACA,IAAI,CAACwR,iBAAiB,CAAE9M,QAAS,CAAC;IACnC,CAAC;IAED+M,YAAY,EAAE,SAAAA,CAAWhG,KAAK,EAAG;MAChC;MACA,IAAI6E,MAAM,GAAGlb,GAAG,CAAC6M,eAAe,CAAE;QACjCmO,IAAI,EAAE3E;MACP,CAAE,CAAC;;MAEH;MACA,IAAK,CAAE6E,MAAM,CAAC3Y,MAAM,EAAG;QACtB8T,KAAK,CAAC3Q,QAAQ,CAAE,QAAS,CAAC;QAC1B2Q,KAAK,CACHpD,OAAO,CAAE,sBAAuB,CAAC,CACjC7M,KAAK,CAAC,CAAC,CACPV,QAAQ,CAAE,QAAS,CAAC;QACtB;MACD;;MAEA;MACA2Q,KAAK,CAAC5Q,WAAW,CAAE,QAAS,CAAC;MAC7B4Q,KAAK,CACHpD,OAAO,CAAE,sBAAuB,CAAC,CACjC7M,KAAK,CAAC,CAAC,CACPX,WAAW,CAAE,QAAS,CAAC;;MAEzB;MACAyV,MAAM,CAACpO,GAAG,CAAE,UAAWlC,KAAK,EAAEsM,CAAC,EAAG;QACjCtM,KAAK,CAAC3C,IAAI,CAAE,YAAY,EAAEiP,CAAE,CAAC;MAC9B,CAAE,CAAC;IACJ,CAAC;IAEDpI,UAAU,EAAE,SAAAA,CAAWlJ,CAAC,EAAElF,GAAG,EAAG;MAC/B,IAAI2V,KAAK;MAET,IAAK3V,GAAG,CAACgR,QAAQ,CAAE,iBAAkB,CAAC,EAAG;QACxC2E,KAAK,GAAG3V,GAAG,CAACuS,OAAO,CAAE,iBAAkB,CAAC,CAACyK,EAAE,CAAE,CAAE,CAAC;MACjD,CAAC,MAAM,IACNhd,GAAG,CAACyD,MAAM,CAAC,CAAC,CAACuN,QAAQ,CAAE,uBAAwB,CAAC,IAChDhR,GAAG,CAACyD,MAAM,CAAC,CAAC,CAACuN,QAAQ,CAAE,yBAA0B,CAAC,EACjD;QACD2E,KAAK,GAAGvW,CAAC,CAAE,uBAAwB,CAAC;MACrC,CAAC,MAAM,IAAKY,GAAG,CAACyD,MAAM,CAAC,CAAC,CAACuN,QAAQ,CAAE,2BAA4B,CAAC,EAAG;QAClE2E,KAAK,GAAG3V,GAAG,CACTuS,OAAO,CAAE,kBAAmB,CAAC,CAC7BjS,IAAI,CAAE,uBAAwB,CAAC;MAClC,CAAC,MAAM;QACNqV,KAAK,GAAG3V,GAAG,CACTiJ,OAAO,CAAE,YAAa,CAAC,CACvBqF,QAAQ,CAAE,iBAAkB,CAAC;MAChC;MAEA,IAAI,CAAC2O,QAAQ,CAAEtH,KAAM,CAAC;IACvB,CAAC;IAEDsH,QAAQ,EAAE,SAAAA,CAAWtH,KAAK,EAAG;MAC5B;MACA,IAAIlV,IAAI,GAAGrB,CAAC,CAAE,iBAAkB,CAAC,CAACqB,IAAI,CAAC,CAAC;MACxC,IAAIT,GAAG,GAAGZ,CAAC,CAAEqB,IAAK,CAAC;MACnB,IAAIkW,MAAM,GAAG3W,GAAG,CAACR,IAAI,CAAE,IAAK,CAAC;MAC7B,IAAIyW,MAAM,GAAG3W,GAAG,CAAC4W,MAAM,CAAE,QAAS,CAAC;;MAEnC;MACA,IAAIC,SAAS,GAAG7W,GAAG,CAACyO,SAAS,CAAE;QAC9BuE,MAAM,EAAEtS,GAAG;QACXY,MAAM,EAAE+V,MAAM;QACdP,OAAO,EAAEH,MAAM;QACf9T,MAAM,EAAE,SAAAA,CAAWnC,GAAG,EAAEkd,IAAI,EAAG;UAC9BvH,KAAK,CAACxT,MAAM,CAAE+a,IAAK,CAAC;QACrB;MACD,CAAE,CAAC;;MAEH;MACA,IAAItO,QAAQ,GAAGtP,GAAG,CAAC0H,cAAc,CAAEmP,SAAU,CAAC;;MAE9C;MACAvH,QAAQ,CAACrH,IAAI,CAAE,KAAK,EAAE0O,MAAO,CAAC;MAC9BrH,QAAQ,CAACrH,IAAI,CAAE,IAAI,EAAE,CAAE,CAAC;MACxBqH,QAAQ,CAACrH,IAAI,CAAE,OAAO,EAAE,EAAG,CAAC;MAC5BqH,QAAQ,CAACrH,IAAI,CAAE,MAAM,EAAE,EAAG,CAAC;;MAE3B;MACA4O,SAAS,CAAC7S,IAAI,CAAE,UAAU,EAAE2S,MAAO,CAAC;MACpCE,SAAS,CAAC7S,IAAI,CAAE,SAAS,EAAE2S,MAAO,CAAC;;MAEnC;MACArH,QAAQ,CAACqL,YAAY,CAAC,CAAC;;MAEvB;MACA,IAAIkD,KAAK,GAAGvO,QAAQ,CAAC5D,MAAM,CAAE,MAAO,CAAC;MACrCxG,UAAU,CAAE,YAAY;QACvB,IAAKmR,KAAK,CAAC3E,QAAQ,CAAE,oBAAqB,CAAC,EAAG;UAC7C2E,KAAK,CAAC5Q,WAAW,CAAE,oBAAqB,CAAC;QAC1C,CAAC,MAAM;UACNoY,KAAK,CAACrY,OAAO,CAAE,OAAQ,CAAC;QACzB;MACD,CAAC,EAAE,GAAI,CAAC;;MAER;MACA8J,QAAQ,CAACxO,IAAI,CAAC,CAAC;;MAEf;MACA,IAAI,CAACub,YAAY,CAAEhG,KAAM,CAAC;;MAE1B;MACArW,GAAG,CAACkB,QAAQ,CAAE,kBAAkB,EAAEoO,QAAS,CAAC;MAC5CtP,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAEoO,QAAS,CAAC;IAChD;EACD,CAAE,CAAC;AACJ,CAAC,EAAIlI,MAAO,CAAC;;;;;;;;;;AChfb,CAAE,UAAWtH,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAI+d,eAAe,GAAG,IAAI9d,GAAG,CAAC+J,KAAK,CAAE;IACpCiD,EAAE,EAAE,iBAAiB;IACrB+Q,IAAI,EAAE,OAAO;IAEbzd,MAAM,EAAE;MACP,0BAA0B,EAAE,gBAAgB;MAC5C,2BAA2B,EAAE,iBAAiB;MAC9C,6BAA6B,EAAE,mBAAmB;MAClD,+BAA+B,EAAE;IAClC,CAAC;IAEDO,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,IAAI,CAACH,GAAG,GAAGZ,CAAC,CAAE,0BAA2B,CAAC;MAC1C,IAAI,CAACke,eAAe,CAAC,CAAC;MACtB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACzB,CAAC;IAEDD,eAAe,EAAE,SAAAA,CAAA,EAAY;MAC5B;MACA,IAAKhe,GAAG,CAACwB,GAAG,CAAE,QAAS,CAAC,EAAG;QAC1B;MACD;;MAEA;MACA,MAAM0c,gBAAgB,GAAGle,GAAG,CAACwB,GAAG,CAAE,kBAAmB,CAAC;MACtD,IAAK,OAAO0c,gBAAgB,KAAK,QAAQ,EAAG;MAE5C,MAAMC,WAAW,GAAG,IAAI,CAACzd,GAAG,CAC1BM,IAAI,CAAE,8BAA+B,CAAC,CACtCA,IAAI,CAAE,yBAA0B,CAAC;MAEnC,KAAM,MAAM,CAAE6F,GAAG,EAAE9E,IAAI,CAAE,IAAIN,MAAM,CAAC8R,OAAO,CAAE2K,gBAAiB,CAAC,EAAG;QACjEC,WAAW,CAACtb,MAAM,CACjB,2CAA2C,GAC1Cd,IAAI,GACJ,IAAI,GACJ/B,GAAG,CAACmN,EAAE,CAAE,UAAW,CAAC,GACpB,YACF,CAAC;MACF;IACD,CAAC;IAEDiR,cAAc,EAAE,SAAAA,CAAWxY,CAAC,EAAElF,GAAG,EAAG;MACnC,IAAI,CAAC2d,OAAO,CAAE3d,GAAG,CAACiJ,OAAO,CAAE,IAAK,CAAE,CAAC;IACpC,CAAC;IAED2U,iBAAiB,EAAE,SAAAA,CAAW1Y,CAAC,EAAElF,GAAG,EAAG;MACtC,IAAI,CAAC6d,UAAU,CAAE7d,GAAG,CAACiJ,OAAO,CAAE,IAAK,CAAE,CAAC;IACvC,CAAC;IAED6U,kBAAkB,EAAE,SAAAA,CAAW5Y,CAAC,EAAElF,GAAG,EAAG;MACvC,IAAI,CAAC+d,UAAU,CAAE/d,GAAG,CAACiJ,OAAO,CAAE,IAAK,CAAE,CAAC;IACvC,CAAC;IAED0E,eAAe,EAAE,SAAAA,CAAWzI,CAAC,EAAElF,GAAG,EAAG;MACpC,IAAI,CAAC4N,QAAQ,CAAC,CAAC;IAChB,CAAC;IAED+P,OAAO,EAAE,SAAAA,CAAWK,GAAG,EAAG;MACzB1e,GAAG,CAACyO,SAAS,CAAEiQ,GAAI,CAAC;MACpB,IAAI,CAACT,iBAAiB,CAAC,CAAC;IACzB,CAAC;IAEDM,UAAU,EAAE,SAAAA,CAAWG,GAAG,EAAG;MAC5B,IAAKA,GAAG,CAAC1P,QAAQ,CAAE,IAAK,CAAC,CAACzM,MAAM,IAAI,CAAC,EAAG;QACvCmc,GAAG,CAAC/U,OAAO,CAAE,aAAc,CAAC,CAAC5C,MAAM,CAAC,CAAC;MACtC,CAAC,MAAM;QACN2X,GAAG,CAAC3X,MAAM,CAAC,CAAC;MACb;;MAEA;MACA,IAAIwH,MAAM,GAAG,IAAI,CAACzO,CAAC,CAAE,mBAAoB,CAAC;MAC1CyO,MAAM,CAACvN,IAAI,CAAE,IAAK,CAAC,CAAC+C,IAAI,CAAE/D,GAAG,CAACmN,EAAE,CAAE,0BAA2B,CAAE,CAAC;MAEhE,IAAI,CAAC8Q,iBAAiB,CAAC,CAAC;IACzB,CAAC;IAEDQ,UAAU,EAAE,SAAAA,CAAWlT,KAAK,EAAG;MAC9B;MACA,IAAIgD,MAAM,GAAGhD,KAAK,CAAC5B,OAAO,CAAE,aAAc,CAAC;MAC3C,IAAIuQ,MAAM,GAAG3O,KAAK,CAChBvK,IAAI,CAAE,iBAAkB,CAAC,CACzBgD,IAAI,CAAE,MAAO,CAAC,CACd8S,OAAO,CAAE,SAAS,EAAE,EAAG,CAAC;;MAE1B;MACA,IAAI6H,QAAQ,GAAG,CAAC,CAAC;MACjBA,QAAQ,CAAC3G,MAAM,GAAG,sCAAsC;MACxD2G,QAAQ,CAACC,IAAI,GAAG5e,GAAG,CAACkV,SAAS,CAAE3J,KAAK,EAAE2O,MAAO,CAAC;MAC9CyE,QAAQ,CAACC,IAAI,CAAC5R,EAAE,GAAGzB,KAAK,CAACrL,IAAI,CAAE,IAAK,CAAC;MACrCye,QAAQ,CAACC,IAAI,CAACC,KAAK,GAAGtQ,MAAM,CAACrO,IAAI,CAAE,IAAK,CAAC;;MAEzC;MACAF,GAAG,CAACmM,OAAO,CAAEZ,KAAK,CAACvK,IAAI,CAAE,UAAW,CAAE,CAAC;;MAEvC;MACAlB,CAAC,CAACqS,IAAI,CAAE;QACP9O,GAAG,EAAErD,GAAG,CAACwB,GAAG,CAAE,SAAU,CAAC;QACzBtB,IAAI,EAAEF,GAAG,CAACkY,cAAc,CAAEyG,QAAS,CAAC;QACpCla,IAAI,EAAE,MAAM;QACZ0T,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,SAAAA,CAAWjX,IAAI,EAAG;UAC1B,IAAK,CAAEA,IAAI,EAAG;UACdoK,KAAK,CAACuT,WAAW,CAAE3d,IAAK,CAAC;QAC1B;MACD,CAAE,CAAC;IACJ,CAAC;IAEDmN,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB;MACA,IAAIC,MAAM,GAAG,IAAI,CAACzO,CAAC,CAAE,kBAAmB,CAAC;;MAEzC;MACA0O,OAAO,GAAGxO,GAAG,CAACyO,SAAS,CAAEF,MAAO,CAAC;;MAEjC;MACAC,OAAO,CAACxN,IAAI,CAAE,IAAK,CAAC,CAAC+C,IAAI,CAAE/D,GAAG,CAACmN,EAAE,CAAE,IAAK,CAAE,CAAC;;MAE3C;MACAqB,OAAO,CAACxN,IAAI,CAAE,IAAK,CAAC,CAAC0N,GAAG,CAAE,QAAS,CAAC,CAAC3H,MAAM,CAAC,CAAC;;MAE7C;MACA,IAAI,CAACkX,iBAAiB,CAAC,CAAC;IACzB,CAAC;IAEDA,iBAAiB,EAAE,SAAAA,CAAA,EAAY;MAC9B,IAAI1P,MAAM,GAAG,IAAI,CAACzO,CAAC,CAAE,kBAAmB,CAAC;MAEzC,IAAIif,WAAW,GAAGxQ,MAAM,CAAC5E,OAAO,CAAE,cAAe,CAAC;MAElD,IAAIqV,UAAU,GAAGD,WAAW,CAAC/d,IAAI,CAAE,eAAgB,CAAC,CAACuB,MAAM;MAE3D,IAAKyc,UAAU,GAAG,CAAC,EAAG;QACrBD,WAAW,CAACrZ,QAAQ,CAAE,sBAAuB,CAAC;MAC/C,CAAC,MAAM;QACNqZ,WAAW,CAACtZ,WAAW,CAAE,sBAAuB,CAAC;MAClD;IACD;EACD,CAAE,CAAC;AACJ,CAAC,EAAI2B,MAAO,CAAC;;;;;;;;;;ACxJb,CAAE,UAAWtH,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIkf,OAAO,GAAG,SAAAA,CAAWxa,IAAI,EAAG;IAC/B,OAAOzE,GAAG,CAACkf,aAAa,CAAEza,IAAI,IAAI,EAAG,CAAC,GAAG,cAAc;EACxD,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECzE,GAAG,CAACiP,oBAAoB,GAAG,UAAW7G,KAAK,EAAG;IAC7C,IAAI+W,KAAK,GAAG/W,KAAK,CAAC0F,SAAS;IAC3B,IAAIsR,GAAG,GAAGH,OAAO,CAAEE,KAAK,CAAC1a,IAAI,GAAG,GAAG,GAAG0a,KAAK,CAACpd,IAAK,CAAC;IAClD,IAAI,CAACiF,MAAM,CAAEoY,GAAG,CAAE,GAAGhX,KAAK;EAC3B,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECpI,GAAG,CAACqf,eAAe,GAAG,UAAWzU,KAAK,EAAG;IACxC;IACA,IAAInG,IAAI,GAAGmG,KAAK,CAACpJ,GAAG,CAAE,SAAU,CAAC,IAAI,EAAE;IACvC,IAAIO,IAAI,GAAG6I,KAAK,CAACpJ,GAAG,CAAE,MAAO,CAAC,IAAI,EAAE;IACpC,IAAI4d,GAAG,GAAGH,OAAO,CAAExa,IAAI,GAAG,GAAG,GAAG1C,IAAK,CAAC;IACtC,IAAIqG,KAAK,GAAGpI,GAAG,CAACgH,MAAM,CAAEoY,GAAG,CAAE,IAAI,IAAI;;IAErC;IACA,IAAKhX,KAAK,KAAK,IAAI,EAAG,OAAO,KAAK;;IAElC;IACA,IAAIyB,OAAO,GAAG,IAAIzB,KAAK,CAAEwC,KAAM,CAAC;;IAEhC;IACA,OAAOf,OAAO;EACf,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC7J,GAAG,CAACsf,eAAe,GAAG,UAAW1U,KAAK,EAAG;IACxC;IACA,IAAKA,KAAK,YAAYxD,MAAM,EAAG;MAC9BwD,KAAK,GAAG5K,GAAG,CAACuf,QAAQ,CAAE3U,KAAM,CAAC;IAC9B;;IAEA;IACA,OAAOA,KAAK,CAACf,OAAO;EACrB,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAI2V,eAAe,GAAG,IAAIxf,GAAG,CAAC+J,KAAK,CAAE;IACpCC,OAAO,EAAE;MACRyV,SAAS,EAAE;IACZ,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAW9U,KAAK,EAAG;MAC9BA,KAAK,CAACf,OAAO,GAAG7J,GAAG,CAACqf,eAAe,CAAEzU,KAAM,CAAC;IAC7C;EACD,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC5K,GAAG,CAACsL,YAAY,GAAGtL,GAAG,CAAC+J,KAAK,CAACtJ,MAAM,CAAE;IACpCmK,KAAK,EAAE,KAAK;IACZnG,IAAI,EAAE,EAAE;IACR1C,IAAI,EAAE,EAAE;IACRgc,IAAI,EAAE,OAAO;IACbnO,UAAU,EAAE,YAAY;IAExBtP,MAAM,EAAE;MACPwP,MAAM,EAAE;IACT,CAAC;IAEDvP,KAAK,EAAE,SAAAA,CAAWqK,KAAK,EAAG;MACzB;MACA,IAAInD,MAAM,GAAGmD,KAAK,CAAClK,GAAG;;MAEtB;MACA,IAAI,CAACA,GAAG,GAAG+G,MAAM;MACjB,IAAI,CAACmD,KAAK,GAAGA,KAAK;MAClB,IAAI,CAAC+U,YAAY,GAAGlY,MAAM,CAACkC,OAAO,CAAE,mBAAoB,CAAC;MACzD,IAAI,CAACnF,WAAW,GAAGxE,GAAG,CAAC0H,cAAc,CAAE,IAAI,CAACiY,YAAa,CAAC;;MAE1D;MACA7f,CAAC,CAACW,MAAM,CAAE,IAAI,CAACP,IAAI,EAAE0K,KAAK,CAAC1K,IAAK,CAAC;IAClC,CAAC;IAEDW,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,IAAI,CAACD,MAAM,CAAC,CAAC;IACd,CAAC;IAEDA,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;IAAA;EAEF,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIgf,oBAAoB,GAAG5f,GAAG,CAACsL,YAAY,CAAC7K,MAAM,CAAE;IACnDgE,IAAI,EAAE,EAAE;IACR1C,IAAI,EAAE,EAAE;IACRnB,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAIif,iBAAiB,GAAG,IAAI,CAACrb,WAAW,CAAC4L,QAAQ,CAAE,UAAW,CAAC;MAC/D,IAAI0P,eAAe,GAAGD,iBAAiB,CAAC7e,IAAI,CAC3C,8BACD,CAAC;MACD,IAAK8e,eAAe,CAACjL,EAAE,CAAE,UAAW,CAAC,EAAG;QACvC,IAAI,CAACrQ,WAAW,CAAC9D,GAAG,CAACgF,QAAQ,CAAE,uBAAwB,CAAC;MACzD,CAAC,MAAM;QACN,IAAI,CAAClB,WAAW,CAAC9D,GAAG,CAAC+E,WAAW,CAAE,uBAAwB,CAAC;MAC5D;IACD;EACD,CAAE,CAAC;EAEH,IAAIsa,6BAA6B,GAAGH,oBAAoB,CAACnf,MAAM,CAAE;IAChEgE,IAAI,EAAE,WAAW;IACjB1C,IAAI,EAAE;EACP,CAAE,CAAC;EAEH,IAAIie,uBAAuB,GAAGJ,oBAAoB,CAACnf,MAAM,CAAE;IAC1DgE,IAAI,EAAE,KAAK;IACX1C,IAAI,EAAE;EACP,CAAE,CAAC;EAEH/B,GAAG,CAACiP,oBAAoB,CAAE8Q,6BAA8B,CAAC;EACzD/f,GAAG,CAACiP,oBAAoB,CAAE+Q,uBAAwB,CAAC;;EAEnD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIC,yBAAyB,GAAGjgB,GAAG,CAACsL,YAAY,CAAC7K,MAAM,CAAE;IACxDgE,IAAI,EAAE,EAAE;IACR1C,IAAI,EAAE,EAAE;IACRnB,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAI8K,MAAM,GAAG,IAAI,CAAC5L,CAAC,CAAE,6BAA8B,CAAC;MACpD,IAAK4L,MAAM,CAACpG,GAAG,CAAC,CAAC,IAAI,OAAO,EAAG;QAC9B,IAAI,CAACxF,CAAC,CAAE,oBAAqB,CAAC,CAACwF,GAAG,CAAEoG,MAAM,CAACpG,GAAG,CAAC,CAAE,CAAC;MACnD;IACD;EACD,CAAE,CAAC;EAEH,IAAI4a,mCAAmC,GAAGD,yBAAyB,CAACxf,MAAM,CACzE;IACCgE,IAAI,EAAE,aAAa;IACnB1C,IAAI,EAAE;EACP,CACD,CAAC;EAED,IAAIoe,kCAAkC,GAAGF,yBAAyB,CAACxf,MAAM,CAAE;IAC1EgE,IAAI,EAAE,aAAa;IACnB1C,IAAI,EAAE;EACP,CAAE,CAAC;EAEH/B,GAAG,CAACiP,oBAAoB,CAAEiR,mCAAoC,CAAC;EAC/DlgB,GAAG,CAACiP,oBAAoB,CAAEkR,kCAAmC,CAAC;;EAE9D;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIC,uCAAuC,GAC1CH,yBAAyB,CAACxf,MAAM,CAAE;IACjCgE,IAAI,EAAE,kBAAkB;IACxB1C,IAAI,EAAE;EACP,CAAE,CAAC;EAEJ,IAAIse,sCAAsC,GACzCJ,yBAAyB,CAACxf,MAAM,CAAE;IACjCgE,IAAI,EAAE,kBAAkB;IACxB1C,IAAI,EAAE;EACP,CAAE,CAAC;EAEJ/B,GAAG,CAACiP,oBAAoB,CAAEmR,uCAAwC,CAAC;EACnEpgB,GAAG,CAACiP,oBAAoB,CAAEoR,sCAAuC,CAAC;;EAElE;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIC,mCAAmC,GAAGL,yBAAyB,CAACxf,MAAM,CACzE;IACCgE,IAAI,EAAE,aAAa;IACnB1C,IAAI,EAAE;EACP,CACD,CAAC;EAED,IAAIwe,kCAAkC,GAAGN,yBAAyB,CAACxf,MAAM,CAAE;IAC1EgE,IAAI,EAAE,aAAa;IACnB1C,IAAI,EAAE;EACP,CAAE,CAAC;EAEH/B,GAAG,CAACiP,oBAAoB,CAAEqR,mCAAoC,CAAC;EAC/DtgB,GAAG,CAACiP,oBAAoB,CAAEsR,kCAAmC,CAAC;;EAE9D;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIC,uBAAuB,GAAGxgB,GAAG,CAACsL,YAAY,CAAC7K,MAAM,CAAE;IACtDgE,IAAI,EAAE,cAAc;IACpB1C,IAAI,EAAE,gBAAgB;IACtBnB,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAI6f,sBAAsB,GACzB,IAAI,CAACjc,WAAW,CAAC4L,QAAQ,CAAE,eAAgB,CAAC;MAC7C,IAAIsQ,sBAAsB,GACzB,IAAI,CAAClc,WAAW,CAAC4L,QAAQ,CAAE,eAAgB,CAAC;MAC7C,IAAIuQ,UAAU,GAAGF,sBAAsB,CACrCzf,IAAI,CAAE,qCAAsC,CAAC,CAC7CmD,MAAM,CAAE,OAAQ,CAAC,CACjByc,QAAQ,CAAC,CAAC,CACVC,IAAI,CAAC,CAAC;MACR,IAAIC,mBAAmB,GACtBJ,sBAAsB,CAAC1f,IAAI,CAAE,oBAAqB,CAAC;MACpD,IAAI+f,IAAI,GAAG/gB,GAAG,CAACwB,GAAG,CAAE,iBAAkB,CAAC;MAEvC,IAAK,IAAI,CAACoJ,KAAK,CAACtF,GAAG,CAAC,CAAC,EAAG;QACvBqb,UAAU,CAAC7B,WAAW,CAAEiC,IAAI,CAACC,WAAY,CAAC;QAC1CF,mBAAmB,CAAC9c,IAAI,CACvB,aAAa,EACb,uBACD,CAAC;MACF,CAAC,MAAM;QACN2c,UAAU,CAAC7B,WAAW,CAAEiC,IAAI,CAACE,UAAW,CAAC;QACzCH,mBAAmB,CAAC9c,IAAI,CAAE,aAAa,EAAE,SAAU,CAAC;MACrD;IACD;EACD,CAAE,CAAC;EACHhE,GAAG,CAACiP,oBAAoB,CAAEuR,uBAAwB,CAAC;AACpD,CAAC,EAAIpZ,MAAO,CAAC;;;;;;;;;;ACtTb,CAAE,UAAWtH,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAImhB,iBAAiB,GAAG,IAAIlhB,GAAG,CAAC+J,KAAK,CAAE;IACtCiD,EAAE,EAAE,mBAAmB;IAEvB1M,MAAM,EAAE;MACP,cAAc,EAAE,UAAU;MAC1B,mBAAmB,EAAE,SAAS;MAC9B,+BAA+B,EAAE,yBAAyB;MAC1D,kBAAkB,EAAE,eAAe;MACnC,mBAAmB,EAAE;IACtB,CAAC;IAED6gB,OAAO,EAAE;MACRC,gBAAgB,EAAE,qBAAqB;MACvCC,oBAAoB,EAAE;IACvB,CAAC;IAEDxgB,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvBb,GAAG,CAACgc,SAAS,CAAE,SAAS,EAAE,IAAI,CAACsF,sBAAuB,CAAC;MACvDthB,GAAG,CAACmJ,UAAU,CAAE,cAAc,EAAE,IAAI,CAACoY,2BAA4B,CAAC;MAClEvhB,GAAG,CAACmJ,UAAU,CACb,mBAAmB,EACnB,IAAI,CAACqY,mCACN,CAAC;IACF,CAAC;IAEDD,2BAA2B,EAAE,SAAAA,CAC5B9d,IAAI,EACJmJ,OAAO,EACP0N,QAAQ,EACR1P,KAAK,EACL6W,QAAQ,EACP;MAAA,IAAAC,WAAA;MACD,IAAK,CAAA9W,KAAK,aAALA,KAAK,gBAAA8W,WAAA,GAAL9W,KAAK,CAAE1K,IAAI,cAAAwhB,WAAA,uBAAXA,WAAA,CAAAC,IAAA,CAAA/W,KAAK,EAAU,KAAM,CAAC,MAAK,sBAAsB,EAAG,OAAOnH,IAAI;MAEpEA,IAAI,CAAC8O,gBAAgB,GAAG,2BAA2B;MAEnD9O,IAAI,CAAC+O,cAAc,GAAG,UAAWC,SAAS,EAAG;QAC5C,IAAK,WAAW,KAAK,OAAOA,SAAS,CAACE,OAAO,EAAG;UAC/C,OAAOF,SAAS;QACjB;QAEA,IAAKA,SAAS,CAACpD,QAAQ,EAAG;UACzB,OAAOoD,SAAS,CAAC1O,IAAI;QACtB;QAEA,IACC0O,SAAS,CAACC,OAAO,IACfD,SAAS,CAACE,OAAO,IAClBF,SAAS,CAACE,OAAO,CAACC,QAAQ,KAAK,UAAY,EAC3C;UACD,IAAIC,UAAU,GAAG/S,CAAC,CAAE,qCAAsC,CAAC;UAC3D+S,UAAU,CAAC1R,IAAI,CAAEnB,GAAG,CAAC8S,OAAO,CAAEL,SAAS,CAAC1O,IAAK,CAAE,CAAC;UAChD,OAAO8O,UAAU;QAClB;QAEA,IACC,WAAW,KAAK,OAAOJ,SAAS,CAACmP,gBAAgB,IACjD,WAAW,KAAK,OAAOnP,SAAS,CAACoP,UAAU,IAC3C,WAAW,KAAK,OAAOpP,SAAS,CAACqP,UAAU,EAC1C;UACD,OAAOrP,SAAS,CAAC1O,IAAI;QACtB;QAEA,IAAI8O,UAAU,GAAG/S,CAAC,CACjB,YAAY,GACXE,GAAG,CAAC8S,OAAO,CAAEL,SAAS,CAACmP,gBAAiB,CAAC,GACzC,2CAA2C,GAC3C5hB,GAAG,CAAC8S,OAAO,CACVL,SAAS,CAACoP,UAAU,CAAC1e,UAAU,CAAE,GAAG,EAAE,GAAI,CAC3C,CAAC,GACD,6CAA6C,GAC7CnD,GAAG,CAAC8S,OAAO,CAAEL,SAAS,CAAC1O,IAAK,CAAC,GAC7B,SACF,CAAC;QACD,IAAK0O,SAAS,CAACqP,UAAU,EAAG;UAC3BjP,UAAU,CACRgO,IAAI,CAAC,CAAC,CACNhe,MAAM,CACN,yCAAyC,GACxC7C,GAAG,CAACmN,EAAE,CAAE,YAAa,CAAC,GACtB,SACF,CAAC;QACH;QACA0F,UAAU,CAAC3S,IAAI,CAAE,SAAS,EAAEuS,SAAS,CAACE,OAAQ,CAAC;QAC/C,OAAOE,UAAU;MAClB,CAAC;MAED,OAAOpP,IAAI;IACZ,CAAC;IAED+d,mCAAmC,EAAE,SAAAA,CACpCthB,IAAI,EACJuD,IAAI,EACJiI,MAAM,EACNd,KAAK,EACL6W,QAAQ,EACP;MACD,IAAKvhB,IAAI,CAAC6hB,SAAS,KAAK,sBAAsB,EAAG,OAAO7hB,IAAI;MAE5D,MAAMyf,YAAY,GAAG3f,GAAG,CAACuW,gBAAgB,CAAE;QAAE9G,KAAK,EAAE7E;MAAM,CAAE,CAAC;MAC7D,MAAMpG,WAAW,GAAGxE,GAAG,CAAC0H,cAAc,CAAEiY,YAAa,CAAC;MACtDzf,IAAI,CAAC6hB,SAAS,GAAG,2BAA2B;MAC5C7hB,IAAI,CAAC8hB,UAAU,GAAGxd,WAAW,CAAChD,GAAG,CAAE,KAAM,CAAC;MAC1CtB,IAAI,CAAC2hB,UAAU,GAAGrd,WAAW,CAAChD,GAAG,CAAE,MAAO,CAAC;;MAE3C;MACAtB,IAAI,CAAC+hB,SAAS,GAAGjiB,GAAG,CAClBuf,QAAQ,CACRvf,GAAG,CAACkiB,UAAU,CAAE;QAAE/d,MAAM,EAAEwb,YAAY;QAAE9Y,GAAG,EAAE;MAAY,CAAE,CAC5D,CAAC,CACAvB,GAAG,CAAC,CAAC;MAEP,OAAOpF,IAAI;IACZ,CAAC;IAEDohB,sBAAsB,EAAE,SAAAA,CAAA,EAAY;MACnC,IAAIa,mBAAmB,GAAGriB,CAAC,CAC1B,6EACD,CAAC;MAED,IAAKqiB,mBAAmB,CAAC5f,MAAM,EAAG;QACjCzC,CAAC,CAAE,mCAAoC,CAAC,CAAC0F,OAAO,CAAE,OAAQ,CAAC;QAC3D1F,CAAC,CAAE,wBAAyB,CAAC,CAAC0F,OAAO,CAAE,OAAQ,CAAC;MACjD;IACD,CAAC;IAED2W,QAAQ,EAAE,SAAAA,CAAWvW,CAAC,EAAElF,GAAG,EAAG;MAC7B;MACA,IAAI0hB,MAAM,GAAGtiB,CAAC,CAAE,wBAAyB,CAAC;;MAE1C;MACA,IAAK,CAAEsiB,MAAM,CAAC9c,GAAG,CAAC,CAAC,EAAG;QACrB;QACAM,CAAC,CAAC4S,cAAc,CAAC,CAAC;;QAElB;QACAxY,GAAG,CAACqiB,UAAU,CAAE3hB,GAAI,CAAC;;QAErB;QACA0hB,MAAM,CAAC5c,OAAO,CAAE,OAAQ,CAAC;MAC1B;IACD,CAAC;IAED8c,OAAO,EAAE,SAAAA,CAAW1c,CAAC,EAAG;MACvBA,CAAC,CAAC4S,cAAc,CAAC,CAAC;IACnB,CAAC;IAED+J,uBAAuB,EAAE,SAAAA,CAAW3c,CAAC,EAAElF,GAAG,EAAG;MAC5CkF,CAAC,CAAC4S,cAAc,CAAC,CAAC;MAClB9X,GAAG,CAACgF,QAAQ,CAAE,QAAS,CAAC;;MAExB;MACA1F,GAAG,CAACiW,UAAU,CAAE;QACfE,OAAO,EAAE,IAAI;QACbnD,MAAM,EAAEtS,GAAG;QACX8I,OAAO,EAAE,IAAI;QACbzF,IAAI,EAAE/D,GAAG,CAACmN,EAAE,CAAE,4BAA6B,CAAC;QAC5CgJ,OAAO,EAAE,SAAAA,CAAA,EAAY;UACpBhP,MAAM,CAACqb,QAAQ,CAACC,IAAI,GAAG/hB,GAAG,CAACsD,IAAI,CAAE,MAAO,CAAC;QAC1C,CAAC;QACDoS,MAAM,EAAE,SAAAA,CAAA,EAAY;UACnB1V,GAAG,CAAC+E,WAAW,CAAE,QAAS,CAAC;QAC5B;MACD,CAAE,CAAC;IACJ,CAAC;IAEDid,aAAa,EAAE,SAAAA,CAAW9c,CAAC,EAAElF,GAAG,EAAG;MAClC,IAAIiiB,aAAa,GAAG7iB,CAAC,CAAE,cAAe,CAAC;MAEvC,IAAK,CAAEY,GAAG,CAAC4E,GAAG,CAAC,CAAC,EAAG;QAClB5E,GAAG,CAACgF,QAAQ,CAAE,iBAAkB,CAAC;QACjCid,aAAa,CAACjd,QAAQ,CAAE,UAAW,CAAC;QACpC5F,CAAC,CAAE,cAAe,CAAC,CAAC4F,QAAQ,CAAE,UAAW,CAAC;MAC3C,CAAC,MAAM;QACNhF,GAAG,CAAC+E,WAAW,CAAE,iBAAkB,CAAC;QACpCkd,aAAa,CAACld,WAAW,CAAE,UAAW,CAAC;QACvC3F,CAAC,CAAE,cAAe,CAAC,CAAC2F,WAAW,CAAE,UAAW,CAAC;MAC9C;IACD,CAAC;IAEDmd,mBAAmB,EAAE,SAAAA,CAAWnf,IAAI,EAAG;MACtCA,IAAI,CAACof,OAAO,GAAG,IAAI;MAEnB,IACCpf,IAAI,CAACU,MAAM,KACTV,IAAI,CAACU,MAAM,CAACuN,QAAQ,CAAE,kBAAmB,CAAC,IAC3CjO,IAAI,CAACU,MAAM,CAACuN,QAAQ,CAAE,8BAA+B,CAAC,IACtDjO,IAAI,CAACU,MAAM,CAAC8O,OAAO,CAAE,mBAAoB,CAAC,CAAC1Q,MAAM,CAAE,EACnD;QACDkB,IAAI,CAACof,OAAO,GAAG,KAAK;QACpBpf,IAAI,CAACqf,gBAAgB,GAAG,IAAI;MAC7B;;MAEA;MACA,IACCrf,IAAI,CAACU,MAAM,IACXV,IAAI,CAACU,MAAM,CAACnD,IAAI,CAAE,wBAAyB,CAAC,CAACuB,MAAM,EAClD;QACDkB,IAAI,CAACqf,gBAAgB,GAAG,KAAK;MAC9B;MAEA,OAAOrf,IAAI;IACZ,CAAC;IAEDsf,wBAAwB,EAAE,SAAAA,CAAWpa,QAAQ,EAAG;MAC/C,OAAOA,QAAQ,GAAG,4CAA4C;IAC/D;EACD,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIqa,oBAAoB,GAAG,IAAIhjB,GAAG,CAAC+J,KAAK,CAAE;IACzCiD,EAAE,EAAE,sBAAsB;IAC1B+Q,IAAI,EAAE,SAAS;IAEfzd,MAAM,EAAE;MACP,4BAA4B,EAAE,mBAAmB;MACjD,iCAAiC,EAAE,2BAA2B;MAC9D,gCAAgC,EAAE;IACnC,CAAC;IAEDO,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB;MACA,IAAIoL,IAAI,GAAGnM,CAAC,CAAE,eAAgB,CAAC;MAC/B,IAAImjB,OAAO,GAAGnjB,CAAC,CAAE,4BAA6B,CAAC;;MAE/C;MACAmM,IAAI,CAACjL,IAAI,CAAE,gBAAiB,CAAC,CAAC6B,MAAM,CAAEogB,OAAO,CAAC9hB,IAAI,CAAC,CAAE,CAAC;MACtD8K,IAAI,CAACjL,IAAI,CAAE,mBAAoB,CAAC,CAAC+F,MAAM,CAAC,CAAC;;MAEzC;MACAkc,OAAO,CAAClc,MAAM,CAAC,CAAC;;MAEhB;MACA,IAAI,CAACrG,GAAG,GAAGZ,CAAC,CAAE,sBAAuB,CAAC;;MAEtC;MACA,IAAI,CAACc,MAAM,CAAC,CAAC;IACd,CAAC;IAEDsiB,kBAAkB,EAAE,SAAAA,CAAA,EAAY;MAC/B,OAAO,IAAI,CAACxiB,GAAG,CAACM,IAAI,CAAE,qBAAsB,CAAC,CAACiH,IAAI,CAAE,SAAU,CAAC;IAChE,CAAC;IAEDkb,0BAA0B,EAAE,SAAAA,CAAA,EAAY;MACvC,MAAMzX,MAAM,GAAG,IAAI,CAAChL,GAAG,CAACM,IAAI,CAAE,0BAA2B,CAAC;;MAE1D;MACA,IAAK,CAAE0K,MAAM,CAACnJ,MAAM,EAAG;QACtB,OAAO,KAAK;MACb;MAEA,OAAOmJ,MAAM,CAACzD,IAAI,CAAE,SAAU,CAAC;IAChC,CAAC;IAEDmb,sBAAsB,EAAE,SAAAA,CAAA,EAAY;MACnC,OAAO,IAAI,CAAC1iB,GAAG,CACbM,IAAI,CAAE,sCAAuC,CAAC,CAC9CsE,GAAG,CAAC,CAAC;IACR,CAAC;IAED+d,iBAAiB,EAAE,SAAAA,CAAWzd,CAAC,EAAElF,GAAG,EAAG;MACtC,IAAI4E,GAAG,GAAG,IAAI,CAAC4d,kBAAkB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MAC3CljB,GAAG,CAACsjB,iBAAiB,CAAE,iBAAiB,EAAEhe,GAAI,CAAC;MAC/C,IAAI,CAAC1E,MAAM,CAAC,CAAC;IACd,CAAC;IAED2iB,yBAAyB,EAAE,SAAAA,CAAA,EAAY;MACtC,MAAMje,GAAG,GAAG,IAAI,CAAC6d,0BAA0B,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MACrDnjB,GAAG,CAACsjB,iBAAiB,CAAE,0BAA0B,EAAEhe,GAAI,CAAC;MACxD,IAAI,CAAC1E,MAAM,CAAC,CAAC;IACd,CAAC;IAEDA,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAK,IAAI,CAACsiB,kBAAkB,CAAC,CAAC,EAAG;QAChCpjB,CAAC,CAAE,yBAA0B,CAAC,CAAC4F,QAAQ,CAAE,iBAAkB,CAAC;MAC7D,CAAC,MAAM;QACN5F,CAAC,CAAE,yBAA0B,CAAC,CAAC2F,WAAW,CAAE,iBAAkB,CAAC;MAChE;MAEA,IAAK,CAAE,IAAI,CAAC0d,0BAA0B,CAAC,CAAC,EAAG;QAC1CrjB,CAAC,CAAE,yBAA0B,CAAC,CAAC4F,QAAQ,CAAE,WAAY,CAAC;QACtD5F,CAAC,CAAE,0BAA2B,CAAC,CAC7B2F,WAAW,CAAE,YAAa,CAAC,CAC3BwC,IAAI,CAAE,QAAQ,EAAE,KAAM,CAAC;MAC1B,CAAC,MAAM;QACNnI,CAAC,CAAE,yBAA0B,CAAC,CAAC2F,WAAW,CAAE,WAAY,CAAC;QAEzD3F,CAAC,CAAE,mBAAoB,CAAC,CAAC8C,IAAI,CAAE,YAAY;UAC1C,MAAM4gB,SAAS,GAAGxjB,GAAG,CAACwQ,SAAS,CAAE;YAChC/L,IAAI,EAAE,KAAK;YACXN,MAAM,EAAErE,CAAC,CAAE,IAAK,CAAC;YACjBgjB,gBAAgB,EAAE,IAAI;YACtBxS,KAAK,EAAE;UACR,CAAE,CAAC;UAEH,IAAKkT,SAAS,CAACjhB,MAAM,EAAG;YACvBihB,SAAS,CAAE,CAAC,CAAE,CAACjJ,IAAI,CAAC7V,GAAG,CAAE,aAAa,EAAE,KAAM,CAAC;UAChD;UAEA1E,GAAG,CAACkB,QAAQ,CAAE,MAAM,EAAEpB,CAAC,CAAE,IAAK,CAAE,CAAC;QAClC,CAAE,CAAC;MACJ;MAEA,IAAK,IAAI,CAACsjB,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAG;QACzCtjB,CAAC,CAAE,MAAO,CAAC,CAAC2F,WAAW,CAAE,WAAY,CAAC;QACtC3F,CAAC,CAAE,MAAO,CAAC,CAAC4F,QAAQ,CAAE,WAAY,CAAC;MACpC,CAAC,MAAM;QACN5F,CAAC,CAAE,MAAO,CAAC,CAAC2F,WAAW,CAAE,WAAY,CAAC;QACtC3F,CAAC,CAAE,MAAO,CAAC,CAAC4F,QAAQ,CAAE,WAAY,CAAC;MACpC;IACD;EACD,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAI+d,kBAAkB,GAAG,IAAIzjB,GAAG,CAAC+J,KAAK,CAAE;IACvCC,OAAO,EAAE;MACRyV,SAAS,EAAE;IACZ,CAAC;IAEDC,UAAU,EAAE,SAAAA,CAAW9U,KAAK,EAAG;MAC9B;MACA,IAAK,CAAEA,KAAK,CAACkG,GAAG,CAAE,QAAS,CAAC,EAAG;;MAE/B;MACA,IAAIjO,MAAM,GAAG+H,KAAK,CAACpJ,GAAG,CAAE,QAAS,CAAC;MAClC,IAAIkiB,QAAQ,GAAG9Y,KAAK,CAAClK,GAAG,CACtBsO,QAAQ,CAAE,cAAc,GAAGnM,MAAM,GAAG,IAAK,CAAC,CAC1CuD,KAAK,CAAC,CAAC;;MAET;MACA,IAAK,CAAEsd,QAAQ,CAACnhB,MAAM,EAAG;;MAEzB;MACA,IAAI0J,IAAI,GAAGyX,QAAQ,CAACrU,QAAQ,CAAE,YAAa,CAAC;MAC5C,IAAIsU,GAAG,GAAG1X,IAAI,CAACoD,QAAQ,CAAE,IAAK,CAAC;;MAE/B;MACA,IAAK,CAAEsU,GAAG,CAACphB,MAAM,EAAG;QACnB0J,IAAI,CAAC2X,SAAS,CAAE,mCAAoC,CAAC;QACrDD,GAAG,GAAG1X,IAAI,CAACoD,QAAQ,CAAE,IAAK,CAAC;MAC5B;;MAEA;MACA,IAAIlO,IAAI,GAAGyJ,KAAK,CAAC9K,CAAC,CAAE,YAAa,CAAC,CAACqB,IAAI,CAAC,CAAC;MACzC,IAAI0iB,GAAG,GAAG/jB,CAAC,CAAE,MAAM,GAAGqB,IAAI,GAAG,OAAQ,CAAC;MACtCwiB,GAAG,CAAC9gB,MAAM,CAAEghB,GAAI,CAAC;MACjBF,GAAG,CAAC3f,IAAI,CAAE,WAAW,EAAE2f,GAAG,CAACtU,QAAQ,CAAC,CAAC,CAAC9M,MAAO,CAAC;;MAE9C;MACAqI,KAAK,CAAC7D,MAAM,CAAC,CAAC;IACf;EACD,CAAE,CAAC;AACJ,CAAC,EAAIK,MAAO,CAAC;;;;;;;;;;;;;;;;ACnYkC;AAChC;AACf,QAAQ,6DAAa;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACdkC;AACnB;AACf,MAAM,sDAAO;AACb;AACA;AACA;AACA,QAAQ,sDAAO;AACf;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACVkC;AACS;AAC5B;AACf,YAAY,2DAAW;AACvB,SAAS,sDAAO;AAChB;;;;;;;;;;;;;;;ACLe;AACf;;AAEA;AACA;AACA,IAAI;AACJ;AACA,GAAG;AACH;;;;;;UCRA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA,eAAe,4BAA4B;WAC3C,eAAe;WACf,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA,8CAA8C;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;ACN2B;AACM;AACG;AACE;AACJ;AACG;AACI", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_browse-fields-modal.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-compatibility.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-conditions.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-field.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-fields.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-locations.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-settings.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/defineProperty.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/toPrimitive.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/toPropertyKey.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/typeof.js", "webpack://advanced-custom-fields-pro/webpack/bootstrap", "webpack://advanced-custom-fields-pro/webpack/runtime/compat get default export", "webpack://advanced-custom-fields-pro/webpack/runtime/define property getters", "webpack://advanced-custom-fields-pro/webpack/runtime/hasOwnProperty shorthand", "webpack://advanced-custom-fields-pro/webpack/runtime/make namespace object", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/acf-field-group.js"], "sourcesContent": ["/**\n * Extends acf.models.Modal to create the field browser.\n *\n * @package Advanced Custom Fields\n */\n\n( function ( $, undefined, acf ) {\n\tconst browseFieldsModal = {\n\t\tdata: {\n\t\t\topenedBy: null,\n\t\t\tcurrentFieldType: null,\n\t\t\tpopularFieldTypes: [\n\t\t\t\t'text',\n\t\t\t\t'textarea',\n\t\t\t\t'email',\n\t\t\t\t'url',\n\t\t\t\t'file',\n\t\t\t\t'gallery',\n\t\t\t\t'select',\n\t\t\t\t'true_false',\n\t\t\t\t'link',\n\t\t\t\t'post_object',\n\t\t\t\t'relationship',\n\t\t\t\t'repeater',\n\t\t\t\t'flexible_content',\n\t\t\t\t'clone',\n\t\t\t],\n\t\t},\n\n\t\tevents: {\n\t\t\t'click .acf-modal-close': 'onClickClose',\n\t\t\t'keydown .acf-browse-fields-modal': 'onPressEscapeClose',\n\t\t\t'click .acf-select-field': 'onClickSelectField',\n\t\t\t'click .acf-field-type': 'onClickFieldType',\n\t\t\t'changed:currentFieldType': 'onChangeFieldType',\n\t\t\t'input .acf-search-field-types': 'onSearchFieldTypes',\n\t\t\t'click .acf-browse-popular-fields': 'onClickBrowsePopular',\n\t\t},\n\n\t\tsetup: function ( props ) {\n\t\t\t$.extend( this.data, props );\n\t\t\tthis.$el = $( this.tmpl() );\n\t\t\tthis.render();\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tthis.open();\n\t\t\tthis.lockFocusToModal( true );\n\t\t\tthis.$el.find( '.acf-modal-title' ).focus();\n\t\t\tacf.doAction( 'show', this.$el );\n\t\t},\n\n\t\ttmpl: function () {\n\t\t\treturn $( '#tmpl-acf-browse-fields-modal' ).html();\n\t\t},\n\n\t\tgetFieldTypes: function ( category, search ) {\n\t\t\tlet fieldTypes;\n\t\t\tif ( ! acf.get( 'is_pro' ) ) {\n\t\t\t\t// Add in the pro fields.\n\t\t\t\tfieldTypes = Object.values( {\n\t\t\t\t\t...acf.get( 'fieldTypes' ),\n\t\t\t\t\t...acf.get( 'PROFieldTypes' ),\n\t\t\t\t} );\n\t\t\t} else {\n\t\t\t\tfieldTypes = Object.values( acf.get( 'fieldTypes' ) );\n\t\t\t}\n\n\t\t\tif ( category ) {\n\t\t\t\tif ( 'popular' === category ) {\n\t\t\t\t\treturn fieldTypes.filter( ( fieldType ) =>\n\t\t\t\t\t\tthis.get( 'popularFieldTypes' ).includes(\n\t\t\t\t\t\t\tfieldType.name\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\tif ( 'pro' === category ) {\n\t\t\t\t\treturn fieldTypes.filter( ( fieldType ) => fieldType.pro );\n\t\t\t\t}\n\n\t\t\t\tfieldTypes = fieldTypes.filter(\n\t\t\t\t\t( fieldType ) => fieldType.category === category\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif ( search ) {\n\t\t\t\tfieldTypes = fieldTypes.filter( ( fieldType ) => {\n\t\t\t\t\tconst label = fieldType.label.toLowerCase();\n\t\t\t\t\tconst labelParts = label.split( ' ' );\n\t\t\t\t\tlet match = false;\n\n\t\t\t\t\tif ( label.startsWith( search.toLowerCase() ) ) {\n\t\t\t\t\t\tmatch = true;\n\t\t\t\t\t} else if ( labelParts.length > 1 ) {\n\t\t\t\t\t\tlabelParts.forEach( ( part ) => {\n\t\t\t\t\t\t\tif ( part.startsWith( search.toLowerCase() ) ) {\n\t\t\t\t\t\t\t\tmatch = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} );\n\t\t\t\t\t}\n\n\t\t\t\t\treturn match;\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\treturn fieldTypes;\n\t\t},\n\n\t\trender: function () {\n\t\t\tacf.doAction( 'append', this.$el );\n\n\t\t\tconst $tabs = this.$el.find( '.acf-field-types-tab' );\n\t\t\tconst self = this;\n\n\t\t\t$tabs.each( function () {\n\t\t\t\tconst category = $( this ).data( 'category' );\n\t\t\t\tconst fieldTypes = self.getFieldTypes( category );\n\t\t\t\tfieldTypes.forEach( ( fieldType ) => {\n\t\t\t\t\t$( this ).append( self.getFieldTypeHTML( fieldType ) );\n\t\t\t\t} );\n\t\t\t} );\n\n\t\t\tthis.initializeFieldLabel();\n\t\t\tthis.initializeFieldType();\n\t\t\tthis.onChangeFieldType();\n\t\t},\n\n\t\tgetFieldTypeHTML: function ( fieldType ) {\n\t\t\tconst iconName = fieldType.name.replaceAll( '_', '-' );\n\n\t\t\treturn `\n\t\t\t<a href=\"#\" class=\"acf-field-type\" data-field-type=\"${ fieldType.name }\">\n\t\t\t\t${\n\t\t\t\t\tfieldType.pro && ! acf.get( 'is_pro' )\n\t\t\t\t\t\t? '<span class=\"field-type-requires-pro\"><i class=\"acf-icon acf-icon-lock\"></i>PRO</span>'\n\t\t\t\t\t\t: fieldType.pro\n\t\t\t\t\t\t? '<span class=\"field-type-requires-pro\">PRO</span>'\n\t\t\t\t\t\t: ''\n\t\t\t\t}\n\t\t\t\t<i class=\"field-type-icon field-type-icon-${ iconName }\"></i>\n\t\t\t\t<span class=\"field-type-label\">${ fieldType.label }</span>\n\t\t\t</a>\n\t\t\t`;\n\t\t},\n\n\t\tdecodeFieldTypeURL: function ( url ) {\n\t\t\tif ( typeof url != 'string' ) return url;\n\t\t\treturn url.replaceAll( '&#038;', '&' );\n\t\t},\n\n\t\trenderFieldTypeDesc: function ( fieldType ) {\n\t\t\tconst fieldTypeInfo =\n\t\t\t\tthis.getFieldTypes().filter(\n\t\t\t\t\t( fieldTypeFilter ) => fieldTypeFilter.name === fieldType\n\t\t\t\t)[ 0 ] || {};\n\n\t\t\tconst args = acf.parseArgs( fieldTypeInfo, {\n\t\t\t\tlabel: '',\n\t\t\t\tdescription: '',\n\t\t\t\tdoc_url: false,\n\t\t\t\ttutorial_url: false,\n\t\t\t\tpreview_image: false,\n\t\t\t\tpro: false,\n\t\t\t} );\n\n\t\t\tthis.$el.find( '.field-type-name' ).text( args.label );\n\t\t\tthis.$el.find( '.field-type-desc' ).text( args.description );\n\n\t\t\tif ( args.doc_url ) {\n\t\t\t\tthis.$el\n\t\t\t\t\t.find( '.field-type-doc' )\n\t\t\t\t\t.attr( 'href', this.decodeFieldTypeURL( args.doc_url ) )\n\t\t\t\t\t.show();\n\t\t\t} else {\n\t\t\t\tthis.$el.find( '.field-type-doc' ).hide();\n\t\t\t}\n\n\t\t\tif ( args.tutorial_url ) {\n\t\t\t\tthis.$el\n\t\t\t\t\t.find( '.field-type-tutorial' )\n\t\t\t\t\t.attr(\n\t\t\t\t\t\t'href',\n\t\t\t\t\t\tthis.decodeFieldTypeURL( args.tutorial_url )\n\t\t\t\t\t)\n\t\t\t\t\t.parent()\n\t\t\t\t\t.show();\n\t\t\t} else {\n\t\t\t\tthis.$el.find( '.field-type-tutorial' ).parent().hide();\n\t\t\t}\n\n\t\t\tif ( args.preview_image ) {\n\t\t\t\tthis.$el\n\t\t\t\t\t.find( '.field-type-image' )\n\t\t\t\t\t.attr( 'src', args.preview_image )\n\t\t\t\t\t.show();\n\t\t\t} else {\n\t\t\t\tthis.$el.find( '.field-type-image' ).hide();\n\t\t\t}\n\n\t\t\tconst isPro = acf.get( 'is_pro' );\n\t\t\tconst $upgateToProButton = this.$el.find( '.acf-btn-pro' );\n\t\t\tconst $upgradeToUnlockButton = this.$el.find(\n\t\t\t\t'.field-type-upgrade-to-unlock'\n\t\t\t);\n\n\t\t\tif ( args.pro && ! isPro ) {\n\t\t\t\t$upgateToProButton.show();\n\t\t\t\t$upgateToProButton.attr(\n\t\t\t\t\t'href',\n\t\t\t\t\t$upgateToProButton.data( 'urlBase' ) + fieldType\n\t\t\t\t);\n\n\t\t\t\t$upgradeToUnlockButton.show();\n\t\t\t\t$upgradeToUnlockButton.attr(\n\t\t\t\t\t'href',\n\t\t\t\t\t$upgradeToUnlockButton.data( 'urlBase' ) + fieldType\n\t\t\t\t);\n\t\t\t\tthis.$el\n\t\t\t\t\t.find( '.acf-insert-field-label' )\n\t\t\t\t\t.attr( 'disabled', true );\n\t\t\t\tthis.$el.find( '.acf-select-field' ).hide();\n\t\t\t} else {\n\t\t\t\t$upgateToProButton.hide();\n\t\t\t\t$upgradeToUnlockButton.hide();\n\t\t\t\tthis.$el\n\t\t\t\t\t.find( '.acf-insert-field-label' )\n\t\t\t\t\t.attr( 'disabled', false );\n\t\t\t\tthis.$el.find( '.acf-select-field' ).show();\n\t\t\t}\n\t\t},\n\n\t\tinitializeFieldType: function () {\n\t\t\tconst fieldObject = this.get( 'openedBy' );\n\t\t\tconst fieldType = fieldObject?.data?.type;\n\n\t\t\t// Select default field type\n\t\t\tif ( fieldType ) {\n\t\t\t\tthis.set( 'currentFieldType', fieldType );\n\t\t\t} else {\n\t\t\t\tthis.set( 'currentFieldType', 'text' );\n\t\t\t}\n\n\t\t\t// Select first tab with selected field type\n\t\t\t// If type selected is wthin Popular, select Popular Tab\n\t\t\t// Else select first tab the type belongs\n\t\t\tconst fieldTypes = this.getFieldTypes();\n\t\t\tconst isFieldTypePopular =\n\t\t\t\tthis.get( 'popularFieldTypes' ).includes( fieldType );\n\n\t\t\tlet category = '';\n\t\t\tif ( isFieldTypePopular ) {\n\t\t\t\tcategory = 'popular';\n\t\t\t} else {\n\t\t\t\tconst selectedFieldType = fieldTypes.find( ( x ) => {\n\t\t\t\t\treturn x.name === fieldType;\n\t\t\t\t} );\n\n\t\t\t\tcategory = selectedFieldType.category;\n\t\t\t}\n\n\t\t\tconst uppercaseCategory =\n\t\t\t\tcategory[ 0 ].toUpperCase() + category.slice( 1 );\n\t\t\tconst searchTabElement = `.acf-modal-content .acf-tab-wrap a:contains('${ uppercaseCategory }')`;\n\t\t\tsetTimeout( () => {\n\t\t\t\t$( searchTabElement ).click();\n\t\t\t}, 0 );\n\t\t},\n\n\t\tinitializeFieldLabel: function () {\n\t\t\tconst fieldObject = this.get( 'openedBy' );\n\t\t\tconst labelText = fieldObject.$fieldLabel().val();\n\t\t\tconst $fieldLabel = this.$el.find( '.acf-insert-field-label' );\n\t\t\tif ( labelText ) {\n\t\t\t\t$fieldLabel.val( labelText );\n\t\t\t} else {\n\t\t\t\t$fieldLabel.val( '' );\n\t\t\t}\n\t\t},\n\n\t\tupdateFieldObjectFieldLabel: function () {\n\t\t\tconst label = this.$el.find( '.acf-insert-field-label' ).val();\n\t\t\tconst fieldObject = this.get( 'openedBy' );\n\t\t\tfieldObject.$fieldLabel().val( label );\n\t\t\tfieldObject.$fieldLabel().trigger( 'blur' );\n\t\t},\n\n\t\tonChangeFieldType: function () {\n\t\t\tconst fieldType = this.get( 'currentFieldType' );\n\n\t\t\tthis.$el.find( '.selected' ).removeClass( 'selected' );\n\t\t\tthis.$el\n\t\t\t\t.find( '.acf-field-type[data-field-type=\"' + fieldType + '\"]' )\n\t\t\t\t.addClass( 'selected' );\n\n\t\t\tthis.renderFieldTypeDesc( fieldType );\n\t\t},\n\n\t\tonSearchFieldTypes: function ( e ) {\n\t\t\tconst $modal = this.$el.find( '.acf-browse-fields-modal' );\n\t\t\tconst inputVal = this.$el.find( '.acf-search-field-types' ).val();\n\t\t\tconst self = this;\n\t\t\tlet searchString,\n\t\t\t\tresultsHtml = '';\n\t\t\tlet matches = [];\n\n\t\t\tif ( 'string' === typeof inputVal ) {\n\t\t\t\tsearchString = inputVal.trim();\n\t\t\t\tmatches = this.getFieldTypes( false, searchString );\n\t\t\t}\n\n\t\t\tif ( searchString.length && matches.length ) {\n\t\t\t\t$modal.addClass( 'is-searching' );\n\t\t\t} else {\n\t\t\t\t$modal.removeClass( 'is-searching' );\n\t\t\t}\n\n\t\t\tif ( ! matches.length ) {\n\t\t\t\t$modal.addClass( 'no-results-found' );\n\t\t\t\tthis.$el\n\t\t\t\t\t.find( '.acf-invalid-search-term' )\n\t\t\t\t\t.text( searchString );\n\t\t\t\treturn;\n\t\t\t} else {\n\t\t\t\t$modal.removeClass( 'no-results-found' );\n\t\t\t}\n\n\t\t\tmatches.forEach( ( fieldType ) => {\n\t\t\t\tresultsHtml = resultsHtml + self.getFieldTypeHTML( fieldType );\n\t\t\t} );\n\n\t\t\t$( '.acf-field-type-search-results' ).html( resultsHtml );\n\n\t\t\tthis.set( 'currentFieldType', matches[ 0 ].name );\n\t\t\tthis.onChangeFieldType();\n\t\t},\n\n\t\tonClickBrowsePopular: function () {\n\t\t\tthis.$el\n\t\t\t\t.find( '.acf-search-field-types' )\n\t\t\t\t.val( '' )\n\t\t\t\t.trigger( 'input' );\n\t\t\tthis.$el.find( '.acf-tab-wrap a' ).first().trigger( 'click' );\n\t\t},\n\n\t\tonClickSelectField: function ( e ) {\n\t\t\tconst fieldObject = this.get( 'openedBy' );\n\n\t\t\tfieldObject\n\t\t\t\t.$fieldTypeSelect()\n\t\t\t\t.val( this.get( 'currentFieldType' ) );\n\t\t\tfieldObject.$fieldTypeSelect().trigger( 'change' );\n\n\t\t\tthis.updateFieldObjectFieldLabel();\n\n\t\t\tthis.close();\n\t\t},\n\n\t\tonClickFieldType: function ( e ) {\n\t\t\tconst $fieldType = $( e.currentTarget );\n\t\t\tthis.set( 'currentFieldType', $fieldType.data( 'field-type' ) );\n\t\t},\n\n\t\tonClickClose: function () {\n\t\t\tthis.close();\n\t\t},\n\n\t\tonPressEscapeClose: function ( e ) {\n\t\t\tif ( e.key === 'Escape' ) {\n\t\t\t\tthis.close();\n\t\t\t}\n\t\t},\n\n\t\tclose: function () {\n\t\t\tthis.lockFocusToModal( false );\n\t\t\tthis.returnFocusToOrigin();\n\t\t\tthis.remove();\n\t\t},\n\n\t\tfocus: function () {\n\t\t\tthis.$el.find( 'button' ).first().trigger( 'focus' );\n\t\t},\n\t};\n\n\tacf.models.browseFieldsModal = acf.models.Modal.extend( browseFieldsModal );\n\tacf.newBrowseFieldsModal = ( props ) =>\n\t\tnew acf.models.browseFieldsModal( props );\n} )( window.jQuery, undefined, window.acf );\n", "( function ( $, undefined ) {\n\tvar _acf = acf.getCompatibility( acf );\n\n\t/**\n\t *  fieldGroupCompatibility\n\t *\n\t *  Compatibility layer for extinct acf.field_group\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\t_acf.field_group = {\n\t\tsave_field: function ( $field, type ) {\n\t\t\ttype = type !== undefined ? type : 'settings';\n\t\t\tacf.getFieldObject( $field ).save( type );\n\t\t},\n\n\t\tdelete_field: function ( $field, animate ) {\n\t\t\tanimate = animate !== undefined ? animate : true;\n\t\t\tacf.getFieldObject( $field ).delete( {\n\t\t\t\tanimate: animate,\n\t\t\t} );\n\t\t},\n\n\t\tupdate_field_meta: function ( $field, name, value ) {\n\t\t\tacf.getFieldObject( $field ).prop( name, value );\n\t\t},\n\n\t\tdelete_field_meta: function ( $field, name ) {\n\t\t\tacf.getFieldObject( $field ).prop( name, null );\n\t\t},\n\t};\n\n\t/**\n\t *  fieldGroupCompatibility.field_object\n\t *\n\t *  Compatibility layer for extinct acf.field_group.field_object\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\t_acf.field_group.field_object = acf.model.extend( {\n\t\t// vars\n\t\ttype: '',\n\t\to: {},\n\t\t$field: null,\n\t\t$settings: null,\n\n\t\ttag: function ( tag ) {\n\t\t\t// vars\n\t\t\tvar type = this.type;\n\n\t\t\t// explode, add 'field' and implode\n\t\t\t// - open \t\t\t=> open_field\n\t\t\t// - change_type\t=> change_field_type\n\t\t\tvar tags = tag.split( '_' );\n\t\t\ttags.splice( 1, 0, 'field' );\n\t\t\ttag = tags.join( '_' );\n\n\t\t\t// add type\n\t\t\tif ( type ) {\n\t\t\t\ttag += '/type=' + type;\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn tag;\n\t\t},\n\n\t\tselector: function () {\n\t\t\t// vars\n\t\t\tvar selector = '.acf-field-object';\n\t\t\tvar type = this.type;\n\n\t\t\t// add type\n\t\t\tif ( type ) {\n\t\t\t\tselector += '-' + type;\n\t\t\t\tselector = acf.str_replace( '_', '-', selector );\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn selector;\n\t\t},\n\n\t\t_add_action: function ( name, callback ) {\n\t\t\t// vars\n\t\t\tvar model = this;\n\n\t\t\t// add action\n\t\t\tacf.add_action( this.tag( name ), function ( $field ) {\n\t\t\t\t// focus\n\t\t\t\tmodel.set( '$field', $field );\n\n\t\t\t\t// callback\n\t\t\t\tmodel[ callback ].apply( model, arguments );\n\t\t\t} );\n\t\t},\n\n\t\t_add_filter: function ( name, callback ) {\n\t\t\t// vars\n\t\t\tvar model = this;\n\n\t\t\t// add action\n\t\t\tacf.add_filter( this.tag( name ), function ( $field ) {\n\t\t\t\t// focus\n\t\t\t\tmodel.set( '$field', $field );\n\n\t\t\t\t// callback\n\t\t\t\tmodel[ callback ].apply( model, arguments );\n\t\t\t} );\n\t\t},\n\n\t\t_add_event: function ( name, callback ) {\n\t\t\t// vars\n\t\t\tvar model = this;\n\t\t\tvar event = name.substr( 0, name.indexOf( ' ' ) );\n\t\t\tvar selector = name.substr( name.indexOf( ' ' ) + 1 );\n\t\t\tvar context = this.selector();\n\n\t\t\t// add event\n\t\t\t$( document ).on( event, context + ' ' + selector, function ( e ) {\n\t\t\t\t// append $el to event object\n\t\t\t\te.$el = $( this );\n\t\t\t\te.$field = e.$el.closest( '.acf-field-object' );\n\n\t\t\t\t// focus\n\t\t\t\tmodel.set( '$field', e.$field );\n\n\t\t\t\t// callback\n\t\t\t\tmodel[ callback ].apply( model, [ e ] );\n\t\t\t} );\n\t\t},\n\n\t\t_set_$field: function () {\n\t\t\t// vars\n\t\t\tthis.o = this.$field.data();\n\n\t\t\t// els\n\t\t\tthis.$settings = this.$field.find( '> .settings > table > tbody' );\n\n\t\t\t// focus\n\t\t\tthis.focus();\n\t\t},\n\n\t\tfocus: function () {\n\t\t\t// do nothing\n\t\t},\n\n\t\tsetting: function ( name ) {\n\t\t\treturn this.$settings.find( '> .acf-field-setting-' + name );\n\t\t},\n\t} );\n\n\t/*\n\t *  field\n\t *\n\t *  This model fires actions and filters for registered fields\n\t *\n\t *  @type\tfunction\n\t *  @date\t21/02/2014\n\t *  @since\t3.5.1\n\t *\n\t *  @param\tn/a\n\t *  @return\tn/a\n\t */\n\n\tvar actionManager = new acf.Model( {\n\t\tactions: {\n\t\t\topen_field_object: 'onOpenFieldObject',\n\t\t\tclose_field_object: 'onCloseFieldObject',\n\t\t\tadd_field_object: 'onAddFieldObject',\n\t\t\tduplicate_field_object: 'onDuplicateFieldObject',\n\t\t\tdelete_field_object: 'onDeleteFieldObject',\n\t\t\tchange_field_object_type: 'onChangeFieldObjectType',\n\t\t\tchange_field_object_label: 'onChangeFieldObjectLabel',\n\t\t\tchange_field_object_name: 'onChangeFieldObjectName',\n\t\t\tchange_field_object_parent: 'onChangeFieldObjectParent',\n\t\t\tsortstop_field_object: 'onChangeFieldObjectParent',\n\t\t},\n\n\t\tonOpenFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'open_field', field.$el );\n\t\t\tacf.doAction( 'open_field/type=' + field.get( 'type' ), field.$el );\n\n\t\t\tacf.doAction( 'render_field_settings', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'render_field_settings/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonCloseFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'close_field', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'close_field/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonAddFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'add_field', field.$el );\n\t\t\tacf.doAction( 'add_field/type=' + field.get( 'type' ), field.$el );\n\t\t},\n\n\t\tonDuplicateFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'duplicate_field', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'duplicate_field/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonDeleteFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'delete_field', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'delete_field/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonChangeFieldObjectType: function ( field ) {\n\t\t\tacf.doAction( 'change_field_type', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'change_field_type/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\n\t\t\tacf.doAction( 'render_field_settings', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'render_field_settings/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonChangeFieldObjectLabel: function ( field ) {\n\t\t\tacf.doAction( 'change_field_label', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'change_field_label/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonChangeFieldObjectName: function ( field ) {\n\t\t\tacf.doAction( 'change_field_name', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'change_field_name/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonChangeFieldObjectParent: function ( field ) {\n\t\t\tacf.doAction( 'update_field_parent', field.$el );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  ConditionalLogicFieldSetting\n\t *\n\t *  description\n\t *\n\t *  @date\t3/2/18\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar ConditionalLogicFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: '',\n\t\tname: 'conditional_logic',\n\t\tevents: {\n\t\t\t'change .conditions-toggle': 'onChangeToggle',\n\t\t\t'click .add-conditional-group': 'onClickAddGroup',\n\t\t\t'focus .condition-rule-field': 'onFocusField',\n\t\t\t'change .condition-rule-field': 'onChangeField',\n\t\t\t'change .condition-rule-operator': 'onChangeOperator',\n\t\t\t'click .add-conditional-rule': 'onClickAdd',\n\t\t\t'click .remove-conditional-rule': 'onClickRemove',\n\t\t},\n\n\t\t$rule: false,\n\n\t\tscope: function ( $rule ) {\n\t\t\tthis.$rule = $rule;\n\t\t\treturn this;\n\t\t},\n\n\t\truleData: function ( name, value ) {\n\t\t\treturn this.$rule.data.apply( this.$rule, arguments );\n\t\t},\n\n\t\t$input: function ( name ) {\n\t\t\treturn this.$rule.find( '.condition-rule-' + name );\n\t\t},\n\n\t\t$td: function ( name ) {\n\t\t\treturn this.$rule.find( 'td.' + name );\n\t\t},\n\n\t\t$toggle: function () {\n\t\t\treturn this.$( '.conditions-toggle' );\n\t\t},\n\n\t\t$control: function () {\n\t\t\treturn this.$( '.rule-groups' );\n\t\t},\n\n\t\t$groups: function () {\n\t\t\treturn this.$( '.rule-group' );\n\t\t},\n\n\t\t$rules: function () {\n\t\t\treturn this.$( '.rule' );\n\t\t},\n\n\t\t$tabLabel: function () {\n\t\t\treturn this.fieldObject.$el.find('.conditional-logic-badge');\n\t\t},\n\n\t\topen: function () {\n\t\t\tvar $div = this.$control();\n\t\t\t$div.show();\n\t\t\tacf.enable( $div );\n\t\t},\n\n\t\tclose: function () {\n\t\t\tvar $div = this.$control();\n\t\t\t$div.hide();\n\t\t\tacf.disable( $div );\n\t\t},\n\n\t\trender: function () {\n\t\t\t// show\n\t\t\tif ( this.$toggle().prop( 'checked' ) ) {\n\t\t\t\tthis.$tabLabel().addClass('is-enabled');\n\t\t\t\tthis.renderRules();\n\t\t\t\tthis.open();\n\n\t\t\t\t// hide\n\t\t\t} else {\n\t\t\t\tthis.$tabLabel().removeClass('is-enabled');\n\t\t\t\tthis.close();\n\t\t\t}\n\t\t},\n\n\t\trenderRules: function () {\n\t\t\t// vars\n\t\t\tvar self = this;\n\n\t\t\t// loop\n\t\t\tthis.$rules().each( function () {\n\t\t\t\tself.renderRule( $( this ) );\n\t\t\t} );\n\t\t},\n\n\t\trenderRule: function ( $rule ) {\n\t\t\tthis.scope( $rule );\n\t\t\tthis.renderField();\n\t\t\tthis.renderOperator();\n\t\t\tthis.renderValue();\n\t\t},\n\n\t\trenderField: function () {\n\t\t\t// vars\n\t\t\tvar choices = [];\n\t\t\tvar validFieldTypes = [];\n\t\t\tvar cid = this.fieldObject.cid;\n\t\t\tvar $select = this.$input( 'field' );\n\n\t\t\t// loop\n\t\t\tacf.getFieldObjects().map( function ( fieldObject ) {\n\t\t\t\t// vars\n\t\t\t\tvar choice = {\n\t\t\t\t\tid: fieldObject.getKey(),\n\t\t\t\t\ttext: fieldObject.getLabel(),\n\t\t\t\t};\n\n\t\t\t\t// bail early if is self\n\t\t\t\tif ( fieldObject.cid === cid ) {\n\t\t\t\t\tchoice.text += ' ' + acf.__( '(this field)' );\n\t\t\t\t\tchoice.disabled = true;\n\t\t\t\t}\n\n\t\t\t\t// get selected field conditions\n\t\t\t\tvar conditionTypes = acf.getConditionTypes( {\n\t\t\t\t\tfieldType: fieldObject.getType(),\n\t\t\t\t} );\n\n\t\t\t\t// bail early if no types\n\t\t\t\tif ( ! conditionTypes.length ) {\n\t\t\t\t\tchoice.disabled = true;\n\t\t\t\t}\n\n\t\t\t\t// calulate indents\n\t\t\t\tvar indents = fieldObject.getParents().length;\n\t\t\t\tchoice.text = '- '.repeat( indents ) + choice.text;\n\n\t\t\t\t// append\n\t\t\t\tchoices.push( choice );\n\t\t\t} );\n\n\t\t\t// allow for scenario where only one field exists\n\t\t\tif ( ! choices.length ) {\n\t\t\t\tchoices.push( {\n\t\t\t\t\tid: '',\n\t\t\t\t\ttext: acf.__( 'No toggle fields available' ),\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\t// render\n\t\t\tacf.renderSelect( $select, choices );\n\n\t\t\t// set\n\t\t\tthis.ruleData( 'field', $select.val() );\n\t\t},\n\n\t\trenderOperator: function () {\n\t\t\t// bail early if no field selected\n\t\t\tif ( ! this.ruleData( 'field' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// vars\n\t\t\tvar $select = this.$input( 'operator' );\n\t\t\tvar val = $select.val();\n\t\t\tvar choices = [];\n\n\t\t\t// set saved value on first render\n\t\t\t// - this allows the 2nd render to correctly select an option\n\t\t\tif ( $select.val() === null ) {\n\t\t\t\tacf.renderSelect( $select, [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: this.ruleData( 'operator' ),\n\t\t\t\t\t\ttext: '',\n\t\t\t\t\t},\n\t\t\t\t] );\n\t\t\t}\n\n\t\t\t// get selected field\n\t\t\tvar $field = acf.findFieldObject( this.ruleData( 'field' ) );\n\t\t\tvar field = acf.getFieldObject( $field );\n\n\t\t\t// get selected field conditions\n\t\t\tvar conditionTypes = acf.getConditionTypes( {\n\t\t\t\tfieldType: field.getType(),\n\t\t\t} );\n\n\t\t\t// html\n\t\t\tconditionTypes.map( function ( model ) {\n\t\t\t\tchoices.push( {\n\t\t\t\t\tid: model.prototype.operator,\n\t\t\t\t\ttext: model.prototype.label,\n\t\t\t\t} );\n\t\t\t} );\n\n\t\t\t// render\n\t\t\tacf.renderSelect( $select, choices );\n\n\t\t\t// set\n\t\t\tthis.ruleData( 'operator', $select.val() );\n\t\t},\n\n\t\trenderValue: function () {\n\t\t\t// bail early if no field selected\n\t\t\tif ( ! this.ruleData( 'field' ) || ! this.ruleData( 'operator' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// vars\n\t\t\tvar $select = this.$input( 'value' );\n\t\t\tvar $td = this.$td( 'value' );\n\t\t\tvar val = $select.val();\n\n\t\t\t// get selected field\n\t\t\tvar $field = acf.findFieldObject( this.ruleData( 'field' ) );\n\t\t\tvar field = acf.getFieldObject( $field );\n\n\t\t\t// get selected field conditions\n\t\t\tvar conditionTypes = acf.getConditionTypes( {\n\t\t\t\tfieldType: field.getType(),\n\t\t\t\toperator: this.ruleData( 'operator' ),\n\t\t\t} );\n\n\t\t\t// html\n\t\t\tvar conditionType = conditionTypes[ 0 ].prototype;\n\t\t\tvar choices = conditionType.choices( field );\n\n\t\t\t// create html: array\n\t\t\tif ( choices instanceof Array ) {\n\t\t\t\tvar $newSelect = $( '<select></select>' );\n\t\t\t\tacf.renderSelect( $newSelect, choices );\n\n\t\t\t\t// create html: string (<input />)\n\t\t\t} else {\n\t\t\t\tvar $newSelect = $( choices );\n\t\t\t}\n\n\t\t\t// append\n\t\t\t$select.detach();\n\t\t\t$td.html( $newSelect );\n\n\t\t\t// copy attrs\n\t\t\t// timeout needed to avoid browser bug where \"disabled\" attribute is not applied\n\t\t\tsetTimeout( function () {\n\t\t\t\t[ 'class', 'name', 'id' ].map( function ( attr ) {\n\t\t\t\t\t$newSelect.attr( attr, $select.attr( attr ) );\n\t\t\t\t} );\n\t\t\t}, 0 );\n\n\t\t\t// select existing value (if not a disabled input)\n\t\t\tif ( ! $newSelect.prop( 'disabled' ) ) {\n\t\t\t\tacf.val( $newSelect, val, true );\n\t\t\t}\n\n\t\t\t// set\n\t\t\tthis.ruleData( 'value', $newSelect.val() );\n\t\t},\n\n\t\tonChangeToggle: function () {\n\t\t\tthis.render();\n\t\t},\n\n\t\tonClickAddGroup: function ( e, $el ) {\n\t\t\tthis.addGroup();\n\t\t},\n\n\t\taddGroup: function () {\n\t\t\t// vars\n\t\t\tvar $group = this.$( '.rule-group:last' );\n\n\t\t\t// duplicate\n\t\t\tvar $group2 = acf.duplicate( $group );\n\n\t\t\t// update h4\n\t\t\t$group2.find( 'h4' ).text( acf.__( 'or' ) );\n\n\t\t\t// remove all tr's except the first one\n\t\t\t$group2.find( 'tr' ).not( ':first' ).remove();\n\n\t\t\t// save field\n\t\t\tthis.fieldObject.save();\n\t\t},\n\n\t\tonFocusField: function ( e, $el ) {\n\t\t\tthis.renderField();\n\t\t},\n\n\t\tonChangeField: function ( e, $el ) {\n\t\t\t// scope\n\t\t\tthis.scope( $el.closest( '.rule' ) );\n\n\t\t\t// set data\n\t\t\tthis.ruleData( 'field', $el.val() );\n\n\t\t\t// render\n\t\t\tthis.renderOperator();\n\t\t\tthis.renderValue();\n\t\t},\n\n\t\tonChangeOperator: function ( e, $el ) {\n\t\t\t// scope\n\t\t\tthis.scope( $el.closest( '.rule' ) );\n\n\t\t\t// set data\n\t\t\tthis.ruleData( 'operator', $el.val() );\n\n\t\t\t// render\n\t\t\tthis.renderValue();\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\t// duplciate\n\t\t\tvar $rule = acf.duplicate( $el.closest( '.rule' ) );\n\n\t\t\t// render\n\t\t\tthis.renderRule( $rule );\n\t\t},\n\n\t\tonClickRemove: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar $rule = $el.closest( '.rule' );\n\n\t\t\t// save field\n\t\t\tthis.fieldObject.save();\n\n\t\t\t// remove group\n\t\t\tif ( $rule.siblings( '.rule' ).length == 0 ) {\n\t\t\t\t$rule.closest( '.rule-group' ).remove();\n\t\t\t}\n\n\t\t\t// remove\n\t\t\t$rule.remove();\n\t\t},\n\t} );\n\n\tacf.registerFieldSetting( ConditionalLogicFieldSetting );\n\n\t/**\n\t *  conditionalLogicHelper\n\t *\n\t *  description\n\t *\n\t *  @date\t20/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar conditionalLogicHelper = new acf.Model( {\n\t\tactions: {\n\t\t\tduplicate_field_objects: 'onDuplicateFieldObjects',\n\t\t},\n\n\t\tonDuplicateFieldObjects: function ( children, newField, prevField ) {\n\t\t\t// vars\n\t\t\tvar data = {};\n\t\t\tvar $selects = $();\n\n\t\t\t// reference change in key\n\t\t\tchildren.map( function ( child ) {\n\t\t\t\t// store reference of changed key\n\t\t\t\tdata[ child.get( 'prevKey' ) ] = child.get( 'key' );\n\n\t\t\t\t// append condition select\n\t\t\t\t$selects = $selects.add( child.$( '.condition-rule-field' ) );\n\t\t\t} );\n\n\t\t\t// loop\n\t\t\t$selects.each( function () {\n\t\t\t\t// vars\n\t\t\t\tvar $select = $( this );\n\t\t\t\tvar val = $select.val();\n\n\t\t\t\t// bail early if val is not a ref key\n\t\t\t\tif ( ! val || ! data[ val ] ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// modify selected option\n\t\t\t\t$select.find( 'option:selected' ).attr( 'value', data[ val ] );\n\n\t\t\t\t// set new val\n\t\t\t\t$select.val( data[ val ] );\n\t\t\t} );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\tacf.FieldObject = acf.Model.extend( {\n\t\t// class used to avoid nested event triggers\n\t\teventScope: '.acf-field-object',\n\n\t\t// variable for field type select2\n\t\tfieldTypeSelect2: false,\n\n\t\t// events\n\t\tevents: {\n\t\t\t'click .copyable': 'onClickCopy',\n\t\t\t'click .handle': 'onClickEdit',\n\t\t\t'click .close-field': 'onClickEdit',\n\t\t\t'click a[data-key=\"acf_field_settings_tabs\"]':\n\t\t\t\t'onChangeSettingsTab',\n\t\t\t'click .delete-field': 'onClickDelete',\n\t\t\t'click .duplicate-field': 'duplicate',\n\t\t\t'click .move-field': 'move',\n\t\t\t'click .browse-fields': 'browseFields',\n\n\t\t\t'focus .edit-field': 'onFocusEdit',\n\t\t\t'blur .edit-field, .row-options a': 'onBlurEdit',\n\n\t\t\t'change .field-type': 'onChangeType',\n\t\t\t'change .field-required': 'onChangeRequired',\n\t\t\t'blur .field-label': 'onChangeLabel',\n\t\t\t'blur .field-name': 'onChangeName',\n\n\t\t\tchange: 'onChange',\n\t\t\tchanged: 'onChanged',\n\t\t},\n\n\t\t// data\n\t\tdata: {\n\t\t\t// Similar to ID, but used for HTML puposes.\n\t\t\t// It is possbile for a new field to have an ID of 0, but an id of 'field_123' */\n\t\t\tid: 0,\n\n\t\t\t// The field key ('field_123')\n\t\t\tkey: '',\n\n\t\t\t// The field type (text, image, etc)\n\t\t\ttype: '',\n\n\t\t\t// The $post->ID of this field\n\t\t\t//ID: 0,\n\n\t\t\t// The field's parent\n\t\t\t//parent: 0,\n\n\t\t\t// The menu order\n\t\t\t//menu_order: 0\n\t\t},\n\n\t\tsetup: function ( $field ) {\n\t\t\t// set $el\n\t\t\tthis.$el = $field;\n\n\t\t\t// inherit $field data (id, key, type)\n\t\t\tthis.inherit( $field );\n\n\t\t\t// load additional props\n\t\t\t// - this won't trigger 'changed'\n\t\t\tthis.prop( 'ID' );\n\t\t\tthis.prop( 'parent' );\n\t\t\tthis.prop( 'menu_order' );\n\t\t},\n\n\t\t$input: function ( name ) {\n\t\t\treturn $( '#' + this.getInputId() + '-' + name );\n\t\t},\n\n\t\t$meta: function () {\n\t\t\treturn this.$( '.meta:first' );\n\t\t},\n\n\t\t$handle: function () {\n\t\t\treturn this.$( '.handle:first' );\n\t\t},\n\n\t\t$settings: function () {\n\t\t\treturn this.$( '.settings:first' );\n\t\t},\n\n\t\t$setting: function ( name ) {\n\t\t\treturn this.$(\n\t\t\t\t'.acf-field-settings:first .acf-field-setting-' + name\n\t\t\t);\n\t\t},\n\n\t\t$fieldTypeSelect: function () {\n\t\t\treturn this.$( '.field-type' );\n\t\t},\n\n\t\t$fieldLabel: function () {\n\t\t\treturn this.$( '.field-label' );\n\t\t},\n\n\t\tgetParent: function () {\n\t\t\treturn acf.getFieldObjects( { child: this.$el, limit: 1 } ).pop();\n\t\t},\n\n\t\tgetParents: function () {\n\t\t\treturn acf.getFieldObjects( { child: this.$el } );\n\t\t},\n\n\t\tgetFields: function () {\n\t\t\treturn acf.getFieldObjects( { parent: this.$el } );\n\t\t},\n\n\t\tgetInputName: function () {\n\t\t\treturn 'acf_fields[' + this.get( 'id' ) + ']';\n\t\t},\n\n\t\tgetInputId: function () {\n\t\t\treturn 'acf_fields-' + this.get( 'id' );\n\t\t},\n\n\t\tnewInput: function ( name, value ) {\n\t\t\t// vars\n\t\t\tvar inputId = this.getInputId();\n\t\t\tvar inputName = this.getInputName();\n\n\t\t\t// append name\n\t\t\tif ( name ) {\n\t\t\t\tinputId += '-' + name;\n\t\t\t\tinputName += '[' + name + ']';\n\t\t\t}\n\n\t\t\t// create input (avoid HTML + JSON value issues)\n\t\t\tvar $input = $( '<input />' ).attr( {\n\t\t\t\tid: inputId,\n\t\t\t\tname: inputName,\n\t\t\t\tvalue: value,\n\t\t\t} );\n\t\t\tthis.$( '> .meta' ).append( $input );\n\n\t\t\t// return\n\t\t\treturn $input;\n\t\t},\n\n\t\tgetProp: function ( name ) {\n\t\t\t// check data\n\t\t\tif ( this.has( name ) ) {\n\t\t\t\treturn this.get( name );\n\t\t\t}\n\n\t\t\t// get input value\n\t\t\tvar $input = this.$input( name );\n\t\t\tvar value = $input.length ? $input.val() : null;\n\n\t\t\t// set data silently (cache)\n\t\t\tthis.set( name, value, true );\n\n\t\t\t// return\n\t\t\treturn value;\n\t\t},\n\n\t\tsetProp: function ( name, value ) {\n\t\t\t// get input\n\t\t\tvar $input = this.$input( name );\n\t\t\tvar prevVal = $input.val();\n\n\t\t\t// create if new\n\t\t\tif ( ! $input.length ) {\n\t\t\t\t$input = this.newInput( name, value );\n\t\t\t}\n\n\t\t\t// remove\n\t\t\tif ( value === null ) {\n\t\t\t\t$input.remove();\n\n\t\t\t\t// update\n\t\t\t} else {\n\t\t\t\t$input.val( value );\n\t\t\t}\n\n\t\t\t//console.log('setProp', name, value, this);\n\n\t\t\t// set data silently (cache)\n\t\t\tif ( ! this.has( name ) ) {\n\t\t\t\t//console.log('setting silently');\n\t\t\t\tthis.set( name, value, true );\n\n\t\t\t\t// set data allowing 'change' event to fire\n\t\t\t} else {\n\t\t\t\t//console.log('setting loudly!');\n\t\t\t\tthis.set( name, value );\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn this;\n\t\t},\n\n\t\tprop: function ( name, value ) {\n\t\t\tif ( value !== undefined ) {\n\t\t\t\treturn this.setProp( name, value );\n\t\t\t} else {\n\t\t\t\treturn this.getProp( name );\n\t\t\t}\n\t\t},\n\n\t\tprops: function ( props ) {\n\t\t\tObject.keys( props ).map( function ( key ) {\n\t\t\t\tthis.setProp( key, props[ key ] );\n\t\t\t}, this );\n\t\t},\n\n\t\tgetLabel: function () {\n\t\t\t// get label with empty default\n\t\t\tvar label = this.prop( 'label' );\n\t\t\tif ( label === '' ) {\n\t\t\t\tlabel = acf.__( '(no label)' );\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn label;\n\t\t},\n\n\t\tgetName: function () {\n\t\t\treturn this.prop( 'name' );\n\t\t},\n\n\t\tgetType: function () {\n\t\t\treturn this.prop( 'type' );\n\t\t},\n\n\t\tgetTypeLabel: function () {\n\t\t\tvar type = this.prop( 'type' );\n\t\t\tvar types = acf.get( 'fieldTypes' );\n\t\t\treturn types[ type ] ? types[ type ].label : type;\n\t\t},\n\n\t\tgetKey: function () {\n\t\t\treturn this.prop( 'key' );\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tthis.checkCopyable();\n\t\t},\n\n\t\tmakeCopyable: function ( text ) {\n\t\t\tif ( ! navigator.clipboard )\n\t\t\t\treturn (\n\t\t\t\t\t'<span class=\"copyable copy-unsupported\">' +\n\t\t\t\t\ttext +\n\t\t\t\t\t'</span>'\n\t\t\t\t);\n\t\t\treturn '<span class=\"copyable\">' + text + '</span>';\n\t\t},\n\n\t\tcheckCopyable: function () {\n\t\t\tif ( ! navigator.clipboard ) {\n\t\t\t\tthis.$el.find( '.copyable' ).addClass( 'copy-unsupported' );\n\t\t\t}\n\t\t},\n\n\t\tinitializeFieldTypeSelect2: function () {\n\t\t\tif ( this.fieldTypeSelect2 ) return;\n\n\t\t\t// Support disabling via filter.\n\t\t\tif ( this.$fieldTypeSelect().hasClass( 'disable-select2' ) ) return;\n\n\t\t\t// Check for a full modern version of select2, bail loading if not found with a console warning.\n\t\t\ttry {\n\t\t\t\t$.fn.select2.amd.require( 'select2/compat/dropdownCss' );\n\t\t\t} catch ( err ) {\n\t\t\t\tconsole.warn(\n\t\t\t\t\t'ACF was not able to load the full version of select2 due to a conflicting version provided by another plugin or theme taking precedence. Select2 fields may not work as expected.'\n\t\t\t\t);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.fieldTypeSelect2 = acf.newSelect2( this.$fieldTypeSelect(), {\n\t\t\t\tfield: false,\n\t\t\t\tajax: false,\n\t\t\t\tmultiple: false,\n\t\t\t\tallowNull: false,\n\t\t\t\tsuppressFilters: true,\n\t\t\t\tdropdownCssClass: 'field-type-select-results',\n\t\t\t\ttemplateResult: function ( selection ) {\n\t\t\t\t\tif (\n\t\t\t\t\t\tselection.loading ||\n\t\t\t\t\t\t( selection.element &&\n\t\t\t\t\t\t\tselection.element.nodeName === 'OPTGROUP' )\n\t\t\t\t\t) {\n\t\t\t\t\t\tvar $selection = $(\n\t\t\t\t\t\t\t'<span class=\"acf-selection\"></span>'\n\t\t\t\t\t\t);\n\t\t\t\t\t\t$selection.html( acf.escHtml( selection.text ) );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvar $selection = $(\n\t\t\t\t\t\t\t'<i class=\"field-type-icon field-type-icon-' +\n\t\t\t\t\t\t\t\tselection.id.replaceAll( '_', '-' ) +\n\t\t\t\t\t\t\t\t'\"></i><span class=\"acf-selection has-icon\">' +\n\t\t\t\t\t\t\t\tacf.escHtml( selection.text ) +\n\t\t\t\t\t\t\t\t'</span>'\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t$selection.data( 'element', selection.element );\n\t\t\t\t\treturn $selection;\n\t\t\t\t},\n\t\t\t\ttemplateSelection: function ( selection ) {\n\t\t\t\t\tvar $selection = $(\n\t\t\t\t\t\t'<i class=\"field-type-icon field-type-icon-' +\n\t\t\t\t\t\t\tselection.id.replaceAll( '_', '-' ) +\n\t\t\t\t\t\t\t'\"></i><span class=\"acf-selection has-icon\">' +\n\t\t\t\t\t\t\tacf.escHtml( selection.text ) +\n\t\t\t\t\t\t\t'</span>'\n\t\t\t\t\t);\n\t\t\t\t\t$selection.data( 'element', selection.element );\n\t\t\t\t\treturn $selection;\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\tthis.fieldTypeSelect2.on( 'select2:open', function () {\n\t\t\t\t$(\n\t\t\t\t\t'.field-type-select-results input.select2-search__field'\n\t\t\t\t).attr( 'placeholder', acf.__( 'Type to search...' ) );\n\t\t\t} );\n\n\t\t\tthis.fieldTypeSelect2.on( 'change', function ( e ) {\n\t\t\t\t$( e.target )\n\t\t\t\t\t.parents( 'ul:first' )\n\t\t\t\t\t.find( 'button.browse-fields' )\n\t\t\t\t\t.prop( 'disabled', true );\n\t\t\t} );\n\n\t\t\t// When typing happens on the li element above the select2.\n\t\t\tthis.fieldTypeSelect2.$el\n\t\t\t\t.parent()\n\t\t\t\t.on(\n\t\t\t\t\t'keydown',\n\t\t\t\t\t'.select2-selection.select2-selection--single',\n\t\t\t\t\tthis.onKeyDownSelect\n\t\t\t\t);\n\t\t},\n\t\taddProFields: function () {\n\t\t\t// Make sure we're only running this on free version.\n\t\t\tif ( acf.get( 'is_pro' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Make sure we haven't appended these fields before.\n\t\t\tvar $fieldTypeSelect = this.$fieldTypeSelect();\n\t\t\tif ( $fieldTypeSelect.hasClass( 'acf-free-field-type' ) ) return;\n\n\t\t\t// Loop over each pro field type and append it to the select.\n\t\t\tconst PROFieldTypes = acf.get( 'PROFieldTypes' );\n\t\t\tif ( typeof PROFieldTypes !== 'object' ) return;\n\n\t\t\tconst $layoutGroup = $fieldTypeSelect\n\t\t\t\t.find( 'optgroup option[value=\"group\"]' )\n\t\t\t\t.parent();\n\n\t\t\tconst $contentGroup = $fieldTypeSelect\n\t\t\t\t.find( 'optgroup option[value=\"image\"]' )\n\t\t\t\t.parent();\n\n\t\t\tfor ( const [ name, field ] of Object.entries( PROFieldTypes ) ) {\n\t\t\t\tconst $useGroup =\n\t\t\t\t\tfield.category === 'content' ? $contentGroup : $layoutGroup;\n\t\t\t\t$useGroup.append(\n\t\t\t\t\t'<option value=\"null\" disabled=\"disabled\">' +\n\t\t\t\t\t\tfield.label +\n\t\t\t\t\t\t' (' +\n\t\t\t\t\t\tacf.__( 'PRO Only' ) +\n\t\t\t\t\t\t')</option>'\n\t\t\t\t);\n\t\t\t}\n\n\t\t\t$fieldTypeSelect.addClass( 'acf-free-field-type' );\n\t\t},\n\n\t\trender: function () {\n\t\t\t// vars\n\t\t\tvar $handle = this.$( '.handle:first' );\n\t\t\tvar menu_order = this.prop( 'menu_order' );\n\t\t\tvar label = this.getLabel();\n\t\t\tvar name = this.prop( 'name' );\n\t\t\tvar type = this.getTypeLabel();\n\t\t\tvar key = this.prop( 'key' );\n\t\t\tvar required = this.$input( 'required' ).prop( 'checked' );\n\n\t\t\t// update menu order\n\t\t\t$handle.find( '.acf-icon' ).html( parseInt( menu_order ) + 1 );\n\n\t\t\t// update required\n\t\t\tif ( required ) {\n\t\t\t\tlabel += ' <span class=\"acf-required\">*</span>';\n\t\t\t}\n\n\t\t\t// update label\n\t\t\t$handle.find( '.li-field-label strong a' ).html( label );\n\n\t\t\t// update name\n\t\t\t$handle.find( '.li-field-name' ).html( this.makeCopyable( name ) );\n\n\t\t\t// update type\n\t\t\tconst iconName = acf.strSlugify( this.getType() );\n\t\t\t$handle.find( '.field-type-label' ).text( ' ' + type );\n\t\t\t$handle\n\t\t\t\t.find( '.field-type-icon' )\n\t\t\t\t.removeClass()\n\t\t\t\t.addClass( 'field-type-icon field-type-icon-' + iconName );\n\n\t\t\t// update key\n\t\t\t$handle.find( '.li-field-key' ).html( this.makeCopyable( key ) );\n\n\t\t\t// action for 3rd party customization\n\t\t\tacf.doAction( 'render_field_object', this );\n\t\t},\n\n\t\trefresh: function () {\n\t\t\tacf.doAction( 'refresh_field_object', this );\n\t\t},\n\n\t\tisOpen: function () {\n\t\t\treturn this.$el.hasClass( 'open' );\n\t\t},\n\n\t\tonClickCopy: function ( e ) {\n\t\t\te.stopPropagation();\n\t\t\tif ( ! navigator.clipboard ) return;\n\t\t\tnavigator.clipboard.writeText( $( e.target ).text() ).then( () => {\n\t\t\t\t$( e.target ).addClass( 'copied' );\n\t\t\t\tsetTimeout( function () {\n\t\t\t\t\t$( e.target ).removeClass( 'copied' );\n\t\t\t\t}, 2000 );\n\t\t\t} );\n\t\t},\n\n\t\tonClickEdit: function ( e ) {\n\t\t\t$target = $( e.target );\n\t\t\tif (\n\t\t\t\t$target.parent().hasClass( 'row-options' ) &&\n\t\t\t\t! $target.hasClass( 'edit-field' )\n\t\t\t)\n\t\t\t\treturn;\n\t\t\tthis.isOpen() ? this.close() : this.open();\n\t\t},\n\n\t\tonChangeSettingsTab: function () {\n\t\t\tconst $settings = this.$el.children( '.settings' );\n\t\t\tacf.doAction( 'show', $settings );\n\t\t},\n\n\t\t/**\n\t\t * Adds 'active' class to row options nearest to the target.\n\t\t */\n\t\tonFocusEdit: function ( e ) {\n\t\t\tvar $rowOptions = $( e.target )\n\t\t\t\t.closest( 'li' )\n\t\t\t\t.find( '.row-options' );\n\t\t\t$rowOptions.addClass( 'active' );\n\t\t},\n\n\t\t/**\n\t\t * Removes 'active' class from row options if links in same row options area are no longer in focus.\n\t\t */\n\t\tonBlurEdit: function ( e ) {\n\t\t\tvar focusDelayMilliseconds = 50;\n\t\t\tvar $rowOptionsBlurElement = $( e.target )\n\t\t\t\t.closest( 'li' )\n\t\t\t\t.find( '.row-options' );\n\n\t\t\t// Timeout so that `activeElement` gives the new element in focus instead of the body.\n\t\t\tsetTimeout( function () {\n\t\t\t\tvar $rowOptionsFocusElement = $( document.activeElement )\n\t\t\t\t\t.closest( 'li' )\n\t\t\t\t\t.find( '.row-options' );\n\t\t\t\tif ( ! $rowOptionsBlurElement.is( $rowOptionsFocusElement ) ) {\n\t\t\t\t\t$rowOptionsBlurElement.removeClass( 'active' );\n\t\t\t\t}\n\t\t\t}, focusDelayMilliseconds );\n\t\t},\n\n\t\topen: function () {\n\t\t\t// vars\n\t\t\tvar $settings = this.$el.children( '.settings' );\n\n\t\t\t// initialise field type select\n\t\t\tthis.addProFields();\n\t\t\tthis.initializeFieldTypeSelect2();\n\n\t\t\t// action (open)\n\t\t\tacf.doAction( 'open_field_object', this );\n\t\t\tthis.trigger( 'openFieldObject' );\n\n\t\t\t// action (show)\n\t\t\tacf.doAction( 'show', $settings );\n\n\t\t\tthis.hideEmptyTabs();\n\n\t\t\t// open\n\t\t\t$settings.slideDown();\n\t\t\tthis.$el.addClass( 'open' );\n\t\t},\n\n\t\tonKeyDownSelect: function ( e ) {\n\t\t\t// Omit events from special keys.\n\t\t\tif (\n\t\t\t\t! (\n\t\t\t\t\t( e.which >= 186 && e.which <= 222 ) || // punctuation and special characters\n\t\t\t\t\t[\n\t\t\t\t\t\t8, 9, 13, 16, 17, 18, 19, 20, 27, 32, 33, 34, 35, 36,\n\t\t\t\t\t\t37, 38, 39, 40, 45, 46, 91, 92, 93, 144, 145,\n\t\t\t\t\t].includes( e.which ) || // Special keys\n\t\t\t\t\t( e.which >= 112 && e.which <= 123 )\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\t// Function keys\n\t\t\t\t$( this )\n\t\t\t\t\t.closest( '.select2-container' )\n\t\t\t\t\t.siblings( 'select:enabled' )\n\t\t\t\t\t.select2( 'open' );\n\t\t\t\treturn;\n\t\t\t}\n\t\t},\n\n\t\tclose: function () {\n\t\t\t// vars\n\t\t\tvar $settings = this.$el.children( '.settings' );\n\n\t\t\t// close\n\t\t\t$settings.slideUp();\n\t\t\tthis.$el.removeClass( 'open' );\n\n\t\t\t// action (close)\n\t\t\tacf.doAction( 'close_field_object', this );\n\t\t\tthis.trigger( 'closeFieldObject' );\n\n\t\t\t// action (hide)\n\t\t\tacf.doAction( 'hide', $settings );\n\t\t},\n\n\t\tserialize: function () {\n\t\t\treturn acf.serialize( this.$el, this.getInputName() );\n\t\t},\n\n\t\tsave: function ( type ) {\n\t\t\t// defaults\n\t\t\ttype = type || 'settings'; // meta, settings\n\n\t\t\t// vars\n\t\t\tvar save = this.getProp( 'save' );\n\n\t\t\t// bail if already saving settings\n\t\t\tif ( save === 'settings' ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// prop\n\t\t\tthis.setProp( 'save', type );\n\n\t\t\t// debug\n\t\t\tthis.$el.attr( 'data-save', type );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'save_field_object', this, type );\n\t\t},\n\n\t\tsubmit: function () {\n\t\t\t// vars\n\t\t\tvar inputName = this.getInputName();\n\t\t\tvar save = this.get( 'save' );\n\n\t\t\t// close\n\t\t\tif ( this.isOpen() ) {\n\t\t\t\tthis.close();\n\t\t\t}\n\n\t\t\t// allow all inputs to save\n\t\t\tif ( save == 'settings' ) {\n\t\t\t\t// do nothing\n\t\t\t\t// allow only meta inputs to save\n\t\t\t} else if ( save == 'meta' ) {\n\t\t\t\tthis.$( '> .settings [name^=\"' + inputName + '\"]' ).remove();\n\n\t\t\t\t// prevent all inputs from saving\n\t\t\t} else {\n\t\t\t\tthis.$( '[name^=\"' + inputName + '\"]' ).remove();\n\t\t\t}\n\n\t\t\t// action\n\t\t\tacf.doAction( 'submit_field_object', this );\n\t\t},\n\n\t\tonChange: function ( e, $el ) {\n\t\t\t// save settings\n\t\t\tthis.save();\n\n\t\t\t// action for 3rd party customization\n\t\t\tacf.doAction( 'change_field_object', this );\n\t\t},\n\n\t\tonChanged: function ( e, $el, name, value ) {\n\t\t\tif ( this.getType() === $el.attr( 'data-type' ) ) {\n\t\t\t\t$( 'button.acf-btn.browse-fields' ).prop( 'disabled', false );\n\t\t\t}\n\n\t\t\t// ignore 'save'\n\t\t\tif ( name == 'save' ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// save meta\n\t\t\tif ( [ 'menu_order', 'parent' ].indexOf( name ) > -1 ) {\n\t\t\t\tthis.save( 'meta' );\n\n\t\t\t\t// save field\n\t\t\t} else {\n\t\t\t\tthis.save();\n\t\t\t}\n\n\t\t\t// render\n\t\t\tif (\n\t\t\t\t[\n\t\t\t\t\t'menu_order',\n\t\t\t\t\t'label',\n\t\t\t\t\t'required',\n\t\t\t\t\t'name',\n\t\t\t\t\t'type',\n\t\t\t\t\t'key',\n\t\t\t\t].indexOf( name ) > -1\n\t\t\t) {\n\t\t\t\tthis.render();\n\t\t\t}\n\n\t\t\t// action for 3rd party customization\n\t\t\tacf.doAction( 'change_field_object_' + name, this, value );\n\t\t},\n\n\t\tonChangeLabel: function ( e, $el ) {\n\t\t\t// set\n\t\t\tvar label = $el.val();\n\t\t\tthis.set( 'label', label );\n\n\t\t\t// render name\n\t\t\tif ( this.prop( 'name' ) == '' ) {\n\t\t\t\tvar name = acf.applyFilters(\n\t\t\t\t\t'generate_field_object_name',\n\t\t\t\t\tacf.strSanitize( label ),\n\t\t\t\t\tthis\n\t\t\t\t);\n\t\t\t\tthis.prop( 'name', name );\n\t\t\t}\n\t\t},\n\n\t\tonChangeName: function ( e, $el ) {\n\t\t\t// set\n\t\t\tvar name = $el.val();\n\t\t\tthis.set( 'name', name );\n\n\t\t\t// error\n\t\t\tif ( name.substr( 0, 6 ) === 'field_' ) {\n\t\t\t\talert(\n\t\t\t\t\tacf.__(\n\t\t\t\t\t\t'The string \"field_\" may not be used at the start of a field name'\n\t\t\t\t\t)\n\t\t\t\t);\n\t\t\t}\n\t\t},\n\n\t\tonChangeRequired: function ( e, $el ) {\n\t\t\t// set\n\t\t\tvar required = $el.prop( 'checked' ) ? 1 : 0;\n\t\t\tthis.set( 'required', required );\n\t\t},\n\n\t\tdelete: function ( args ) {\n\t\t\t// defaults\n\t\t\targs = acf.parseArgs( args, {\n\t\t\t\tanimate: true,\n\t\t\t} );\n\n\t\t\t// add to remove list\n\t\t\tvar id = this.prop( 'ID' );\n\n\t\t\tif ( id ) {\n\t\t\t\tvar $input = $( '#_acf_delete_fields' );\n\t\t\t\tvar newVal = $input.val() + '|' + id;\n\t\t\t\t$input.val( newVal );\n\t\t\t}\n\n\t\t\t// action\n\t\t\tacf.doAction( 'delete_field_object', this );\n\n\t\t\t// animate\n\t\t\tif ( args.animate ) {\n\t\t\t\tthis.removeAnimate();\n\t\t\t} else {\n\t\t\t\tthis.remove();\n\t\t\t}\n\t\t},\n\n\t\tonClickDelete: function ( e, $el ) {\n\t\t\t// Bypass confirmation when holding down \"shift\" key.\n\t\t\tif ( e.shiftKey ) {\n\t\t\t\treturn this.delete();\n\t\t\t}\n\n\t\t\t// add class\n\t\t\tthis.$el.addClass( '-hover' );\n\n\t\t\t// add tooltip\n\t\t\tvar tooltip = acf.newTooltip( {\n\t\t\t\tconfirmRemove: true,\n\t\t\t\ttarget: $el,\n\t\t\t\tcontext: this,\n\t\t\t\tconfirm: function () {\n\t\t\t\t\tthis.delete();\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\tthis.$el.removeClass( '-hover' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tremoveAnimate: function () {\n\t\t\t// vars\n\t\t\tvar field = this;\n\t\t\tvar $list = this.$el.parent();\n\t\t\tvar $fields = acf.findFieldObjects( {\n\t\t\t\tsibling: this.$el,\n\t\t\t} );\n\n\t\t\t// remove\n\t\t\tacf.remove( {\n\t\t\t\ttarget: this.$el,\n\t\t\t\tendHeight: $fields.length ? 0 : 50,\n\t\t\t\tcomplete: function () {\n\t\t\t\t\tfield.remove();\n\t\t\t\t\tacf.doAction( 'removed_field_object', field, $list );\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'remove_field_object', field, $list );\n\t\t},\n\n\t\tduplicate: function () {\n\t\t\t// vars\n\t\t\tvar newKey = acf.uniqid( 'field_' );\n\n\t\t\t// duplicate\n\t\t\tvar $newField = acf.duplicate( {\n\t\t\t\ttarget: this.$el,\n\t\t\t\tsearch: this.get( 'id' ),\n\t\t\t\treplace: newKey,\n\t\t\t} );\n\n\t\t\t// set new key\n\t\t\t$newField.attr( 'data-key', newKey );\n\n\t\t\t// get instance\n\t\t\tvar newField = acf.getFieldObject( $newField );\n\n\t\t\t// update newField label / name\n\t\t\tvar label = newField.prop( 'label' );\n\t\t\tvar name = newField.prop( 'name' );\n\t\t\tvar end = name.split( '_' ).pop();\n\t\t\tvar copy = acf.__( 'copy' );\n\n\t\t\t// increase suffix \"1\"\n\t\t\tif ( acf.isNumeric( end ) ) {\n\t\t\t\tvar i = end * 1 + 1;\n\t\t\t\tlabel = label.replace( end, i );\n\t\t\t\tname = name.replace( end, i );\n\n\t\t\t\t// increase suffix \"(copy1)\"\n\t\t\t} else if ( end.indexOf( copy ) === 0 ) {\n\t\t\t\tvar i = end.replace( copy, '' ) * 1;\n\t\t\t\ti = i ? i + 1 : 2;\n\n\t\t\t\t// replace\n\t\t\t\tlabel = label.replace( end, copy + i );\n\t\t\t\tname = name.replace( end, copy + i );\n\n\t\t\t\t// add default \"(copy)\"\n\t\t\t} else {\n\t\t\t\tlabel += ' (' + copy + ')';\n\t\t\t\tname += '_' + copy;\n\t\t\t}\n\n\t\t\tnewField.prop( 'ID', 0 );\n\t\t\tnewField.prop( 'label', label );\n\t\t\tnewField.prop( 'name', name );\n\t\t\tnewField.prop( 'key', newKey );\n\n\t\t\t// close the current field if it's open.\n\t\t\tif ( this.isOpen() ) {\n\t\t\t\tthis.close();\n\t\t\t}\n\t\t\t\n\t\t\t// open the new field and initialise correctly.\n\t\t\tnewField.open();\n\n\t\t\t// focus label\n\t\t\tvar $label = newField.$setting( 'label input' );\n\t\t\tsetTimeout( function () {\n\t\t\t\t$label.trigger( 'focus' );\n\t\t\t}, 251 );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'duplicate_field_object', this, newField );\n\t\t\tacf.doAction( 'append_field_object', newField );\n\t\t},\n\n\t\twipe: function () {\n\t\t\t// vars\n\t\t\tvar prevId = this.get( 'id' );\n\t\t\tvar prevKey = this.get( 'key' );\n\t\t\tvar newKey = acf.uniqid( 'field_' );\n\n\t\t\t// rename\n\t\t\tacf.rename( {\n\t\t\t\ttarget: this.$el,\n\t\t\t\tsearch: prevId,\n\t\t\t\treplace: newKey,\n\t\t\t} );\n\n\t\t\t// data\n\t\t\tthis.set( 'id', newKey );\n\t\t\tthis.set( 'prevId', prevId );\n\t\t\tthis.set( 'prevKey', prevKey );\n\n\t\t\t// props\n\t\t\tthis.prop( 'key', newKey );\n\t\t\tthis.prop( 'ID', 0 );\n\n\t\t\t// attr\n\t\t\tthis.$el.attr( 'data-key', newKey );\n\t\t\tthis.$el.attr( 'data-id', newKey );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'wipe_field_object', this );\n\t\t},\n\n\t\tmove: function () {\n\t\t\t// helper\n\t\t\tvar hasChanged = function ( field ) {\n\t\t\t\treturn field.get( 'save' ) == 'settings';\n\t\t\t};\n\n\t\t\t// vars\n\t\t\tvar changed = hasChanged( this );\n\n\t\t\t// has sub fields changed\n\t\t\tif ( ! changed ) {\n\t\t\t\tacf.getFieldObjects( {\n\t\t\t\t\tparent: this.$el,\n\t\t\t\t} ).map( function ( field ) {\n\t\t\t\t\tchanged = hasChanged( field ) || field.changed;\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\t// bail early if changed\n\t\t\tif ( changed ) {\n\t\t\t\talert(\n\t\t\t\t\tacf.__(\n\t\t\t\t\t\t'This field cannot be moved until its changes have been saved'\n\t\t\t\t\t)\n\t\t\t\t);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// step 1.\n\t\t\tvar id = this.prop( 'ID' );\n\t\t\tvar field = this;\n\t\t\tvar popup = false;\n\t\t\tvar step1 = function () {\n\t\t\t\t// popup\n\t\t\t\tpopup = acf.newPopup( {\n\t\t\t\t\ttitle: acf.__( 'Move Custom Field' ),\n\t\t\t\t\tloading: true,\n\t\t\t\t\twidth: '300px',\n\t\t\t\t\topenedBy: field.$el.find( '.move-field' ),\n\t\t\t\t} );\n\n\t\t\t\t// ajax\n\t\t\t\tvar ajaxData = {\n\t\t\t\t\taction: 'acf/field_group/move_field',\n\t\t\t\t\tfield_id: id,\n\t\t\t\t};\n\n\t\t\t\t// get HTML\n\t\t\t\t$.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tdataType: 'html',\n\t\t\t\t\tsuccess: step2,\n\t\t\t\t} );\n\t\t\t};\n\n\t\t\tvar step2 = function ( html ) {\n\t\t\t\t// update popup\n\t\t\t\tpopup.loading( false );\n\t\t\t\tpopup.content( html );\n\n\t\t\t\t// submit form\n\t\t\t\tpopup.on( 'submit', 'form', step3 );\n\t\t\t};\n\n\t\t\tvar step3 = function ( e, $el ) {\n\t\t\t\t// prevent\n\t\t\t\te.preventDefault();\n\n\t\t\t\t// disable\n\t\t\t\tacf.startButtonLoading( popup.$( '.button' ) );\n\n\t\t\t\t// ajax\n\t\t\t\tvar ajaxData = {\n\t\t\t\t\taction: 'acf/field_group/move_field',\n\t\t\t\t\tfield_id: id,\n\t\t\t\t\tfield_group_id: popup.$( 'select' ).val(),\n\t\t\t\t};\n\n\t\t\t\t// get HTML\n\t\t\t\t$.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tdataType: 'html',\n\t\t\t\t\tsuccess: step4,\n\t\t\t\t} );\n\t\t\t};\n\n\t\t\tvar step4 = function ( html ) {\n\t\t\t\tpopup.content( html );\n\n\t\t\t\tif ( wp.a11y && wp.a11y.speak && acf.__ ) {\n\t\t\t\t\twp.a11y.speak(\n\t\t\t\t\t\tacf.__( 'Field moved to other group' ),\n\t\t\t\t\t\t'polite'\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\tpopup.$( '.acf-close-popup' ).focus();\n\n\t\t\t\tfield.removeAnimate();\n\t\t\t};\n\n\t\t\t// start\n\t\t\tstep1();\n\t\t},\n\n\t\tbrowseFields: function ( e, $el ) {\n\t\t\te.preventDefault();\n\n\t\t\tconst modal = acf.newBrowseFieldsModal( {\n\t\t\t\topenedBy: this,\n\t\t\t} );\n\t\t},\n\n\t\tonChangeType: function ( e, $el ) {\n\t\t\t// clea previous timout\n\t\t\tif ( this.changeTimeout ) {\n\t\t\t\tclearTimeout( this.changeTimeout );\n\t\t\t}\n\n\t\t\t// set new timeout\n\t\t\t// - prevents changing type multiple times whilst user types in newType\n\t\t\tthis.changeTimeout = this.setTimeout( function () {\n\t\t\t\tthis.changeType( $el.val() );\n\t\t\t}, 300 );\n\t\t},\n\n\t\tchangeType: function ( newType ) {\n\t\t\tvar prevType = this.prop( 'type' );\n\t\t\tvar prevClass = acf.strSlugify( 'acf-field-object-' + prevType );\n\t\t\tvar newClass = acf.strSlugify( 'acf-field-object-' + newType );\n\n\t\t\t// Update props.\n\t\t\tthis.$el.removeClass( prevClass ).addClass( newClass );\n\t\t\tthis.$el.attr( 'data-type', newType );\n\t\t\tthis.$el.data( 'type', newType );\n\n\t\t\t// Abort XHR if this field is already loading AJAX data.\n\t\t\tif ( this.has( 'xhr' ) ) {\n\t\t\t\tthis.get( 'xhr' ).abort();\n\t\t\t}\n\n\t\t\t// Store old settings so they can be reused later.\n\t\t\tconst $oldSettings = {};\n\n\t\t\tthis.$el\n\t\t\t\t.find(\n\t\t\t\t\t'.acf-field-settings:first > .acf-field-settings-main > .acf-field-type-settings'\n\t\t\t\t)\n\t\t\t\t.each( function () {\n\t\t\t\t\tlet tab = $( this ).data( 'parent-tab' );\n\t\t\t\t\tlet $tabSettings = $( this ).children().removeData();\n\n\t\t\t\t\t$oldSettings[ tab ] = $tabSettings;\n\n\t\t\t\t\t$tabSettings.detach();\n\t\t\t\t} );\n\n\t\t\tthis.set( 'settings-' + prevType, $oldSettings );\n\n\t\t\t// Show the settings if we already have them cached.\n\t\t\tif ( this.has( 'settings-' + newType ) ) {\n\t\t\t\tlet $newSettings = this.get( 'settings-' + newType );\n\n\t\t\t\tthis.showFieldTypeSettings( $newSettings );\n\t\t\t\tthis.set( 'type', newType );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Add loading spinner.\n\t\t\tconst $loading = $(\n\t\t\t\t'<div class=\"acf-field\"><div class=\"acf-input\"><div class=\"acf-loading\"></div></div></div>'\n\t\t\t);\n\t\t\tthis.$el\n\t\t\t\t.find(\n\t\t\t\t\t'.acf-field-settings-main-general .acf-field-type-settings'\n\t\t\t\t)\n\t\t\t\t.before( $loading );\n\n\t\t\tconst ajaxData = {\n\t\t\t\taction: 'acf/field_group/render_field_settings',\n\t\t\t\tfield: this.serialize(),\n\t\t\t\tprefix: this.getInputName(),\n\t\t\t};\n\n\t\t\t// Get the settings for this field type over AJAX.\n\t\t\tvar xhr = $.ajax( {\n\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\ttype: 'post',\n\t\t\t\tdataType: 'json',\n\t\t\t\tcontext: this,\n\t\t\t\tsuccess: function ( response ) {\n\t\t\t\t\tif ( ! acf.isAjaxSuccess( response ) ) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.showFieldTypeSettings( response.data );\n\t\t\t\t},\n\t\t\t\tcomplete: function () {\n\t\t\t\t\t// also triggered by xhr.abort();\n\t\t\t\t\t$loading.remove();\n\t\t\t\t\tthis.set( 'type', newType );\n\t\t\t\t\t//this.refresh();\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// set\n\t\t\tthis.set( 'xhr', xhr );\n\t\t},\n\n\t\tshowFieldTypeSettings: function ( settings ) {\n\t\t\tif ( 'object' !== typeof settings ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = this;\n\t\t\tconst tabs = Object.keys( settings );\n\n\t\t\ttabs.forEach( ( tab ) => {\n\t\t\t\tconst $tab = self.$el.find(\n\t\t\t\t\t'.acf-field-settings-main-' +\n\t\t\t\t\t\ttab.replace( '_', '-' ) +\n\t\t\t\t\t\t' .acf-field-type-settings'\n\t\t\t\t);\n\t\t\t\tlet tabContent = '';\n\n\t\t\t\tif (\n\t\t\t\t\t[ 'object', 'string' ].includes( typeof settings[ tab ] )\n\t\t\t\t) {\n\t\t\t\t\ttabContent = settings[ tab ];\n\t\t\t\t}\n\n\t\t\t\t$tab.prepend( tabContent );\n\t\t\t\tacf.doAction( 'append', $tab );\n\t\t\t} );\n\n\t\t\tthis.hideEmptyTabs();\n\t\t},\n\n\t\tupdateParent: function () {\n\t\t\t// vars\n\t\t\tvar ID = acf.get( 'post_id' );\n\n\t\t\t// check parent\n\t\t\tvar parent = this.getParent();\n\t\t\tif ( parent ) {\n\t\t\t\tID = parseInt( parent.prop( 'ID' ) ) || parent.prop( 'key' );\n\t\t\t}\n\n\t\t\t// update\n\t\t\tthis.prop( 'parent', ID );\n\t\t},\n\n\t\thideEmptyTabs: function() {\n\t\t\tconst $settings = this.$settings();\n\t\t\tconst $tabs = $settings.find( '.acf-field-settings:first > .acf-field-settings-main' );\n\n\t\t\t$tabs.each( function() {\n\t\t\t\tconst $tabContent = $( this );\n\t\t\t\tconst tabName = $tabContent.find( '.acf-field-type-settings:first' ).data( 'parentTab' );\n\t\t\t\tconst $tabLink = $settings.find( '.acf-settings-type-' + tabName ).first();\n\n\t\t\t\tif ( $.trim( $tabContent.text() ) === '' ) {\n\t\t\t\t\t$tabLink.hide();\n\t\t\t\t} else if ( $tabLink.is( ':hidden' ) ) {\n\t\t\t\t\t$tabLink.show();\n\t\t\t\t}\n\t\t\t} );\n\t\t},\n\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  acf.findFieldObject\n\t *\n\t *  Returns a single fieldObject $el for a given field key\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tstring key The field key\n\t *  @return\tjQuery\n\t */\n\n\tacf.findFieldObject = function ( key ) {\n\t\treturn acf.findFieldObjects( {\n\t\t\tkey: key,\n\t\t\tlimit: 1,\n\t\t} );\n\t};\n\n\t/**\n\t *  acf.findFieldObjects\n\t *\n\t *  Returns an array of fieldObject $el for the given args\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tobject args\n\t *  @return\tjQuery\n\t */\n\n\tacf.findFieldObjects = function ( args ) {\n\t\t// vars\n\t\targs = args || {};\n\t\tvar selector = '.acf-field-object';\n\t\tvar $fields = false;\n\n\t\t// args\n\t\targs = acf.parseArgs( args, {\n\t\t\tid: '',\n\t\t\tkey: '',\n\t\t\ttype: '',\n\t\t\tlimit: false,\n\t\t\tlist: null,\n\t\t\tparent: false,\n\t\t\tsibling: false,\n\t\t\tchild: false,\n\t\t} );\n\n\t\t// id\n\t\tif ( args.id ) {\n\t\t\tselector += '[data-id=\"' + args.id + '\"]';\n\t\t}\n\n\t\t// key\n\t\tif ( args.key ) {\n\t\t\tselector += '[data-key=\"' + args.key + '\"]';\n\t\t}\n\n\t\t// type\n\t\tif ( args.type ) {\n\t\t\tselector += '[data-type=\"' + args.type + '\"]';\n\t\t}\n\n\t\t// query\n\t\tif ( args.list ) {\n\t\t\t$fields = args.list.children( selector );\n\t\t} else if ( args.parent ) {\n\t\t\t$fields = args.parent.find( selector );\n\t\t} else if ( args.sibling ) {\n\t\t\t$fields = args.sibling.siblings( selector );\n\t\t} else if ( args.child ) {\n\t\t\t$fields = args.child.parents( selector );\n\t\t} else {\n\t\t\t$fields = $( selector );\n\t\t}\n\n\t\t// limit\n\t\tif ( args.limit ) {\n\t\t\t$fields = $fields.slice( 0, args.limit );\n\t\t}\n\n\t\t// return\n\t\treturn $fields;\n\t};\n\n\t/**\n\t *  acf.getFieldObject\n\t *\n\t *  Returns a single fieldObject instance for a given $el|key\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tstring|jQuery $field The field $el or key\n\t *  @return\tjQuery\n\t */\n\n\tacf.getFieldObject = function ( $field ) {\n\t\t// allow key\n\t\tif ( typeof $field === 'string' ) {\n\t\t\t$field = acf.findFieldObject( $field );\n\t\t}\n\n\t\t// instantiate\n\t\tvar field = $field.data( 'acf' );\n\t\tif ( ! field ) {\n\t\t\tfield = acf.newFieldObject( $field );\n\t\t}\n\n\t\t// return\n\t\treturn field;\n\t};\n\n\t/**\n\t *  acf.getFieldObjects\n\t *\n\t *  Returns an array of fieldObject instances for the given args\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tobject args\n\t *  @return\tarray\n\t */\n\n\tacf.getFieldObjects = function ( args ) {\n\t\t// query\n\t\tvar $fields = acf.findFieldObjects( args );\n\n\t\t// loop\n\t\tvar fields = [];\n\t\t$fields.each( function () {\n\t\t\tvar field = acf.getFieldObject( $( this ) );\n\t\t\tfields.push( field );\n\t\t} );\n\n\t\t// return\n\t\treturn fields;\n\t};\n\n\t/**\n\t *  acf.newFieldObject\n\t *\n\t *  Initializes and returns a new FieldObject instance\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tjQuery $field The field $el\n\t *  @return\tobject\n\t */\n\n\tacf.newFieldObject = function ( $field ) {\n\t\t// instantiate\n\t\tvar field = new acf.FieldObject( $field );\n\n\t\t// action\n\t\tacf.doAction( 'new_field_object', field );\n\n\t\t// return\n\t\treturn field;\n\t};\n\n\t/**\n\t *  actionManager\n\t *\n\t *  description\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar eventManager = new acf.Model( {\n\t\tpriority: 5,\n\n\t\tinitialize: function () {\n\t\t\t// actions\n\t\t\tvar actions = [ 'prepare', 'ready', 'append', 'remove' ];\n\n\t\t\t// loop\n\t\t\tactions.map( function ( action ) {\n\t\t\t\tthis.addFieldActions( action );\n\t\t\t}, this );\n\t\t},\n\n\t\taddFieldActions: function ( action ) {\n\t\t\t// vars\n\t\t\tvar pluralAction = action + '_field_objects'; // ready_field_objects\n\t\t\tvar singleAction = action + '_field_object'; // ready_field_object\n\t\t\tvar singleEvent = action + 'FieldObject'; // readyFieldObject\n\n\t\t\t// global action\n\t\t\tvar callback = function ( $el /*, arg1, arg2, etc*/ ) {\n\t\t\t\t// vars\n\t\t\t\tvar fieldObjects = acf.getFieldObjects( { parent: $el } );\n\n\t\t\t\t// call plural\n\t\t\t\tif ( fieldObjects.length ) {\n\t\t\t\t\t/// get args [$el, arg1]\n\t\t\t\t\tvar args = acf.arrayArgs( arguments );\n\n\t\t\t\t\t// modify args [pluralAction, fields, arg1]\n\t\t\t\t\targs.splice( 0, 1, pluralAction, fieldObjects );\n\t\t\t\t\tacf.doAction.apply( null, args );\n\t\t\t\t}\n\t\t\t};\n\n\t\t\t// plural action\n\t\t\tvar pluralCallback = function (\n\t\t\t\tfieldObjects /*, arg1, arg2, etc*/\n\t\t\t) {\n\t\t\t\t/// get args [fields, arg1]\n\t\t\t\tvar args = acf.arrayArgs( arguments );\n\n\t\t\t\t// modify args [singleAction, fields, arg1]\n\t\t\t\targs.unshift( singleAction );\n\n\t\t\t\t// loop\n\t\t\t\tfieldObjects.map( function ( fieldObject ) {\n\t\t\t\t\t// modify args [singleAction, field, arg1]\n\t\t\t\t\targs[ 1 ] = fieldObject;\n\t\t\t\t\tacf.doAction.apply( null, args );\n\t\t\t\t} );\n\t\t\t};\n\n\t\t\t// single action\n\t\t\tvar singleCallback = function (\n\t\t\t\tfieldObject /*, arg1, arg2, etc*/\n\t\t\t) {\n\t\t\t\t/// get args [$field, arg1]\n\t\t\t\tvar args = acf.arrayArgs( arguments );\n\n\t\t\t\t// modify args [singleAction, $field, arg1]\n\t\t\t\targs.unshift( singleAction );\n\n\t\t\t\t// action variations (ready_field/type=image)\n\t\t\t\tvar variations = [ 'type', 'name', 'key' ];\n\t\t\t\tvariations.map( function ( variation ) {\n\t\t\t\t\targs[ 0 ] =\n\t\t\t\t\t\tsingleAction +\n\t\t\t\t\t\t'/' +\n\t\t\t\t\t\tvariation +\n\t\t\t\t\t\t'=' +\n\t\t\t\t\t\tfieldObject.get( variation );\n\t\t\t\t\tacf.doAction.apply( null, args );\n\t\t\t\t} );\n\n\t\t\t\t// modify args [arg1]\n\t\t\t\targs.splice( 0, 2 );\n\n\t\t\t\t// event\n\t\t\t\tfieldObject.trigger( singleEvent, args );\n\t\t\t};\n\n\t\t\t// add actions\n\t\t\tacf.addAction( action, callback, 5 );\n\t\t\tacf.addAction( pluralAction, pluralCallback, 5 );\n\t\t\tacf.addAction( singleAction, singleCallback, 5 );\n\t\t},\n\t} );\n\n\t/**\n\t *  fieldManager\n\t *\n\t *  description\n\t *\n\t *  @date\t4/1/18\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar fieldManager = new acf.Model( {\n\t\tid: 'fieldManager',\n\n\t\tevents: {\n\t\t\t'submit #post': 'onSubmit',\n\t\t\t'mouseenter .acf-field-list': 'onHoverSortable',\n\t\t\t'click .add-field': 'onClickAdd',\n\t\t},\n\n\t\tactions: {\n\t\t\tremoved_field_object: 'onRemovedField',\n\t\t\tsortstop_field_object: 'onReorderField',\n\t\t\tdelete_field_object: 'onDeleteField',\n\t\t\tchange_field_object_type: 'onChangeFieldType',\n\t\t\tduplicate_field_object: 'onDuplicateField',\n\t\t},\n\n\t\tonSubmit: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar fields = acf.getFieldObjects();\n\n\t\t\t// loop\n\t\t\tfields.map( function ( field ) {\n\t\t\t\tfield.submit();\n\t\t\t} );\n\t\t},\n\n\t\tsetFieldMenuOrder: function ( field ) {\n\t\t\tthis.renderFields( field.$el.parent() );\n\t\t},\n\n\t\tonHoverSortable: function ( e, $el ) {\n\t\t\t// bail early if already sortable\n\t\t\tif ( $el.hasClass( 'ui-sortable' ) ) return;\n\n\t\t\t// sortable\n\t\t\t$el.sortable( {\n\t\t\t\thelper: function( event, element ) {\n\t\t\t\t\t// https://core.trac.wordpress.org/ticket/16972#comment:22\n\t\t\t\t\treturn element.clone()\n\t\t\t\t\t\t.find( ':input' )\n\t\t\t\t\t\t\t.attr( 'name', function( i, currentName ) {\n\t\t\t\t\t\t\t\t\treturn 'sort_' + parseInt( Math.random() * 100000, 10 ).toString() + '_' + currentName;\n\t\t\t\t\t\t\t} )\n\t\t\t\t\t\t.end();\n\t\t\t\t},\n\t\t\t\thandle: '.acf-sortable-handle',\n\t\t\t\tconnectWith: '.acf-field-list',\n\t\t\t\tstart: function ( e, ui ) {\n\t\t\t\t\tvar field = acf.getFieldObject( ui.item );\n\t\t\t\t\tui.placeholder.height( ui.item.height() );\n\t\t\t\t\tacf.doAction( 'sortstart_field_object', field, $el );\n\t\t\t\t},\n\t\t\t\tupdate: function ( e, ui ) {\n\t\t\t\t\tvar field = acf.getFieldObject( ui.item );\n\t\t\t\t\tacf.doAction( 'sortstop_field_object', field, $el );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tonRemovedField: function ( field, $list ) {\n\t\t\tthis.renderFields( $list );\n\t\t},\n\n\t\tonReorderField: function ( field, $list ) {\n\t\t\tfield.updateParent();\n\t\t\tthis.renderFields( $list );\n\t\t},\n\n\t\tonDeleteField: function ( field ) {\n\t\t\t// delete children\n\t\t\tfield.getFields().map( function ( child ) {\n\t\t\t\tchild.delete( { animate: false } );\n\t\t\t} );\n\t\t},\n\n\t\tonChangeFieldType: function ( field ) {\n\t\t\t// enable browse field modal button\n\t\t\tfield.$el.find( 'button.browse-fields' ).prop( 'disabled', false );\n\t\t},\n\n\t\tonDuplicateField: function ( field, newField ) {\n\t\t\t// check for children\n\t\t\tvar children = newField.getFields();\n\t\t\tif ( children.length ) {\n\t\t\t\t// loop\n\t\t\t\tchildren.map( function ( child ) {\n\t\t\t\t\t// wipe field\n\t\t\t\t\tchild.wipe();\n\n\t\t\t\t\t// if the child is open, re-fire the open method to ensure it's initialised correctly.\n\t\t\t\t\tif ( child.isOpen() ) {\n\t\t\t\t\t\tchild.open();\n\t\t\t\t\t}\n\n\t\t\t\t\t// update parent\n\t\t\t\t\tchild.updateParent();\n\t\t\t\t} );\n\n\t\t\t\t// action\n\t\t\t\tacf.doAction(\n\t\t\t\t\t'duplicate_field_objects',\n\t\t\t\t\tchildren,\n\t\t\t\t\tnewField,\n\t\t\t\t\tfield\n\t\t\t\t);\n\t\t\t}\n\n\t\t\t// set menu order\n\t\t\tthis.setFieldMenuOrder( newField );\n\t\t},\n\n\t\trenderFields: function ( $list ) {\n\t\t\t// vars\n\t\t\tvar fields = acf.getFieldObjects( {\n\t\t\t\tlist: $list,\n\t\t\t} );\n\n\t\t\t// no fields\n\t\t\tif ( ! fields.length ) {\n\t\t\t\t$list.addClass( '-empty' );\n\t\t\t\t$list\n\t\t\t\t\t.parents( '.acf-field-list-wrap' )\n\t\t\t\t\t.first()\n\t\t\t\t\t.addClass( '-empty' );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// has fields\n\t\t\t$list.removeClass( '-empty' );\n\t\t\t$list\n\t\t\t\t.parents( '.acf-field-list-wrap' )\n\t\t\t\t.first()\n\t\t\t\t.removeClass( '-empty' );\n\n\t\t\t// prop\n\t\t\tfields.map( function ( field, i ) {\n\t\t\t\tfield.prop( 'menu_order', i );\n\t\t\t} );\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\tlet $list;\n\n\t\t\tif ( $el.hasClass( 'add-first-field' ) ) {\n\t\t\t\t$list = $el.parents( '.acf-field-list' ).eq( 0 );\n\t\t\t} else if (\n\t\t\t\t$el.parent().hasClass( 'acf-headerbar-actions' ) ||\n\t\t\t\t$el.parent().hasClass( 'no-fields-message-inner' )\n\t\t\t) {\n\t\t\t\t$list = $( '.acf-field-list:first' );\n\t\t\t} else if ( $el.parent().hasClass( 'acf-sub-field-list-header' ) ) {\n\t\t\t\t$list = $el\n\t\t\t\t\t.parents( '.acf-input:first' )\n\t\t\t\t\t.find( '.acf-field-list:first' );\n\t\t\t} else {\n\t\t\t\t$list = $el\n\t\t\t\t\t.closest( '.acf-tfoot' )\n\t\t\t\t\t.siblings( '.acf-field-list' );\n\t\t\t}\n\n\t\t\tthis.addField( $list );\n\t\t},\n\n\t\taddField: function ( $list ) {\n\t\t\t// vars\n\t\t\tvar html = $( '#tmpl-acf-field' ).html();\n\t\t\tvar $el = $( html );\n\t\t\tvar prevId = $el.data( 'id' );\n\t\t\tvar newKey = acf.uniqid( 'field_' );\n\n\t\t\t// duplicate\n\t\t\tvar $newField = acf.duplicate( {\n\t\t\t\ttarget: $el,\n\t\t\t\tsearch: prevId,\n\t\t\t\treplace: newKey,\n\t\t\t\tappend: function ( $el, $el2 ) {\n\t\t\t\t\t$list.append( $el2 );\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// get instance\n\t\t\tvar newField = acf.getFieldObject( $newField );\n\n\t\t\t// props\n\t\t\tnewField.prop( 'key', newKey );\n\t\t\tnewField.prop( 'ID', 0 );\n\t\t\tnewField.prop( 'label', '' );\n\t\t\tnewField.prop( 'name', '' );\n\n\t\t\t// attr\n\t\t\t$newField.attr( 'data-key', newKey );\n\t\t\t$newField.attr( 'data-id', newKey );\n\n\t\t\t// update parent prop\n\t\t\tnewField.updateParent();\n\n\t\t\t// focus type\n\t\t\tvar $type = newField.$input( 'type' );\n\t\t\tsetTimeout( function () {\n\t\t\t\tif ( $list.hasClass( 'acf-auto-add-field' ) ) {\n\t\t\t\t\t$list.removeClass( 'acf-auto-add-field' );\n\t\t\t\t} else {\n\t\t\t\t\t$type.trigger( 'focus' );\n\t\t\t\t}\n\t\t\t}, 251 );\n\n\t\t\t// open\n\t\t\tnewField.open();\n\n\t\t\t// set menu order\n\t\t\tthis.renderFields( $list );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'add_field_object', newField );\n\t\t\tacf.doAction( 'append_field_object', newField );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  locationManager\n\t *\n\t *  Field group location rules functionality\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar locationManager = new acf.Model( {\n\t\tid: 'locationManager',\n\t\twait: 'ready',\n\n\t\tevents: {\n\t\t\t'click .add-location-rule': 'onClickAddRule',\n\t\t\t'click .add-location-group': 'onClickAddGroup',\n\t\t\t'click .remove-location-rule': 'onClickRemoveRule',\n\t\t\t'change .refresh-location-rule': 'onChangeRemoveRule',\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tthis.$el = $( '#acf-field-group-options' );\n\t\t\tthis.addProLocations();\n\t\t\tthis.updateGroupsClass();\n\t\t},\n\n\t\taddProLocations: function () {\n\t\t\t// Make sure we're only running this on free version.\n\t\t\tif ( acf.get( 'is_pro' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Loop over each pro field type and append it to the select.\n\t\t\tconst PROLocationTypes = acf.get( 'PROLocationTypes' );\n\t\t\tif ( typeof PROLocationTypes !== 'object' ) return;\n\n\t\t\tconst $formsGroup = this.$el\n\t\t\t\t.find( 'select.refresh-location-rule' )\n\t\t\t\t.find( 'optgroup[label=\"Forms\"]' )\n\n\t\t\tfor ( const [ key, name ] of Object.entries( PROLocationTypes ) ) {\n\t\t\t\t$formsGroup.append(\n\t\t\t\t\t'<option value=\"null\" disabled=\"disabled\">' +\n\t\t\t\t\t\tname +\n\t\t\t\t\t\t' (' +\n\t\t\t\t\t\tacf.__( 'PRO Only' ) +\n\t\t\t\t\t\t')</option>'\n\t\t\t\t);\n\t\t\t}\n\t\t},\n\n\t\tonClickAddRule: function ( e, $el ) {\n\t\t\tthis.addRule( $el.closest( 'tr' ) );\n\t\t},\n\n\t\tonClickRemoveRule: function ( e, $el ) {\n\t\t\tthis.removeRule( $el.closest( 'tr' ) );\n\t\t},\n\n\t\tonChangeRemoveRule: function ( e, $el ) {\n\t\t\tthis.changeRule( $el.closest( 'tr' ) );\n\t\t},\n\n\t\tonClickAddGroup: function ( e, $el ) {\n\t\t\tthis.addGroup();\n\t\t},\n\n\t\taddRule: function ( $tr ) {\n\t\t\tacf.duplicate( $tr );\n\t\t\tthis.updateGroupsClass();\n\t\t},\n\n\t\tremoveRule: function ( $tr ) {\n\t\t\tif ( $tr.siblings( 'tr' ).length == 0 ) {\n\t\t\t\t$tr.closest( '.rule-group' ).remove();\n\t\t\t} else {\n\t\t\t\t$tr.remove();\n\t\t\t}\n\n\t\t\t// Update h4\n\t\t\tvar $group = this.$( '.rule-group:first' );\n\t\t\t$group.find( 'h4' ).text( acf.__( 'Show this field group if' ) );\n\n\t\t\tthis.updateGroupsClass();\n\t\t},\n\n\t\tchangeRule: function ( $rule ) {\n\t\t\t// vars\n\t\t\tvar $group = $rule.closest( '.rule-group' );\n\t\t\tvar prefix = $rule\n\t\t\t\t.find( 'td.param select' )\n\t\t\t\t.attr( 'name' )\n\t\t\t\t.replace( '[param]', '' );\n\n\t\t\t// ajaxdata\n\t\t\tvar ajaxdata = {};\n\t\t\tajaxdata.action = 'acf/field_group/render_location_rule';\n\t\t\tajaxdata.rule = acf.serialize( $rule, prefix );\n\t\t\tajaxdata.rule.id = $rule.data( 'id' );\n\t\t\tajaxdata.rule.group = $group.data( 'id' );\n\n\t\t\t// temp disable\n\t\t\tacf.disable( $rule.find( 'td.value' ) );\n\n\t\t\t// ajax\n\t\t\t$.ajax( {\n\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\tdata: acf.prepareForAjax( ajaxdata ),\n\t\t\t\ttype: 'post',\n\t\t\t\tdataType: 'html',\n\t\t\t\tsuccess: function ( html ) {\n\t\t\t\t\tif ( ! html ) return;\n\t\t\t\t\t$rule.replaceWith( html );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\taddGroup: function () {\n\t\t\t// vars\n\t\t\tvar $group = this.$( '.rule-group:last' );\n\n\t\t\t// duplicate\n\t\t\t$group2 = acf.duplicate( $group );\n\n\t\t\t// update h4\n\t\t\t$group2.find( 'h4' ).text( acf.__( 'or' ) );\n\n\t\t\t// remove all tr's except the first one\n\t\t\t$group2.find( 'tr' ).not( ':first' ).remove();\n\n\t\t\t// update the groups class\n\t\t\tthis.updateGroupsClass();\n\t\t},\n\n\t\tupdateGroupsClass: function () {\n\t\t\tvar $group = this.$( '.rule-group:last' );\n\n\t\t\tvar $ruleGroups = $group.closest( '.rule-groups' );\n\n\t\t\tvar rows_count = $ruleGroups.find( '.acf-table tr' ).length;\n\n\t\t\tif ( rows_count > 1 ) {\n\t\t\t\t$ruleGroups.addClass( 'rule-groups-multiple' );\n\t\t\t} else {\n\t\t\t\t$ruleGroups.removeClass( 'rule-groups-multiple' );\n\t\t\t}\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  mid\n\t *\n\t *  Calculates the model ID for a field type\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.6.5\n\t *\n\t *  @param\tstring type\n\t *  @return\tstring\n\t */\n\n\tvar modelId = function ( type ) {\n\t\treturn acf.strPascalCase( type || '' ) + 'FieldSetting';\n\t};\n\n\t/**\n\t *  registerFieldType\n\t *\n\t *  description\n\t *\n\t *  @date\t14/12/17\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tacf.registerFieldSetting = function ( model ) {\n\t\tvar proto = model.prototype;\n\t\tvar mid = modelId( proto.type + ' ' + proto.name );\n\t\tthis.models[ mid ] = model;\n\t};\n\n\t/**\n\t *  newField\n\t *\n\t *  description\n\t *\n\t *  @date\t14/12/17\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tacf.newFieldSetting = function ( field ) {\n\t\t// vars\n\t\tvar type = field.get( 'setting' ) || '';\n\t\tvar name = field.get( 'name' ) || '';\n\t\tvar mid = modelId( type + ' ' + name );\n\t\tvar model = acf.models[ mid ] || null;\n\n\t\t// bail early if no setting\n\t\tif ( model === null ) return false;\n\n\t\t// instantiate\n\t\tvar setting = new model( field );\n\n\t\t// return\n\t\treturn setting;\n\t};\n\n\t/**\n\t *  acf.getFieldSetting\n\t *\n\t *  description\n\t *\n\t *  @date\t19/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tacf.getFieldSetting = function ( field ) {\n\t\t// allow jQuery\n\t\tif ( field instanceof jQuery ) {\n\t\t\tfield = acf.getField( field );\n\t\t}\n\n\t\t// return\n\t\treturn field.setting;\n\t};\n\n\t/**\n\t * settingsManager\n\t *\n\t * @since\t5.6.5\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar settingsManager = new acf.Model( {\n\t\tactions: {\n\t\t\tnew_field: 'onNewField',\n\t\t},\n\t\tonNewField: function ( field ) {\n\t\t\tfield.setting = acf.newFieldSetting( field );\n\t\t},\n\t} );\n\n\t/**\n\t * acf.FieldSetting\n\t *\n\t * @since\t5.6.5\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tacf.FieldSetting = acf.Model.extend( {\n\t\tfield: false,\n\t\ttype: '',\n\t\tname: '',\n\t\twait: 'ready',\n\t\teventScope: '.acf-field',\n\n\t\tevents: {\n\t\t\tchange: 'render',\n\t\t},\n\n\t\tsetup: function ( field ) {\n\t\t\t// vars\n\t\t\tvar $field = field.$el;\n\n\t\t\t// set props\n\t\t\tthis.$el = $field;\n\t\t\tthis.field = field;\n\t\t\tthis.$fieldObject = $field.closest( '.acf-field-object' );\n\t\t\tthis.fieldObject = acf.getFieldObject( this.$fieldObject );\n\n\t\t\t// inherit data\n\t\t\t$.extend( this.data, field.data );\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tthis.render();\n\t\t},\n\n\t\trender: function () {\n\t\t\t// do nothing\n\t\t},\n\t} );\n\n\t/**\n\t * Accordion and Tab Endpoint Settings\n\t *\n\t * The 'endpoint' setting on accordions and tabs requires an additional class on the\n\t * field object row when enabled.\n\t *\n\t * @since\t6.0.0\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar EndpointFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: '',\n\t\tname: '',\n\t\trender: function () {\n\t\t\tvar $endpoint_setting = this.fieldObject.$setting( 'endpoint' );\n\t\t\tvar $endpoint_field = $endpoint_setting.find(\n\t\t\t\t'input[type=\"checkbox\"]:first'\n\t\t\t);\n\t\t\tif ( $endpoint_field.is( ':checked' ) ) {\n\t\t\t\tthis.fieldObject.$el.addClass( 'acf-field-is-endpoint' );\n\t\t\t} else {\n\t\t\t\tthis.fieldObject.$el.removeClass( 'acf-field-is-endpoint' );\n\t\t\t}\n\t\t},\n\t} );\n\n\tvar AccordionEndpointFieldSetting = EndpointFieldSetting.extend( {\n\t\ttype: 'accordion',\n\t\tname: 'endpoint',\n\t} );\n\n\tvar TabEndpointFieldSetting = EndpointFieldSetting.extend( {\n\t\ttype: 'tab',\n\t\tname: 'endpoint',\n\t} );\n\n\tacf.registerFieldSetting( AccordionEndpointFieldSetting );\n\tacf.registerFieldSetting( TabEndpointFieldSetting );\n\n\t/**\n\t * Date Picker\n\t *\n\t * This field type requires some extra logic for its settings\n\t *\n\t * @since\t5.0.0\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar DisplayFormatFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: '',\n\t\tname: '',\n\t\trender: function () {\n\t\t\tvar $input = this.$( 'input[type=\"radio\"]:checked' );\n\t\t\tif ( $input.val() != 'other' ) {\n\t\t\t\tthis.$( 'input[type=\"text\"]' ).val( $input.val() );\n\t\t\t}\n\t\t},\n\t} );\n\n\tvar DatePickerDisplayFormatFieldSetting = DisplayFormatFieldSetting.extend(\n\t\t{\n\t\t\ttype: 'date_picker',\n\t\t\tname: 'display_format',\n\t\t}\n\t);\n\n\tvar DatePickerReturnFormatFieldSetting = DisplayFormatFieldSetting.extend( {\n\t\ttype: 'date_picker',\n\t\tname: 'return_format',\n\t} );\n\n\tacf.registerFieldSetting( DatePickerDisplayFormatFieldSetting );\n\tacf.registerFieldSetting( DatePickerReturnFormatFieldSetting );\n\n\t/**\n\t * Date Time Picker\n\t *\n\t * This field type requires some extra logic for its settings\n\t *\n\t * @since\t5.0.0\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar DateTimePickerDisplayFormatFieldSetting =\n\t\tDisplayFormatFieldSetting.extend( {\n\t\t\ttype: 'date_time_picker',\n\t\t\tname: 'display_format',\n\t\t} );\n\n\tvar DateTimePickerReturnFormatFieldSetting =\n\t\tDisplayFormatFieldSetting.extend( {\n\t\t\ttype: 'date_time_picker',\n\t\t\tname: 'return_format',\n\t\t} );\n\n\tacf.registerFieldSetting( DateTimePickerDisplayFormatFieldSetting );\n\tacf.registerFieldSetting( DateTimePickerReturnFormatFieldSetting );\n\n\t/**\n\t * Time Picker\n\t *\n\t * This field type requires some extra logic for its settings\n\t *\n\t * @since\t5.0.0\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar TimePickerDisplayFormatFieldSetting = DisplayFormatFieldSetting.extend(\n\t\t{\n\t\t\ttype: 'time_picker',\n\t\t\tname: 'display_format',\n\t\t}\n\t);\n\n\tvar TimePickerReturnFormatFieldSetting = DisplayFormatFieldSetting.extend( {\n\t\ttype: 'time_picker',\n\t\tname: 'return_format',\n\t} );\n\n\tacf.registerFieldSetting( TimePickerDisplayFormatFieldSetting );\n\tacf.registerFieldSetting( TimePickerReturnFormatFieldSetting );\n\n\t/**\n\t * Color Picker Settings.\n\t *\n\t * @date\t16/12/20\n\t * @since\t5.9.4\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar ColorPickerReturnFormat = acf.FieldSetting.extend( {\n\t\ttype: 'color_picker',\n\t\tname: 'enable_opacity',\n\t\trender: function () {\n\t\t\tvar $return_format_setting =\n\t\t\t\tthis.fieldObject.$setting( 'return_format' );\n\t\t\tvar $default_value_setting =\n\t\t\t\tthis.fieldObject.$setting( 'default_value' );\n\t\t\tvar $labelText = $return_format_setting\n\t\t\t\t.find( 'input[type=\"radio\"][value=\"string\"]' )\n\t\t\t\t.parent( 'label' )\n\t\t\t\t.contents()\n\t\t\t\t.last();\n\t\t\tvar $defaultPlaceholder =\n\t\t\t\t$default_value_setting.find( 'input[type=\"text\"]' );\n\t\t\tvar l10n = acf.get( 'colorPickerL10n' );\n\n\t\t\tif ( this.field.val() ) {\n\t\t\t\t$labelText.replaceWith( l10n.rgba_string );\n\t\t\t\t$defaultPlaceholder.attr(\n\t\t\t\t\t'placeholder',\n\t\t\t\t\t'rgba(255,255,255,0.8)'\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\t$labelText.replaceWith( l10n.hex_string );\n\t\t\t\t$defaultPlaceholder.attr( 'placeholder', '#FFFFFF' );\n\t\t\t}\n\t\t},\n\t} );\n\tacf.registerFieldSetting( ColorPickerReturnFormat );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  fieldGroupManager\n\t *\n\t *  Generic field group functionality\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar fieldGroupManager = new acf.Model( {\n\t\tid: 'fieldGroupManager',\n\n\t\tevents: {\n\t\t\t'submit #post': 'onSubmit',\n\t\t\t'click a[href=\"#\"]': 'onClick',\n\t\t\t'click .acf-delete-field-group': 'onClickDeleteFieldGroup',\n\t\t\t'blur input#title': 'validateTitle',\n\t\t\t'input input#title': 'validateTitle',\n\t\t},\n\n\t\tfilters: {\n\t\t\tfind_fields_args: 'filterFindFieldArgs',\n\t\t\tfind_fields_selector: 'filterFindFieldsSelector',\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tacf.addAction( 'prepare', this.maybeInitNewFieldGroup );\n\t\t\tacf.add_filter( 'select2_args', this.setBidirectionalSelect2Args );\n\t\t\tacf.add_filter(\n\t\t\t\t'select2_ajax_data',\n\t\t\t\tthis.setBidirectionalSelect2AjaxDataArgs\n\t\t\t);\n\t\t},\n\n\t\tsetBidirectionalSelect2Args: function (\n\t\t\targs,\n\t\t\t$select,\n\t\t\tsettings,\n\t\t\tfield,\n\t\t\tinstance\n\t\t) {\n\t\t\tif ( field?.data?.( 'key' ) !== 'bidirectional_target' ) return args;\n\n\t\t\targs.dropdownCssClass = 'field-type-select-results';\n\n\t\t\targs.templateResult = function ( selection ) {\n\t\t\t\tif ( 'undefined' !== typeof selection.element ) {\n\t\t\t\t\treturn selection;\n\t\t\t\t}\n\n\t\t\t\tif ( selection.children ) {\n\t\t\t\t\treturn selection.text;\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\tselection.loading ||\n\t\t\t\t\t( selection.element &&\n\t\t\t\t\t\tselection.element.nodeName === 'OPTGROUP' )\n\t\t\t\t) {\n\t\t\t\t\tvar $selection = $( '<span class=\"acf-selection\"></span>' );\n\t\t\t\t\t$selection.html( acf.escHtml( selection.text ) );\n\t\t\t\t\treturn $selection;\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t'undefined' === typeof selection.human_field_type ||\n\t\t\t\t\t'undefined' === typeof selection.field_type ||\n\t\t\t\t\t'undefined' === typeof selection.this_field\n\t\t\t\t) {\n\t\t\t\t\treturn selection.text;\n\t\t\t\t}\n\n\t\t\t\tvar $selection = $(\n\t\t\t\t\t'<i title=\"' +\n\t\t\t\t\t\tacf.escHtml( selection.human_field_type ) +\n\t\t\t\t\t\t'\" class=\"field-type-icon field-type-icon-' +\n\t\t\t\t\t\tacf.escHtml(\n\t\t\t\t\t\t\tselection.field_type.replaceAll( '_', '-' )\n\t\t\t\t\t\t) +\n\t\t\t\t\t\t'\"></i><span class=\"acf-selection has-icon\">' +\n\t\t\t\t\t\tacf.escHtml( selection.text ) +\n\t\t\t\t\t\t'</span>'\n\t\t\t\t);\n\t\t\t\tif ( selection.this_field ) {\n\t\t\t\t\t$selection\n\t\t\t\t\t\t.last()\n\t\t\t\t\t\t.append(\n\t\t\t\t\t\t\t'<span class=\"acf-select2-default-pill\">' +\n\t\t\t\t\t\t\t\tacf.__( 'This Field' ) +\n\t\t\t\t\t\t\t\t'</span>'\n\t\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\t$selection.data( 'element', selection.element );\n\t\t\t\treturn $selection;\n\t\t\t};\n\n\t\t\treturn args;\n\t\t},\n\n\t\tsetBidirectionalSelect2AjaxDataArgs: function (\n\t\t\tdata,\n\t\t\targs,\n\t\t\t$input,\n\t\t\tfield,\n\t\t\tinstance\n\t\t) {\n\t\t\tif ( data.field_key !== 'bidirectional_target' ) return data;\n\n\t\t\tconst $fieldObject = acf.findFieldObjects( { child: field } );\n\t\t\tconst fieldObject = acf.getFieldObject( $fieldObject );\n\t\t\tdata.field_key = '_acf_bidirectional_target';\n\t\t\tdata.parent_key = fieldObject.get( 'key' );\n\t\t\tdata.field_type = fieldObject.get( 'type' );\n\n\t\t\t// This might not be needed, but I wanted to figure out how to get a field setting in the JS API when the key isn't unique.\n\t\t\tdata.post_type = acf\n\t\t\t\t.getField(\n\t\t\t\t\tacf.findFields( { parent: $fieldObject, key: 'post_type' } )\n\t\t\t\t)\n\t\t\t\t.val();\n\n\t\t\treturn data;\n\t\t},\n\n\t\tmaybeInitNewFieldGroup: function () {\n\t\t\tlet $field_list_wrapper = $(\n\t\t\t\t'#acf-field-group-fields > .inside > .acf-field-list-wrap.acf-auto-add-field'\n\t\t\t);\n\n\t\t\tif ( $field_list_wrapper.length ) {\n\t\t\t\t$( '.acf-headerbar-actions .add-field' ).trigger( 'click' );\n\t\t\t\t$( '.acf-title-wrap #title' ).trigger( 'focus' );\n\t\t\t}\n\t\t},\n\n\t\tonSubmit: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar $title = $( '.acf-title-wrap #title' );\n\n\t\t\t// empty\n\t\t\tif ( ! $title.val() ) {\n\t\t\t\t// prevent default\n\t\t\t\te.preventDefault();\n\n\t\t\t\t// unlock form\n\t\t\t\tacf.unlockForm( $el );\n\n\t\t\t\t// focus\n\t\t\t\t$title.trigger( 'focus' );\n\t\t\t}\n\t\t},\n\n\t\tonClick: function ( e ) {\n\t\t\te.preventDefault();\n\t\t},\n\n\t\tonClickDeleteFieldGroup: function ( e, $el ) {\n\t\t\te.preventDefault();\n\t\t\t$el.addClass( '-hover' );\n\n\t\t\t// Add confirmation tooltip.\n\t\t\tacf.newTooltip( {\n\t\t\t\tconfirm: true,\n\t\t\t\ttarget: $el,\n\t\t\t\tcontext: this,\n\t\t\t\ttext: acf.__( 'Move field group to trash?' ),\n\t\t\t\tconfirm: function () {\n\t\t\t\t\twindow.location.href = $el.attr( 'href' );\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\t$el.removeClass( '-hover' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tvalidateTitle: function ( e, $el ) {\n\t\t\tlet $submitButton = $( '.acf-publish' );\n\n\t\t\tif ( ! $el.val() ) {\n\t\t\t\t$el.addClass( 'acf-input-error' );\n\t\t\t\t$submitButton.addClass( 'disabled' );\n\t\t\t\t$( '.acf-publish' ).addClass( 'disabled' );\n\t\t\t} else {\n\t\t\t\t$el.removeClass( 'acf-input-error' );\n\t\t\t\t$submitButton.removeClass( 'disabled' );\n\t\t\t\t$( '.acf-publish' ).removeClass( 'disabled' );\n\t\t\t}\n\t\t},\n\n\t\tfilterFindFieldArgs: function ( args ) {\n\t\t\targs.visible = true;\n\n\t\t\tif (\n\t\t\t\targs.parent &&\n\t\t\t\t( args.parent.hasClass( 'acf-field-object' ) ||\n\t\t\t\t\targs.parent.hasClass( 'acf-browse-fields-modal-wrap' ) ||\n\t\t\t\t\targs.parent.parents( '.acf-field-object' ).length )\n\t\t\t) {\n\t\t\t\targs.visible = false;\n\t\t\t\targs.excludeSubFields = true;\n\t\t\t}\n\n\t\t\t// If the field has any open subfields, don't exclude subfields as they're already being displayed.\n\t\t\tif (\n\t\t\t\targs.parent &&\n\t\t\t\targs.parent.find( '.acf-field-object.open' ).length\n\t\t\t) {\n\t\t\t\targs.excludeSubFields = false;\n\t\t\t}\n\n\t\t\treturn args;\n\t\t},\n\n\t\tfilterFindFieldsSelector: function ( selector ) {\n\t\t\treturn selector + ', .acf-field-acf-field-group-settings-tabs';\n\t\t},\n\t} );\n\n\t/**\n\t *  screenOptionsManager\n\t *\n\t *  Screen options functionality\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar screenOptionsManager = new acf.Model( {\n\t\tid: 'screenOptionsManager',\n\t\twait: 'prepare',\n\n\t\tevents: {\n\t\t\t'change #acf-field-key-hide': 'onFieldKeysChange',\n\t\t\t'change #acf-field-settings-tabs': 'onFieldSettingsTabsChange',\n\t\t\t'change [name=\"screen_columns\"]': 'render',\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\t// vars\n\t\t\tvar $div = $( '#adv-settings' );\n\t\t\tvar $append = $( '#acf-append-show-on-screen' );\n\n\t\t\t// append\n\t\t\t$div.find( '.metabox-prefs' ).append( $append.html() );\n\t\t\t$div.find( '.metabox-prefs br' ).remove();\n\n\t\t\t// clean up\n\t\t\t$append.remove();\n\n\t\t\t// initialize\n\t\t\tthis.$el = $( '#screen-options-wrap' );\n\n\t\t\t// render\n\t\t\tthis.render();\n\t\t},\n\n\t\tisFieldKeysChecked: function () {\n\t\t\treturn this.$el.find( '#acf-field-key-hide' ).prop( 'checked' );\n\t\t},\n\n\t\tisFieldSettingsTabsChecked: function () {\n\t\t\tconst $input = this.$el.find( '#acf-field-settings-tabs' );\n\n\t\t\t// Screen option is hidden by filter.\n\t\t\tif ( ! $input.length ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\treturn $input.prop( 'checked' );\n\t\t},\n\n\t\tgetSelectedColumnCount: function () {\n\t\t\treturn this.$el\n\t\t\t\t.find( 'input[name=\"screen_columns\"]:checked' )\n\t\t\t\t.val();\n\t\t},\n\n\t\tonFieldKeysChange: function ( e, $el ) {\n\t\t\tvar val = this.isFieldKeysChecked() ? 1 : 0;\n\t\t\tacf.updateUserSetting( 'show_field_keys', val );\n\t\t\tthis.render();\n\t\t},\n\n\t\tonFieldSettingsTabsChange: function () {\n\t\t\tconst val = this.isFieldSettingsTabsChecked() ? 1 : 0;\n\t\t\tacf.updateUserSetting( 'show_field_settings_tabs', val );\n\t\t\tthis.render();\n\t\t},\n\n\t\trender: function () {\n\t\t\tif ( this.isFieldKeysChecked() ) {\n\t\t\t\t$( '#acf-field-group-fields' ).addClass( 'show-field-keys' );\n\t\t\t} else {\n\t\t\t\t$( '#acf-field-group-fields' ).removeClass( 'show-field-keys' );\n\t\t\t}\n\n\t\t\tif ( ! this.isFieldSettingsTabsChecked() ) {\n\t\t\t\t$( '#acf-field-group-fields' ).addClass( 'hide-tabs' );\n\t\t\t\t$( '.acf-field-settings-main' )\n\t\t\t\t\t.removeClass( 'acf-hidden' )\n\t\t\t\t\t.prop( 'hidden', false );\n\t\t\t} else {\n\t\t\t\t$( '#acf-field-group-fields' ).removeClass( 'hide-tabs' );\n\n\t\t\t\t$( '.acf-field-object' ).each( function () {\n\t\t\t\t\tconst tabFields = acf.getFields( {\n\t\t\t\t\t\ttype: 'tab',\n\t\t\t\t\t\tparent: $( this ),\n\t\t\t\t\t\texcludeSubFields: true,\n\t\t\t\t\t\tlimit: 1,\n\t\t\t\t\t} );\n\n\t\t\t\t\tif ( tabFields.length ) {\n\t\t\t\t\t\ttabFields[ 0 ].tabs.set( 'initialized', false );\n\t\t\t\t\t}\n\n\t\t\t\t\tacf.doAction( 'show', $( this ) );\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\tif ( this.getSelectedColumnCount() == 1 ) {\n\t\t\t\t$( 'body' ).removeClass( 'columns-2' );\n\t\t\t\t$( 'body' ).addClass( 'columns-1' );\n\t\t\t} else {\n\t\t\t\t$( 'body' ).removeClass( 'columns-1' );\n\t\t\t\t$( 'body' ).addClass( 'columns-2' );\n\t\t\t}\n\t\t},\n\t} );\n\n\t/**\n\t *  appendFieldManager\n\t *\n\t *  Appends fields together\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar appendFieldManager = new acf.Model( {\n\t\tactions: {\n\t\t\tnew_field: 'onNewField',\n\t\t},\n\n\t\tonNewField: function ( field ) {\n\t\t\t// bail early if not append\n\t\t\tif ( ! field.has( 'append' ) ) return;\n\n\t\t\t// vars\n\t\t\tvar append = field.get( 'append' );\n\t\t\tvar $sibling = field.$el\n\t\t\t\t.siblings( '[data-name=\"' + append + '\"]' )\n\t\t\t\t.first();\n\n\t\t\t// bail early if no sibling\n\t\t\tif ( ! $sibling.length ) return;\n\n\t\t\t// ul\n\t\t\tvar $div = $sibling.children( '.acf-input' );\n\t\t\tvar $ul = $div.children( 'ul' );\n\n\t\t\t// create ul\n\t\t\tif ( ! $ul.length ) {\n\t\t\t\t$div.wrapInner( '<ul class=\"acf-hl\"><li></li></ul>' );\n\t\t\t\t$ul = $div.children( 'ul' );\n\t\t\t}\n\n\t\t\t// li\n\t\t\tvar html = field.$( '.acf-input' ).html();\n\t\t\tvar $li = $( '<li>' + html + '</li>' );\n\t\t\t$ul.append( $li );\n\t\t\t$ul.attr( 'data-cols', $ul.children().length );\n\n\t\t\t// clean up\n\t\t\tfield.remove();\n\t\t},\n\t} );\n} )( jQuery );\n", "import toPropertyKey from \"./toPropertyKey.js\";\nexport default function _defineProperty(obj, key, value) {\n  key = toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}", "import _typeof from \"./typeof.js\";\nexport default function _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}", "import _typeof from \"./typeof.js\";\nimport toPrimitive from \"./toPrimitive.js\";\nexport default function _toPropertyKey(arg) {\n  var key = toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}", "export default function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import './_field-group.js';\nimport './_field-group-field.js';\nimport './_field-group-settings.js';\nimport './_field-group-conditions.js';\nimport './_field-group-fields.js';\nimport './_field-group-locations.js';\nimport './_field-group-compatibility.js';\nimport './_browse-fields-modal.js';\n"], "names": ["$", "undefined", "acf", "browseFieldsModal", "data", "openedBy", "currentFieldType", "popularFieldTypes", "events", "setup", "props", "extend", "$el", "tmpl", "render", "initialize", "open", "lockFocusToModal", "find", "focus", "doAction", "html", "getFieldTypes", "category", "search", "fieldTypes", "get", "Object", "values", "_objectSpread", "filter", "fieldType", "includes", "name", "pro", "label", "toLowerCase", "labelParts", "split", "match", "startsWith", "length", "for<PERSON>ach", "part", "$tabs", "self", "each", "append", "getFieldTypeHTML", "initializeFieldLabel", "initializeFieldType", "onChangeFieldType", "iconName", "replaceAll", "decodeFieldTypeURL", "url", "renderFieldTypeDesc", "fieldTypeInfo", "fieldTypeFilter", "args", "parseArgs", "description", "doc_url", "tutorial_url", "preview_image", "text", "attr", "show", "hide", "parent", "isPro", "$upgateToProButton", "$upgradeToUnlockButton", "_fieldObject$data", "fieldObject", "type", "set", "isFieldTypePopular", "selectedFieldType", "x", "uppercaseCategory", "toUpperCase", "slice", "searchTabElement", "setTimeout", "click", "labelText", "$fieldLabel", "val", "updateFieldObjectFieldLabel", "trigger", "removeClass", "addClass", "onSearchFieldTypes", "e", "$modal", "inputVal", "searchString", "resultsHtml", "matches", "trim", "onClickBrowsePopular", "first", "onClickSelectField", "$fieldTypeSelect", "close", "onClickFieldType", "$fieldType", "currentTarget", "onClickClose", "onPressEscapeClose", "key", "returnFocusToOrigin", "remove", "models", "Modal", "newBrowseFieldsModal", "window", "j<PERSON><PERSON><PERSON>", "_acf", "getCompatibility", "field_group", "save_field", "$field", "getFieldObject", "save", "delete_field", "animate", "delete", "update_field_meta", "value", "prop", "delete_field_meta", "field_object", "model", "o", "$settings", "tag", "tags", "splice", "join", "selector", "str_replace", "_add_action", "callback", "add_action", "apply", "arguments", "_add_filter", "add_filter", "_add_event", "event", "substr", "indexOf", "context", "document", "on", "closest", "_set_$field", "setting", "actionManager", "Model", "actions", "open_field_object", "close_field_object", "add_field_object", "duplicate_field_object", "delete_field_object", "change_field_object_type", "change_field_object_label", "change_field_object_name", "change_field_object_parent", "sortstop_field_object", "onOpenFieldObject", "field", "onCloseFieldObject", "onAddFieldObject", "onDuplicateFieldObject", "onDeleteFieldObject", "onChangeFieldObjectType", "onChangeFieldObjectLabel", "onChangeFieldObjectName", "onChangeFieldObjectParent", "ConditionalLogicFieldSetting", "FieldSetting", "$rule", "scope", "ruleData", "$input", "$td", "$toggle", "$control", "$groups", "$rules", "$tabLabel", "$div", "enable", "disable", "renderRules", "renderRule", "renderField", "renderOperator", "renderValue", "choices", "validFieldTypes", "cid", "$select", "getFieldObjects", "map", "choice", "id", "<PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON>", "__", "disabled", "conditionTypes", "getConditionTypes", "getType", "indents", "getParents", "repeat", "push", "renderSelect", "findFieldObject", "prototype", "operator", "conditionType", "Array", "$newSelect", "detach", "onChangeToggle", "onClickAddGroup", "addGroup", "$group", "$group2", "duplicate", "not", "onFocusField", "onChangeField", "onChangeOperator", "onClickAdd", "onClickRemove", "siblings", "registerFieldSetting", "conditionalLog<PERSON><PERSON><PERSON><PERSON>", "duplicate_field_objects", "onDuplicateFieldObjects", "children", "newField", "prevField", "$selects", "child", "add", "FieldObject", "eventScope", "fieldTypeSelect2", "change", "changed", "inherit", "getInputId", "$meta", "$handle", "$setting", "getParent", "limit", "pop", "getFields", "getInputName", "newInput", "inputId", "inputName", "getProp", "has", "setProp", "prevVal", "keys", "getName", "getTypeLabel", "types", "checkCopyable", "makeCopyable", "navigator", "clipboard", "initializeFieldTypeSelect2", "hasClass", "fn", "select2", "amd", "require", "err", "console", "warn", "newSelect2", "ajax", "multiple", "allowNull", "suppressFilters", "dropdownCssClass", "templateResult", "selection", "loading", "element", "nodeName", "$selection", "escHtml", "templateSelection", "target", "parents", "onKeyDownSelect", "addProFields", "PROFieldTypes", "$layoutGroup", "$contentGroup", "entries", "$useGroup", "menu_order", "required", "parseInt", "strSlugify", "refresh", "isOpen", "onClickCopy", "stopPropagation", "writeText", "then", "onClickEdit", "$target", "onChangeSettingsTab", "onFocusEdit", "$rowOptions", "onBlurEdit", "focusDelayMilliseconds", "$rowOptionsBlurElement", "$rowOptionsFocusElement", "activeElement", "is", "hideEmptyTabs", "slideDown", "which", "slideUp", "serialize", "submit", "onChange", "onChanged", "onChangeLabel", "applyFilters", "strSanitize", "onChangeName", "alert", "onChangeRequired", "newVal", "removeAnimate", "onClickDelete", "shift<PERSON>ey", "tooltip", "newTooltip", "confirmRemove", "confirm", "cancel", "$list", "$fields", "findFieldObjects", "sibling", "endHeight", "complete", "new<PERSON>ey", "uniqid", "$newField", "replace", "end", "copy", "isNumeric", "i", "$label", "wipe", "prevId", "prev<PERSON><PERSON>", "rename", "move", "has<PERSON><PERSON>ed", "popup", "step1", "newPopup", "title", "width", "ajaxData", "action", "field_id", "prepareForAjax", "dataType", "success", "step2", "content", "step3", "preventDefault", "startButtonLoading", "field_group_id", "step4", "wp", "a11y", "speak", "browseFields", "modal", "onChangeType", "changeTimeout", "clearTimeout", "changeType", "newType", "prevType", "prevClass", "newClass", "abort", "$oldSettings", "tab", "$tabSettings", "removeData", "$newSettings", "showFieldTypeSettings", "$loading", "before", "prefix", "xhr", "response", "isAjaxSuccess", "settings", "tabs", "$tab", "tab<PERSON>ontent", "prepend", "updateParent", "ID", "$tabContent", "tabName", "$tabLink", "list", "newFieldObject", "fields", "eventManager", "priority", "addFieldActions", "pluralAction", "singleAction", "singleEvent", "fieldObjects", "arrayArgs", "plural<PERSON><PERSON><PERSON>", "unshift", "singleCallback", "variations", "variation", "addAction", "fieldManager", "removed_field_object", "onSubmit", "setFieldMenuOrder", "renderFields", "onHoverSortable", "sortable", "helper", "clone", "currentName", "Math", "random", "toString", "handle", "connectWith", "start", "ui", "item", "placeholder", "height", "update", "onRemovedField", "onReorderField", "onDeleteField", "onDuplicateField", "eq", "addField", "$el2", "$type", "locationManager", "wait", "addProLocations", "updateGroupsClass", "PROLocationTypes", "$formsGroup", "onClickAddRule", "addRule", "onClickRemoveRule", "removeRule", "onChangeRemoveRule", "changeRule", "$tr", "ajaxdata", "rule", "group", "replaceWith", "$ruleGroups", "rows_count", "modelId", "strPascalCase", "proto", "mid", "newFieldSetting", "getFieldSetting", "getField", "settingsManager", "new_field", "onNewField", "$fieldObject", "EndpointFieldSetting", "$endpoint_setting", "$endpoint_field", "AccordionEndpointFieldSetting", "TabEndpointFieldSetting", "DisplayFormatFieldSetting", "DatePickerDisplayFormatFieldSetting", "DatePickerReturnFormatFieldSetting", "DateTimePickerDisplayFormatFieldSetting", "DateTimePickerReturnFormatFieldSetting", "TimePickerDisplayFormatFieldSetting", "TimePickerReturnFormatFieldSetting", "ColorPickerReturnFormat", "$return_format_setting", "$default_value_setting", "$labelText", "contents", "last", "$defaultPlaceholder", "l10n", "rgba_string", "hex_string", "fieldGroupManager", "filters", "find_fields_args", "find_fields_selector", "maybeInitNewFieldGroup", "setBidirectionalSelect2Args", "setBidirectionalSelect2AjaxDataArgs", "instance", "_field$data", "call", "human_field_type", "field_type", "this_field", "field_key", "parent_key", "post_type", "findFields", "$field_list_wrapper", "$title", "unlockForm", "onClick", "onClickDeleteFieldGroup", "location", "href", "validateTitle", "$submitButton", "filterFindFieldArgs", "visible", "excludeSubFields", "filterFindFieldsSelector", "screenOptionsManager", "$append", "isFieldKeysChecked", "isFieldSettingsTabsChecked", "getSelectedColumnCount", "onFieldKeysChange", "updateUserSetting", "onFieldSettingsTabsChange", "tabFields", "appendFieldManager", "$sibling", "$ul", "wrapInner", "$li"], "sourceRoot": ""}